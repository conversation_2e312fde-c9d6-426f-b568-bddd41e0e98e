$(document).ajaxStart(function() {
    $("#Loading").show();
});

$(document).ajaxStop(function() {
    $("#Loading").hide();
});

$(document).ready(function() {
    getEventRules();
});

var eventIdToEventRule;

function getEventRules() {

    $('#eventRulesTable').children().remove();
    var html = ""
    $.ajax({
        url: contexturl + "/superadmin/event_rules/list",
        type: 'GET',
        async: false,
        error: function(e) {
            alert('Unable to fetch event rules. Please refresh the Page!');
        },
        success: function(data) {
            eventIdToEventRule = data.data.reduce((map, obj) => (map[obj.id] = obj, map), {});
            $.each(data.data, function(i, item) {
                var scheduleTypeName = "cron"
                if(item.scheduleType != 1) scheduleTypeName = "rate"
                item.scheduleTypeName = scheduleTypeName
                html += "<tr><td>" + item.name + "</td><td>" + item.label + "</td><td>" + item.expression + "</td><td>" + scheduleTypeName + "</td>" +
                "<td><input type=\"button\" value=\"Edit\" class=\"pad\" onClick=\"addEditEventRules(\'" + item.id + "\')\" /></td></tr>";
            });
            $('#eventRulesTable').append(html);
        }
    });
}

function addEditEventRules(id) {
    var eventRule = eventIdToEventRule[id];
    if (!eventRule) {
        $('#eventruleName').val("socure-batch-");
        $('#eventruleLabel').val("");
        $('#eventruleExpression').val("");
        $('#create_submit_btn').val("");
    } else {
        $('#eventruleName').val(eventRule.name);
        $('#eventruleLabel').val(eventRule.label);
        $('#eventruleExpression').val(eventRule.expression);
        $('#eventruleScheduleType').val(eventRule.scheduleTypeName);
        $('#create_eventrule_submit_btn').val(eventRule.id);
    }
    document.getElementById("create_eventrule_container").style.display = "block";
}

function closeCreateEventRuleForm() {
    document.getElementById("create_eventrule_container").style.display = "none";
    $('#create_eventrule_submit_btn').val('');
}

function createEventRule() {

    var val = $('#create_eventrule_submit_btn').val();
    var payload = getCreateEventRulePayload(val);
    if(!payload){
        return
    }

    $.ajax({
        url: contexturl + "/superadmin/event_rules/update",
        data: JSON.stringify(payload),
        type: 'PUT',
        contentType: 'application/json',
        async: false,
        error: function(e) {
            if(val != ''){
                alert('Unable to update event rules. Please refresh the Page and try again!');
            } else {
                alert('Unable to insert event rules. Please refresh the Page and try again!');
            }
            closeCreateEventRuleForm();
            getEventRules();
        },
        success: function(data) {

            if(data.data > 0){
                if(val != ''){
                    alert('Event rule Edited Successfully!');
                } else {
                    alert('Event rule Added Successfully!');
                }
            } else {
                if(val != ''){
                    alert('Unable to update event rules. Please refresh the Page and try again!');
                } else {
                    alert('Unable to insert event rules. Please refresh the Page and try again!');
                }
            }
            closeCreateEventRuleForm();
            getEventRules();

        }
    });

}

function getCreateEventRulePayload(eventRuleId) {

    var eventruleName = ($("#eventruleName").val()).trim();
    if (eventruleName == '') {
        alert('Event Rule Name is empty!');
        return;
    }

    if (!eventruleName.match(/^socure-batch-.+-(prod|stage|ds|disaster|gen)$/g)) {
        alert('Event Rule Name is not valid!\nExpected format: socure-batch-<name>-<environment>');
        return;
    }

    var eventruleLabel = ($("#eventruleLabel").val()).trim();
    if (eventruleLabel == '') {
        alert('Event Rule Label is empty!');
        return;
    }

    var eventruleScheduleType = ($("#eventruleScheduleType").val()).trim();
    if (eventruleScheduleType == '') {
        alert('Invalid Schedule Type');
        return;
    }

    var eventruleExpression = ($("#eventruleExpression").val()).trim();
    if (eventruleExpression == '') {
        alert('Event Rule Expression is empty!');
        return;
    }

    var eventruleScheduleTypeId = 1
    if(eventruleScheduleType == "rate"){
        eventruleScheduleTypeId = 2
    }

    var payload = {
        name: eventruleName,
        label: eventruleLabel,
        expression: eventruleExpression,
        scheduleType: eventruleScheduleTypeId,
        status: 1
    }

    if(eventRuleId != ''){
        payload.id = parseInt(eventRuleId)
    }

    return payload;

}
