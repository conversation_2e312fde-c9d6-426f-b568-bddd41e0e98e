import React, { useCallback, useMemo } from "react";

const IdsTextarea = ({ value, onChange}) => {

  const handleIdsChange = useCallback((e) => {
    const val = e.target.value;
    let splitValues = val && val.split(',');
    splitValues = splitValues
      ? splitValues.map(val => val.trim())
      : [];
    onChange && onChange(splitValues);
  }, [ onChange ])

  const idsValue = useMemo(() => {
    return value
      ? value.join(', ')
      : ''
  }, [ value ])

  return <textarea value={idsValue} onChange={handleIdsChange} className="socure-c-text-area" rows={5} placeholder='Comma separated ids ex: 1234, 5678' />
}

export default IdsTextarea;