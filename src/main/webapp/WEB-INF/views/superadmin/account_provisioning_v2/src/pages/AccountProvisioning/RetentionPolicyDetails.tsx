import React from "react";
import { InputType, TextInput, Dropdown } from '@socure/components';
import styled from 'styled-components';
import StyledSpecialRowDropdown from "@socure/components/src/components/TreeGrid/components/styled/StyledSpecialRowDropdown";

const CustomDeletionDetails = styled.div`
    display: flex;
    margin-bottom: 10px;

    .socure-c-textInput-OutterContainer {
        margin-right: 10px;
    }
`

const destroyPIICadences = [
    'Days',
    'Weeks',
    'Months',
    'Years'
];
const valueNotEmptyMessage = "Value should not be empty";

type RetentionPolicyDetailsProps = {
    retentionDetails: {
        routine: string,
        cadence: number,
        errorMessageOnValidation: string
    }, 
    onChange: (field: string, value: string | number, errorMessageOnValidation: string) => void
}

const validate = (cadence: number, routine: string) => {
    if (cadence && Number(cadence) === 0) {
        return 'Value cannot be 0';
    }
    switch (routine) {
        case 'Days':
            if (cadence > 365) return 'Days should not exceed 365';
            break;
        case 'Weeks':
            if (cadence > 52) return 'Weeks should not exceed 52';
            break;
        case 'Months':
            if (cadence > 12) return 'Months should not exceed 12';
            break;
        case 'Years':
            if (cadence > 7) return 'Years should not exceed 7';
            break;
    }
    return ''
}


const RetentionPolicyDetails: React.FC<RetentionPolicyDetailsProps> = ({ retentionDetails, onChange }) => {

    const changeHandler = (field: string, value: string | number)  => {
        const errorMessageOnValidation = field === 'cadence' ? validate(value as number, retentionDetails.routine) : validate(retentionDetails.cadence, value as string);
        onChange(field, value, errorMessageOnValidation);
    }

    return <CustomDeletionDetails>
            <TextInput type={InputType.integer} value={retentionDetails.cadence} onChange={value => changeHandler('cadence', value)} doValidate
                errorMsg={(!retentionDetails.cadence || retentionDetails.routine === '' ) ? valueNotEmptyMessage : retentionDetails.errorMessageOnValidation} />
            <StyledSpecialRowDropdown>
                <Dropdown selected={retentionDetails.routine} onChange={value => changeHandler('routine', value)}>
                    {destroyPIICadences.map(cadence => <Dropdown.Item value={cadence}>{cadence}</Dropdown.Item>)}
                </Dropdown>
            </StyledSpecialRowDropdown>
        </CustomDeletionDetails>
}


export default RetentionPolicyDetails;