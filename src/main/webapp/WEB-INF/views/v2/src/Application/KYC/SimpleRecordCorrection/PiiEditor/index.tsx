import React, { useCallback } from "react";
import { <PERSON><PERSON>, Card, CardContent, FormControl, Grid, InputLabel, MenuItem, Select, Typography } from "@mui/material";
import RestoreIcon from '@mui/icons-material/Restore';
import DeleteIcon from '@mui/icons-material/Delete';
import { deepClone } from "../../../../Utilities";
import AddValue from "./AddValue";

interface IPiiEditorProps {
    field: string;
    editingData: any;
    setEditingData: React.Dispatch<React.SetStateAction<any>>;
}

const PiiEditor: React.FC<IPiiEditorProps> = (props: IPiiEditorProps) => {
    const { field, editingData, setEditingData } = props;
    const { summarisedData, addedData, deceased } = editingData;

    const toggleDeleteExistingItem = useCallback((idx: number) => {
        summarisedData[field][idx].deleted = !summarisedData[field][idx].deleted;
        setEditingData(deepClone(editingData));
    }, [editingData, summarisedData, field, setEditingData]);

    const deleteAddedItem = useCallback((idx: number) => {
        addedData[field].splice(idx, 1);
        setEditingData(deepClone(editingData));
    }, [editingData, addedData, field, setEditingData]);

    const renderValueList = () => {
        if (field === "DECEASED") return null;

        return <Grid container direction="row" spacing={1}>
            {summarisedData[field].map((piis: any, idx: number) => {
                const { deleted, value } = piis;
                let display = value;
                let size = 2.4;
                if (field === "A") {
                    const [street, city, zip, state] = value.split("|");
                    display = `${street},\n${city}, ${state}, ${zip}`
                    size = 3;
                }
                let sx = {};
                let hdr = "Existing Data";
                if (deleted) {
                    sx = {
                        bgcolor: "#d32f2f",
                        color: "white"
                    }
                    hdr = "Deleted Data"
                }
                return <Grid sm={size} item key={"existing-" + idx}>
                    <Card sx={sx} variant="outlined">
                        <CardContent>
                            <Typography variant="caption">{hdr}</Typography>
                            <Grid container direction="row" spacing={1} justifyContent="space-between" alignItems="center">
                                <Grid item key="value">
                                    <Typography variant="h6" component="pre">{display}</Typography>
                                </Grid>
                                <Grid item key="toggle-delete">
                                    <Button
                                        variant="contained"
                                        size="small"
                                        color={deleted ? "success" : "error"}
                                        onClick={() => toggleDeleteExistingItem(idx)}
                                    >
                                        {
                                            deleted ?
                                                <RestoreIcon fontSize="small" /> :
                                                <DeleteIcon fontSize="small" />
                                        }
                                    </Button>
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                </Grid>
            })}
            {addedData[field].map((piis: any, idx: number) => {
                let display = piis.cleanedValue;
                let size = 2.4;
                if (field === "A") {
                    const { AX, AY, AZ, AS } = piis;
                    display = `${AX},\n${AY}, ${AS}, ${AZ}`
                    size = 3;
                }
                return <Grid sm={size} item key={"added-" + idx}>
                    <Card sx={{ bgcolor: "#2e7d32", color: "white" }} variant="outlined">
                        <CardContent>
                            <Typography variant="caption">{"Newly Added Data"}</Typography>
                            <Grid container direction="row" spacing={1} justifyContent="space-between" alignItems="center">
                                <Grid item key="value">
                                    <Typography variant="h6" component="pre">{display}</Typography>
                                </Grid>
                                <Grid item key="toggle-delete">
                                    <Button
                                        variant="contained"
                                        size="small"
                                        color={"error"}
                                        onClick={() => deleteAddedItem(idx)}
                                    >
                                        <DeleteIcon fontSize="small" />
                                    </Button>
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                </Grid>
            })}
        </Grid>;
    }

    const addData = useCallback((value: any) => {
        editingData.addedData[field].push(value);
        setEditingData(deepClone(editingData))
    }, [editingData, setEditingData, field]);

    const setDeceasedStatus = useCallback((value: any) => {
        editingData.deceased = value;
        setEditingData(deepClone(editingData))
    }, [editingData, setEditingData]);

    if (field !== "DECEASED") {
        return <Grid container direction="column" spacing={1}>
            <Grid item key="tools">
                <AddValue field={field} addData={addData} />
            </Grid>
            <Grid item key="values">
                {renderValueList()}
            </Grid>
        </Grid>;
    } else {
        return <FormControl sx={{ width: "25vw"}}>
            <InputLabel id="living-status-selector-label">{"Deceased Status"}</InputLabel>
            <Select
                labelId="living-status-selector-label"
                value={deceased}
                label="Deceased Status"
                size="small"
                onChange={e => setDeceasedStatus(e.target.value)}
                fullWidth
            >
                <MenuItem value={"0"} key={"0"}>
                    {"Alive"}
                </MenuItem>
                <MenuItem value={"1"} key={"1"}>
                    {"Deceased"}
                </MenuItem>
            </Select>
        </FormControl>;
    }


}

export default PiiEditor;