package me.socure.resolver

import me.socure.account.client.ManageAccountsV2ClientImpl
import org.mockito.Mockito.when
import org.scalatest.{FunSuite, Matchers}
import org.scalatest.mock.MockitoSugar

import scala.concurrent.Future

class AccountDetailsResolverTest extends FunSuite with MockitoSugar with Matchers {
  val manageAccountsV2Client = mock[ManageAccountsV2ClientImpl]

  val service = new AccountDetailsResolver
  service.manageAccountsV2Client = manageAccountsV2Client

  test("get Consent Reason for the valid accountId"){
    val accountId = 10
    val consentId = 1
    when(manageAccountsV2Client.getConsentReasonId(accountId)) thenReturn(Future.successful(Right(consentId)))
    val res = service.getConsentReason(accountId)
    res shouldBe consentId
  }

  test("update Consent Reason for the valid accountId"){
    val accountId = 10
    val consentId = 2
    when(manageAccountsV2Client.updateConsentId(accountId, consentId)) thenReturn(Future.successful(Right(1)))
    val res = service.updateConsentReason(accountId, consentId)
    res shouldBe 1
  }

}
