var batchJobPage = 1
var nextToken = null;
function callUpdateBackup(payload) {

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/ingest",
        data: payload,
        type: 'POST',
        beforeSend: function() {
            $('#Loading').show();
        },
        complete: function() {
            showShowHistory();
            hidePagination();
            $('#Loading').hide();
        },
        error: function(e) {
            handleLogs(e.responseJSON.logs, false);
            alert("Request Failed! " + e.responseJSON.errorMessage);
        },
        success: function(data) {
            handleLogs(data.logs, false);
            alert("Request Submited!");
        }
    });
}

function next() {
    $('#back').show();
    var number = $('#number').val();
    number = isNaN(number) ? 1 : number;
    number++;
    getHistory(number);
    $('#number').val(number);
}

function back() {
    $('#next').show();
    var number = $('#number').val();
    number--;
    if (number <= 0) {
        $('#number').val(1);
        showNext();
        return;
    } else
        $('#number').val(number);

    getHistory(number);

}

function showBack() {
    $('#next').hide();
    $('#back').show();
}

function showNext() {
    $('#back').hide();
    $('#next').show();
}

function getHistory(number) {

    var payload = {
        pageNumber: number
    };

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/history",
        data: payload,
        type: 'GET',
        beforeSend: function() {
            $('#Loading').show();
        },
        complete: function() {
            showHideHistory();
            $('#Loading').hide();
        },
        error: function(e) {
            showBack();
            var number = $('#number').val();
            number--;
            $('#number').val(number);
        },
        success: function(data) {
            handleLogs(data.logs, true);
        }
    });
}

function getDBInfo() {
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/dbinfo",
        type: 'GET',
        async: false,
        error: function(e) {
            alert("Unable to get the Dbinfo. Reload the Page! ");
        },
        success: function(data) {
            var passiveFromReq = data.passive;
            var isDB1 = passiveFromReq.startsWith("rulecode-1");
            passive = 'DB2';
            active = 'DB1';
            if (isDB1) {
                passive = 'DB1';
                active = 'DB2'
            }
        }
    });
}

function getDBInfoV2() {
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/dbinfo/v2",
        type: 'GET',
        async: false,
        error: function(e) {
            alert("Unable to get the Dynamoinfo. Reload the Page! ");
        },
        success: function(data) {
            var htmlText = ""
            data.forEach(d => {
                var htmlTableText = ""
                Object.keys(d.valid_lookup_names).forEach((key, i) => {
                    var rowClass = "data-row"
                    if(i %2 === 0) {
                        rowClass+= " grey-row"
                    }
                    htmlTableText+= "<tr class='"+rowClass+"'><td>"+key+"</td><td>"+d.valid_lookup_names[key]+"</td></tr>"
                })
                htmlText += "<div class='pad col-6 accordion-group'>"+
                    "<div class='accordion-heading card'>"+
                        "<a class='accordion-toggle' data-toggle='collapse' href='#"+d.key+"' aria-expanded='false'>"+d.key+"</a>"+
                    "</div>"+
                    "<div id='"+d.key+"' class='accordion-body collapse'>"+
                        "<div class='accordion-inner'>"+
                            "<table class='width results-table' border='2'>"+
                                "<thead class='table-header'>"+
                                    "<tr><th>Table Name</th><th>Lookup Name</th></tr>"+
                                "</thead>"+
                                "<tbody>"+htmlTableText+"</tbody>"+
                            "</table>"+
                        "</div>"+
                    "</div>"+
                "</div>"
            })
            
            $("#dynamoTables").html(htmlText)
        }
    });
}

function getAwsImportInfo() {
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/dynamo/imports",
        type: 'GET',
        data: jQuery.param({
            nextToken: nextToken
        }),
        async: false,
        error: function(e) {
            alert("Unable to get the AWS Imports. Reload the Page! ");
        },
        success: function(data) {
            var htmlText = ""
            nextToken = data.nextToken
            data.imports.forEach((d, i) => {
                var rowClass = "data-row"
                if(i %2 === 0) {
                    rowClass+= " grey-row"
                }
                htmlText+= "<tr class='"+rowClass+"'><td>"+d.importArn+"</td><td>"+d.tableArn+"</td><td>"+d.importStatus+"</td>"+
                "<td>"+d.startTime+"</td><td>"+d.endTime+"</td></tr>"
            })
            $("#awsImportsResult").html(htmlText)
        }
    });
}

function getBatchJobList() {
    $.ajax({
        url: contexturl + `/superadmin/batchjobs`,
        type: 'GET',
        data: jQuery.param({
            jobType: 37,
            page: batchJobPage,
            size: 5
        }),
        async: false,
        error: function(e) {
            alert("Unable to get the Dynamoinfo. Reload the Page! ");
        },
        success: function(data) {
            var htmlText = ""
            const count = data.length
            data.forEach((d, i) => {
                var rowClass = "data-row"
                if(i %2 === 0) {
                    rowClass+= " grey-row"
                }
                htmlText+= "<tr class='"+rowClass+"'><td>"+d.publicId+"</td><td>"+d.createdBy+"</td><td>"+new Date(parseInt(d.createdAt)).toLocaleString()+"</td>"+
                "<td>"+getStatusString(d.status)+"</td><td>"+d.parameter+"</td></tr>"
            })
            if(count < 5) {
                $("#next-batch-btn").hide()
            } else {
                $("#next-batch-btn").show()
            }

            if(batchJobPage === 1) {
                $("#prev-batch-btn").hide()
            } else {
                $("#prev-batch-btn").show()
            }
            $("#batchResult").html(htmlText)
        }
    });
}

function getPreviousBatchJobList() {
    if(batchJobPage > 1) {
        batchJobPage--;
        getBatchJobList();
    }
}

function nextPreviousBatchJobList() {
    batchJobPage++;
    getBatchJobList();
}

function handleLogs(data, ishistory) {
    var trHTML = '';
    var currentStatus = '';
    var len = data.length
    $('#resultBody').children().remove();
    var count = 0;
    var filePath = ''
    $.each(data, function(i, item) {
        currentStatus = data[i].status;
        filePath = data[i].filePath;
        var utcSeconds = data[i].dateTime;
        var time = moment.utc(utcSeconds).local().format('YYYY-MM-DD HH:mm:ss');

        var progress  = data[i].progress;
        if(progress.includes(";")){
            progress = progress.split(';').join('</br>');
        }

        if (currentStatus.localeCompare('SWITCHED') == 0 || data[i].jobStatus.localeCompare('FAILED') == 0 || data[i].jobStatus.localeCompare('SUCCEEDED') == 0 || currentStatus.localeCompare('UPDATE_BACKUP2_COMPLETED') == 0 || count < len - 1) {
            trHTML += "<tr><td>" + data[i].jobId + "</td><td>" + data[i].filePath + "</td><td>" + data[i].description + "</td><td>" +  progress + "</td><td>" + data[i].jobStatus + "</td><td>" + data[i].status + "</td><td>" +
                data[i].user + "</td><td>" + time + "</td><td>" + "<input id=\"" + data[i].jobId + "," + data[i].filePath + "\"type=\"button\" value=\"Refresh status\" class=\"pad\" onClick=\"refreshTask(this.id)\" disabled=\"true\" />" + "<input id=\"" + data[i].jobId + "|" + data[i].filePath + "\"type=\"button\" value=\"Terminate Job\" class=\"pad\" onClick=\"terminateJob(this.id)\" disabled=\"true\" />" +
                "</td></tr>";
        } else if (count == len - 1) {
            trHTML += "<tr><td>" + data[i].jobId + "</td><td>" + data[i].filePath + "</td><td>" + data[i].description + "</td><td>" + progress + "</td><td>" + data[i].jobStatus + "</td><td>" + data[i].status + "</td><td>" +
                    data[i].user + "</td><td>" + time + "</td><td>" + "<input id=\"" + data[i].jobId + "," + data[i].filePath + "\"type=\"button\" value=\"Refresh status\" class=\"pad\" onClick=\"refreshTask(this.id)\" />" + "<input id=\"" + data[i].jobId + "|" + data[i].filePath + "\"type=\"button\" value=\"Terminate Job\" class=\"pad\" onClick=\"terminateJob(this.id)\" />" +
                "</td></tr>";
        }
        count++;
    });

    if (currentStatus.localeCompare('SWITCHED') == 0 || currentStatus.localeCompare('UPDATE_BACKUP2_FAILED') == 0 || currentStatus.localeCompare('UPDATE_BACKUP2_INPROGRESS') == 0 || currentStatus.localeCompare('UPDATE_BACKUP2_COMPLETED') == 0) {
        getDBInfo();
        $('#backup2').val('Update Backup 2 - ' + passive);
        $('#backup1').val('Update Backup 1 - ' + active);
    }

    //Based on the current status disable enable..
    $('#path').val(filePath);
    handleCurrentStatus(currentStatus);
    $('#resultBody').append(trHTML);
    if (ishistory)
        showResult();
    else
        showResultWithoutPagination();
}

function handleCurrentStatus(status) {
    if (status.localeCompare('UPDATE_BACKUP1_COMPLETED') == 0) {
        $('#switch').prop("disabled", false);
        $('#backup1').prop("disabled", true);
        $('#backup2').prop("disabled", true);
        $('#path').prop("disabled", true);
    } else if (status.localeCompare('') == 0 || status.localeCompare('UPDATE_BACKUP1_FAILED') == 0 || status.localeCompare('UPDATE_BACKUP2_COMPLETED') == 0) {
        $('#backup1').prop("disabled", false);
        $('#path').prop("disabled", false);
        $('#switch').prop("disabled", true);
        $('#backup2').prop("disabled", true);
    } else if (status.localeCompare('SWITCHED') == 0 || status.localeCompare('UPDATE_BACKUP2_FAILED') == 0) {
        $('#backup2').removeAttr("disabled");
        $('#switch').prop("disabled", true);
        $('#backup1').prop("disabled", true);
        $('#path').prop("disabled", true);
    } else {
        $('#backup1').prop("disabled", true);
        $('#path').prop("disabled", true);
        $('#switch').prop("disabled", true);
        $('#backup2').prop("disabled", true);
    }
}

function validatePath(path) {
    if (path == "") {
        alert("Path  can't be empty.");
        $('#path').focus();
        return false;
    }
    return true;
}

function getCurTime() {
    var today = new Date();
    var date = today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate();
    var time = today.getHours() + ":" + today.getMinutes() + ":" + today.getSeconds();
    return date + ' ' + time;
}

function refreshTask(idStr) {
    var str = idStr.split(",");
    var path = str[1];
    var id = str[0];
    var payload = {
        filePath: path,
        publicJobId: id
    };
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/status",
        data: payload,
        type: 'GET',
        beforeSend: function() {
            $('#Loading').show();
        },
        complete: function() {
            $('#Loading').hide();
        },
        error: function(e) {
            alert("Request failed! " + e.errorMessage);
        },
        success: function(data) {
            handleLogs(data.logs, true);
        }
    });
}

function showHideHistory() {
    $("#hideHistory").show();
    $('#history').hide();
}

function showShowHistory() {
    $("#hideHistory").hide();
    $('#history').show();
    showPagination();
}

function showResult() {
    $('#result').show();
}

function showResultWithoutPagination() {
    $('#result').show();
    hidePagination();
}

function hidePagination() {
    $('#back').hide();
    $('#next').hide();
}

function showPagination() {
    $('#back').show();
    $('#next').show();
}

function clearPage() {
    $('#number').val(1);
    $('#result').hide();
    enableButton1();
    getHistory(1);

}

function enableButton1() {
    $('#backup2').prop("disabled", true);
    $('#switch').prop("disabled", true);
    $('#backup1').prop("disabled", false);
    $('#path').attr("disabled", false);
    $('#path').val('');
    $('#backup1').val('Update Backup 1  - ' + passive);
    $('#backup2').val('Update Backup 2  - ' + active);
}

function disableAllButtons() {
    $('#backup2').prop("disabled", true);
    $('#switch').prop("disabled", true);
    $('#backup1').prop("disabled", true);
    $('#path').attr("disabled", true);
}

function getCurFilePath() {
    return ($('#path').val() || '').trim();
}

function terminateJob(idStr) {

    var str = idStr.split("|");
        var filePath = str[1];
        var publicJobId = str[0];

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/terminate?publicJobId=" + publicJobId + "&filePath=" + filePath,
        type: 'POST',
        beforeSend: function() {
            $('#Loading').show();
        },
        complete: function() {
            $('#Loading').hide();
        },
        error: function(e) {
            alert("Request failed! " + e.responseText);
        },
        success: function(data) {
            handleLogs(data.logs, true);
        }
    });
}

function handleCreateJob() {
    var createButton = $('#create-job-btn');
    createButton.text('Creating...')
    createButton.prop('disabled', true);
    var fileInputField = $('#batch-job-input');

    var mfData = new FormData();
    mfData.append('file', fileInputField[0].files[0]);

    $.ajax({
        url:`${contexturl}/superadmin/batchjobs/rulecode_dynamo_ingestion/create`,
        type: 'POST',
        data : mfData,
        async: false,
        processData: false,
        contentType: false,
        error: function(e) {
            alert(e.responseText || 'Unable to create new batch job please try again!');
            createButton.text('Create job');
            createButton.prop('disabled', false);
        },
        success: function(data) {
            fileInputField.val('');
            alert('Batch job created successfully!');
            createButton.text('Create job');
        }
    });
}

function getStatusString(status) {
    switch(status) {
        case 0:
            return 'CREATED';
        case 1:
            return 'SUBMITTED';
        case 2:
            return 'PENDING';
        case 3:
            return 'RUNNABLE';
        case 4:
            return 'STARTING';
        case 5:
            return 'RUNNING';
        case 6:
            return 'SUCCEEDED';
        case 7:
            return 'FAILED';
        default:
            return 'UNKNOWN';
    }
}