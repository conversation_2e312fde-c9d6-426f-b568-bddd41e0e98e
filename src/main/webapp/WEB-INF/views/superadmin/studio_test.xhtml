<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>

    <script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
        appenv = "#{bucket.appEnv}";
    </script>

    <meta charset="UTF-8"/>

    <style>

        .pad {
          margin-top: 10px;
          margin-bottom: 10px;
        }

    </style>
</h:head>
<h:body>

    <div class="topcorner">
        <select id="testVendors" onchange="selectTestVendor(this)" >
        </select>
        <button id="syncVendorsTestTab" onclick="syncVendorsTestTab()">Sync</button>
    </div>

    <div id="testPage">

        <button id="revertButton" onclick="syncRuleCodes('REVERT')" >Revert Current Config to Previous version</button>
        <center><label id="testStatus"></label></center>
        <br/>
        <div class="pad" id="testwindow" align="center">

            File Path : <input type="text" id="testFilePath" />

            <button id="testTriggerButton" onclick="submitTestJob()" >Test</button>

            <table border="2" style="margin-top:20px">
                <thead>
                <tr>
                    <th>Job Id</th>
                    <th>File</th>
                    <th>Job Status</th>
                    <th>Trigged By</th>
                    <th>Triggered At</th>
                    <th></th>
                    <th></th>
                </tr>
                </thead>
                <tbody id="testResult"></tbody>
            </table>
            <br/>
        </div>

        <select id="testpage" onchange="populateTests(this.value)" style="float: right;">
        </select>

        <div style="display:none" align="center" id="jobwindow">
            <table border="2" id="jobtable">
                <thead>
                <tr>
                    <th>#</th>
                    <th>Customer User Id</th>
                    <th>Status</th>
                </tr>
                </thead>
                <tbody id="jobResult"></tbody>
            </table>
            <button onclick="closeJobWindow()">Close</button>
        </div>

        <center><button id="commitButton" onclick="syncRuleCodes('COMMIT')" style="display:none;" >Commit Current Config Version</button></center>

    </div>
</h:body>
</html>
