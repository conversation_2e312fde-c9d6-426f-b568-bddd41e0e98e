<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Industry</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>DataTables live example</title>
	<link rel="shortcut icon" type="image/ico"
		href="http://www.datatables.net/favicon.ico" />
	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		media="print" />

	<script>
		var bucketurl = "#{bucket.resourceURL}";
		var contexturl = "#{request.contextPath}";
		var marketingurl = "#{bucket.marketingSite}";
		var appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px;">
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>
	<div class="modal fade" id="MessageModal" tabindex="-1" role="dialog" aria-labelledby="MessageModalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="exampleModalLabel">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="information" style="padding-top: 15px;">
						<label id="delete_msg"></label>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					<button type="button" class="btn btn-primary" onclick="delete_industry();">Delete</button>
				</div>
			</div>
		</div>
	</div>

	<div class="accordion" id="accordion2">
		<div class='container' style='margin-top: 10px'>
			<table cellpadding='0' cellspacing='0' border='0'
				class='table table-striped table-bordered' id='industry_table'>
				<thead>
					<tr>
						<th>Image</th>
						<th>Sector</th>
						<th>Description</th>
						<th><input type="checkbox" id="IndustryAll"
							name="IndustryAll" value="select All" /></th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<div style="padding-left: 215px;">
		<a href="javascript:show_industy_add_form();">Add New</a>&nbsp;&nbsp; | &nbsp;
		&nbsp; <a href="javascript:show_industy_delete_form ();">Delete</a>
	</div>

	<div id="AddIndustryModal" tabindex="-1" role="dialog" aria-labelledby="AddIndustryModalLabel" aria-hidden="true" class="modal fade">
		<div class="modal-dialog" role="document">
			<form id="add_industry" action="#" method="post">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title">Industry</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					</div>
					<div class="modal-body">
						<div id="message" class="alert alert-error"></div>
						<div class="form-group">
							<label for="sectorInput">Sector</label>
							<input type="text" class="form-control" id="sectorInput" aria-describedby="sector" placeholder="Sector" />
						</div>
						<div class="form-group">
							<label for="descriptionInput">Password</label>
							<input type="text" class="form-control" id="descriptionInput" placeholder="Description" />
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
						<button type="button" class="btn btn-primary" onclick="save_industry(null);">Save</button>
					</div>
				</div>
			</form>
		</div>
	</div>


	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>

	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
	<script type="text/javascript"
		src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script>
		$(document).ready(function() {
			get_industry();
		});
	</script>
</h:body>
</html>