<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
<link rel="stylesheet" type="text/css" href="resources/styles/socure-dashboard.css" />

<script src="resources/scripts/jquery.min.js"></script>
<script src="resources/scripts/bootstrap.min.js"></script>
    <script>
        bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		appenv = "#{bucket.appEnv}";
		appenv = "#{bucket.appEnv}";
    </script>
<meta charset="UTF-8"/>

<style>
	.trns-table td{
		max-width:200px;
	}
</style>

<title>Test endpoints</title>

<script language="javascript">
		
		$(document).ready(function(){
			$('#txnresult').hide();
		})
		$(function() {
			
			var authScoreBase = '#{bucket.authScoreBase}';
			window.socure = window.socure || {};
			window.socure.apiKey = null;
			window.socure.apiKeyFetchFailed = false;
			
			function getApiKey(){
				if(!window.socure.apiKeyFetchFailed &amp;&amp; window.socure.apiKey == null){
					$.ajax({
						url : "superadmin/1/my_api_key",
						//dataType : 'jsonp',
						type : 'GET',
						async : false,
						success : function(data) {
							if(typeof(data) != 'undefined' &amp;&amp; data != null &amp;&amp; data.trim() != ''){
								window.socure.apiKey = data;
							}
						}
					});
				}
				return window.socure.apiKey;
			}

			// add multiple select / deselect functionality
			$('#pattern').click(function() {
				$('#pat-result').text("Processing...");
				var network =$('#network').val();
				 
				var socureKey=getApiKey();
				if(socureKey == null || socureKey.trim() == ''){
					alert('Unable to fetch API Key. Please make sure you are logged in and try refreshing page.');
				} else {
					$.ajax({
						url : authScoreBase + "/test/pat.jsonp",
						dataType : 'jsonp',
						data : {
							network : network,
							socureKey:socureKey
							
						},
						type : 'GET',
						success : function(data) {
							$('#pat-result').css('white-space','pre');
							$('#pat-result').text(JSON.stringify(data,null,4));
							
						}
	
					});
				}
			});
			
			// add multiple select / deselect functionality
            $('#loglevelsubmit').click(function() {
                $('#loglevelresult').text("Processing...");
                var levelLog =$('#loglevelselect').val();

                var socureKey=getApiKey();
                
                var dat = {
                        logLevel : levelLog,
                        socurekey:socureKey
                    };
                var logger = $("#loglevellogger").val().trim();
                if(logger.length > 0) {
                	dat.logger = logger;
                }
                
                if(socureKey == null || socureKey.trim() == ''){
                    alert('Unable to fetch API Key. Please make sure you are logged in and try refreshing page.');
                } else {
                    $.ajax({
                        url : authScoreBase + "/log/level/change.jsonp",
                        dataType : 'jsonp',
                        data : dat,
                        type : 'GET',
                        success : function(data) {
                            $('#loglevelresult').css('white-space','pre');
                            $('#loglevelresult').text(data);
                        }

                    });
                }
            });

            $('#txnsubmit').click(function() {
                var txnID =$('#txnId').val();

                if(txnID == "" ){
                	alert("Transaction ID can't be empty.");
                	$('#txnId').focus();
                } else {
                	var dat = {
                            txnID : txnID
                        };
                                $.ajax({
                                url : contexturl + "/superadmin/1/txn_search.jsonp",
                                dataType : 'jsonp',
                                data : dat,
                                type : 'GET',
                                success : function(data) {
									if(data['status']=="Error"){
										$('#txnresult').hide();
										$('#txnStatusMsg').text(data['msg']);
										$('#txnId').focus();
										$('#txnId').val('');
										return;
									}
									$('#txnStatusMsg').text("");
                                	$('#txnresult').show(true);
                                    $('#resultId').text(data['data'].id);
                                    $('#resultTxnId').text(data['data'].transactionId);
                                    $('#resultDate').text(data['data'].transactionDate);
                                    $('#resultTime').text(data['data'].processingTime);
                                    $('#resultApi').text(data['data'].api);
                                    $('#resultParam').text(data['data'].parameters);
                                    $('#resultResponse').text(data['data'].response);
                                    $('#resultError').text(data['data'].error);
                                    $('#resultErrorMsg').text(data['data'].errorMsg);
                                    $('#resultIp').text(data['data'].accountIpAddress);
                                    $('#resultInvo').text(data['data'].originOfInvocation);
                                    $('#resultGeo').text(data['data'].geocode);
                                    $('#resultApiKey').text(data['data'].apiKey);
                                    $('#resultaccountId').text(data['data'].accountId);
                                    $('#resultApiName').text(data['data'].apiName);
                                    $('#resultCache').text(data['data'].cachesUsed);
                                    $('#resultDebug').text(data['data'].debug);
                                    $('#resultDetails').text(data['data'].details);
                                    $('#resultScore').text(data['data'].authScore);
                                    $('#resultConf').text(data['data'].confidence);
                                    $('#resultCode').text(data['data'].reasonCodes);
                                    $('#resultRisk').text(data['data'].hasRiskCode);
                                    $('#resultCustomer').text(data['data'].customerUserId);
                                    $('#resultRun').text(data['data'].runId);
                                    $('#resultUri').text(data['data'].requestURI);
                                    $('#resultUid').text(data['data'].uuid);
                                }

                            });
                }
                
            });


			$('#twitter').click(function() {
                
	          $('#twitter-data').text("Processing...");
	          var socureKey=getApiKey();
				if(socureKey == null || socureKey.trim() == ''){
					alert('Unable to fetch API Key. Please make sure you are logged in and try refreshing page.');
				} else {
					$.ajax({
						url : authScoreBase + "/test/twitter/ratelimits.jsonp",
						dataType : 'jsonp',
						data : {
							socureKey:socureKey
							},
						type : 'GET',
						success : function(data) {
							//$('#twitter-data').css('white-space','pre');
							$('#twitter-data').text(JSON.stringify(data));
						}
	
					});
				}
			});
			
			$('#exportButton').click(function(){
				var selectedNetwork = $('#exportNetwork').val();
				
				if(selectedNetwork == "none"){
					alert("Select network to export.");
				} else {
					var url = authScoreBase + '/test/exporttoken?network=' + selectedNetwork;
					$("#downloadHelper").attr('src', url);
				}
			});
			
			/* $('#exportButton').click(function(){
				var selectedNetwork = $('#exportNetwork').val();
				alert(selectedNetwork);
				
				if(selectedNetwork == "none"){
					alert("Select network to export.");
				} else {
					$('#export-result').text('Processing....');
					$.ajax({
						url : authScoreBase + '/test/exporttoken.jsonp',
						dataType : 'jsonp',
						type : 'GET',
						data : {
								network : selectedNetwork
							},
						success : function(data){
							$('#export-result').text('Finished');
						}
					});
				}
			}); */
			
		});

	</script>
</h:head>
<h:body>

<iframe width="1" height="1" frameborder="0" src="" id="downloadHelper" style="display:none;"></iframe>

<div align="center">
		<font color="blue"><h3>
				<h:outputText value="#{param['message']}" />
			</h3> </font>
	</div>
	<br/>
	<br/>
	<div align="right">
	   <h5>
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		
	
		<h:outputLink value="logout">Logout</h:outputLink>
		&nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
	</div>
	<br/>
	<br/>
      <center><h3>Test Endpoints</h3></center>
     <hr/>

    <div id="accordion">
        <div class="card">
            <div class="card-header" id="headingOne">
                <h5 class="mb-0">
                    <button class="btn btn-link" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                        Log Level
                    </button>
                </h5>
            </div>

            <div id="collapseOne" class="collapse show" aria-labelledby="headingOne" data-parent="#accordion">
                <div class="card-body">
                    <div class="accordion-inner">
                        <select id="loglevelselect">
                            <option value="0">TRACE</option>
                            <option value="1">DEBUG</option>
                            <option value="2">INFO</option>
                            <option value="3">WARN</option>
                            <option value="4">ERROR</option>
                        </select>
                        <div>
                            Logger Name(Leave blank for root logger) : <input id="loglevellogger" type="text"/>
                        </div>
                        <input type="button" Value="Update" id="loglevelsubmit"/>

                        <div id="loglevelresult"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-header" id="headingTwo">
                <h5 class="mb-0">
                    <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                        Queue Test
                    </button>
                </h5>
            </div>
            <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordion">
                <div class="card-body">
                    <div class="accordion-inner">
                        <select id="network">
                            <option value="">Select</option>
                            <option value="fb">fb</option>
                            <option value="tw">tw</option>
                            <option value="in">in</option>
                            <option value="gp">gp</option>
                        </select>
                        <input type="button" Value="Search" id="pattern"/>

                        <div id="pat-result"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-header" id="headingThree">
                <h5 class="mb-0">
                    <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                        Transaction ID Search
                    </button>
                </h5>
            </div>
            <div id="collapseThree" class="collapse" aria-labelledby="headingThree" data-parent="#accordion">
                <div class="card-body">
                    <div class="accordion-inner">

                        <div>
                            Enter Transaction ID  <input id="txnId" type="text"/>
                        </div>
                        <input type="button" Value="Search" id="txnsubmit"/>

                        <div id="txnStatusMsg"></div>
                        <div id="txnresult" >
                            <table id="transactionResult" border="1" width="125%" class="trns-table">
                                <tr><th width="20%">Name</th><th width="80%">Details</th></tr>

                                <tr><td><span id="name">ID</span></td>
                                    <td><span id="resultId"></span></td>
                                </tr>

                                <tr><td><span id="name">Transaction ID</span></td>
                                    <td><span id="resultTxnId"></span></td>
                                </tr>

                                <tr><td><span id="name">Transaction Date</span></td>
                                    <td><span id="resultDate"></span></td>
                                </tr>

                                <tr><td><span id="name">Process Time</span></td>
                                    <td><span id="resultTime"></span></td>
                                </tr>

                                <tr><td><span id="name">API</span></td>
                                    <td><span id="resultApi"></span></td>
                                </tr>

                                <tr><td><span id="name">Parameters</span></td>
                                    <td><span id="resultParam"></span></td>
                                </tr>

                                <tr><td><span id="name">Response</span></td>
                                    <td><span id="resultResponse"></span></td>
                                </tr>

                                <tr><td><span id="name">Error</span></td>
                                    <td><span id="resultError"></span></td>
                                </tr>

                                <tr><td><span id="name">Error Message</span></td>
                                    <td><span id="resultErrorMsg"></span></td>
                                </tr>

                                <tr><td><span id="name">Account IP Address</span></td>
                                    <td><span id="resultIp"></span></td>
                                </tr>

                                <tr><td><span id="name">Origin of Invocation</span></td>
                                    <td><span id="resultInvo"></span></td>
                                </tr>

                                <tr><td><span id="name">Geo Code</span></td>
                                    <td><span id="resultGeo"></span></td>
                                </tr>

                                <tr><td><span id="name">API Key</span></td>
                                    <td><span id="resultApiKey"></span></td>
                                </tr>

                                <tr><td><span id="name">Account ID</span></td>
                                    <td><span id="resultaccountId"></span></td>
                                </tr>

                                <tr><td><span id="name">API Name</span></td>
                                    <td><span id="resultApiName"></span></td>
                                </tr>

                                <tr><td><span id="name">Caches Used</span></td>
                                    <td><span id="resultCache"></span></td>
                                </tr>

                                <tr><td><span id="name">Debug</span></td>
                                    <td><span id="resultDebug"></span></td>
                                </tr>

                                <tr><td><span id="name">Details</span></td>
                                    <td><span id="resultDetails"></span></td>
                                </tr>

                                <tr><td><span id="name">Auth Score</span></td>
                                    <td><span id="resultScore"></span></td>
                                </tr>

                                <tr><td><span id="name">Confidence</span></td>
                                    <td><span id="resultConf"></span></td>
                                </tr>

                                <tr><td><span id="name">Reason Codes</span></td>
                                    <td><span id="resultCode"></span></td>
                                </tr>

                                <tr><td><span id="name">Rish Code</span></td>
                                    <td><span id="resultRisk"></span></td>
                                </tr>

                                <tr><td><span id="name">Customer ID</span></td>
                                    <td><span id="resultCustomer"></span></td>
                                </tr>

                                <tr><td><span id="name">Run ID</span></td>
                                    <td><span id="resultRun"></span></td>
                                </tr>

                                <tr><td><span id="name">Request URI</span></td>
                                    <td><span id="resultUri"></span></td>
                                </tr>

                                <tr><td><span id="name">Unique UID</span></td>
                                    <td><span id="resultUid"></span></td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-header" id="headingFour">
                <h5 class="mb-0">
                    <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseFour"
                            aria-expanded="false" aria-controls="collapseFour">
                        Twitter Token Test
                    </button>
                </h5>
            </div>
            <div id="collapseFour" class="collapse" aria-labelledby="headingFour" data-parent="#accordion">
                <div class="card-body">
                    <div class="accordion-inner">
                        <input type="button" Value="Access" id="twitter"/>
                        <div id="twitter-data"><p>To Get the Twitter access token stauts </p></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-header" id="headingFive">
                <h5 class="mb-0">
                    <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseFive"
                            aria-expanded="false" aria-controls="collapseFive">
                        Token Exporter
                    </button>
                </h5>
            </div>
            <div id="collapseFive" class="collapse" aria-labelledby="headingFive" data-parent="#accordion">
                <div class="card-body">
                    <div class="accordion-inner">
                        <select id="exportNetwork">
                            <option value="none">-- Select --</option>
                            <option value="fb">fb</option>
                            <option value="tw">tw</option>
                            <option value="in">in</option>
                            <option value="gp">gp</option>
                        </select>
                        <input type="button" Value="Export" id="exportButton"/>

                        <div id="export-result"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- end -->
    </div>
            
</h:body>
</html>