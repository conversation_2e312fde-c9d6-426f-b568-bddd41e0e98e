$(document).ready(
         function() {
              $.ajax({
                  url: contexturl + "/adminapi/1/get_active_pgp_account_list",
                  type: "GET",
                  beforeSend: function() {
                      $('#Loading').show();
                  },
                  complete: function() {
                      $('#Loading').hide();
                  },
                  error: function(e) {
                     console.log("error ", e);
                  },
                  success: function(data) {
                      var accountList = data["data"];
                      if(data["status"].toLowerCase() === "ok" && accountList.length > 0) {
                          var accountSelectInput = $('#accountList');
                          for (var i = 0; i<accountList.length; i++){
                              var opt = document.createElement('option');
                              var accountId = accountList[i].accountId;
                              var publicAccountId = accountList[i].publicAccountId;
                              var accountName = accountList[i].accountName;
                              opt.value = accountId + ":" + accountName + ":" + publicAccountId;
                              opt.text = accountId + "-" + accountName;
                              accountSelectInput[0].appendChild(opt);
                          }
                      }
                      if(data["status"].toLowerCase() === "error") {
                          $('#super_admin_msg')[0].innerHTML = "Failed to load account list.";
                          $('#super_admin_msg').css("color", "red");
                      }

                      loadSFTPUsers(); // load all existing sftp users
                  }

              })


         }
)

window.onload = function() {
  DD_RUM && DD_RUM.startView({name: "/sftp_users"});
  DD_RUM && DD_RUM.setUser({ "id": loggedinUsername});
}

// prepopulate sftpusername field using accountaname with only alphanumeric char in lowercase

function handleAccountSelect (accountListObj) {
    var accountInfo = accountListObj.options[accountListObj.selectedIndex].value.split(":");
    var accountName = accountInfo[1].replace(/\W/g, '').toLowerCase();
    $('#sftpUsername')[0].value = accountName;
    $('#sshKey')[0].value = '';

}

function loadSFTPUsers () {
        var sftpHost = ''
        var host = document.location.host;
        if(appenv==='dev'){
            sftpHost = 'dev-deliveries-49a0.socure.com';
        } else if(host.includes("stage")) {
            sftpHost = 'stage-deliveries-49a0.socure.com';
        } else if(host.includes("prod") || (appenv==='eks-prod')) {
            sftpHost = 'deliveries-49a0.socure.com';
        } else if(host.includes("disaster")) {
            sftpHost = 'stage-deliveries-49a0.socure.com'; //TODO: server yet to be created
        } else if(host.includes("ds")) {
            sftpHost = 'stage-deliveries-49a0.socure.com'; //TODO: server yet to be created
        }
        var sftpLoginDetails = "You can login as follows </br> " +
                               "sftp -i /path/to/private-key USERNAME@" + sftpHost + "</br>" +
                               "Meaning: </br>" +
                               "User = USERNAME </br>" +
                               "Host = " + sftpHost + "</br>" +
                               "Port = 22 </br>" +
                               "Protocol = sftp </br>";
        $.ajax({
             url: contexturl + "/sftp/users",
             type: "GET",
             beforeSend: function() {
                 $('#Loading').show();
             },
             complete: function() {
                 $('#Loading').hide();
             },
             error: function(e) {
                 console.log("error ", e);
             },
             success: function(data) {
                  var sftpUsers = data["data"];
                  var tbljq = $('#sftp_users_list');
                  if(data["status"].toLowerCase() === "ok" && sftpUsers.length > 0) {
                    tbljq.dataTable({
                            "aaData": sftpUsers,
                            "bProcessing": true,
                            "bDestroy": true,
                            "bAutoWidth": true,
                            "iDisplayLength": 25,
                            "aoColumns": [{
                                "mData": "accountId",
                                "bSearchable": true,
                                "fnRender": function(oObj) {
                                  return oObj.aData["accountId"];
                                }
                              },
                              {
                                "mData": "accountName",
                                "bSearchable": true,
                                "fnRender": function(oObj) {
                                  return oObj.aData["accountName"];
                                }
                              },
                              {
                                "mData": "sftpUser",
                                "bSearchable": true,
                                "fnRender": function(oObj) {
                                  return oObj.aData["sftpUser"];
                                }
                              },
                              {
                                "mData": "createdAt",
                                "fnRender": function(oObj) {
                                  return getFormatedDate(oObj.aData["createdAt"]);
                                }
                              },
                              {
                                 "mData": "id",
                                 "fnRender" : function(oObj) {
                                     return "<div id='href_" + oObj.aData["sftpUser"] + "' onclick='toggleDetails(\"" +  oObj.aData["sftpUser"] + "\")'  style='cursor:pointer;color: #ee8e3c;text-decoration: none;'>Show</div><div id='"+ oObj.aData["sftpUser"]  + "' style='display:none'>" + sftpLoginDetails.replace(/USERNAME/gi, oObj.aData["sftpUser"]) + "</div>";
//
                                 }
                              }
                            ]
                      });
                 }
                 if(data["status"].toLowerCase() === "error") {
                     $('#super_admin_msg')[0].innerHTML = "Failed to load SFTP users list.";
                     $('#super_admin_msg').css("color", "red");
                 }
             }

      });
}

function getSelectedAccountId () {
    var accountList = $('#accountList')[0];
    return accountList.options[accountList.selectedIndex].value.split(":")[0];
}

function getSelectedPublicAccountId () {
     var accountList = $('#accountList')[0];
     return accountList.options[accountList.selectedIndex].value.split(":")[2];
}

function createSFTPUser () {
    var payload = {};
    var sftpUsername = $('#sftpUsername').val();
    var sshKey = $('#sshKey').val();
     if(getSelectedAccountId() == 'none') {
        alert("Please select an account");
     }
    if(sftpUsername === '' || sshKey === '') {
        alert("Input field cannot be empty");
    } else {
        payload["accountId"] =  getSelectedAccountId();
        payload["username"] = sftpUsername;
        payload["publicAccountId"] = getSelectedPublicAccountId();
        payload["sshKey"] = sshKey;

          $.ajax({
              url: contexturl + "/sftp/user",
              type: "POST",
              cache: false,
              contentType: false,
              processData: false,
              contentType: 'application/json',
              data: JSON.stringify(payload),
              beforeSend: function() {
                  $('#Loading').show();
              },
              complete: function() {
                  $('#Loading').hide();
              },
              error: function(e) {
                  console.log("error creating sftp user ", e);
              },
              success: function(data) {
                  if(data["status"].toLowerCase() === "ok") {
                      $('#super_admin_msg')[0].innerHTML = "Successfully created SFTP user " + payload["username"] + "!!!";
                      $('#super_admin_msg').css("color", "blue");
                      loadSFTPUsers();
                      DD_RUM.track("Create SFTP User success", { accountId: payload["accountId"], sftpUserName: payload["username"], publicAccountId: payload["publicAccountId"]  });
                  }
                  if(data["status"].toLowerCase() === "error") {
                      var errorMsg  = data["msg"] ? data["msg"].split("(")[0] : "";
                      $('#super_admin_msg')[0].innerHTML = "Failed to create SFTP user " + payload["username"] + ". " + errorMsg;
                      $('#super_admin_msg').css("color", "red");
                      DD_RUM.track("Create SFTP User failed", { accountId: payload["accountId"], sftpUserName: payload["username"], publicAccountId: payload["publicAccountId"]  });
                  }
                  //reset input fields
                  $('#sshKey').val('');

              }

          });
      }

}

function toggleDetails(elm) {
    var divElm = document.getElementById(elm);
    var divLink = document.getElementById('href_' + elm);
    if(divElm.style.display === 'none') {
        divElm.style.width = "200px";
        divElm.style.display = "block";
        divLink.innerHTML = "Hide"
    } else {
         divElm.style.width = "auto";
         divElm.style.display = "none";
         divLink.innerHTML = "Show"
    }

}

function getFormatedDate(isoDate) {
	var date = new Date(isoDate);
	var dd = String(date.getDate()).padStart(2, '0');
	var mm = date.toLocaleString('default', { month: 'short' });
	var yyyy = date.getFullYear();
	var time = date.toLocaleTimeString();

	return dd + "-" + mm + "-" + yyyy + " " + time;
}
