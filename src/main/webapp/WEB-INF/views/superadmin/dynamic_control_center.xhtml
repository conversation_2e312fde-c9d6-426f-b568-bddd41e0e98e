<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
    <link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
    <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
    <script src="resources/scripts/jquery.min.js"></script>
    <script src="resources/scripts/bootstrap.min.js"></script>
    <script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
    <script type="text/javascript" src="resources/scripts/dynamiccontrolcenter.js"></script>
    <script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
        /* flag to manage page access type
           Full Access
           View Only
           No Access
        */
        canAccessControlCentreFlag = "#{canAccessControlCentre}";
        appenv = "#{bucket.appEnv}";
    </script>

    <style>
        /* Add styles to the form container */
        .form-container {
          padding: 10px;
        }

        /* Full-width input fields */
        .form-container input[type=text], .form-container select {
          width: 100%;
          padding: 5px;
          margin: 5px 0 22px 0;
          border: none;
          background: #f1f1f1;
        }

        /* When the inputs get focus, do something */
        .form-container input[type=text]:focus, .form-container select:focus {
          background-color: #ddd;
          outline: none;
        }

        /* Add a red background color to the cancel button */
        .form-container .cancel {
          background-color: #555;
        }

        /* Add some hover effects to buttons */
        .form-container .btn:hover, .open-button:hover {
          opacity: 1;
        }

        table, th, td {
           border: 1px solid black;
           border-collapse: collapse;
           padding: 5px;
        }
        .loader {
            border: 8px solid #f3f3f3; /* Light grey */
            border-top: 8px solid black; /* Blue */
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn.disabled, .btn.disabled:hover {
          opacity: .4!important;
        }

        #tab-menu {
            display: flex;
            flex-direction: row;
            width: 100%;
            justify-content: center;
        }

        #tab-menu .menu {
            padding: 16px;
            color: #ee8e3c;
            font-size: 16px;
            line-height: 24px;
            font-weight: bold;
            cursor: pointer;
        }

        #idPlus-tab {
            display: block;
        }

        #other-flags-tab {
            display: none;
        }


    </style>

    <meta charset="UTF-8"/>
</h:head>
<h:body>

    <iframe width="1" height="1" frameborder="0" src="" id="downloadHelper" style="display:none;"></iframe>

    <div align="center">
        <font color="blue"><h3>
            <h:outputText value="#{param['message']}" />
        </h3> </font>
    </div>
    <br/>
    <br/>

    <h:panelGroup rendered="#{canAccessControlCentre == 'No Access'}" id="access-message">
        <h3 align="center">You do not have access to this page</h3>
    </h:panelGroup>
    <h:panelGroup align="right" rendered="#{canAccessControlCentre != 'No Access'}" id="main-page">
        <div align="right">
            <h5>
                <h:outputLink value="leaderboard">Leaderboard</h:outputLink>
                &nbsp;&nbsp; | &nbsp; &nbsp;
                <h:outputLink value="industry">Manage Industry</h:outputLink>
                &nbsp;&nbsp; | &nbsp; &nbsp;
                <h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
                &nbsp;&nbsp; | &nbsp; &nbsp;
                <h:outputLink value="active_users">Active Users</h:outputLink>
                &nbsp;&nbsp; | &nbsp; &nbsp;
                <h:outputLink value="new_control_center">New Control Center</h:outputLink>
                &nbsp;&nbsp; | &nbsp; &nbsp;
                <h:outputLink value="inactive_users">Inactive Users</h:outputLink>
                &nbsp;&nbsp; | &nbsp; &nbsp;
                <h:outputLink value="logout">Logout</h:outputLink>
                &nbsp;&nbsp;  &nbsp; &nbsp;
            </h5>
        </div>
        <br/>

        <div id="tab-menu">
            <div id="idPlus" class="menu" onclick="changeTab('idPlus')">ID+ Features</div>
            <div id="others" class="menu" onclick="changeTab('otherFlags')">Other flags</div>
        </div>
        <br/>

        <div id="idPlus-tab">
            <center><a id="downloadSettings" href="superadmin/control_center/settings/download">Download existing settings</a></center>
            <br/>

            <center>
                <div>
                    <form id="controlCenterForm" action="" class="form-container">
                        <table id="settingsTable" align="center" cellpadding="0" cellspacing="0" border="0" class="table table-striped table-bordered dataTable" style="width: 40%;">
                            <thead>
                            <th><b>Feature</b></th>
                            <th><b>Description</b></th>
                            <th style="width:150px"><b>Value</b></th>
                            </thead>
                            <tbody id="settingsTableBody">
                            </tbody>
                        </table>
                        <br />
                        <div id="message" style="display:none"></div>
                    </form>
                </div>
            </center>
        </div>


        <div id="other-flags-tab">
            <center><h4>Dynamic Control Center</h4></center>
            <hr/>
            <br/>
            <form id="configUploadForm" align="center" enctype='multipart/form-data' class="form-container">
                <label for="file"><b>Config File:</b></label>
                <input type="file" id="file" accept=".json" name="file" required="true"/>
                <label for="ttl"><b>Expiry in Seconds:</b></label>
                <input type="number" id="ttl" name="ttl" required="true"/>
                <br/><br/>
                <button type="button" id="settingsUploadBtn" class="btn btn-primary" onclick="uploadDynamicControlCenterConfig()" align="center">Upload Config</button>
                <br/>
                <div id="message" style="display:none"></div>
            </form>
            <hr/>
            <br/>
            <center><h4>Existing Configs</h4></center>
            <hr/>
            <div id="listTable">
                <center><div class="loader"></div></center>
            </div>
        </div>
        <hr/>
    </h:panelGroup>
</h:body>
</html>
