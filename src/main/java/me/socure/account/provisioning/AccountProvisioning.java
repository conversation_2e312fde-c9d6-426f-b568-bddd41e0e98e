package me.socure.account.provisioning;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import me.socure.account.provisioning.Bundle;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccountProvisioning {
    String bundleReference;
    ArrayList<DtoAccountProducts> products;
    ProductConfiguration productConfiguration;

    public AccountProvisioning() {
    }

    public AccountProvisioning(String bundleReference, ArrayList<DtoAccountProducts> products, ProductConfiguration productConfiguration) {
        this.products = products;
        this.bundleReference = bundleReference;
        this.productConfiguration = productConfiguration;
    }

    public AccountProvisioning(String bundleReference, ArrayList<DtoAccountProducts> products) {
        this.products = products;
        this.bundleReference = bundleReference;
    }

    public AccountProvisioning(ArrayList<DtoAccountProducts> products) {
        this.products = products;
    }

    public List<DtoAccountProducts> getProducts() {
        return products;
    }

    public void setProducts(ArrayList<DtoAccountProducts> products) {
        this.products = products;
    }

    public String getBundleReference() {
        return bundleReference;
    }

    public void setBundleReference(String bundleReference) {
        this.bundleReference = bundleReference;
    }

    public ProductConfiguration getProductConfiguration() {
        return productConfiguration;
    }

    public void setProductConfiguration(ProductConfiguration productConfiguration) {
        this.productConfiguration = productConfiguration;
    }
}
