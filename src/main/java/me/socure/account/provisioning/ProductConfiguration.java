package me.socure.account.provisioning;

import me.socure.model.sai.SAIPreferences;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductConfiguration {
    String ein;
    SaiPreferences saiPreferences;
    RetentionSchedule retentionSchedule;
    String docVStrategy;
    String lookupApiKey;
    String serviceId;

    public ProductConfiguration() {
    }

    public ProductConfiguration(String ein, RetentionSchedule retentionSchedule) {
        this.ein = ein;
        this.retentionSchedule = retentionSchedule;
    }

    public String getEin() {
        return ein;
    }

    public void setEin(String ein) {
        this.ein = ein;
    }

    public SaiPreferences getSaiPreferences() {
        return saiPreferences;
    }

    public void setSaiPreferences(SaiPreferences saiPreferences) {
        this.saiPreferences = saiPreferences;
    }

    public void setSaiPreferencesFromAccService(SAIPreferences saiPreferences) {
        this.saiPreferences =
            SaiPreferences
                .builder()
                .memo(saiPreferences.memo().get())
                .depositorName(saiPreferences.depositorName().get())
                .physicalAddress(saiPreferences.physicalAddress().get())
                .city(saiPreferences.city().get())
                .state(saiPreferences.state().get())
                .zip(saiPreferences.zip().get())
                .country(saiPreferences.country().get())
                .build();
    }

    public RetentionSchedule getRetentionSchedule() {
        return retentionSchedule;
    }

    public void setRetentionSchedule(RetentionSchedule retentionSchedule) {
        this.retentionSchedule = retentionSchedule;
    }

    public void setRetentionSchedule(int cadence, String routine) {
        this.retentionSchedule = new RetentionSchedule(cadence, routine);
    }

    public String getDocVStrategy() {
        return docVStrategy;
    }

    public void setDocVStrategy(String docVStrategy) {
        this.docVStrategy = docVStrategy;
    }

    public String getLookupApiKey() {
        return lookupApiKey;
    }

    public void setLookupApiKey(String lookupApiKey) {
        this.lookupApiKey = lookupApiKey;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }
}
