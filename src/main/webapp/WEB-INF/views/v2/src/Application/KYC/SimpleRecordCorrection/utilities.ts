import { deepClone } from "../../../Utilities";
import { vendorShortCodes } from "./data";

const piiTags = ["NF", "NM", "NL", "NS", "BD", "SSN", "PN", "EM", "A"];

const EMPTY_SET = new Set();

function randomString(length: number) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
        counter += 1;
    }
    return result;
}

export const processRetrievedRecords = (data: Array<{ type: string, result: any }>) => {
    let deceased = "0";
    const summarisedData = {
        NF: {},
        NM: {},
        NL: {},
        NS: {},
        BD: {},
        SSN: {},
        PN: {},
        EM: {},
        A: {},
    }
    data.forEach(({ result }) => {
        piiTags.forEach(piiTag => {
            if (result.attributes[piiTag] === undefined) {
                result.attributes[piiTag] = [];
            }
            result.attributes[piiTag].forEach((i: any, idx: number) => {
                let v;
                if (piiTag === "A") {
                    v = `${i.AX}|${i.AY}|${i.AZ}|${i.AS}`
                } else {
                    v = i.cleanedValue
                }
                v = v.toLowerCase();
                if ((summarisedData as any)[piiTag][v] === undefined) {
                    (summarisedData as any)[piiTag][v] = {
                        deleted: false,
                        list: []
                    }
                }
                (summarisedData as any)[piiTag][v].list.push({
                    cluster: result.clusterId,
                    idx
                });
            });

        })
    })
    piiTags.forEach(piiTag => {
        (summarisedData as any)[piiTag] = Object.entries((summarisedData as any)[piiTag]).map(_ => ({
            value: _[0],
            list: (_[1] as any).list,
            deleted: (_[1] as any).deleted
        }))
    })
    data.forEach(({ result }) => {
        result.attributes["SSN"].forEach((_: any) => {
            if ((_.metaData || {}).ssn_deceased === "1" || (_.metaData || {}).cid_deceased === "1") {
                deceased = "1";
            }
        })
    })
    return {
        retrievedData: data,
        summarisedData,
        deceased,
        addedData: {
            NF: [],
            NM: [],
            NL: [],
            NS: [],
            BD: [],
            SSN: [],
            PN: [],
            EM: [],
            A: [],
        }
    }
}

export const generateEditingDataDiff = (data: any, modifyPii: boolean, modifyDeceased: boolean) => {
    const suffix = `_CHM_RC_${randomString(5)}`;
    const { summarisedData, addedData, retrievedData } = data;
    const dataUpdate: any = {};
    let updatedRecordCount = 0;
    piiTags.forEach(piiTag => {
        summarisedData[piiTag].filter((_: any) => _.deleted === true)
            .forEach((_: any) => {
                _.list.forEach((d: any) => {
                    if (dataUpdate[d.cluster] === undefined) {
                        dataUpdate[d.cluster] = {};
                    }
                    if (dataUpdate[d.cluster][piiTag] === undefined) {
                        dataUpdate[d.cluster][piiTag] = new Set();
                    }
                    dataUpdate[d.cluster][piiTag].add(d.idx);
                })
            });
    });
    retrievedData.forEach((i: any) => {
        const { clusterId } = i.result;
        i.result.updatedAttributes = deepClone(i.result.attributes);
        if (modifyPii) {
            piiTags.forEach(piiTag => {
                if (dataUpdate[clusterId] === undefined) {
                    dataUpdate[clusterId] = {};
                }
                if (dataUpdate[clusterId][piiTag] === undefined) {
                    dataUpdate[clusterId][piiTag] = EMPTY_SET;
                }
                if (dataUpdate[clusterId][piiTag] && dataUpdate[clusterId][piiTag].size !== 0) {
                    i.result.updatedAttributes[piiTag] = i.result.updatedAttributes[piiTag].filter((_: any, idx: number) => !(dataUpdate[clusterId][piiTag].has(idx)))
                }
                addedData[piiTag].forEach((ad: any) => {
                    i.result.updatedAttributes[piiTag].push({
                        ...ad,
                        rowId: `${clusterId}${suffix}`,
                        rowIdSource: `${clusterId}${suffix}`
                    })
                })
            })
        }
        if (modifyDeceased) {
            const { deceased } = data;
            i.result.updatedAttributes["SSN"].forEach((_: any) => {
                if (_.metaData === undefined) {
                    _.metaData = {};
                }
                _.metaData.ssn_deceased = deceased;
                _.metaData.cid_deceased = deceased;
            })
        }
    })
    retrievedData.forEach(({ result }: any) => {
        if (JSON.stringify(result.attributes) !== JSON.stringify(result.updatedAttributes)) {
            updatedRecordCount = updatedRecordCount + 1;
        }
    });
    data.updatedRecordCount = updatedRecordCount;
    return data;
}

export function replaceWithVendorShortCode(value:string):string {
    let result:string = value;
    vendorShortCodes.forEach(v => {
        if(value.startsWith(v.fullName)) {
            result = value.replace(v.fullName, v.shortName);
        }
    });
    return result;
}