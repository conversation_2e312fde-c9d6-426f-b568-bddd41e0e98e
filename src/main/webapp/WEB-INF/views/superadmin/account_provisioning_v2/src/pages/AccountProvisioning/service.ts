import axios from 'axios';
import { AccountProvisioningDetails, AccountProvisioningUpdateDetails, Metadata } from './AccountProvisioning.type';
import { showErrorToast, showSuccessToast } from './Toast';

export const getAccountModules = (accountId: string) : Promise<AccountProvisioningDetails> => {
  return axios.get("/automation/account/" + accountId)
    .then(response => response.data.data)
    .catch(ex => {
      showErrorToast("Failed to get account products");
      throw ex;
    });
}

export const updateAccountProvisioning = (accountId: string, payload: AccountProvisioningUpdateDetails): Promise<void> => {
  return axios.post("/automation/account/" + accountId, payload)
    .then(() => showSuccessToast("Updated successfully"))
    .catch(ex => {
      showErrorToast("Update failed");
      throw ex;
    })
}

export const getMetadata = () : Promise<Metadata> => {
  const bundleMappingDetailsResult = axios.get("/automation/bundles")
  const docVStrategiesResult = axios.get("/superadmin/1/docv/authenticId/fetch/all")
  return Promise.all([bundleMappingDetailsResult, docVStrategiesResult])
    .then(([bundleMappingResponse, docVStrategiesResponse]) => ({
      bundleMapping: bundleMappingResponse.data.data,
      docVStrategies: docVStrategiesResponse?.data?.data
    }))
    .catch(ex => {
      console.log("Failed to fetch metadata");
      showErrorToast("Failed to get metadata");
      throw ex;
    })
}

export const getAccountState = (accountId: string): Promise<boolean> => {
  return axios.get("/superadmin/account/details/1/preferences?accountid=" + accountId)
    .then((response) => response.data.data.active)
    .catch(ex => {
      showErrorToast("Failed to get account status");
      throw ex;
    })
}
