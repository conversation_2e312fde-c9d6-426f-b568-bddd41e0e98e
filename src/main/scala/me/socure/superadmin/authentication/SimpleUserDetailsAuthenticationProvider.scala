package me.socure.superadmin.authentication

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.authentication._
import org.springframework.security.core.userdetails.{UserDetails, UserDetailsService, UsernameNotFoundException}
import org.springframework.security.core.{Authentication, AuthenticationException}
import org.springframework.stereotype.Component

import scala.util.control.NonFatal

@Component("simpleUserDetailsAuthenticationProvider")
class SimpleUserDetailsAuthenticationProvider() extends AuthenticationProvider {

  import SimpleUserDetailsAuthenticationProvider._

  @Autowired
  private[authentication] var userDetailsService: UserDetailsService = _

  override def supports(authentication: Class[_]): Boolean = {
    classOf[SimpleUnauthenticatedToken].isAssignableFrom(authentication)
  }

  override def authenticate(authentication: Authentication): Authentication = {
    try {
      Option(authentication) match {
        case Some(SimpleUnauthenticatedToken(username, expirationTime, samlAttribute)) =>
          val userDetails = validateUserDetails(authentication.getPrincipal.toString)
          SimpleAuthenticatedToken(
            username = username,
            userDetails = userDetails,
            expirationTime = expirationTime,
            samlAttribute = samlAttribute
          )
        case Some(auth) => throw new UnsupportedAuthenticationTokenException(s"Unsupported Authentication Token[${auth.getClass}] provided. Expected ${classOf[SimpleUnauthenticatedToken].getName}")
        case None => throw new BadCredentialsException("Authentication object is null")
      }
    } catch {
      case ex: AuthenticationException => throw ex
      case NonFatal(ex) =>
        throw new UnknownAuthenticationException("Unknown error while authenticating", ex)
    }
  }

  private def validateUserDetails(username: String): UserDetails = {
    val userDetails: UserDetails = userDetailsService.loadUserByUsername(username)

    if(userDetails == null) throw new UsernameNotFoundException("User not found")

    if (!userDetails.isAccountNonLocked) throw new LockedException("User account is locked")
    if (!userDetails.isEnabled) throw new DisabledException("User is disabled")
    if (!userDetails.isAccountNonExpired) throw new AccountExpiredException("User account has expired")
    if (!userDetails.isCredentialsNonExpired) throw new CredentialsExpiredException("User credentials have expired")

    userDetails
  }
}

object SimpleUserDetailsAuthenticationProvider {

  class UnsupportedAuthenticationTokenException(msg: String) extends AuthenticationServiceException(msg)

  class UnknownAuthenticationException(msg: String, cause: Throwable) extends AuthenticationServiceException(msg, cause)

}
