#!/bin/bash

# Navigate to the v2 directory
cd "$(dirname "$0")/src/main/webapp/WEB-INF/views/v2"

# Try to build with existing node_modules, ignoring errors
echo "Attempting to build React application..."

# Try with npm first
echo "Trying with npm..."
NODE_ENV=development npx react-scripts build --no-audit --no-fund --strict-ssl=false || {
  echo "npm build failed, trying with yarn..."

  # Try with yarn if npm fails
  if command -v yarn &> /dev/null; then
    yarn build || {
      echo "Yarn build failed too..."
    }
  else
    echo "Yarn not found. Both build methods failed."
  fi
  echo "Build failed, but continuing with file operations..."
}

# Perform the file operations even if build fails
echo "Renaming and moving files..."
mkdir -p build/static/js build/static/css 2>/dev/null

# Try to rename JS files
find ./build/static/js -name "main*.js" -exec cp {} ./build/static/js/main.js \; 2>/dev/null
find ./build/static/js -name "main*.js.map" -exec cp {} ./build/static/js/main.js.map \; 2>/dev/null

# Try to rename CSS files
find ./build/static/css -name "main*.css" -exec cp {} ./build/static/css/main.css \; 2>/dev/null
find ./build/static/css -name "main*.css.map" -exec cp {} ./build/static/css/main.css.map \; 2>/dev/null

# Move to resources directory
echo "Moving to resources directory..."
rm -rf ../../../resources/v2 2>/dev/null
mkdir -p ../../../resources/v2 2>/dev/null
cp -r build/* ../../../resources/v2/ 2>/dev/null

echo "Process completed. Check the resources/v2 directory for the output."
echo "If you don't see your changes, you may need to fix the npm installation issues."
