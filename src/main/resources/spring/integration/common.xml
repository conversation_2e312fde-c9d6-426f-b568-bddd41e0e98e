<?xml version="1.0" encoding="UTF-8"?>
<beans:beans xmlns="http://www.springframework.org/schema/integration"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:beans="http://www.springframework.org/schema/beans"
	xmlns:jms="http://www.springframework.org/schema/integration/jms"
	xmlns:stream="http://www.springframework.org/schema/integration/stream"
	xmlns:integration="http://www.springframework.org/schema/integration"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
			http://www.springframework.org/schema/beans/spring-beans.xsd
			http://www.springframework.org/schema/integration
			http://www.springframework.org/schema/integration/spring-integration.xsd
			http://www.springframework.org/schema/integration/jms
			http://www.springframework.org/schema/integration/jms/spring-integration-jms.xsd
			http://www.springframework.org/schema/integration/stream
			http://www.springframework.org/schema/integration/stream/spring-integration-stream.xsd
			http://www.springframework.org/schema/integration
			http://www.springframework.org/schema/integration/spring-integration.xsd">

	<beans:bean id="connectionFactory"
		class="org.springframework.jms.connection.CachingConnectionFactory">
		<beans:property name="targetConnectionFactory">
			<beans:bean class="org.apache.activemq.ActiveMQConnectionFactory">
				<beans:property name="brokerURL" value="vm://localhost" />
			</beans:bean>
		</beans:property>
		<beans:property name="sessionCacheSize" value="10" />
		<beans:property name="cacheProducers" value="false" />
	</beans:bean>

	<beans:bean id="requestQueue" class="org.apache.activemq.command.ActiveMQQueue">
		<beans:constructor-arg value="queue.pat.request" />
	</beans:bean>

	<beans:bean id="fbRequestQueue" class="org.apache.activemq.command.ActiveMQQueue">
		<beans:constructor-arg value="queue.pat.request.fb" />
	</beans:bean>
	<beans:bean id="twRequestQueue" class="org.apache.activemq.command.ActiveMQQueue">
		<beans:constructor-arg value="queue.pat.request.tw" />
	</beans:bean>
	<beans:bean id="inRequestQueue" class="org.apache.activemq.command.ActiveMQQueue">
		<beans:constructor-arg value="queue.pat.request.in" />
	</beans:bean>
	<beans:bean id="gpRequestQueue" class="org.apache.activemq.command.ActiveMQQueue">
		<beans:constructor-arg value="queue.pat.request.gp" />
	</beans:bean>

	<beans:bean id="replyQueue" class="org.apache.activemq.command.ActiveMQQueue">
		<beans:constructor-arg value="queue.pat.reply" />
	</beans:bean>

	<integration:poller id="poller" default="true"
		fixed-delay="1000" />

	<jms:message-driven-channel-adapter
		id="jmsIn" destination="requestQueue" channel="jmsInChannel" />

	<channel id="jmsInChannel" />

	<channel id="fbQueueChannel">
		<queue />
	</channel>

	<channel id="twQueueChannel">
		<queue />
	</channel>

	<channel id="inQueueChannel">
		<queue />
	</channel>

	<channel id="gpQueueChannel">
		<queue />
	</channel>

	<jms:outbound-channel-adapter id="fbjmsout"
		channel="fbQueueChannel" destination="requestQueue" />

	<jms:outbound-channel-adapter id="twjmsout"
		channel="twQueueChannel" destination="requestQueue" />

	<jms:outbound-channel-adapter id="injmsout"
		channel="inQueueChannel" destination="requestQueue" />

	<jms:outbound-channel-adapter id="gpjmsout"
		channel="gpQueueChannel" destination="requestQueue" />

	<header-value-router input-channel="jmsInChannel"
		header-name="network">
		<mapping value="fb" channel="fbQueueChannel" />
		<mapping value="tw" channel="twQueueChannel" />
		<mapping value="in" channel="inQueueChannel" />
		<mapping value="gp" channel="gpQueueChannel" />
	</header-value-router>

</beans:beans>
