{"version": "v0.7-SNAPSHOT-0.1", "weights": {"weight": 0, "scoreComponents": {"ASANL": {"100500": {"description": "See documentation", "identity": "AUTHSCORE_PLUS", "weight": 0}, "100501": {"description": "See documentation", "identity": "PII_SCORE", "authscore_type": "authscore", "weight": 1}, "100502": {"description": "See documentation", "identity": "ONLINE_SOCIAL_BEHAVIOR_SCORE", "authscore_type": "authscore", "weight": 0.2}, "100503": {"description": "See documentation", "identity": "ONLINE_PRESENCE_SCORE", "authscore_type": "authscore", "weight": 0.8}}, "BOVAL": {"100131": {"description": "Timeout error", "identity": "BOSS_TIMEOUT", "weight": 0}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "minScore": 0, "weight": 0}, "600001": {"description": "Match", "identity": "MATCH", "weight": 0}, "600002": {"description": "Boss match with FC or PIPL match. if it exists and it's 0, means there are no match between FC+PIPL and LINKEDIN", "identity": "BOSS__FC_PI_IN_MATCH", "weight": 0}, "600003": {"description": "Boss match on no match situation for FC and PIPL", "identity": "BOSS_FC_PI_NO_MATCH", "weight": 0}, "600006": {"description": "Linkedin id missmatch", "identity": "LI_MISSMATCH", "weight": 0}, "confidence": 0, "maxScore": 0, "minScore": 0}, "COVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout error", "identity": "TIMEOUT", "weight": 0}, "400010": {"description": "Probability of relation between name and phone in input", "identity": "NAME_PHONE_CORRELATION", "weight": 0}, "400011": {"description": "If COVAL.400010 >= threshold", "identity": "NAME_PHONE_MATCH", "reasoncode": "I618", "weight": 0}, "400012": {"description": "If COVAL.400010 < threshold", "identity": "NAME_PHONE_NO_MATCH", "reasoncode": "R608", "weight": 0}, "400013": {"description": "Probability of relation between name and email in input", "identity": "NAME_EMAIL_CORRELATION", "weight": 0}, "400014": {"description": "If COVAL.400013 >= threshold", "identity": "NAME_EMAIL_MATCH", "reasoncode": "I556", "weight": 0}, "400015": {"description": "If COVAL.400013 < threshold", "identity": "NAME_EMAIL_NO_MATCH", "reasoncode": "R559", "weight": 0}, "400016": {"description": "Probability of relation between name and address in input", "identity": "NAME_ADDRESS_CORRELATION", "weight": 0}, "400017": {"description": "If COVAL.400016 >= threshold", "identity": "NAME_ADDRESS_MATCH", "reasoncode": "I708", "weight": 0}, "400018": {"description": "If COVAL.400016 < threshold", "identity": "NAME_ADDRESS_NO_MATCH", "reasoncode": "R705", "weight": 0}, "400019": {"description": "Combined correlation score for name, address, email", "identity": "FV_NAME_ADDRESS_EMAIL_MATCH", "weight": 0}, "400020": {"description": "Combined correlation score for name, address, phone", "identity": "FV_NAME_ADDRESS_PHONE_MATCH", "weight": 0}, "400021": {"description": "Combined correlation score for name, address, email, phone", "identity": "FV_NAME_ADDRESS_PHONE_EMAIL_MATCH", "weight": 0}, "400022": {"description": "Combined correlation score for name, phone, email", "identity": "FV_NAME_PHONE_EMAIL_MATCH", "weight": 0}}, "RKVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout error", "identity": "TIMEOUT", "weight": 0}, "400001": {"description": "Email Risk Score", "identity": "RK_EMAIL_RISK_SCORE", "weight": 0}, "400002": {"description": "Phone Risk Score", "identity": "RK_PHONE_RISK_SCORE", "weight": 0}, "400003": {"description": "Address Risk Score", "identity": "RK_ADDRESS_RISK_SCORE", "weight": 0}}, "EMVAL": {"100113": {"description": "Domain is invalid or email format is bad", "identity": "DOMAIN_INVALID", "positive": false, "weight": -2.01}, "100114": {"description": "Email can't be validated (permanent)", "identity": "EMAIL_CANT_VALIDATE", "positive": false, "weight": 0}, "100118": {"description": "Email validation failed", "identity": "EMAIL_VALIDATION_FAILED", "positive": false, "weight": 0}, "100126": {"description": "Name part of email doesn't match first name last name", "identity": "FIRST_LAST_NAME_NO_MATCH", "positive": false, "weight": -0.51}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200125": {"description": "Email is valid", "identity": "EMAIL_VALID", "positive": true, "weight": 0.49}, "confidence": 0.1, "maxScore": 1.01, "minScore": -2.01}, "FBANL": {"100001": {"description": "InSufficientAccountAge", "identity": "FB_INSUFFICIENT_ACC", "positive": false, "reasoncode": "I131", "weight": -2}, "100003": {"description": "EducatedAtSameAsNegative", "identity": "FB_EDUCATE_SAME_NEGATIVE", "positive": false, "weight": -0.26}, "100004": {"description": "WorkedAtSameAsNegative", "identity": "FB_WORK_SAME_NEGATIVE", "positive": false, "weight": -0.26}, "100005": {"description": "LivesAtSameAsNegative", "identity": "FB_LIVES_SAME_NEGATIVE", "positive": false, "weight": -0.26}, "100006": {"description": "HometownAtSameAsNegative", "identity": "FB_HOME_TOWN_SAME_NEGATIVE", "positive": false, "weight": -0.26}, "100007": {"description": "NotEnoughPostsOnBirthday", "identity": "FB_NOT_ENOUGH_POST_BDAY", "reasoncode": "I133", "positive": false, "weight": -0.76}, "100008": {"description": "InsufficientTagsForFacebookProfile", "identity": "FB_INSUFFICIENT_TAG_PROFILE", "reasoncode": "I133", "positive": false, "weight": -0.76}, "100010": {"description": "TooManyTagsPerPhoto or NoTagsPerPhoto", "identity": "FB_TO_MANY_TAG_PHOTO", "reasoncode": "I133", "positive": false, "weight": -0.76}, "100012": {"description": "((PostFrequencyMean / No of Posts) * 100) greater than or equal to 8", "identity": "FB_POST_FRQ_GREATER_8", "reasoncode": "I133", "positive": false, "weight": -1}, "100013": {"description": "Too many or too few gender specific friends for their gender", "identity": "FB_MANY_OR_FEW_SPECIFIC_GENDER_FRIENDS", "reasoncode": "I132", "positive": false, "weight": -0.51}, "100014": {"description": "Users friends share less than or equal 8 friends on average", "identity": "FB_FRIEND_SHARE_LESS", "reasoncode": "I132", "positive": false, "weight": -0.51}, "100015": {"description": "AccountAge greater than or equal 5 months AND no of Friends less than or equal 50", "identity": "FB_AGE_GREATER", "reasoncode": "I134", "positive": false, "weight": -0.51}, "100016": {"description": "Name does not match email", "identity": "FB_NAME_DOES_NOT_MATCH", "positive": false, "weight": -0.51}, "100017": {"description": "Account appears abandoned", "identity": "FB_ACC_ABANDONED", "positive": false, "weight": -0.51}, "100018": {"description": "More than 10 timeline posts, but no personal comments or no personal likes", "identity": "FB_TIMELINE_POST", "reasoncode": "I133", "positive": false, "weight": -0.51}, "100019": {"description": "Account no longer exists", "identity": "FB_ACC_NO_EXIST", "positive": false, "reasoncode": "I130", "weight": -0.51}, "100020": {"description": "COUNT(100003, 100004, 100005, 100006) greater than or equal 3", "identity": "FB_NEGATIVE_COUNT_LESSER", "reasoncode": "I132", "positive": false, "weight": -1}, "100029": {"description": "Account suspended", "identity": "FB_ACC_SUSPENED", "positive": false, "reasoncode": "R131", "weight": -1.01}, "200002": {"description": "VerifiedPOSITIVE", "identity": "FB_VERIFIED_POSITIVE", "positive": true, "weight": 0}, "200003": {"description": "EducatedAtSameAsPOSITIVE", "identity": "FB_EDUCATE_SAME_POSITIVE", "positive": true, "weight": 0.26}, "200004": {"description": "WorkedAtSameAsPOSITIVE", "identity": "FB_WORK_SAME_POSITIVE", "positive": true, "weight": 0.26}, "200005": {"description": "LivesAtSameAsPOSITIVE", "identity": "FB_LIVES_SAME_POSITIVE", "positive": true, "weight": 0.26}, "200006": {"description": "HometownAtSameAsPOSITIVE", "identity": "FB_HOME_TOWN_SAME_POSITIVE", "positive": true, "weight": 0.26}, "200007": {"description": "EnoughPostOnBirthday", "identity": "FB_ENOUGH_POST_BDAY", "reasoncode": "I233", "positive": true, "weight": 0.76}, "200008": {"description": "SufficiantTagsForFacebookProfile", "identity": "FB_SUFFICIANT_TAG_PROFILE", "reasoncode": "I233", "positive": true, "weight": 0.76}, "200009": {"description": "AverageTagsPerPhoto", "identity": "FB_AVE_TAG_PHOTO", "positive": true, "weight": 0.51}, "200010": {"description": "((PostFrequencyMean / No of Posts) * 100) less than or equal 8", "identity": "FB_POST_FRQ_LESS_8", "reasoncode": "I233", "positive": true, "weight": 0.75}, "200011": {"description": "HasCoverPhoto true", "identity": "FB_HAS_COVER_PHOTO_TRUE", "positive": true, "weight": 0.51}, "200013": {"description": "Name matches email", "identity": "FB_NAME_MATCH", "positive": true, "weight": 0.51}, "200014": {"description": "Users friends share greater than or equal 18 friends on average", "identity": "FB_FRIEND_SHARE_GREATER", "reasoncode": "I232", "positive": true, "weight": 0.51}, "200019": {"description": "Has at least one close friend", "identity": "FB_HAS_ONE_CLOSE_FRIEND", "positive": true, "weight": 0.51}, "200020": {"description": "COUNT(200003, 200004, 200005, 200006) greter than or equal 3", "identity": "FB_POSSIVE_COUNT_GREATER", "reasoncode": "I232", "positive": true, "weight": 1}, "300001": {"description": "Account age in months", "identity": "FB_ACC_AGE_MONTHS", "weight": 0}, "300002": {"description": "Number of friends", "identity": "FB_NO_FRIENDS", "weight": 0}, "300003": {"description": "Number of posts", "identity": "FB_NO_POST", "weight": 0}, "300004": {"description": "Number of close friends", "identity": "FB_NO_CLOSE_FRIENDS", "weight": 0}, "300005": {"description": "Number of personal posts", "identity": "FB_NO_PESONAL_POST", "weight": 0}, "300006": {"description": "Number of personal comments", "identity": "FB_NO_PERSONAL_COMMENTS", "weight": 0}, "300007": {"description": "Average of number of posts per day of posting (non-posting days are ignored)", "identity": "FB_AVE_POST_PER_DAY", "weight": 0}, "300008": {"description": "Standard deviation of number of posts on days of posting (non-posting days are ignored)", "identity": "FB_STD_DEVIATION_NO_POST", "weight": 0}, "300009": {"description": "Number of things the user likes", "identity": "FB_NO_USER_LIKE", "weight": 0}, "300010": {"description": "Average number of days between the user liking something new", "identity": "FB_AVG_DAYS_USER_LIKE", "weight": 0}, "300011": {"description": "Standard deviation (in millis) of the above mean", "identity": "FB_STD_DEVIATION", "weight": 0}, "300012": {"description": "Mutual friends average", "identity": "FB_MUTAL_FRIEND", "weight": 0}, "300013": {"description": "isVerified (if present, it's true)", "identity": "FB_IS_VERIFIED", "weight": 0}, "300014": {"description": "educatedSameAs", "identity": "FB_EDUCATE_SAME", "weight": 0}, "300015": {"description": "workedSameAs", "identity": "FB_WORK_SAME", "weight": 0}, "300016": {"description": "livesSameAs", "identity": "FB_LIVES_SAME", "weight": 0}, "300017": {"description": "hometownSameAs", "identity": "FB_HOME_TOWN_SAME", "weight": 0}, "300018": {"description": "Number of posts on bday", "identity": "FB_NO_POST_BDAY", "weight": 0}, "300019": {"description": "Number of Profile tags", "identity": "FB_NO_PROFILE_TAG", "weight": 0}, "300020": {"description": "Number of photos", "identity": "FB_NO_PHOTOS", "weight": 0}, "300021": {"description": "Number of photos with tags", "identity": "FB_PHOTO_TAG", "weight": 0}, "300022": {"description": "has cover photo", "identity": "FB_COVER_PHOTOS", "weight": 0}, "300023": {"description": "gender from FB (0 for male. 1 for female)", "identity": "FB_GENDER", "weight": 0}, "300024": {"description": "male friend count", "identity": "FB_MALE_FRI_COUNT", "weight": 0}, "confidence": 0.075, "maxScore": 6, "minScore": -9}, "FBVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0.25, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "minScore": -0.51, "positive": true, "weight": 1.01}, "200131": {"description": "Facebook URL is in profilesFound section", "identity": "FB_URL_FOUND", "reasoncode": "I230", "weight": 0}, "FBPERM": {"description": "Enough FB Permission available.", "identity": "FB_PERMISSION_AVAILABLE", "weight": 0}, "PERFECT_ID": {"weight": 0}, "maxScore": 3.01, "minScore": -0.51}, "FCVAL": {"100601": {"description": "Address resolved by DSTK", "identity": "DSTK_ADDRESS_RESOLVED", "weight": 0}, "100602": {"description": "Address not resolved by DSTK", "identity": "DSTK_ADDRESS_NOT_RESOLVED", "weight": 0}, "100603": {"description": "DSTK Timeout", "identity": "DSTK_TIMEOUT", "weight": 0}, "100604": {"description": "DSTK Error", "identity": "DSTK_ERROR", "weight": 0}, "100611": {"description": "Address resolved by MapQuest", "identity": "MQ_ADDRESS_RESOLVED", "weight": 0}, "100612": {"description": "Address not resolved by MapQuest", "identity": "MQ_ADDRESS_NOT_RESOLVED", "weight": 0}, "100613": {"description": "MapQuest Timeout", "identity": "MQ_TIMEOUT", "weight": 0}, "100614": {"description": "MapQuest Error", "identity": "MQ_ERROR", "weight": 0}, "100621": {"description": "Address resolved by Google_Geocode", "identity": "GG_ADDRESS_RESOLVED", "weight": 0}, "100622": {"description": "Address not resolved by Google_Geocode", "identity": "GG_ADDRESS_NOT_RESOLVED", "weight": 0}, "100623": {"description": "Google_Geocode Timeout", "identity": "GG_TIMEOUT", "weight": 0}, "100624": {"description": "Google_Geocode Error", "identity": "GG_ERROR", "weight": 0}, "100117": {"confidence": 0.1, "description": "FC no match", "identity": "FC_NO_MATCH", "positive": false, "weight": -0.67}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "100199": {"confidence": 0.15, "description": "FC address is not good", "identity": "FC_ADDRESS_NOT_GOOD", "positive": false, "weight": -1.01}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "minScore": 0, "positive": true, "weight": 0}, "200128": {"confidence": 0.1, "description": "Number of non-social perfect matches", "identity": "NO_SOCIAL_PERFECT_MATCH", "minScore": -0.51, "positive": true, "reasoncode": "I127", "weight": 0.33}, "200277": {"description": "FullContact match score", "identity": "FC_MATCH_SCORE", "weight": 0}, "700011": {"confidence": 0, "description": "Fullcontact bad result. Should this fire 100117 instead ?", "identity": "FC_BAD_RESULT", "positive": false, "weight": 0}, "300001": {"description": "Has family name", "identity": "FC_HAS_FAMILY_NAME", "weight": 0}, "300002": {"description": "Has given name", "identity": "FC_HAS_GIVEN_NAME", "weight": 0}, "300003": {"description": "Has full name", "identity": "FC_HAS_FULL_NAME", "weight": 0}, "300004": {"description": "Age", "identity": "FC_AGE", "weight": 0}, "300005": {"description": "Gender", "identity": "FC_GENDER", "type": "string", "length": 6, "weight": 0}, "300006": {"description": "Has location (usually city)", "identity": "FC_HAS_LOCATION", "weight": 0}, "300007": {"description": "Number of websites", "identity": "FC_WEBSITES_NUM", "weight": 0}, "300008": {"description": "Number of photos", "identity": "FC_PHOTOS_NUM", "weight": 0}, "300009": {"description": "Number of photos from social profiles", "identity": "FC_PHOTOS_SOCIAL_PROFILES_NUM", "weight": 0}, "300010": {"description": "Number of photos from non-social profiles", "identity": "FC_PHOTOS_NOT_SOCIAL_PROFILES_NUM", "weight": 0}, "300011": {"description": "Number of digital footprint topics", "identity": "FC_DIGITALFOOTPRINT_TOPICS_NUM", "weight": 0}, "300012": {"description": "Digital footprint score", "identity": "FC_DIGITALFOOTPRINT_KLOUT_SCORE", "weight": 0}, "300013": {"description": "Number of organisations", "identity": "FC_ORGANIZATIONS_NUM", "weight": 0}, "300014": {"description": "Number of social profiles", "identity": "FC_SOCIAL_PROFILES_NUM", "weight": 0}, "maxScore": 1.01, "minScore": -1.51}, "FMVAL": {"100145": {"description": "Geo IP distance from address", "identity": "GEO_IP_DISTANCE_FROM_ADDRESS", "weight": 0}, "100601": {"description": "Address resolved by DSTK", "identity": "DSTK_ADDRESS_RESOLVED", "weight": 0}, "100602": {"description": "Address not resolved by DSTK", "identity": "DSTK_ADDRESS_NOT_RESOLVED", "weight": 0}, "100603": {"description": "DSTK Timeout", "identity": "DSTK_TIMEOUT", "weight": 0}, "100604": {"description": "DSTK Error", "identity": "DSTK_ERROR", "weight": 0}, "100611": {"description": "Address resolved by MapQuest", "identity": "MQ_ADDRESS_RESOLVED", "weight": 0}, "100612": {"description": "Address not resolved by MapQuest", "identity": "MQ_ADDRESS_NOT_RESOLVED", "weight": 0}, "100613": {"description": "MapQuest Timeout", "identity": "MQ_TIMEOUT", "weight": 0}, "100614": {"description": "MapQuest Error", "identity": "MQ_ERROR", "weight": 0}, "100621": {"description": "Address resolved by Google_Geocode", "identity": "GG_ADDRESS_RESOLVED", "weight": 0}, "100622": {"description": "Address not resolved by Google_Geocode", "identity": "GG_ADDRESS_NOT_RESOLVED", "weight": 0}, "100623": {"description": "Google_Geocode Timeout", "identity": "GG_TIMEOUT", "weight": 0}, "100624": {"description": "Google_Geocode Error", "identity": "GG_ERROR", "weight": 0}, "100122": {"description": "Address fake", "identity": "ADDRESS_FAKE", "positive": false, "reasoncode": "I122", "weight": 0}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "100140": {"confidence": 0.1, "description": "If physical address is fake (address search doesn't find anything)", "identity": "FAKE_PHYSICAL_ADDRESS", "positive": false, "weight": -0.76}, "100142": {"description": "Phone number fake", "identity": "PHONE_NO_FAKE", "positive": false, "weight": 0}, "100143": {"description": "Phone number different country", "identity": "PHONE_NO_DIFF_COUNTRY", "positive": false, "weight": 0}, "100144": {"description": "Phone number does not match address by 500km", "identity": "PHONE_NO_UNMATCH", "positive": false, "weight": 0}, "100150": {"description": "Gender unknown", "identity": "GENDER_UNKWON", "positive": false, "weight": 0}, "200150": {"description": "Genderize probability that the user is female", "identity": "USER_FEMALE", "positive": true, "weight": 0}, "300666": {"description": "Age in years at time of Email API request ?", "identity": "AGE_YEAR", "weight": 0}, "500101": {"description": "State must be two characters", "identity": "FM_STATE_TWO_CHAR", "positive": false, "reasoncode": "V101", "weight": 0}, "500102": {"description": "Country must meet ISO two character standard", "identity": "FM_COUNTRY_ISO_STD", "positive": false, "reasoncode": "V102", "weight": 0}, "500103": {"description": "State is only permitted when country is US", "identity": "FM_STATE_ONLY_IN_US", "positive": false, "reasoncode": "V103", "weight": 0}, "500104": {"description": "Physical address requires at least zip/country or city/country", "identity": "FM_ADDRESS_NEED_ATLEAST_ZIP", "positive": false, "reasoncode": "V104", "weight": 0}, "500105": {"description": "Physical address and city in US requires at least zip or state", "identity": "FM_ADDRESS_NEED_ZIP", "positive": false, "reasoncode": "V105", "weight": 0}, "500106": {"description": "Country code is required unless all are specified (physical address, city, state, zip)", "identity": "FM_COUNTRY_CODE_NEED", "positive": false, "reasoncode": "V106", "weight": 0}, "500107": {"description": "Supported date or birth formats are yyyyMMdd, yyyy-MM-dd, yyyy/MM/dd", "identity": "FM_", "positive": false, "reasoncode": "V107", "weight": 0}, "500108": {"description": "IPAddress must meet IPv4 or IPv6 standard", "identity": "FM_IP_MEET_IP_STD", "positive": false, "reasoncode": "V108", "weight": 0}, "500109": {"description": "Geocode format is invalid", "identity": "FM_GEOCODE_INVALID", "positive": false, "reasoncode": "V109", "weight": 0}, "500110": {"description": "Supported phone number formats are +*********** or + **************", "identity": "FM_SUPPORT_PHONE_FORMAT", "positive": false, "reasoncode": "V110", "weight": 0}, "500111": {"description": "Missing Required fields firstname/surname", "identity": "FM_MISSING_FIRST_NAME", "positive": false, "reasoncode": "V111", "weight": 0}, "500112": {"description": "Missing Required fields provider", "identity": "FM_MISSING_PROVIDER", "positive": false, "reasoncode": "V112", "weight": 0}, "500116": {"description": "National ID is permitted,only when country is US", "identity": "FM_COUNTRY_NEEDED", "positive": false, "reasoncode": "V116", "weight": 0}, "500117": {"description": "Invalid driver license format", "identity": "FM_INVALID_DRIVING_LICENSE", "positive": false, "reasoncode": "V117", "weight": 0}, "500118": {"description": "Invalid national id format", "identity": "FM_INVALID_NATIONAL_ID", "positive": false, "weight": 0}, "500119": {"description": "User is blacklisted", "identity": "FM_BLACKLISTED_USER", "positive": false, "weight": 0}, "300001": {"description": "Email domain", "identity": "FM_EMAL_DOMAIN", "type": "string", "length": 11, "weight": 0}, "300002": {"description": "Email matches first name", "identity": "FM_EMAIL_CONTAIN_FIRST_NAME", "weight": 0}, "300003": {"description": "Email matches surname", "identity": "FM_EMAIL_CONTAIN_SURNAME", "weight": 0}, "300004": {"description": "Email matches last two digits of DOB year", "identity": "FM_EMAIL_CONTAINS_DOB_YEAR", "weight": 0}, "300005": {"description": "Phone is associated with a major US carrier", "identity": "FM_PHONE_MAJOR_US_CARRIER", "reasoncode": "I611", "positive": true, "weight": 0}, "300006": {"description": "Phone is not associated with a major US carrier", "identity": "FM_PHONE_NOT_MAJOR_US_CARRIER", "reasoncode": "I612", "positive": false, "weight": 0}, "400001": {"description": "Order channel", "identity": "FM_ORDER_CHANNEL", "type": "string", "weight": 0}, "400002": {"description": "Date of last order", "identity": "FM_LAST_ORDER_DATE", "type": "string", "weight": 0}, "400003": {"description": "Previous order count", "identity": "FM_PREV_ORDER_COUNT", "weight": 0}, "400004": {"description": "Date when account was created", "identity": "FM_ACCOUNT_CREATION_DATE", "type": "string", "weight": 0}, "400005": {"description": "Order amount", "identity": "FM_ORDER_AMOUNT", "weight": 0}, "400006": {"description": "Submission date", "identity": "FM_SUBMISSION_DATE", "type": "string", "weight": 0}, "300015": {"description": "Number of days since last order", "identity": "FM_NUMBER_DAYS_SINCE_LAST_ORDER", "weight": 0}, "300016": {"description": "Number of days since account creation date i.e. account age", "identity": "FM_NUMBER_DAYS_SINCE_ACCOUNT_CREATION_DATE", "weight": 0}}, "FVANL": {"400001": {"description": "Person is not <16 years old or >100 years old", "identity": "DOB_AGE_LESS_16_OR_MORE_100", "reasoncode": "R350", "weight": 0}, "400002": {"description": "Online sources could not validate the DOB for this profile", "identity": "DOB_NOT_VALIDATED_BY_ONLINE_SOCIAL_DATA", "reasoncode": "R352", "weight": 0}, "400003": {"description": "Check if digits are all 0s, 1s, 2s, 3s, 4s, 5s, 6s, 7s 8s or 9s, or non-consumer/toll-free phone numbers like numbers starting with 555, 800, 877, 866 etc.", "identity": "PHONE_NOT_ALLOWED_FOR_CONSUMERS", "reasoncode": "R611", "weight": 0}}, "GLOBAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "100137": {"description": "RISK  Search by email and by address on perfect match doesn't have any url in common", "identity": "GLOBAL_SEARCH_BY_EMAIL_PERFECT_MATCH_DOESNT", "positive": false, "weight": 0}, "100138": {"description": "RISK Search by email and by address on perfect and partial doesn't have any url in common", "identity": "GLOBAL_SEARCH_BY_EMAIL_PARTIAL_MATCH_DOESNT", "positive": false, "weight": -0.51}, "100139": {"description": "Access token and email refers to different emails", "identity": "GLOBAL_SEARCH_BY_EMAIL", "positive": false, "weight": 0}, "100140": {"description": "The two emails are linked to two different facebook accounts", "identity": "GLOBAL_EMAIL_DIFF_FB_ACC", "positive": false, "reasoncode": "R130", "weight": -0.5}, "100141": {"description": "The two emails are linked to two different twitter accounts", "identity": "GLOBAL_EMAIL_DIFF_TW_ACC", "positive": false, "reasoncode": "R140", "weight": -0.5}, "100142": {"description": "The two emails are linked to two different linkedin accounts", "identity": "GLOBAL_EMAIL_DIFF_IN_ACC", "positive": false, "reasoncode": "R150", "weight": -0.5}, "100143": {"description": "RISK Search by email doesn't provide any urls", "identity": "GLOBAL_MAIL_DOESNT_PROVIDE_URL", "positive": false, "weight": -1.01}, "100144": {"description": "The two emails are linked to two different googleplus accounts", "identity": "GLOBAL_EMAIL_DIFF_GP_ACC", "positive": false, "reasoncode": "R160", "weight": -0.5}, "200127": {"description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0}, "300001": {"description": "Profile found count", "identity": "PROFILE_FOUND_COUNT", "weight": 0}, "300002": {"description": "Profile found domain count", "identity": "PROFILE_FOUND_DOMAIN_COUNT", "weight": 0}, "300003": {"description": "Social profile urls count", "identity": "SOCIAL_PROFILE_URLS_COUNT", "weight": 0}, "600001": {"description": "Match", "identity": "MATCH", "weight": 0}, "300004": {"description": "Indicator for popular email domain and major US phone carrier", "identity": "GLOBAL_EMAIL_POPULAR_DOMAIN_AND_MAJOR_US_PHONE_CARRIER", "weight": 0}, "300036": {"description": "adjusted logic of I551", "identity": "GLOBAL_I551", "reasoncode": "I551", "weight": 0}, "300037": {"description": "current logic of I551", "identity": "GLOBAL_I551_ORIG", "weight": 0}, "300038": {"description": "adjusted logic of I552", "identity": "GLOBAL_I552", "reasoncode": "I552", "weight": 0}, "300039": {"description": "current logic of I552", "identity": "GLOBAL_I552_ORIG", "weight": 0}, "300040": {"description": "adjusted logic of I553", "identity": "GLOBAL_I553", "reasoncode": "I553", "weight": 0}, "300041": {"description": "current logic of I553", "identity": "GLOBAL_I553_ORIG", "weight": 0}, "300042": {"description": "adjusted logic of I554", "identity": "GLOBAL_I554", "reasoncode": "I554", "weight": 0}, "300043": {"description": "current logic of I554", "identity": "GLOBAL_I554_ORIG", "weight": 0}, "300044": {"description": "adjusted logic of I555", "identity": "GLOBAL_I555", "reasoncode": "I555", "weight": 0}, "300045": {"description": "current logic of I555", "identity": "GLOBAL_I555_ORIG", "weight": 0}, "300046": {"description": "adjusted logic of R603", "identity": "GLOBAL_R603", "reasoncode": "R603", "weight": 0}, "300047": {"description": "current logic of R603", "identity": "GLOBAL_R603_ORIG", "weight": 0}, "300048": {"description": "adjusted logic of R604", "identity": "GLOBAL_R604", "reasoncode": "R604", "weight": 0}, "300049": {"description": "current logic of R604", "identity": "GLOBAL_R604_ORIG", "weight": 0}, "300050": {"description": "adjusted logic of R605", "identity": "GLOBAL_R605", "reasoncode": "R605", "weight": 0}, "300051": {"description": "current logic of R605", "identity": "GLOBAL_R605_ORIG", "weight": 0}, "300052": {"description": "adjusted logic of I614", "identity": "GLOBAL_I614", "reasoncode": "I614", "weight": 0}, "300053": {"description": "current logic of I614", "identity": "GLOBAL_I614_ORIG", "weight": 0}, "300054": {"description": "adjusted logic of I615", "identity": "GLOBAL_I615", "reasoncode": "I615", "weight": 0}, "300055": {"description": "current logic of I615", "identity": "GLOBAL_I615_ORIG", "weight": 0}, "confidence": 0.05, "maxScore": 0, "minScore": -0.5, "weight": -0.26}, "GPANL": {"100001": {"description": "Account age less than or equal 5 months", "identity": "GP_ACC_AGE_LESS_5_MONTHS", "positive": false, "reasoncode": "I161", "weight": -2}, "100002": {"description": "no activity count", "identity": "GP_NO_ACTIVITY_COUNT", "reasoncode": "I163", "positive": false, "weight": -1}, "100003": {"description": "no resharers", "identity": "GP_NO_RESHARER", "reasoncode": "I162", "positive": false, "weight": -1}, "100004": {"description": "no +1s at all", "identity": "GP_NO_LS_AT_ALL", "reasoncode": "I163", "positive": false, "weight": -1}, "100005": {"description": "NoReplyAtAllAnybodyListening", "identity": "GP_NO_REPLY", "reasoncode": "I162", "positive": false, "weight": -1}, "100006": {"description": "No companies listed", "identity": "GP_NO_COMPANY_LIST", "positive": false, "weight": -1}, "100007": {"description": "Has no profile picture", "identity": "HAS_NO_PROFILE_PIC", "positive": false, "reasoncode": "I164", "weight": -1}, "100008": {"description": "Has No birthday listed", "identity": "HAS_NO_BDAY_LIST", "positive": false, "weight": -1}, "100009": {"description": "Missing one of the above", "identity": "MISSING_ONE_ABOVE", "positive": false, "weight": -1}, "100019": {"description": "Account no longer exists", "identity": "ACC_NO_LONGER_EXIST", "positive": false, "reasoncode": "I160", "weight": -0.51}, "200001": {"description": "Account age more than 5 months", "identity": "GP_ACC_MORE_5_MONTHS", "positive": true, "weight": 1}, "200002": {"description": "at least one Activity Count", "identity": "GP_ONE_ACTIVITY_COUNT", "reasoncode": "I263", "positive": true, "weight": 1}, "200003": {"description": "at least one resharer", "identity": "GP_ONE_RESHARER", "positive": true, "weight": 0.5}, "200004": {"description": "at least one +1", "identity": "GP_LEAST_ONE", "reasoncode": "I263", "positive": true, "weight": 1}, "200005": {"description": "GooglePlus connections are good", "identity": "GP_ONE_REPLY", "reasoncode": "I262", "positive": true, "weight": 1}, "200006": {"description": "One or more companies listed", "identity": "GP_ONE_MORE_COMPANY_LIST", "positive": true, "weight": 0.5}, "200007": {"description": "Has profile pic", "identity": "HAS_PROFILE_PIC", "positive": true, "weight": 0.5}, "200008": {"description": "Has birthday listed", "identity": "HAS_BDAY_LIST", "positive": true, "weight": 0.5}, "200009": {"description": "Has fName , lName, email and gender", "identity": "HAS_NAME_GENDER", "positive": true, "weight": 1}, "300001": {"description": "Account age in months", "identity": "GP_ACC_AGE_MONTHS", "weight": 0}, "300002": {"description": "Activity Count", "identity": "GP_ACTIVITY_COUNT", "weight": 0}, "300003": {"description": "resharers", "identity": "GP_RESHARERS", "weight": 0}, "300004": {"description": "plusOners", "identity": "GP_PLUSONERS", "weight": 0}, "300005": {"description": "replies to activites", "identity": "GP_REPLIES", "weight": 0}, "300006": {"description": "Company count", "identity": "GP_COMPANY_COUNT", "weight": 0}, "confidence": 0.075, "maxScore": 3.01, "minScore": -3.01}, "GPVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0.1, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "minScore": -0.51, "positive": true, "weight": 0.51}, "400001": {"description": "Google plus match", "identity": "GP_MATCH", "positive": true, "weight": 0.51}, "400002": {"description": "No google plus match", "identity": "NO_GP_MATCH", "positive": false, "weight": -0.51}, "400003": {"description": "API error", "identity": "GP_API_ERROR", "weight": 0}, "400004": {"description": "Error on google plus search", "identity": "ERROR_GP_SEARCH", "weight": 0}, "410001": {"description": "Number of gplus matches", "identity": "NO_GP_MATCH", "weight": 0}, "200131": {"description": "Google+ URL is in profilesFound section", "identity": "GP_URL_FOUND", "reasoncode": "I260", "weight": 0}, "confidence": 0.1, "maxScore": 0.51, "minScore": -0.51}, "IMVAL": {"100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "800001": {"description": "Perfect profile image match found", "identity": "FACE_PERFECT_MATCH", "reasoncode": "I436", "positive": true, "weight": 1}, "800002": {"description": "Partial profile image match found", "identity": "FACE_PARTIAL_MATCH", "reasoncode": "I437", "positive": true, "weight": 0.5}, "800003": {"description": "No profile image match found", "identity": "FACE_NO_MATCH", "reasoncode": "I438", "positive": false, "weight": 0}, "800004": {"description": "Face match score", "identity": "FACE_MATCH_SCORE", "weight": 0}}, "INANL": {"100001": {"identity": "IN_NO_NAME", "weight": 0, "description": "IN_NO_NAME"}, "100002": {"identity": "IN_NO_COUNTRY", "weight": 0, "description": "IN_NO_COUNTRY"}, "100003": {"identity": "IN_NO_LOCATION", "weight": 0, "description": "IN_NO_LOCATION"}, "100004": {"identity": "IN_NO_HEADLINE", "weight": 0, "description": "IN_NO_HEADLINE"}, "100005": {"identity": "IN_NO_SUMMARY", "weight": 0, "description": "IN_NO_SUMMARY"}, "100006": {"identity": "IN_NO_SKILLS", "weight": 0, "description": "IN_NO_SKILLS"}, "100007": {"identity": "IN_NO_EDUCATIONS", "weight": 0, "description": "IN_NO_EDUCATIONS"}, "100008": {"identity": "IN_NO_EXPERIENCES", "weight": 0, "description": "IN_NO_EXPERIENCES"}, "100009": {"identity": "IN_NUMBER_OF_DAYS_SINCE_LAST_VISIT_MORE_180", "weight": 0, "reasoncode": "I183", "description": "IN_NUMBER_OF_DAYS_SINCE_LAST_VISIT_MORE_180"}, "100010": {"identity": "IN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE_MORE_180", "weight": 0, "description": "IN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE_MORE_180"}, "100011": {"identity": "IN_COUNTRY_DIFF_INPUT_COUNTRY", "weight": 0, "reasoncode": "I185", "description": "IN_COUNTRY_DIFF_INPUT_COUNTRY"}, "100012": {"identity": "IN_LOCATION_DIFF_INPUT_CITY", "weight": 0, "description": "IN_LOCATION_DIFF_INPUT_CITY"}, "100130": {"identity": "IN_INTERNAL_ERROR", "weight": 0, "description": "Internal Error"}, "200000": {"identity": "IN_MATCH", "weight": 0, "description": "IN_MATCH"}, "200001": {"identity": "IN_HAS_NAME", "weight": 0, "reasoncode": "I180", "description": "IN_HAS_NAME"}, "200002": {"identity": "IN_HAS_COUNTRY", "weight": 0, "description": "IN_HAS_COUNTRY"}, "200003": {"identity": "IN_HAS_LOCATION", "weight": 0, "description": "IN_HAS_LOCATION"}, "200004": {"identity": "IN_HAS_HEADLINE", "weight": 0, "description": "IN_HAS_HEADLINE"}, "200005": {"identity": "IN_HAS_SUMMARY", "weight": 0, "description": "IN_HAS_SUMMARY"}, "200006": {"identity": "IN_NUMBER_OF_EDUCATIONS_MORE_1", "weight": 0, "description": "IN_NUMBER_OF_EDUCATIONS_MORE_1"}, "200007": {"identity": "IN_NUMBER_OF_EXPERIENCES_MORE_1", "weight": 0, "description": "IN_NUMBER_OF_EXPERIENCES_MORE_1"}, "200011": {"identity": "IN_NUMBER_OF_DAYS_SINCE_LAST_VISIT_LESS_180", "weight": 0, "description": "IN_NUMBER_OF_DAYS_SINCE_LAST_VISIT_LESS_180"}, "200012": {"identity": "IN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE_LESS_180", "weight": 0, "reasoncode": "I184", "description": "IN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE_LESS_180"}, "400001": {"identity": "IN_NUMBER_OF_SKILLS", "weight": 0, "description": "IN_NUMBER_OF_SKILLS"}, "400002": {"identity": "IN_NUMBER_OF_EDUCATIONS", "weight": 0, "reasoncode": "I181", "description": "IN_NUMBER_OF_EDUCATIONS"}, "400003": {"identity": "IN_NUMBER_OF_EXPERIENCES", "weight": 0, "reasoncode": "I182", "description": "IN_NUMBER_OF_EXPERIENCES"}, "400004": {"identity": "IN_NUMBER_OF_DAYS_SINCE_LAST_VISIT", "weight": 0, "description": "IN_NUMBER_OF_DAYS_SINCE_LAST_VISIT"}, "400005": {"identity": "IN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE", "weight": 0, "description": "IN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE"}}, "LIANL": {"100001": {"description": "No education listed", "identity": "NO_EDU_LIST", "weight": 0}, "100002": {"description": "Has no legit companies connected to them", "identity": "NO_LEGIT_COMPANY", "weight": 0}, "100003": {"description": "Works for a company , but has no education listed", "identity": "WORKS_COMPANY_NOT_EDU_LIST", "weight": 0}, "100004": {"description": "No birthday listed", "identity": "LI_NO_BDAY_LIST", "weight": 0}, "100005": {"description": "Less than 75 connections", "identity": "LESS_75_CONNECTION", "weight": 0}, "100006": {"description": "Is an open connector", "identity": "IS_OPEN_CONNECTOR", "weight": 0}, "100008": {"description": "worksameas less than or equal 4", "identity": "WORK_LESS_4", "weight": 0}, "100009": {"description": "locationsameas less than or equal 4", "identity": "LOCATIONSAME_LESS_4", "weight": 0}, "100504": {"description": "0.5 + weighted sum of linkedin activity codes  bounded by [0,1]", "identity": "LI_ACTIVITY_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.33, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100505": {"description": "0.5 + weighted sum of linkedin connections codes  bounded by [0,1]", "identity": "LI_CONNECTION_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.33, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100506": {"description": "0.5 + weighted sum of linkedin maintenance codes  bounded by [0,1]", "identity": "LI_MAINTENANCE_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.33, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100507": {"description": "w1*LI_ACTIVITY_SCORE + w2*LI_CONNECTION_SCORE + w3*LI_MAINTENANCE_SCORE", "identity": "LI_LINKEDIN_OSB_SCORE", "authscore_type": "osb_score", "weight": -999}, "200002": {"description": "Has at least one legit company", "identity": "ONE_LEGIT_COMPANY", "weight": 0}, "200005": {"description": "More than 400 connections", "identity": "MORE_400_CONNECTION", "weight": 0}, "200006": {"description": "Is not an open connector", "identity": "NOT_OPEN_CONNECTOR", "weight": 0}, "200007": {"description": "Has at least one recommendation", "identity": "HAS_ONE_RECOMMED", "weight": 0}, "200008": {"description": "worksameas greater than 50", "identity": "WORK_GREATER_50", "weight": 0}, "200009": {"description": "locationsameas greater than 50", "identity": "LOCATIONSAME_GREATER_50", "weight": 0}, "200010": {"description": "Has at least one group", "identity": "HAS_ONE_GROUP", "weight": 0}, "300002": {"description": "Number of Connections", "identity": "LI_NO_CONNECTION", "weight": 0}, "300003": {"description": "workSameAs", "identity": "LI_WORK_SAME", "weight": 0}, "300004": {"description": "livesSameAs", "identity": "LI_LIVES_SAME", "weight": 0}, "300006": {"description": "Number of Recommendations", "identity": "LI_NO_RECOMMEND", "weight": 0}, "300007": {"description": "Number of Groups", "identity": "LI_NO_GROUP", "weight": 0}, "400003": {"description": "LI_CONNECTION_SCORE < 0.3", "identity": "LI_CONNECTIONS_UNNATURAL", "reasoncode": "I152", "weight": 0}, "400004": {"description": "LI_CONNECTION_SCORE > 0.7", "identity": "LI_CONNECTIONS_GOOD", "reasoncode": "I252", "weight": 0}, "400005": {"description": "LI_ACTIVITY_SCORE < 0.3", "identity": "LI_ACTIVITY_UNNATURAL", "reasoncode": "I153", "weight": 0}, "400006": {"description": "LI_ACTIVITY_SCORE > 0.7", "identity": "LI_ACTIVITY_GOOD", "reasoncode": "I253", "weight": 0}, "400007": {"description": "LI_MAINTENANCE_SCORE < 0.3", "identity": "LI_ACCOUNT_UNMAINTAINED", "reasoncode": "I154", "weight": 0}, "400008": {"description": "LI_MAINTENANCE_SCORE > 0.7", "identity": "LI_ACCOUNT_WELL_MAINTAINED", "reasoncode": "I254", "weight": 0}, "987647": {"description": "number of viewed profiles", "identity": "LI_NUMBER_OF_VIEWED_PROFILES", "weight": 0}, "987648": {"description": "number of connections", "identity": "LI_NUMBER_OF_CONNECTIONS", "weight": 0}, "987649": {"description": "number of educations", "identity": "LI_NUMBER_OF_EDUCATIONS", "weight": 0}, "987650": {"description": "number of skills", "identity": "LI_NUMBER_OF_SKILLS", "weight": 0}, "987651": {"description": "number of courses", "identity": "LI_NUMBER_OF_COURSES", "weight": 0}, "987652": {"description": "duration of most recent experience", "identity": "LI_DURATION_OF_MOST_RECENT_EXPERIENCE", "weight": 0}, "987653": {"description": "average experience duration", "identity": "LI_AVERAGE_EXPERIENCE_DURATION", "weight": 0}, "987654": {"description": "has valid given/first name", "identity": "LI_HAS_FIRST_NAME", "weight": 0}, "987655": {"description": "has valid family name", "identity": "LI_HAS_FAMILY_NAME", "weight": 0}, "987656": {"description": "has valid full name", "identity": "LI_HAS_FULL_NAME", "weight": 0}, "987657": {"description": "currently employed", "identity": "LI_CURRENTLY_EMPLOYED", "authscore_type": "maintenance", "reasoncode": "I157", "weight": 0.1}, "987693": {"description": "number of days since last update", "identity": "LI_NUMBER_OF_DAYS_SINCE_LAST_UPDATE", "weight": 0}, "987660": {"description": "number of days since last update - adjusted", "identity": "LI_NUMBER_OF_DAYS_SINCE_LAST_UPDATE", "weight": 0}, "987661": {"description": "Boolean as to whether headline is empty", "identity": "LI_HEADLINE_IS_EMPTY", "authscore_type": "maintenance", "reasoncode": "I155", "weight": -0.1}, "987662": {"description": "has profile image", "identity": "LI_HAS_PROFILE_IMAGE", "authscore_type": "maintenance", "weight": 0.1}, "987663": {"description": "has industry", "identity": "LI_HAS_INDUSTRY", "authscore_type": "maintenance", "reasoncode": "I159", "weight": 0.1}, "987664": {"description": "number of certifications", "identity": "LI_NUMBER_OF_CERTIFICATIONS", "weight": 0}, "987665": {"description": "number of experiences", "identity": "LI_NUMBER_OF_EXPERIENCES", "weight": 0}, "987666": {"description": "no full name", "identity": "LI_NO_FULL_NAME", "weight": 0}, "987667": {"description": "Not currently employed", "identity": "LI_NOT_CURRENTLY_EMPLOYED", "authscore_type": "maintenance", "weight": -0.1}, "987668": {"description": "Headline is not empty", "identity": "LI_HAS_HEADLINE", "authscore_type": "maintenance", "weight": 0.1}, "987669": {"description": "Has no profile image", "identity": "LI_NO_PROFILE_IMAGE", "authscore_type": "maintenance", "weight": -0.1}, "987670": {"description": "Has no industry", "identity": "LI_NO_INDUSTRY", "authscore_type": "maintenance", "weight": -0.1}, "987671": {"description": "Number of certifications = 0", "identity": "LI_NO_CERTIFICATIONS", "authscore_type": "maintenance", "weight": -0.1}, "987672": {"description": "Number of job/experiences = 0", "identity": "LI_NO_EXPERIENCES", "authscore_type": "maintenance", "weight": -0.1}, "987673": {"description": "Number of courses = 0", "identity": "LI_NO_COURSES", "authscore_type": "maintenance", "weight": -0.1}, "987674": {"description": "Number of skills = 0", "identity": "LI_NO_SKILLS", "authscore_type": "maintenance", "weight": -0.1}, "987675": {"description": "Number of educations = 0", "identity": "LI_NO_EDUCATIONS", "authscore_type": "maintenance", "weight": -0.1}, "987676": {"description": "Number of connections = 0", "identity": "LI_NO_CONNECTIONS", "weight": 0}, "987677": {"description": "Number of viewed profiles = 0", "identity": "LI_NO_VIEWED_PROFILES", "weight": 0}, "987678": {"description": "Number of skills > 10", "identity": "LI_NUMBER_OF_SKILLS_GREATER", "authscore_type": "maintenance", "reasoncode": "I156", "weight": 0.1}, "987680": {"description": "Days since last update > 180 (adjusted)", "identity": "LI_NUMBER_OF_DAYS_LAST_UPDATE_GREATER", "authscore_type": "activity", "reasoncode": "I255", "weight": -0.5}, "987694": {"description": "Days since last update > 180", "identity": "LI_NUMBER_OF_DAYS_LAST_UPDATE_GREATER"}, "987681": {"description": "Number of viewed profiles > 5", "identity": "LI_NUMBER_OF_VIEWED_PROFILES_GREATER", "authscore_type": "activity", "weight": 0.5}, "987682": {"description": "Number of viewed profiles <= 5", "identity": "LI_NUMBER_OF_VIEWED_PROFILES_LESSER", "authscore_type": "activity", "weight": -0.5}, "987683": {"description": "Number of connections > 5", "identity": "LI_NUMBER_OF_CONNECTIONS_GREATER", "authscore_type": "connections", "reasoncode": "I257", "weight": 1}, "987684": {"description": "Number of educations >=1", "identity": "LI_NUMBER_OF_EDUCATIONS_GREATER", "authscore_type": "maintenance", "reasoncode": "I256", "weight": 0.1}, "987685": {"description": "Number of certifications >= 3", "identity": "LI_NUMBER_OF_CERTIFICATIONS_GREATER", "authscore_type": "maintenance", "weight": 0.1}, "987686": {"description": "Number of job/experiences >= 5", "identity": "LI_NUMBER_OF_EXPERIENCES_GREATER", "authscore_type": "maintenance", "weight": 0.1}, "987687": {"description": "Number of courses >= 2", "identity": "LI_NUMBER_OF_COURSES_GREATER", "authscore_type": "maintenance", "weight": 0.1}, "987688": {"description": "Duration of most recent experience > 30 days", "identity": "LI_DURATION_RECENT_EXPIRIENCE_GREATER", "authscore_type": "maintenance", "reasoncode": "I158", "weight": 0.1}, "987689": {"description": "Duration of most recent experience <= 30 days", "identity": "LI_DURATION_RECENT_EXPIRIENCE_LESSER", "authscore_type": "maintenance", "weight": -0.1}, "987691": {"description": "Days since last update <= 180 (adjusted)", "identity": "LI_NUMBER_OF_DAYS_LAST_UPDATE_LESSER", "authscore_type": "activity", "weight": 0.5}, "987695": {"description": "Days since last update <= 180", "identity": "LI_NUMBER_OF_DAYS_LAST_UPDATE_LESSER"}, "987692": {"description": "Number of connections <= 5", "identity": "LI_NUMBER_OF_CONNECTIONS_LESSER", "authscore_type": "connections", "weight": -1}}, "LIVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200126": {"description": "LinkedIn ID found online (received from search engines)", "identity": "LI_ACCOUNT_MATCH", "weight": 0}, "200131": {"description": "LinkedIn URL is in profilesFound section", "identity": "LI_URL_FOUND", "reasoncode": "I250", "weight": 0}, "200127": {"description": "Match LinkedIn ID/URL in LinkedIn API, i.e. profile received", "identity": "PERFECT_MATCH_COUNT", "weight": 0}, "200130": {"description": "Match LinkedIn ID/URL in KissingHub", "identity": "LI_KH_MATCH", "weight": 0}}, "LNVAL": {"100001": {"description": "LN_SSN_SUSPICIOUS", "identity": "valLN_SSN_SUSPICIOUS", "weight": 0}, "100002": {"description": "LN_SSN_TRUE_NAME_FRAUD", "identity": "LN_SSN_TRUE_NAME_FRAUD", "weight": 0}, "100003": {"description": "LN_SSN_DECEASED_OR_DEATHBENEFITS", "identity": "LN_SSN_DECEASED_OR_DEATHBENEFITS", "reasoncode": "R907", "weight": 0}, "100004": {"description": "LexisNexis was score was less than 30", "identity": "LN_VERIFICATION_SCORE", "reasoncode": "R908", "weight": 0}, "100005": {"description": "R_TEL_SUSPICIOUS", "identity": "LN_TEL_SUSPICIOUS", "weight": 0}, "100006": {"description": "LN_TEL_TRUE_NAME_FRAUD", "identity": "LN_TEL_TRUE_NAME_FRAUD", "weight": 0}, "100007": {"description": "LN_ADD_SUSPICIOUS", "identity": "LN_ADD_SUSPICIOUS", "weight": 0}, "100008": {"description": "LN_ADD_TRUE_NAME_FRAUD", "identity": "LN_ADD_TRUE_NAME_FRAUD", "weight": 0}, "100009": {"description": "LN_PRS_ISDECEASED", "identity": "LN_PRS_ISDECEASED", "reasoncode": "R909", "weight": 0}, "100010": {"description": "LN_PRS_WATCHLIST", "identity": "LN_PRS_WATCHLIST", "weight": 0}, "100011": {"description": "LN_TEL_ISANSWERING_SERVICE", "identity": "LN_TEL_ISANSWERING_SERVICE", "weight": 0}, "100012": {"description": "LN_TEL_ISCELLULAR", "identity": "LN_TEL_ISCELLULAR", "reasoncode": "I901", "weight": 0}, "100013": {"description": "LN_TEL_ISPUBLIC", "identity": "LN_TEL_ISPUBLIC", "weight": 0}, "100014": {"description": "LN_TEL_ISCOMM_INST_GOV", "identity": "LN_TEL_ISCOMM_INST_GOV", "weight": 0}, "100015": {"description": "LN_ADD_ISRECFRDSERVICE", "identity": "LN_ADD_ISRECFRDSERVICE", "reasoncode": "I421", "weight": 0}, "100016": {"description": "LN_ADD_ISCOMM_INST_GOV", "identity": "LN_ADD_ISCOMM_INST_GOV", "reasoncode": "I423", "weight": 0}, "100017": {"description": "LN_ADD_HIGHUSAGE", "identity": "LN_ADD_HIGHUSAGE", "reasoncode": "I424", "weight": 0}, "100018": {"description": "Given state matches SSN state code", "identity": "LN_SSN_AREA_MATCH", "reasoncode": "I305", "weight": 0}, "100019": {"description": "SSN cannot be resolved to an entity", "identity": "LN_INVALID_SSN", "reasoncode": "R901", "weight": 0}, "100020": {"description": "GLBA record validation failed on Name", "identity": "LN_NAME_RESOLV_FAIL", "reasoncode": "R902", "weight": 0}, "100021": {"description": "GLBA record validation failed on Date of Birth", "identity": "LN_DOB_RESOLV_FAIL", "reasoncode": "R903", "weight": 0}, "100022": {"description": "GLBA record validation failed on Address", "identity": "LN_ADDRESS_RESOLV_FAIL", "reasoncode": "R904", "weight": 0}, "100023": {"description": "GLBA record validation failed on Mobile Number", "identity": "LN_MOBILE_NUMBER_RESOLV_FAIL", "reasoncode": "R905", "weight": 0}, "100024": {"description": "GLBA record validation failed on Driver's License", "identity": "LN_DRIVERS_LICENSE_RESOLV_FAIL", "reasoncode": "R906", "weight": 0}, "100025": {"description": "The input SSN was issued prior to the input date-of-birth", "identity": "LN_SSN_PRIOR_TO_DOB", "reasoncode": "R911", "weight": 0}, "100026": {"description": "The input last name and SSN are verified", "identity": "LN_SSN_VERIFIED_WO_ADDR_PHONE", "reasoncode": "R912", "weight": 0}, "100027": {"description": "SSN improperly formatted or cannot exist", "identity": "LN_SSN_INVALID", "reasoncode": "R913", "weight": 0}, "100028": {"description": "The input phone number may be disconnected", "identity": "LN_PHONE_DISCONNECTED", "reasoncode": "R914", "weight": 0}, "100029": {"description": "The input phone number is potentially invalid", "identity": "LN_PHONE_INVALID", "reasoncode": "R915", "weight": 0}, "100030": {"description": "The input phone number is a pager number", "identity": "LN_PHONE_IS_PAGER", "reasoncode": "R962", "weight": 0}, "100031": {"description": "The input address may be invalid according to postal specifications", "identity": "LN_ADDRESS_INVALID", "reasoncode": "R916", "weight": 0}, "100032": {"description": "The input zip code belongs to a post office box", "identity": "LN_ADDRESS_ZIP_IS_PO_BOX", "reasoncode": "R963", "weight": 0}, "100033": {"description": "The input address is a transient commercial or institutional address", "identity": "LN_ADDRESS_IS_TRANSIENT", "reasoncode": "R964", "weight": 0}, "100034": {"description": "The input phone number matches a transient commercial or institutional address", "identity": "LN_PHONE_IS_TRANSIENT", "reasoncode": "R965", "weight": 0}, "100035": {"description": "The input phone number and input zip code combination is invalid", "identity": "LN_PHONE_PLUS_ZIP_IS_INVALID", "reasoncode": "R917", "weight": 0}, "100036": {"description": "Unable to verify name", "identity": "LN_UNABLE_TO_VERIFY_NAS", "reasoncode": "R918", "weight": 0}, "100037": {"description": "Unable to verify address", "identity": "LN_UNABLE_TO_VERIFY_ADDRESS", "reasoncode": "R919", "weight": 0}, "100038": {"description": "SSN/TIN cannot be resolved to an entity", "identity": "LN_UNABLE_TO_VERIFY_SSN", "reasoncode": "R920", "weight": 0}, "100039": {"description": "Unable to verify phone number", "identity": "LN_UNABLE_TO_VERIFY_PHONE", "reasoncode": "R921", "weight": 0}, "100040": {"description": "Unable to verify date-of-birth", "identity": "LN_UNABLE_TO_VERIFY_DOB", "reasoncode": "R922", "weight": 0}, "100041": {"description": "The input SSN/TIN may have been miskeyed", "identity": "LN_SSN_MISKEYED", "reasoncode": "R923", "weight": 0}, "100042": {"description": "The input address may have been miskeyed", "identity": "LN_ADDRESS_MISKEYED", "reasoncode": "R924", "weight": 0}, "100043": {"description": "The input phone number may have been miskeyed", "identity": "LN_PHONE_MISKEYED", "reasoncode": "R925", "weight": 0}, "100044": {"description": "The input name matches the OFAC file", "identity": "LN_WATCHLIST_OFAC", "reasoncode": "R926", "weight": 0}, "100045": {"description": "Unable to verify name", "identity": "LN_UNABLE_TO_VERIFY_NAME", "reasoncode": "R927", "weight": 0}, "100046": {"description": "The input SSN is associated with multiple last names", "identity": "LN_SSN_ASSOC_WITH_MULTI_LNAME", "reasoncode": "R928", "weight": 0}, "100047": {"description": "The input SSN is recently issued", "identity": "LN_SSN_RECENTLY_ISSUED", "reasoncode": "R966", "weight": 0}, "100048": {"description": "The input driver's license number is invalid for the input DL State", "identity": "LN_DL_NUMBER_INVALID_FOR_STATE", "reasoncode": "R929", "weight": 0}, "100049": {"description": "The input phone area code is changing", "identity": "LN_PHONE_AREA_CODE_IS_CHANGING", "reasoncode": "R967", "weight": 0}, "100050": {"description": "The input work phone is a pager number", "identity": "LN_WORK_PHONE_IS_PAGER_NUMBER", "reasoncode": "R968", "weight": 0}, "100051": {"description": "Unable to verify first name", "identity": "LN_UNABLE_TO_VERIFY_FIRST_NAME", "reasoncode": "R930", "weight": 0}, "100052": {"description": "The input phone and address are geographically distant (>10 miles", "identity": "LN_PHONE_AND_ADDRESS_ARE_GEOGRAPHICALLY_DISTANT", "reasoncode": "R931", "weight": 0}, "100053": {"description": "The input address matches a prison address", "identity": "LN_ADDRESS_IS_PRISON", "reasoncode": "R932", "weight": 0}, "100054": {"description": "The input last name is not associated with the input SSN", "identity": "LN_LAST_NAME_SSN_DESCREPANT", "reasoncode": "R933", "weight": 0}, "100055": {"description": "The input first name is not associated with the input SSN", "identity": "LN_FIRST_NAME_SSN_DESCREPANT", "reasoncode": "R934", "weight": 0}, "100056": {"description": "The input home phone and work phone are geographically distant (>100 miles", "identity": "LN_HOME_AND_WORK_PHONE_ARE_DISTANT", "reasoncode": "R935", "weight": 0}, "100057": {"description": "The input work phone is potentially invalid", "identity": "LN_WORK_PHONE_INVALID", "reasoncode": "R936", "weight": 0}, "100058": {"description": "The input work phone is potentially disconnected", "identity": "LN_WORK_PHONE_DISCONNECTED", "reasoncode": "R937", "weight": 0}, "100059": {"description": "The input work phone is a mobile number", "identity": "LN_WORK_PHONE_IS_MOBILE", "reasoncode": "R969", "weight": 0}, "100060": {"description": "The input address returns a different phone number", "identity": "LN_ADDRESS_HAS_DIFFERENT_PHONE", "reasoncode": "R938", "weight": 0}, "100061": {"description": "The input SSN is associated with a different last name", "identity": "LN_SSN_HAS_DIFFERENT_NAME", "reasoncode": "R939", "weight": 0}, "100062": {"description": "The input SSN is not found in the public record", "identity": "LN_SSN_NOT_IN_PUBLIC_REC", "reasoncode": "R940", "weight": 0}, "100063": {"description": "The input SSN is associated with a different name and address", "identity": "LN_SSN_DIFFERENT_NAS", "reasoncode": "R941", "weight": 0}, "100064": {"description": "The input phone number is associated with a different name and address", "identity": "LN_PHONE_DIFFERENT_NAS", "reasoncode": "R942", "weight": 0}, "100065": {"description": "The input name and address are associated with an unlisted/non-published phone number", "identity": "LN_NAS_HAS_UNLISTED_PHONE", "reasoncode": "R943", "weight": 0}, "100066": {"description": "The input name may have been miskeyed", "identity": "LN_NAME_MISKEYED", "reasoncode": "R944", "weight": 0}, "100067": {"description": "The input name was missing", "identity": "LN_NAME_MISSING", "reasoncode": "I557", "weight": 0}, "100068": {"description": "The input address was missing", "identity": "LN_ADDRESS_MISSING", "reasoncode": "I903", "weight": 0}, "100069": {"description": "The input SSN/TIN was missing or incomplete", "identity": "LN_SSN_MISSING", "reasoncode": "I904", "weight": 0}, "100070": {"description": "The input phone was missing or incomplete", "identity": "LN_PHONE_MISSING", "reasoncode": "I905", "weight": 0}, "100071": {"description": "The input date-of-birth was missing or incomplete", "identity": "LN_DOB_MISSING", "reasoncode": "I906", "weight": 0}, "100072": {"description": "The input name and address return a different phone number", "identity": "LN_NAME_DIFFERENT_ADDRESS_PHONE", "reasoncode": "R945", "weight": 0}, "100073": {"description": "The input date-of-birth may have been miskeyed", "identity": "LN_DOB_MISKEYED", "reasoncode": "R946", "weight": 0}, "100074": {"description": "The input SSN was issued to a non-US citizen", "identity": "LN_SSN_NON_US_CITIZEN", "reasoncode": "I907", "weight": 0}, "100075": {"description": "The input SSN was issued within the last three years", "identity": "LN_SSN_RECENT", "reasoncode": "R970", "weight": 0}, "100076": {"description": "The input SSN was issued after age five (post-1990", "identity": "LN_SSN_ISSUED_AFTER_FIVE", "reasoncode": "R971", "weight": 0}, "100077": {"description": "The primary input address is a Commercial Mail Receiving Agency", "identity": "LN_ADDRESS_IS_COMMERCIAL", "reasoncode": "R972", "weight": 0}, "100078": {"description": "The input SSN is not the primary SSN for the input identity", "identity": "LN_SSN_IS_NOT_PRIMARY", "reasoncode": "R947", "weight": 0}, "100079": {"description": "The input zip code is a corporate only zip code", "identity": "LN_ZIP_IS_CORPORATE", "reasoncode": "R973", "weight": 0}, "100080": {"description": "Address mismatch between city/state and zip code", "identity": "LN_ADDRESS_MISMATCH_CITY_STATE_ZIP", "reasoncode": "R948", "weight": 0}, "100081": {"description": "A different driver's license number has been found for the input applicant", "identity": "LN_DL_DIFFERENT", "reasoncode": "R949", "weight": 0}, "100082": {"description": "The input driver’s license number is valid and is not on record", "identity": "LN_DL_NOT_IN_RECORD", "reasoncode": "R950", "weight": 0}, "100083": {"description": "The input driver's license number may have been miskeyed", "identity": "LN_DL_MISKEYED", "reasoncode": "R951", "weight": 0}, "100084": {"description": "Unable to verify driver's license number", "identity": "LN_DL_NOT_FOUND", "reasoncode": "R952", "weight": 0}, "100085": {"description": "Input SSN possibly randomly issued by SSA", "identity": "LN_SSN_RANDOM", "reasoncode": "R953", "weight": 0}, "100086": {"description": "The input SSN is an ITIN", "identity": "LN_SSN_IS_ITIN", "reasoncode": "R954", "weight": 0}, "100087": {"description": "Multiple identities associated with the input SSN", "identity": "LN_SSN_WITH_MULTI_IDENTITY", "reasoncode": "R955", "weight": 0}, "100088": {"description": "The input zip code is a military only zip code", "identity": "LN_ZIP_IS_MILITARY", "reasoncode": "I908", "weight": 0}, "100089": {"description": "Multiple SSNs reported with applicant", "identity": "LN_APPLICANT_MULTI_SSN", "reasoncode": "R956", "weight": 0}, "100090": {"description": "No date-of-birth reported for the input identity", "identity": "LN_NO_DOB", "reasoncode": "R957", "weight": 0}, "100091": {"description": "The input first name and last name may have been flipped", "identity": "LN_FIRST_AND_LAST_NAME_FLIPPED", "reasoncode": "I909", "weight": 0}, "100092": {"description": "Potential address discrepancy - the Input address may be previous address", "identity": "LN_ADDRESS_IS_PREVIOUS", "reasoncode": "I910", "weight": 0}, "100093": {"description": "The primary input address is a PO Box", "identity": "LN_ADDRESS_IS_PO_BOX", "reasoncode": "I911", "weight": 0}, "100094": {"description": "The input SSN was possibly randomly issued by the SSA", "identity": "LN_SSN_IS_RANDOM", "reasoncode": "I912", "weight": 0}, "100095": {"description": "The input address State is different than the LN best address State for the input identity", "identity": "LN_ADDRESS_STATE_INCONSISTENT", "reasoncode": "R958", "weight": 0}, "100096": {"description": "Address mismatch on secondary address range", "identity": "LN_ADDRESS_ON_SECONDARY_RANGE", "weight": 0}, "100097": {"description": "The input address is a vacant address", "identity": "LN_ADDRESS_IS_VACANT", "reasoncode": "I913", "weight": 0}, "100098": {"description": "The input name matches one or more of the non-OFAC global watchlist(s", "identity": "LN_WATCHLIST_NON_OFAC", "reasoncode": "R960", "weight": 0}, "100099": {"description": "Unable to verify zip code", "identity": "LN_UNABLE_TO_VERIFY_ZIP", "reasoncode": "R961", "weight": 0}, "100100": {"description": "", "identity": "LN_NAP_NOTHING_FOUND", "weight": 0}, "100101": {"description": "", "identity": "LN_NAP_PHONE_ASSOC_WITH_DIFF_NAME_ADDR", "weight": 0}, "100102": {"description": "", "identity": "LN_NAP_FIRST_LAST_NAME_MATCHED", "weight": 0}, "100103": {"description": "", "identity": "LN_NAP_FIRST_NAME_ADDR_MATCHED", "weight": 0}, "100104": {"description": "", "identity": "LN_NAP_FIRST_NAME_PHONE_MATCHED", "weight": 0}, "100105": {"description": "", "identity": "LN_NAP_LAST_NAME_ADDR_MATCHED", "weight": 0}, "100106": {"description": "", "identity": "LN_NAP_ADDR_PHONE_MATCHED", "weight": 0}, "100107": {"description": "", "identity": "LN_NAP_LAST_NAME_PHONE_MATCHED", "weight": 0}, "100108": {"description": "", "identity": "LN_NAP_FIRST_LAST_NAME_ADDR_MATCHED", "reasoncode": "I914", "weight": 0}, "100109": {"description": "", "identity": "LN_NAP_FIRST_LAST_NAME_PHONE_MATCHED", "reasoncode": "I915", "weight": 0}, "100110": {"description": "", "identity": "LN_NAP_FIRST_NAME_ADDR_PHONE_MATCHED", "weight": 0}, "100111": {"description": "", "identity": "LN_NAP_LAST_NAME_ADDR_PHONE_MATCHED", "weight": 0}, "100112": {"description": "", "identity": "LN_NAP_FIRST_LAST_NAME_ADDR_PHONE_MATCHED", "reasoncode": "I916", "weight": 0}, "100113": {"description": "", "identity": "LN_NAS_NOTHING_FOUND", "weight": 0}, "100114": {"description": "", "identity": "LN_NAS_SSN_ASSOC_WITH_DIFF_NAME_ADDR", "weight": 0}, "100115": {"description": "", "identity": "LN_NAS_FIRST_LAST_NAME_MATCHED", "weight": 0}, "100116": {"description": "", "identity": "LN_NAS_FIRST_NAME_ADDR_MATCHED", "weight": 0}, "100117": {"description": "", "identity": "LN_NAS_FIRST_NAME_SSN_MATCHED", "weight": 0}, "100118": {"description": "", "identity": "LN_NAS_LAST_NAME_ADDR_MATCHED", "weight": 0}, "100119": {"description": "", "identity": "LN_NAS_ADDR_SSN_MATCHED", "weight": 0}, "100120": {"description": "", "identity": "LN_NAS_LAST_NAME_SSN_MATCHED", "weight": 0}, "100121": {"description": "", "identity": "LN_NAS_FIRST_LAST_NAME_ADDR_MATCHED", "reasoncode": "I917", "weight": 0}, "100122": {"description": "", "identity": "LN_NAS_FIRST_LAST_NAME_SSN_MATCHED", "reasoncode": "I918", "weight": 0}, "100123": {"description": "", "identity": "LN_NAS_FIRST_NAME_ADDR_SSN_MATCHED", "weight": 0}, "100124": {"description": "", "identity": "LN_NAS_LAST_NAME_ADDR_SSN_MATCHED", "weight": 0}, "100125": {"description": "", "identity": "LN_NAS_FIRST_LAST_NAME_ADDR_SSN_MATCHED", "reasoncode": "I919", "weight": 0}, "100126": {"description": "", "identity": "LN_NAME_ADDRESS_PHONE_CONNECTED", "weight": 0}, "100130": {"description": "Internal Error", "identity": "LN_INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "LN_TIMEOUT", "weight": 0}, "100132": {"description": "Emerging Identity Indicator", "identity": "LN_EMERGING_IDENTITY_INDICATOR", "weight": 0, "reasoncode": "I920"}}, "META": {"ACCOUNTID": {"weight": 0}, "INDUSTRYID": {"weight": 0}}, "NSVAL": {"100015": {"description": "", "identity": "NS_ADDRESS_USPS_TYPE_POBOX", "positive": false, "weight": -0.02}, "100017": {"description": "", "identity": "NS_ADDRESS_RBDI_RESIDENTIAL", "positive": true, "weight": 0.02}, "100020": {"description": "", "identity": "NS_ADDRESS_VACANT", "positive": false, "weight": -0.02}, "100023": {"description": "", "identity": "NS_ADDRESS_CMRA", "positive": false, "weight": -0.02}, "100024": {"description": "", "identity": "NS_ADDRESS_NOT_CMRA", "positive": true, "weight": 0.02}, "100025": {"description": "", "identity": "NS_PHONE_NOT_IN_SERVICE_AS_FAR_AS_WE_KNOW", "positive": true, "weight": 0.0}, "100027": {"description": "", "identity": "NS_ADDRESS_PRISON", "positive": false, "weight": -0.02}, "100028": {"description": "", "identity": "NS_ADDRESS_VERIFIED", "positive": true, "weight": 0.02}, "100029": {"description": "", "identity": "NS_ADDRESS_NOT_VERIFIED", "positive": false, "weight": -0.02}, "100038": {"description": "", "identity": "NS_1ST_PHONE_NOT_DIALABLE", "positive": false, "weight": -0.02}, "100052": {"description": "", "identity": "NS_1ST_PHONE_DA_CONNECTED", "positive": false, "weight": -0.02}, "100053": {"description": "", "identity": "NS_1ST_PHONE_NOT_DA_CONNECTED", "positive": true, "weight": 0.02}, "100058": {"description": "", "identity": "NS_1ST_PHONE_NOT_ACTIVE", "positive": false, "weight": -0.02}, "100066": {"description": "", "identity": "NS_1ST_EMAIL_VALID", "positive": true, "weight": 0.02}, "100067": {"description": "", "identity": "NS_1ST_EMAIL_INVALID", "positive": false, "weight": -0.02}, "100068": {"description": "", "identity": "NS_1ST_EMAIL_NO_FAILURE", "positive": true, "weight": 0.02}, "100069": {"description": "", "identity": "NS_1ST_EMAIL_FAILURE", "positive": false, "weight": -0.02}, "100071": {"description": "", "identity": "NS_1ST_EMAIL_IN_REPOSITORY", "positive": true, "weight": 0.02}, "100072": {"description": "", "identity": "NS_1ST_EMAIL_NOT_IN_REPOSITORY", "positive": false, "weight": -0.02}, "100073": {"description": "", "identity": "NS_PHONE_PREPAID", "positive": false, "weight": -0.02}, "100120": {"description": "", "identity": "NS_PHONE_NOT_IN_SERVICE", "positive": false, "weight": -0.02}, "100121": {"description": "", "identity": "NS_PHONE_IN_SERVICE_LESS_90_DAYS", "positive": false, "weight": -0.02}, "100122": {"description": "", "identity": "NS_PHONE_IN_SERVICE_MORE_90_DAYS", "positive": true, "weight": 0.02}, "100123": {"description": "", "identity": "NS_PHONE_DISCONTINUED", "positive": false, "weight": -0.02}, "100124": {"description": "", "identity": "NS_PHONE_DISCONNECTED_DELISTED_DISABLED_NOT_ACTIVE_NOT_IN_SERVICE", "positive": false, "weight": -0.02}, "100129": {"description": "", "identity": "NS_ADDRESS_INVALID_INACTIVE_NOT_RECEIVING_MAIL", "positive": false, "weight": -0.02}, "100130": {"description": "Internal Error", "identity": "NS_INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "NS_TIMEOUT", "weight": 0}, "100132": {"description": "", "identity": "NS_PHONE_POSTAL_CODE_DIFF_INPUT_ADDRESS", "positive": false, "weight": -0.02}, "100133": {"description": "", "identity": "NS_PHONE_TIME_ZONE_DIFF_INPUT_ADDRESS_TIME_ZONE", "positive": false, "weight": -0.02}, "300001": {"description": "", "identity": "NS_ADDRESS_SCORE", "weight": 0}, "300002": {"description": "", "identity": "NS_ADDRESS_VALIDATION", "weight": 0}, "300003": {"description": "", "identity": "NS_ADDRESS_DPV_CONFIRM", "weight": 0, "type": "string", "length": 1}, "300004": {"description": "", "identity": "NS_ADDRESS_USPS_TYPE", "weight": 0, "type": "string", "length": 1}, "300005": {"description": "", "identity": "NS_ADDRESS_RBDI", "weight": 0, "type": "string", "length": 1}, "300006": {"description": "", "identity": "NS_ADDRESS_VACANCY", "weight": 0, "type": "string", "length": 1}, "300007": {"description": "", "identity": "NS_ADDRESS_CMRA2", "weight": 0, "type": "string", "length": 1}, "300008": {"description": "", "identity": "NS_ADDRESS_PRISON2", "weight": 0, "type": "string", "length": 1}, "300009": {"description": "", "identity": "NS_1ST_PHONE_SCORE", "weight": 0}, "300010": {"description": "", "identity": "NS_1ST_PHONE_VALIDATION", "weight": 0}, "300011": {"description": "", "identity": "NS_1ST_PHONE_MOBILE", "weight": 0, "type": "string", "length": 1}, "300012": {"description": "", "identity": "NS_1ST_PHONE_TIME_ZONE", "weight": 0}, "300013": {"description": "", "identity": "NS_1ST_PHONE_DAYLIGHT_SAVINGS_TIME_OBSERVED", "weight": 0, "type": "string", "length": 1}, "300014": {"description": "", "identity": "NS_1ST_PHONE_POSTAL_STATE_CODE", "weight": 0, "type": "string", "length": 2}, "300015": {"description": "", "identity": "NS_1ST_PHONE_CONNECTED_IN_DA", "weight": 0, "type": "string", "length": 1}, "300016": {"description": "", "identity": "NS_1ST_PHONE_BPI", "weight": 0, "type": "string", "length": 1}, "300017": {"description": "", "identity": "NS_1ST_PHONE_ACTIVE", "weight": 0}, "300018": {"description": "", "identity": "NS_1ST_EMAIL_SCORE", "weight": 0}, "300019": {"description": "", "identity": "NS_1ST_EMAIL_VALIDATION", "weight": 0}, "300020": {"description": "", "identity": "NS_1ST_EMAIL_REASON", "weight": 0}, "300021": {"description": "", "identity": "NS_1ST_EMAIL_REPOSITORY", "weight": 0}, "300022": {"description": "", "identity": "NS_PREPAID_PHONE_ATTRIBUTE", "weight": 0, "type": "string", "length": 1}, "300023": {"description": "", "identity": "NS_BUSINESS_PHONE_INDICATOR", "weight": 0, "type": "string", "length": 1}, "300039": {"description": "", "identity": "NS_PHONE_IN_SERVICE_INDICATOR", "weight": 0, "type": "string", "length": 2}, "300024": {"description": "Adjusted 300039", "identity": "NS_PHONE_IN_SERVICE_INDICATOR", "weight": 0, "type": "string", "length": 2}, "300040": {"description": "Adjusted 300039 - numeric", "identity": "NS_PHONE_IN-SERVICE_INDICATOR_NUM", "weight": 0}, "300025": {"description": "", "identity": "NS_PHONE_TYPE_INDICATOR", "weight": 0, "type": "string", "length": 1}, "300026": {"description": "", "identity": "NS_SERVICE_DISCONTINUED_INDICATOR", "weight": 0}, "300027": {"description": "", "identity": "NS_VOIP_INDICATOR", "weight": 0}, "300028": {"description": "", "identity": "NS_MVNO_INDICATOR", "weight": 0, "type": "string", "length": 1}, "300029": {"description": "", "identity": "NS_EID_1320_RESULTCODE", "weight": 0}, "300030": {"description": "", "identity": "NS_EID_1800_RESULTCODE", "weight": 0}, "300031": {"description": "", "identity": "NS_EID_3227_RESULTCODE", "weight": 0}, "300032": {"description": "", "identity": "NS_HAS_FIRST_NAME", "weight": 0}, "300033": {"description": "", "identity": "NS_HAS_LAST_NAME", "weight": 0}, "300034": {"description": "", "identity": "NS_NAME_TYPE", "weight": 0, "type": "string", "length": 1}, "300035": {"description": "", "identity": "NS_ADDRESS_IS_VERIFIED", "weight": 0}, "300036": {"description": "", "identity": "NS_IP_IS_VALID", "weight": 0}, "300037": {"description": "", "identity": "NS_IP_COUNTRY_MATCH", "weight": 0}, "300038": {"description": "", "identity": "NS_IP_STATE_MATCH", "weight": 0}}, "PBVAL": {"100601": {"description": "Address resolved by DSTK", "identity": "DSTK_ADDRESS_RESOLVED", "weight": 0}, "100602": {"description": "Address not resolved by DSTK", "identity": "DSTK_ADDRESS_NOT_RESOLVED", "weight": 0}, "100603": {"description": "DSTK Timeout", "identity": "DSTK_TIMEOUT", "weight": 0}, "100604": {"description": "DSTK Error", "identity": "DSTK_ERROR", "weight": 0}, "100611": {"description": "Address resolved by MapQuest", "identity": "MQ_ADDRESS_RESOLVED", "weight": 0}, "100612": {"description": "Address not resolved by MapQuest", "identity": "MQ_ADDRESS_NOT_RESOLVED", "weight": 0}, "100613": {"description": "MapQuest Timeout", "identity": "MQ_TIMEOUT", "weight": 0}, "100614": {"description": "MapQuest Error", "identity": "MQ_ERROR", "weight": 0}, "100621": {"description": "Address resolved by Google_Geocode", "identity": "GG_ADDRESS_RESOLVED", "weight": 0}, "100622": {"description": "Address not resolved by Google_Geocode", "identity": "GG_ADDRESS_NOT_RESOLVED", "weight": 0}, "100623": {"description": "Google_Geocode Timeout", "identity": "GG_TIMEOUT", "weight": 0}, "100624": {"description": "Google_Geocode Error", "identity": "GG_ERROR", "weight": 0}, "100116": {"confidence": 0, "description": "PIPL returns empty response (JSON has only query params, no data)", "identity": "PB_PIPL_NO_MATCH", "positive": false, "weight": 0}, "100117": {"confidence": 0, "description": "PIPL returns partial matches and ER is not able to aggregate them into one profile", "identity": "PB_NO_MATCH", "positive": false, "weight": -1}, "100118": {"confidence": 0, "description": "PIPL returns perfect match", "identity": "PB_PERFECT_MATCH", "positive": true, "weight": 1}, "100119": {"confidence": 0, "description": "PIPL returns partial matches", "identity": "PB_ONLY_PARTIAL_MATCH", "positive": true, "weight": 0}, "100120": {"confidence": 0, "description": "PIPL returns partial matches and ER is able to aggregate them into one profile", "identity": "PB_ONLY_PARTIAL_MATCH", "positive": true, "weight": 0.3}, "100130": {"description": "Internal Error", "identity": "PB_INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "PB_TIMEOUT", "weight": 0}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PB_PERFECT_MATCH_COUNT", "minScore": 0, "positive": true, "weight": 0}, "200128": {"confidence": 0, "description": "Number of non-social sources", "identity": "PB_NO_NON_SOCIAL_SOURCE", "minScore": -0.51, "positive": true, "reasoncode": "I127", "weight": 0.66}, "200201": {"confidence": 0, "description": "Number of names", "identity": "PB_NUMBER_OF_NAMES", "positive": true, "maxScore": 0.1, "weight": 1}, "200203": {"confidence": 0, "description": "Number of usernames", "identity": "PB_NUMBER_OF_USERNAMES", "positive": true, "maxScore": 0.1, "weight": 1}, "200204": {"confidence": 0, "description": "Number of phones", "identity": "PB_NUMBER_OF_PHONES", "positive": true, "maxScore": 0.1, "weight": 1}, "200205": {"confidence": 0, "description": "Nominal feature with 2 values: male, female", "identity": "PB_GENDER", "weight": 0}, "200206": {"confidence": 0, "description": "Age (current date - DOB) in years", "identity": "PB_AGE", "weight": 0}, "200208": {"confidence": 0, "description": "Number of languages", "identity": "PB_NUMBER_OF_LANGUAGES", "positive": true, "maxScore": 0.1, "weight": 1}, "200209": {"confidence": 0, "description": "Numebr of origin countries", "identity": "PB_NUMBER_OF_ORIGIN_COUNTRIES", "positive": true, "maxScore": 0.1, "weight": 1}, "200210": {"confidence": 0, "description": "Number of addresses", "identity": "PB_NUMBER_OF_ADDRESSES", "positive": true, "maxScore": 0.1, "weight": 1}, "200211": {"confidence": 0, "description": "Number of jobs", "identity": "PB_NUMBER_OF_JOBS", "positive": true, "maxScore": 0.1, "weight": 1}, "200212": {"confidence": 0, "description": "Number of educations", "identity": "PB_NUMBER_OF_EDUCATIONS", "positive": true, "maxScore": 0.1, "weight": 1}, "200213": {"confidence": 0, "description": "Number of relationships", "identity": "PB_NUMBER_OF_RELATIONSHIPS", "positive": true, "maxScore": 0.1, "weight": 1}, "200214": {"confidence": 0, "description": "Number of user ids", "identity": "PB_NUMBER_OF_USER_IDS", "positive": true, "maxScore": 0.1, "weight": 1}, "200215": {"confidence": 0, "description": "Number of images", "identity": "PB_NUMBER_OF_IMAGES", "positive": true, "maxScore": 0.1, "weight": 1}, "200216": {"confidence": 0, "description": "Number of urls", "identity": "PB_NUMBER_OF_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200235": {"confidence": 0, "description": "One of names has person title", "identity": "PB_NAME_PREFIX", "positive": true, "weight": 0.1}, "200243": {"confidence": 0, "description": "Number of friends (friend relationships)", "identity": "PB_NUMBER_OF_FRIEND_RELATIONSHIPS", "positive": true, "maxScore": 0.1, "weight": 1}, "200244": {"confidence": 0, "description": "Number of family relationships; number of relatives", "identity": "PB_NUMBER_OF_FAMILY_RELATIONSHIPS", "positive": true, "maxScore": 0.1, "weight": 1}, "200245": {"confidence": 0, "description": "Number of work relationships; number of work contacts", "identity": "PB_NUMBER_OF_WORK_RELATIONSHIPS", "positive": true, "maxScore": 0.1, "weight": 1}, "200246": {"confidence": 0, "description": "Number of other relationships", "identity": "PB_NUMBER_OF_OTHER_RELATIONSHIPS", "positive": true, "maxScore": 0.1, "weight": 1}, "200247": {"confidence": 0, "description": "Background report urls", "identity": "PB_NUMBER_OF_BACKGROUND_REPORT_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200248": {"confidence": 0, "description": "number of contact details url", "identity": "PB_NUMBER_OF_CONTACT_DETAILS_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200249": {"confidence": 0, "description": "Number of email urls", "identity": "PB_NUMBER_OF_EMAIL_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200250": {"confidence": 0, "description": "Number of media urls", "identity": "PB_NUMBER_OF_MEDIA_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200251": {"confidence": 0, "description": "Number of personal profile urls", "identity": "PB_NUMBER_OF_PERSONAL_PROFILE_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200252": {"confidence": 0, "description": "Number of business urls", "identity": "PB_NUMBER_OF_BUSINESS_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200253": {"confidence": 0, "description": "Number of public record urls", "identity": "PB_NUMBER_OF_PUBLIC_RECORD_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200254": {"confidence": 0, "description": "Number of publication urls", "identity": "PB_NUMBER_OF_PUBLICATION_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200255": {"confidence": 0, "description": "Number of classmate urls", "identity": "PB_NUMBER_OF_CLASSMATE_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200256": {"confidence": 0, "description": "Number of webpage urls", "identity": "PB_NUMBER_OF_WEBPAGE_URLS", "positive": true, "maxScore": 0.1, "weight": 1}, "200259": {"confidence": 0, "description": "form address country is different from returned country", "identity": "PB_LEAST_ONE_ADDRESS_DIFF_REGION", "positive": false, "weight": -0.1}, "200260": {"confidence": 0, "description": "Origin Country is different from primary/input address country", "identity": "PB_INPUT_COUNTRY_DIFF_ORIGIN_COUNTRY", "reasoncode": "I334", "positive": false, "weight": -0.1}, "200261": {"confidence": 0, "description": "Number of filtered persons passed into Entity resolution", "identity": "PB_NUMBER_OF_PERSONS_TO_ER", "weight": 0}, "200262": {"confidence": 0, "description": "No Names found", "identity": "PB_NO_NAMES", "positive": false, "weight": -0.1}, "200263": {"confidence": 0, "description": "No User Names found", "identity": "PB_NO_USERNAMES", "positive": false, "weight": -0.1}, "200264": {"confidence": 0, "description": "No Phones found", "identity": "PB_NO_PHONES", "positive": false, "weight": -0.1}, "200265": {"confidence": 0, "description": "No Gender found", "identity": "PB_NO_GENDER", "positive": false, "weight": -0.1}, "200266": {"confidence": 0, "description": "No Age found", "identity": "PB_NO_AGE", "positive": false, "weight": -0.1}, "200267": {"confidence": 0, "description": "No Languages found", "identity": "PB_NO_LANGUAGES", "positive": false, "weight": -0.1}, "200268": {"confidence": 0, "description": "No origin of countries found", "identity": "PB_NO_ORIGIN_COUNTRIES", "positive": false, "weight": -0.1}, "200269": {"confidence": 0, "description": "No Address found", "identity": "PB_NO_ADDRESSES", "positive": false, "weight": -0.1}, "200270": {"confidence": 0, "description": "No Jobes found", "identity": "PB_NO_JOBS", "positive": false, "weight": -0.1}, "200271": {"confidence": 0, "description": "No Educations found", "identity": "PB_NO_EDUCATIONS", "positive": false, "weight": -0.1}, "200272": {"confidence": 0, "description": "No relationships found", "identity": "PB_NO_RELATIONSHIPS", "positive": false, "weight": -0.1}, "200273": {"confidence": 0, "description": "No User Ids found", "identity": "PB_NO_USER_IDS", "positive": false, "weight": -0.1}, "200274": {"confidence": 0, "description": "No Images found", "identity": "PB_NO_IMAGES", "positive": false, "weight": -0.1}, "200275": {"confidence": 0, "description": "No Urls found", "identity": "PB_NO_URLS", "positive": false, "weight": -0.1}, "200276": {"confidence": 0, "description": "No name with prefix found", "identity": "PB_NO_NAME_PREFIX", "positive": false, "weight": -0.1}, "200277": {"description": "PIPL Basic match score", "identity": "PB_MATCH_SCORE", "weight": 0}, "200278": {"description": "Value in @available_sources", "identity": "PB_AVALIABLE_SOURCES", "weight": 0}, "200279": {"description": "Value in @persons_count", "identity": "PB_PERSONS_COUNT", "weight": 0}, "200280": {"description": "Value in available_data_basic_landline_phones", "identity": "PB_NUMBER_OF_LANDLINE_PHONES", "weight": 0}, "200281": {"description": "Value in available_data_basic_mobile_phones", "identity": "PB_NUMBER_OF_MOBILE_PHONES", "weight": 0}, "200282": {"description": "Value in available_data_basic_social_profiles field", "identity": "PB_NUMBER_OF_SOCIAL_PROFILES", "weight": 0}, "200283": {"description": "Number of elements in person_emails section", "identity": "PB_NUMBER_OF_EMAILS", "weight": 0}, "200284": {"description": "Number of person.urls[0].@sponsored TRUE values in person.urls section", "identity": "PB_NUMBER_OF_SPONSORED_URLS", "weight": 0}, "300101": {"confidence": 0, "description": "Total number of sources if two partials become perfect", "identity": "PB_PARTIAL_PERFECT", "minScore": 0, "weight": 0}, "maxScore": 2.01, "minScore": -1.51}, "PNANL": {"100504": {"description": "0.5 + weighted sum of pinterest activity codes  bounded by [0,1]", "identity": "PN_ACTIVITY_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.33, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100505": {"description": "0.5 + weighted sum of pinterest connections codes  bounded by [0,1]", "identity": "PN_CONNECTION_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.33, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100506": {"description": "0.5 + weighted sum of pinterest maintenance codes  bounded by [0,1]", "identity": "PN_MAINTENANCE_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.33, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100507": {"description": "w1*PN_ACTIVITY_SCORE + w2*PN_CONNECTION_SCORE + w3*PN_MAINTENANCE_SCORE", "identity": "PN_PINTEREST_OSB_SCORE", "authscore_type": "osb_score", "weight": -999}, "400003": {"description": "PN_CONNECTION_SCORE < 0.3", "identity": "PN_CONNECTIONS_UNNATURAL", "reasoncode": "I181", "weight": 0}, "400004": {"description": "PN_CONNECTION_SCORE > 0.7", "identity": "PN_CONNECTIONS_GOOD", "reasoncode": "I182", "weight": 0}, "400005": {"description": "PN_ACTIVITY_SCORE < 0.3", "identity": "PN_ACTIVITY_UNNATURAL", "reasoncode": "I183", "weight": 0}, "400006": {"description": "PN_ACTIVITY_SCORE > 0.7", "identity": "PN_ACTIVITY_GOOD", "reasoncode": "I184", "weight": 0}, "400007": {"description": "PN_MAINTENANCE_SCORE < 0.3", "identity": "PN_ACCOUNT_UNMAINTAINED", "reasoncode": "I185", "weight": 0}, "400008": {"description": "PN_MAINTENANCE_SCORE > 0.7", "identity": "PN_ACCOUNT_WELL_MAINTAINED", "reasoncode": "I186", "weight": 0}, "987665": {"description": "Date of data acquisition ???", "identity": "PN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE", "weight": 0}, "987666": {"description": "Self-evident", "identity": "PN_NUMBER_OF_FOLLOWERS", "weight": 0}, "987667": {"description": "Self-evident", "identity": "PN_NUMBER_OF_FOLLOWING", "weight": 0}, "987668": {"description": "Self-evident", "identity": "PN_NUMBER_OF_LIKES", "weight": 0}, "987669": {"description": "Self-evident", "identity": "PN_NUMBER_OF_PINS", "weight": 0}, "987670": {"description": "Self-evident", "identity": "PN_NUMBER_OF_BOARDS", "weight": 0}, "987671": {"description": "Self-evident", "identity": "PN_NUMBER_OF_SOCIAL_NETWORKS", "weight": 0}, "987672": {"description": "Self-evident", "identity": "PN_HAS_NAME", "authscore_type": "maintenance", "weight": 0.34}, "987673": {"description": "Self-evident", "identity": "PN_HAS_PROFILE_IMAGE", "authscore_type": "maintenance", "reasoncode": "I178", "weight": 0.34}, "987674": {"description": "Self-evident", "identity": "PN_RATIO_BETWEEN_FOLLOWERS_AND_FOLLOWING", "weight": 0}, "987675": {"description": "Self-evident", "identity": "PN_RATIO_BETWEEN_PINS_AND_BOARDS", "weight": 0}, "987676": {"description": "Number of Followers = 0", "identity": "PN_NO_FOLLOWERS", "weight": 0}, "987677": {"description": "Number of Following = 0", "identity": "PN_NO_FOLLOWING", "weight": 0}, "987678": {"description": "Number of Likes = 0", "identity": "PN_NO_LIKES", "authscore_type": "activity", "weight": -0.25}, "987679": {"description": "Number of pins = 0", "identity": "PN_NO_PINS", "authscore_type": "activity", "weight": -0.25}, "987680": {"description": "Number of boards = 0", "identity": "PN_NO_BOARDS", "authscore_type": "activity", "weight": -0.25}, "987681": {"description": "Number of social networks linked to the profile = 0", "identity": "PN_NO_SOCIAL_NETWORKS", "authscore_type": "maintenance", "weight": -0.34}, "987682": {"description": "Has no valid name", "identity": "PN_NO_NAME", "authscore_type": "maintenance", "weight": -0.34}, "987683": {"description": "Has no profile image", "identity": "PN_NO_PROFILE_IMAGE", "authscore_type": "maintenance", "weight": -0.34}, "987686": {"description": "Days since last update <= 180", "identity": "PN_NUMBER_OF_DAYS_LAST_UPDATE_LESSER", "authscore_type": "activity", "weight": 0.25}, "987687": {"description": "Days since last update > 180", "identity": "PN_NUMBER_OF_DAYS_LAST_UPDATE_GREATER", "authscore_type": "activity", "reasoncode": "I171", "weight": -0.25}, "987688": {"description": "Number of Likes > 1", "identity": "PN_NUMBER_OF_LIKES_GREATER", "authscore_type": "activity", "reasoncode": "I175", "weight": 0.25}, "987689": {"description": "Number of Pins > 1", "identity": "PN_NUMBER_OF_PINS_GREATER", "authscore_type": "activity", "reasoncode": "I176", "weight": 0.25}, "987690": {"description": "Number of Boards > 1", "identity": "PN_NUMBER_OF_BOARDS_GREATER", "authscore_type": "activity", "reasoncode": "I177", "weight": 0.25}, "987691": {"description": "Number of followers > 5", "identity": "PN_NUMBER_OF_FOLLOWERS_GREATER", "authscore_type": "connections", "reasoncode": "I172", "weight": 0.5}, "987692": {"description": "Number of following > 5", "identity": "PN_NUMBER_OF_FOLLOWING_GREATER", "authscore_type": "connections", "reasoncode": "I173", "weight": 0.5}, "987693": {"description": "Number of social networks > 1", "identity": "PN_NUMBER_OF_SOCIAL_NETWORKS_GREATER", "authscore_type": "maintenance", "reasoncode": "I174", "weight": 0.34}, "987694": {"description": "Number of followers <= 5", "identity": "PN_NUMBER_OF_FOLLOWERS_LESSER", "authscore_type": "connections", "weight": -0.5}, "987695": {"description": "Number of following <= 5", "identity": "PN_NUMBER_OF_FOLLOWERS_LESSER", "authscore_type": "connections", "weight": -0.5}}, "PNVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200126": {"description": "Pinterest ID found online (received from search engines)", "identity": "PN_ACCOUNT_MATCH", "weight": 0}, "200131": {"description": "Pinterest URL is in profilesFound section", "identity": "PN_URL_FOUND", "reasoncode": "I180", "weight": 0}, "200130": {"description": "Match Pinterest ID/URL in KissingHub", "identity": "PN_KH_MATCH", "weight": 0}}, "TWANL": {"100001": {"description": "Number of friends (user follows them, they follow the user) is less than or equal 49", "identity": "TW_NO_FRIENDS_LESSER", "authscore_type": "connections", "weight": -0.25}, "100002": {"description": "No profile picture", "identity": "TW_NO_PROFILE_PIC", "authscore_type": "maintenance", "weight": -0.25}, "100003": {"description": "Profile complete percent less 50%", "identity": "TW_PROFILE_COMLETE_LESSER_PERCENTAGE", "weight": 0}, "100004": {"description": "rule 100001 and tweet count greater than 50", "identity": "TW_TWEET_COUNT_LESSER", "authscore_type": "activity", "weight": -0.18}, "100005": {"description": "numTweets / numDayOfPosting greater than or equal 100", "identity": "TW_POSTING_LESSER", "authscore_type": "activity", "weight": -0.18}, "100006": {"description": "following 1000+ users", "identity": "TW_FOLLOWING_GREATER", "authscore_type": "connections", "weight": -0.18}, "100007": {"description": "rule 100006 and does NOT have an AuthorityWebsite", "identity": "TW_HAS_NO_AUTH_WEBSITE", "authscore_type": "connections", "weight": -0.18}, "100008": {"description": "account age 4 months or less (adjusted)", "identity": "TW_ACC_AGE_LESSER", "authscore_type": "history", "weight": -1}, "200023": {"description": "account age 4 months or less", "identity": "TW_ACC_AGE_LESSER"}, "100009": {"description": "account not verified", "identity": "TW_ACC_NOT_VERIFIED", "authscore_type": "history", "weight": 0}, "100010": {"description": "no re-tweets", "identity": "TW_NO_RETWEETED", "authscore_type": "activity", "weight": -0.18}, "100011": {"description": "numRetweets less than 1", "identity": "TW_NO_TWEETS_LESSER", "authscore_type": "activity", "weight": -0.18}, "100012": {"description": "rule 200008 and following less than 10 people", "identity": "TW_FOLLOWING_PEOPLE_LESSER", "authscore_type": "connections", "weight": -0.25}, "100015": {"description": "Has no value for any of the following profile image, name, firstName, lastName, gender, locale, and email", "identity": "TW_HAS_NO_DETAILS", "weight": 0}, "100016": {"description": "The word BOT appears in at least one tweet.(Highly likely for large number of tweets ?)", "identity": "TW_BOT_WORD", "authscore_type": "activity", "weight": -0.18}, "100017": {"description": "freqMean / numTweets * 100 greater 8", "identity": "TW_FRQ_MEAN_GREATER", "authscore_type": "activity", "weight": -0.18}, "100018": {"description": "'user:description' field has no text", "identity": "TW_NO_DESCRIPTION", "authscore_type": "maintenance", "weight": -0.25}, "100019": {"description": "Account no longer exists", "identity": "TW_ACC_NOT_EXISTS", "reasoncode": "I140", "weight": 0}, "100020": {"description": "'user:geoEnabled':false", "identity": "TW_GEO_DISABLED", "authscore_type": "maintenance", "weight": -0.25}, "100021": {"description": "'user:location' field has no text", "identity": "TW_NO_LOCATION", "authscore_type": "maintenance", "weight": -0.25}, "100029": {"description": "Account suspended", "identity": "TW_ACC_SUSPENDED", "reasoncode": "I146", "weight": 0}, "100503": {"description": "0.5 + weighted sum of twitter history codes  bounded by [0,1]", "identity": "TW_HISTORY_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.25, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100504": {"description": "0.5 + weighted sum of twitter acitvity codes bounded by [0,1]", "identity": "TW_ACTIVITY_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.25, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100505": {"description": "0.5 + weighted sum of twitter connections codes bounded by [0,1]", "identity": "TW_CONNECTIONS_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.25, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100506": {"description": "0.5 + weighted sum of twitter maintenance codes bounded by [0,1]", "identity": "TW_MAINTENANCE_SCORE", "authscore_type": "sn_score", "intercept": 0.5, "weight": 0.25, "maxScore": 1, "minScore": 0, "lower_bound": 0, "upper_bound": 1}, "100507": {"description": "w1*TW_HISTORY_SCORE + w2*TW_ACTIVITY_SCORE + w3*TW_CONNECTIONS_SCORE + w4*TW_MAINTENANCE_SCORE", "identity": "TW_TWITTER_OSB_SCORE", "authscore_type": "osb_score", "weight": -999}, "200001": {"description": "Number of friends (user follows them, they follow the user) is greater than or equal 50", "identity": "TW_NO_FRIENDS_GREATER", "authscore_type": "connections", "weight": 0.25}, "200002": {"description": "Has a profile picture", "identity": "TW_HAS_PROFILE_PIC", "authscore_type": "maintenance", "weight": 0.25}, "200003": {"description": "Profile complete percent greater than or equal 50%", "identity": "TW_PROFILE_COMLETE_GREATER_PERCENTAGE", "weight": 0}, "200004": {"description": "rule 200001 and tweet count greater than or equal 50", "identity": "TW_TWEET_COUNT_GREATER", "authscore_type": "activity", "weight": 0.18}, "200005": {"description": "numTweets / numDayOfPosting less than 100", "identity": "TW_POSTING_GREATER", "authscore_type": "activity", "weight": 0.18}, "200006": {"description": "following between 10 and 1000 users", "identity": "TW_FOLLOWING_LESSER", "authscore_type": "connections", "weight": 0.25}, "200007": {"description": "rule 100006 and has an AuthorityWebsite (so we gave you a -0.5, now you get +1)", "identity": "TW_HAS_AUTH_WEBSITE", "authscore_type": "connections", "weight": 0.25}, "200008": {"description": "account age 5 months or more (adjusted)", "identity": "TW_ACC_AGE_GREATER", "authscore_type": "history", "weight": 1}, "200022": {"description": "account age 5 months or more", "identity": "TW_ACC_AGE_GREATER"}, "200009": {"description": "verified account", "identity": "TW_VERIFIED_ACC", "authscore_type": "history", "weight": 1}, "200010": {"description": "has been re-tweeted", "identity": "TW_HAS_RETWEETED", "authscore_type": "activity", "weight": 0.18}, "200011": {"description": "numRetweets greater than or equal 1", "identity": "TW_NO_TWEETS_GREATER", "authscore_type": "activity", "weight": 0.18}, "200012": {"description": "Account age &lt;= 1 month and following less than 100 people", "identity": "TW_FOLLOWING_PEOPLE_GREATER", "authscore_type": "connections", "weight": 0.25}, "200015": {"description": "Has profile image, name, firstName, lastName, gender, locale, and email", "identity": "TW_HAS_DETAILS", "weight": 0}, "200016": {"description": "The word BOT never appears in a tweet", "identity": "TW_NO_BOT_WORD", "authscore_type": "activity", "weight": 0.18}, "200017": {"description": "freqMean / numTweets * 100 less than or equal 8", "identity": "TW_FRQ_MEAN_LESSER", "authscore_type": "activity", "weight": 0.18}, "200018": {"description": "'user:description' field has some text", "identity": "TW_HAS_DESCRIPTION", "authscore_type": "maintenance", "weight": 0.25}, "200020": {"description": "'user:geoEnabled' : true", "identity": "TW_GEO_ENABLED", "authscore_type": "maintenance", "weight": 0.25}, "200021": {"description": "'user:location' field has some text", "identity": "TW_HAS_LOCATION", "authscore_type": "maintenance", "weight": 0.25}, "300013": {"description": "Account age in months", "identity": "TW_ACC_AGE_MONTHS", "weight": 0}, "300001": {"description": "Account age in months - adjusted", "identity": "TW_ACC_AGE_MONTHS", "weight": 0}, "300002": {"description": "Number of tweets", "identity": "TW_NO_TWEETS", "weight": 0}, "300003": {"description": "Number of tweets retweeted at least once", "identity": "TW_NO_TWEETS_RETWEETS", "weight": 0}, "300004": {"description": "Number of tweets per day on a day of tweeting", "identity": "TW_NO_TWEET_PER_DAY", "weight": 0}, "300005": {"description": "Number of followers", "identity": "TW_NO_FOLLOWERS", "weight": 0}, "300006": {"description": "Number of accounts user follows", "identity": "TW_NO_ACC_USER_FOLLOW", "weight": 0}, "300007": {"description": "Number of mutual followings (user follows them, they follow user)", "identity": "TW_NO_MUTUAL_FOLLOWING", "weight": 0}, "300008": {"description": "Number of favorites", "identity": "TW_NO_FAV", "weight": 0}, "300009": {"description": "Active days", "identity": "TW_ACTIVE_DAYS", "weight": 0}, "300010": {"description": "Burst days", "identity": "TW_BURST_DAYS", "weight": 0}, "300011": {"description": "Frequency mean", "identity": "TW_FRQ_MEAN", "weight": 0}, "300012": {"description": "Standard deviation (in millis) of the above mean", "identity": "TW_STD_DEVIATION", "weight": 0}, "400001": {"description": "TW_HISTORY_SCORE < 0.3", "identity": "TW_INSUFFICIENT_ACCOUNT_HISTORY", "reasoncode": "I141", "weight": 0}, "400002": {"description": "TW_HISTORY_SCORE > 0.7", "identity": "TW_GOOD_ACCOUNT_HISTORY", "reasoncode": "I241", "weight": 0}, "400003": {"description": "TW_CONNECTIONS_SCORE < 0.3", "identity": "TW_CONNECTIONS_UNNATURAL", "reasoncode": "I142", "weight": 0}, "400004": {"description": "TW_CONNECTIONS_SCORE > 0.7", "identity": "TW_CONNECTIONS_GOOD", "reasoncode": "I242", "weight": 0}, "400005": {"description": "TW_ACTIVITY_SCORE < 0.3", "identity": "TW_ACTIVITY_UNNATURAL", "reasoncode": "I143", "weight": 0}, "400006": {"description": "TW_ACTIVITY_SCORE > 0.7", "identity": "TW_ACTIVITY_GOOD", "reasoncode": "I243", "weight": 0}, "400007": {"description": "TW_MAINTENANCE_SCORE < 0.3", "identity": "TW_ACCOUNT_UNMAINTAINED", "reasoncode": "I144", "weight": 0}, "400008": {"description": "TW_MAINTENANCE_SCORE > 0.7", "identity": "TW_ACCOUNT_WELL_MAINTAINED", "reasoncode": "I244", "weight": 0}, "500001": {"description": "Twitter Profile Match", "identity": "TW_PROFILE_MATCH", "weight": 0}, "500003": {"description": "Account age calculated", "identity": "ACCOUNT_AGE_FOUND", "weight": 0}, "500002": {"description": "Account age calculated - adjusted", "identity": "ACCOUNT_AGE_FOUND", "weight": 0}}, "TWVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200126": {"description": "Twitter ID found online (received from search engines)", "identity": "TW_ACCOUNT_MATCH", "weight": 0}, "200131": {"description": "Twitter URL is in profilesFound section", "identity": "TW_URL_FOUND", "reasoncode": "I240", "weight": 0}, "200127": {"description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "weight": 0}, "500003": {"description": "if is verified", "identity": "TW_VERFIED", "weight": 0}}, "VDANL": {"300001": {"description": "Number of days since last visit", "identity": "VD_NUMBER_OF_DAYS_SINCE_LAST_VISIT", "weight": 0}, "300002": {"description": "Number of days since last update", "identity": "VD_NUMBER_OF_DAYS_SINCE_LAST_UPDATE", "weight": 0}, "300003": {"description": "Number of languages", "identity": "VD_NUMBER_OF_LANGUAGES", "positive": true, "weight": 0}, "300004": {"description": "Number of skills", "identity": "VD_NUMBER_OF_SKILLS", "positive": true, "weight": 0}, "300005": {"description": "Number of visited profiles", "identity": "VD_NUMBER_OF_VISTED_PROFILES", "positive": true, "weight": 0}, "300006": {"description": "Number of groups", "identity": "VD_NUMBER_OF_GROUPS", "positive": true, "weight": 0}, "300007": {"description": "Number of social links", "identity": "VD_NUMBER_OF_SOCIAL_LINKS", "positive": true, "weight": 0}, "300008": {"description": "Number of last contacts", "identity": "VD_NUMBER_OF_LAST_CONTACTS", "positive": true, "weight": 0}, "300009": {"description": "Number of contacts", "identity": "VD_NUMBER_OF_CONTACTS", "positive": true, "weight": 0}, "300010": {"description": "Number of keywords", "identity": "VD_NUMBER_OF_KEYWORDS", "positive": true, "weight": 0}, "300011": {"description": "Number of jobs/careers/employments in career timeline", "identity": "VD_NUMBER_OF_EMPLOYMENTS", "positive": true, "weight": 0}, "300012": {"description": "avg experience duration", "identity": "VD_AVERAGE_EMPLOYMENT_DURATION", "weight": 0}, "300013": {"description": "duration of most recent experience", "identity": "VD_DURATION_OF_MOST_RECENT_EMPLOYMENT", "weight": 0}, "300014": {"description": "Is premium member", "identity": "VD_IS_PREMIUM_MEMBER", "reasoncode": "I284", "positive": true, "weight": 0}, "300015": {"description": "Has resume", "identity": "VD_HAS_RESUME", "reasoncode": "I279", "positive": true, "weight": 0}, "300016": {"description": "Length of resume in words", "identity": "VD_LENGTH_OF_RESUME_IN_WORDS", "weight": 0}, "300017": {"description": "Has profile image", "identity": "VD_HAS_PROFILE_IMAGE", "reasoncode": "I278", "positive": true, "weight": 0}, "300018": {"description": "has valid full name", "identity": "VD_HAS_FULL_NAME", "reasoncode": "I277", "positive": true, "weight": 0}, "300019": {"description": "has valid first name", "identity": "VD_HAS_FIRST_NAME", "weight": 0}, "300020": {"description": "has valid last name", "identity": "VD_HAS_LAST_NAME", "weight": 0}, "300021": {"description": "Number of Educations", "identity": "VD_NUMBER_OF_EDUCATIONS", "positive": true, "weight": 0}, "300022": {"description": "Not premium number", "identity": "VD_NOT_PREMIUM_MEMBER", "positive": false, "weight": 0}, "300023": {"description": "No resume", "identity": "VD_NO_RESUME", "positive": false, "weight": 0}, "300024": {"description": "No profile image", "identity": "VD_NO_PROFILE_IMAGE", "positive": false, "weight": 0}, "300025": {"description": "No name", "identity": "VD_NO_NAME", "positive": false, "weight": 0}, "300026": {"description": "Has location", "identity": "VD_HAS_LOCATION", "positive": true, "weight": 0}, "300027": {"description": "Has country", "identity": "VD_HAS_COUNTRY", "positive": true, "weight": 0}, "300028": {"description": "Has current title", "identity": "VD_HAS_CURRENT_TITLE", "positive": true, "weight": 0}, "300029": {"description": "Has city", "identity": "VD_HAS_CITY", "positive": true, "weight": 0}, "300030": {"description": "no location", "identity": "VD_NO_LOCATION", "positive": false, "weight": 0}, "300031": {"description": "no country", "identity": "VD_NO_COUNTRY", "positive": false, "weight": 0}, "300032": {"description": "no current title", "identity": "VD_NO_CURRENT_TITLE", "positive": false, "weight": 0}, "300033": {"description": "no city", "identity": "VD_NO_CITY", "positive": false, "weight": 0}, "300034": {"description": "number of visit less than 180 days", "identity": "VD_NUMBER_OF_DAYS_SINCE_LAST_VISIT_LESS_180_DAYS", "reasoncode": "I275", "weight": 0}, "300035": {"description": "number of visit more than 180 days", "identity": "VD_NUMBER_OF_DAYS_SINCE_LAST_VISIT_MORE_180_DAYS", "weight": 0}, "300036": {"description": "connected groups", "identity": "VD_CONNECTED_TO_GROUPS", "reasoncode": "I282", "positive": true, "weight": 0}, "300037": {"description": "not connected groups", "identity": "VD_NOT_CONNECTED_TO_GROUPS", "positive": false, "weight": 0}, "300038": {"description": "normal number of contacts", "identity": "VD_NORMAL_NUMBER_OF_CONTACTS", "reasoncode": "I283", "positive": true, "weight": 0}, "300039": {"description": "low number of contacts", "identity": "VD_LOW_NUMBER_OF_CONTACTS", "positive": false, "weight": 0}, "300040": {"description": "has language", "identity": "VD_HAS_LANGUAGE", "positive": true, "weight": 0}, "300041": {"description": "has skills", "identity": "VD_HAS_SKILLS", "positive": true, "weight": 0}, "300042": {"description": "visited profiles", "identity": "VD_HAS_VISITED_PROFILES", "positive": true, "weight": 0}, "300043": {"description": "has social links", "identity": "VD_HAS_SOCIAL_LINKS", "positive": true, "weight": 0}, "300044": {"description": "no language", "identity": "VD_NO_LANGUAGE", "positive": false, "weight": 0}, "300045": {"description": "no skills", "identity": "VD_NO_SKILLS", "positive": false, "weight": 0}, "300046": {"description": "no visited profiles", "identity": "VD_NO_VISITED_PROFILES", "positive": false, "weight": 0}, "300047": {"description": "no social links", "identity": "VD_NO_SOCIAL_LINKS", "positive": false, "weight": 0}, "300048": {"description": "last update than 180 days", "identity": "VD_NUMBER_OF_DAYS_SINCE_LAST_UPDATE_LESS_180_DAYS", "reasoncode": "I276", "weight": 0}, "300049": {"description": "last update more than 180 days", "identity": "VD_NUMBER_OF_DAYS_SINCE_LAST_UPDATE_MORE_180_DAYS", "weight": 0}, "300050": {"description": "duration of most recent job less than 90 days", "identity": "VD_NEW_JOB", "reasoncode": "I281", "positive": false, "weight": 0}, "300051": {"description": "duration of most recent job more than 90 days", "identity": "VD_OLD_JOB", "positive": true, "weight": 0}, "300052": {"description": "number of job greater than or euqal 1", "identity": "VD_HAS_JOBS", "positive": true, "weight": 0}, "300053": {"description": "number of job 0", "identity": "VD_NO_JOBS", "positive": false, "weight": 0}}, "WLVAL": {"100007": {"description": "Watch list match", "identity": "WATCH_LIST_MATCH", "reasoncode": "R186", "weight": 0}, "100130": {"description": "Internal Error", "identity": "WL_INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "WL_TIMEOUT", "weight": 0}, "200001": {"description": "No watch list match", "identity": "WATCH_LIST_NO_MATCH", "reasoncode": "I196", "weight": 0}}, "WPVAL": {"100002": {"description": "Phone not valid", "identity": "WP_PHONE_NOT_VALID", "positive": false, "weight": -0.02}, "100003": {"description": "", "identity": "WP_PHONE_NAME_MATCH", "positive": true, "weight": 0.03}, "100004": {"description": "", "identity": "WP_PHONE_NAME_NO_MATCH", "positive": false, "weight": -0.02}, "100006": {"description": "", "identity": "WP_PHONE_ADDRESS_MATCH", "positive": true, "weight": 0.02}, "100007": {"description": "", "identity": "WP_PHONE_ADDRESS_POSTAL_MATCH", "positive": true, "weight": 0.02}, "100008": {"description": "", "identity": "WP_PHONE_ADDRESS_NO_MATCH", "positive": false, "weight": -0.02}, "100020": {"description": "", "identity": "WP_PHONE_PREPAID", "positive": false, "weight": -0.02}, "100021": {"description": "", "identity": "WP_PHONE_NOT_CONNECTED", "positive": false, "weight": -0.02}, "100022": {"description": "", "identity": "WP_PHONE_REALLY_NOT_CONNECTED", "positive": false, "weight": 0.0}, "100025": {"description": "", "identity": "WP_ADDRESS_NOT_FOUND", "positive": false, "weight": -0.02}, "100026": {"description": "", "identity": "WP_ADDRESS_NAME_MATCH", "positive": true, "weight": 0.02}, "100027": {"description": "", "identity": "WP_ADDRESS_NAME_NO_MATCH", "positive": false, "weight": -0.02}, "100030": {"description": "", "identity": "WP_ADDRESS_TYPE_POBOXTHROWBACK", "positive": false, "weight": -0.02}, "100031": {"description": "", "identity": "WP_ADDRESS_TYPE_POBOX", "positive": false, "weight": -0.02}, "100034": {"description": "", "identity": "WP_ADDRESS_ACTIVE", "positive": true, "weight": 0.02}, "100035": {"description": "", "identity": "WP_ADDRESS_NOT_ACTIVE", "positive": false, "weight": -0.02}, "100039": {"description": "", "identity": "WP_EMAIL_DELIVERABLE", "positive": true, "weight": 0.02}, "100048": {"description": "", "identity": "WP_EMAIL_DISPOSABLE", "positive": false, "weight": -0.02}, "100049": {"description": "", "identity": "WP_EMAIL_AUTOGENERATED", "positive": false, "weight": -0.02}, "100050": {"description": "", "identity": "WP_EMAIL_NAME_MATCH", "positive": true, "weight": 0.02}, "100051": {"description": "", "identity": "WP_EMAIL_NAME_NO_MATCH", "positive": false, "weight": -0.02}, "100052": {"description": "", "identity": "WP_NUMBER_OF_YEARS_EMAIL_SEEN_MORE_2", "positive": true, "weight": 0.02}, "100053": {"description": "", "identity": "WP_NUMBER_OF_DAYS_EMAIL_SEEN_LESS_180", "positive": false, "weight": -0.02}, "100056": {"description": "", "identity": "WP_IP_DISTANCE_FROM_ADDRESS_LESS_100_MILES", "positive": true, "weight": 0.02}, "100057": {"description": "", "identity": "WP_IP_DISTANCE_FROM_ADDRESS_MORE_100_MILES", "positive": false, "weight": -0.02}, "100058": {"description": "", "identity": "WP_IP_OVER_100_MILES_AWAY", "positive": false, "weight": 0.0}, "100060": {"description": "", "identity": "WP_PHONE_COUNTRY_NO_MATCH", "positive": false, "weight": -0.02}, "100130": {"description": "Internal Error", "identity": "WP_INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "WP_TIMEOUT", "weight": 0}, "300001": {"description": "", "identity": "WP_BILLING_NAME_WARNING", "weight": 0, "type": "string", "length": 50}, "300002": {"description": "", "identity": "WP_NAME_MATCHES_CELEBRITY", "weight": 0, "type": "string", "length": 5}, "300003": {"description": "", "identity": "WP_BILLING_NAME_IS_GARBLED", "weight": 0, "type": "string", "length": 5}, "300004": {"description": "", "identity": "WP_BILLING_PHONE_WARNING", "weight": 0, "type": "string", "length": 50}, "300005": {"description": "", "identity": "WP_BILLING_PHONE_TO_NAME", "weight": 0, "type": "string", "length": 13}, "300006": {"description": "", "identity": "WP_BILLING_PHONE_TO_ADDRESS", "weight": 0, "type": "string", "length": 16}, "300007": {"description": "", "identity": "WP_BILLING_PHONE_COUNTRY_CODE", "weight": 0, "type": "string", "length": 2}, "300008": {"description": "", "identity": "WP_BILLING_PHONE_LINE_TYPE", "weight": 0, "type": "string", "length": 14}, "300009": {"description": "", "identity": "WP_BILLING_PHONE_CARRIER", "weight": 0, "type": "string", "length": 50}, "300010": {"description": "", "identity": "WP_BILLING_PHONE_IS_PREPAID", "weight": 0, "type": "string", "length": 5}, "300011": {"description": "", "identity": "WP_BILLING_PHONE_IS_CONNECTED", "weight": 0, "type": "string", "length": 5}, "300012": {"description": "", "identity": "WP_BILLING_PHONE_ON_DNC", "weight": 0, "type": "string", "length": 5}, "300013": {"description": "", "identity": "WP_BILLING_PHONE_IS_COMMERCIAL", "weight": 0, "type": "string", "length": 5}, "300014": {"description": "", "identity": "WP_BILLING_ADDRESS_WARNING", "weight": 0, "type": "string", "length": 50}, "300015": {"description": "", "identity": "WP_BILLING_ADDRESS_TO_NAME", "weight": 0, "type": "string", "length": 13}, "300016": {"description": "", "identity": "WP_BILLING_ADDRESS_TYPE", "weight": 0, "type": "string", "length": 20}, "300017": {"description": "", "identity": "WP_BILLING_ADDRESS_IS_ACTIVE", "weight": 0, "type": "string", "length": 5}, "300018": {"description": "", "identity": "WP_BILLING_ADDRESS_IS_COMMERCIAL", "weight": 0, "type": "string", "length": 5}, "300039": {"description": "", "identity": "WP_EMAIL_WARNING", "weight": 0, "type": "string", "length": 50}, "300040": {"description": "", "identity": "WP_EMAIL_IS_VALID", "weight": 0, "type": "string", "length": 5}, "300041": {"description": "", "identity": "WP_EMAIL_IS_VALID_DETAILS", "weight": 0, "type": "string", "length": 65}, "300042": {"description": "", "identity": "WP_EMAIL_IS_DISPOSABLE", "weight": 0, "type": "string", "length": 5}, "300043": {"description": "", "identity": "WP_EMAIL_IS_AUTO_GENERATED", "weight": 0, "type": "string", "length": 5}, "300044": {"description": "", "identity": "WP_EMAIL_TO_NAME", "weight": 0, "type": "string", "length": 13}, "300045": {"description": "", "identity": "WP_EMAIL_FIRST_SEEN_DATE", "weight": 0, "type": "string", "length": 10}, "300065": {"description": "", "identity": "WP_EMAIL_FIRST_SEEN_DAYS", "weight": 0}, "300046": {"description": "Adjusted 300065", "identity": "WP_EMAIL_FIRST_SEEN_DAYS", "weight": 0}, "300047": {"description": "", "identity": "WP_EMAIL_DOMAIN_CREATION_DATE", "weight": 0, "type": "string", "length": 10}, "300066": {"description": "", "identity": "WP_EMAIL_DOMAIN_CREATION_DAYS", "weight": 0}, "300048": {"description": "Adjusted 300066", "identity": "WP_EMAIL_DOMAIN_CREATION_DAYS", "weight": 0}, "300049": {"description": "", "identity": "WP_IP_WARNING", "weight": 0, "type": "string", "length": 50}, "300050": {"description": "", "identity": "WP_IP_DISTANCE_FROM_ADDRESS", "weight": 0}, "300051": {"description": "", "identity": "WP_IP_IS_PROXY", "weight": 0, "type": "string", "length": 5}, "300052": {"description": "Value of address_checks/is_valid field in JSON response", "identity": "WP_ADDRESS_NOT_VALID", "weight": 0, "type": "string", "length": 5}, "300053": {"description": "name_checks.fake_name (TRUE, FALSE)", "identity": "WP_NAME_IS_FAKE", "weight": 0, "type": "string", "length": 5}, "300054": {"description": "address_checks.is_resident_deceased (TRUE, FALSE)", "identity": "WP_BILLING_IS_RESIDENT_DECEASED", "weight": 0, "type": "string", "length": 5}, "300055": {"description": "phone_checks.is_subscriber_deceased (TRUE, FALSE)", "identity": "WP_BILLING_PHONE_IS_SUBSCRIBER_DECEASED", "weight": 0, "type": "string", "length": 5}, "300056": {"description": "phone_checks.is_valid (TRUE, FALSE)", "identity": "WP_BILLING_PHONE_IS_VALID", "weight": 0, "type": "string", "length": 5}, "300057": {"description": "ip_address_checks.connection_type (STRING)", "identity": "WP_IP_CONNECTION_TYPE", "weight": 0, "type": "string", "length": 10}, "300058": {"description": "ip_address_checks.distance_from_phone (NUMBER)", "identity": "WP_IP_DISTANCE_FROM_PHONE", "weight": 0}, "300059": {"description": "Name match algorithm: email_address_checks.registered_name vs input name (NUMBER, NA if no input email)", "identity": "WP_EMAIL_REGISTERED_NAME_MATCH", "weight": 0}, "300060": {"description": "Name match algorithm: phone_checks.subscriber_name vs input name (NUMBER, NA if no input phone)", "identity": "WP_PHONE_SUBSCRIBER_NAME_MATCH", "weight": 0}, "300061": {"description": "Name match algorithm: address_checks.resident_name vs input name (NUMBER, NA if no input address)", "identity": "WP_ADDRESS_RESIDENT_NAME_MATCH", "weight": 0}, "300062": {"description": "Address match algorithm: ip_address_checks.geolocation zip (element 1) vs input address zip (NUMBER, NA if no input address zip or no input ip address)", "identity": "WP_IP_GEOLOCATION_ADDRESS_ZIP_MATCH", "weight": 0}, "300063": {"description": "Address match algorithm: ip_address_checks.geolocation zip (element 2) vs input address zip (NUMBER, NA if no input address city or no input ip address)", "identity": "WP_IP_GEOLOCATION_ADDRESS_CITY_MATCH", "weight": 0}, "300064": {"description": "Address match algorithm: ip_address_checks.geolocation zip (element 3) vs input address zip (NUMBER, NA if no input address country or no input ip address)", "identity": "WP_IP_GEOLOCATION_ADDRESS_COUNTRY_MATCH", "weight": 0}, "700025": {"description": "TEMPORARY, TO BE REMOVED", "identity": "WP_ADDRESS_TYPE_COMMERCIALMAILDROP", "weight": 0}, "700028": {"description": "TEMPORARY, TO BE REMOVED", "identity": "WP_ADDRESS_TYPE_MULTIUNIT", "weight": 0}, "700029": {"description": "TEMPORARY, TO BE REMOVED", "identity": "WP_ADDRESS_TYPE_SINGLEUNIT", "weight": 0}, "700047": {"description": "TEMPORARY, TO BE REMOVED", "identity": "WP_NUMBER_OF_DAYS_EMAIL_SEEN", "weight": 0}}, "XNANL": {"987676": {"description": "", "identity": "XN_NUMBER_OF_DAYS_SINCE_LAST_VISIT", "weight": 0}, "987677": {"description": "", "identity": "XN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE", "weight": 0}, "987678": {"description": "", "identity": "XN_CURRENT_STATUS_IS", "positive": true, "weight": 0}, "987679": {"description": "", "identity": "XN_HAS_CURRENT_TITLE", "positive": true, "weight": 0}, "987680": {"description": "", "identity": "XN_HAS_CURRENT_COMPANY", "reasoncode": "I299", "positive": true, "weight": 0}, "987681": {"description": "", "identity": "XN_NUMBER_OF_LANGUAGES", "positive": true, "weight": 0}, "987682": {"description": "", "identity": "XN_NUMBER_OF_EXPERIENCES", "positive": true, "weight": 0}, "987683": {"description": "", "identity": "XN_NUMBER_OF_VIEWED_PROFILES", "positive": true, "weight": 0}, "987684": {"description": "", "identity": "XN_NUMBER_OF_DAYS_SINCE_LAST_VISIT_LESS_180_DAYS", "reasoncode": "I295", "weight": 0}, "987685": {"description": "", "identity": "XN_NUMBER_OF_DAYS_SINCE_LAST_UPDATE_LESS_180_DAYS", "reasoncode": "I296", "weight": 0}, "987686": {"description": "has name", "identity": "XN_HAS_NAME", "reasoncode": "I297", "positive": true, "weight": 0}, "987687": {"description": "no name", "identity": "XN_NO_NAME", "positive": false, "weight": 0}, "987688": {"description": "no jobs", "identity": "XN_NO_JOBS", "positive": false, "weight": 0}, "987689": {"description": "no current title", "identity": "XN_NO_CURRENT_TITLE", "positive": false, "weight": 0}, "987690": {"description": "no current status", "identity": "XN_NO_CURRENT_STATUS", "positive": false, "weight": 0}, "987691": {"description": "no company", "identity": "XN_NO_COMPANY", "positive": false, "weight": 0}, "987692": {"description": "has language", "identity": "XN_HAS_LANGUAGE", "positive": true, "weight": 0}, "987693": {"description": "no language", "identity": "XN_NO_LANGUAGE", "positive": false, "weight": 0}, "987694": {"description": "has experience", "identity": "XN_HAS_EXPERIENCES", "positive": true, "weight": 0}, "987695": {"description": "no experience", "identity": "XN_NO_EXPERIENCES", "positive": false, "weight": 0}, "987696": {"description": "has viewed profiles", "identity": "XN_HAS_VIEWED_PROFILES", "positive": true, "weight": 0}, "987697": {"description": "no viewed profiles", "identity": "XN_NO_VIEWED_PROFILES", "positive": false, "weight": 0}}, "YOVAL": {"100131": {"description": "Timeout error", "identity": "YO_TIMEOUT", "weight": 0}, "200127": {"confidence": 0.1, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "minScore": -0.51, "weight": 0.51}, "600001": {"description": "Match", "identity": "MATCH", "positive": true, "weight": 0.51}, "600002": {"description": "Doesn exist anymore", "identity": "YO_DOES_EXIST", "positive": false, "weight": -0.51}, "600003": {"description": "API error", "identity": "YO_API_ERROR", "weight": 0}, "600004": {"description": "Error on Yahoo search", "identity": "ERROR_YOHOO_SEARCH", "weight": 0}, "610001": {"description": "Number of Foursquare matches", "identity": "NO_YO_MATCH", "weight": 0}, "confidence": 0.1, "maxScore": 0.51, "minScore": -0.51}, "AUVAL": {"100001": {"description": "Wether the document is authentic, forged, or we can't decide", "identity": "AU_VERIFICATION", "type": "string", "weight": 0}, "100002": {"description": "Wether the Quality of the document is acceptable, not acceptable, not applicable", "identity": "AU_QUALITY", "type": "string", "weight": 0}, "100003": {"description": "Country to which the document holder belongs to", "identity": "AU_TYPE_COUNTRY", "type": "string", "weight": 0}, "100004": {"description": "Document type - passport, driving license etc.", "identity": "AU_TYPE_NAME", "type": "string", "weight": 0}, "100005": {"description": "Document type version", "identity": "AU_TYPE_VERSION", "type": "string", "weight": 0}, "100006": {"description": "Document Type State", "identity": "AU_TYPE_STATE", "type": "string", "weight": 0}, "100007": {"description": "If the document is expired or not", "identity": "AU_DOCUMENT_EXPIRED", "type": "string", "weight": 0}, "100008": {"description": "Nationality of the document", "identity": "AU_NATIONALITY", "type": "string", "weight": 0}, "100009": {"description": "Gender of the document holder, male or female", "identity": "AU_GENDER", "type": "string", "weight": 0}}}, "entityResolution": {"FBvIN": {"name": {"weight": 0, "maxScore": 0}}, "FBvKL": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FBvNS": {"email": {"maxScore": 0}, "name": {"maxScore": 0}, "weight": 0}, "FBvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FBvVD": {"image": {"weight": 0}, "name": {"maxScore": 0}, "username": {"weight": 0}, "weight": 0}, "FBvWP": {"email": {"maxScore": 0}, "weight": 0}, "FBvXN": {"name": {"maxScore": 0}, "weight": 0}, "FCvFB": {"email": {"maxScore": 0}, "gender": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}, "username": {"weight": 0}, "geocode": {"maxScore": 1.51}}, "FCvFM": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"maxScore": 0}, "gender": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}, "username": {"weight": 0}, "geocode": {"maxScore": 1.51}}, "FCvIN": {"companyname": {"weight": 0}, "name": {"weight": 0, "maxScore": 0}}, "FCvKL": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FCvNS": {"address": {"weight": 0}, "email": {"maxScore": 0}, "name": {"maxScore": 0}, "weight": 0}, "FCvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FCvVD": {"address": {"weight": 0}, "companyname": {"weight": 0}, "image": {"weight": 0}, "name": {"maxScore": 0}, "username": {"weight": 0}, "weight": 0}, "FCvWP": {"email": {"maxScore": 0}, "weight": 0}, "FCvXN": {"companyname": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FMvFB": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "gender": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}, "username": {"weight": 0}, "geocode": {"maxScore": 1.51}, "weight": 4.01}, "FMvGP": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "gender": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "FMvIN": {"companyname": {"weight": 0}, "name": {"weight": 0, "maxScore": 0}}, "FMvKL": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FMvNS": {"address": {"weight": 0}, "email": {"maxScore": 0}, "mobilenumber": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FMvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FMvTW": {"image": {"weight": 1}, "name": {"maxScore": 0}, "username": {"weight": 0}, "weight": 4.01}, "FMvVD": {"address": {"weight": 0}, "companyname": {"weight": 0}, "image": {"weight": 0}, "name": {"maxScore": 0}, "username": {"weight": 0}, "weight": 0}, "FMvWP": {"email": {"maxScore": 0}, "mobilenumber": {"weight": 0}, "weight": 0}, "FMvXN": {"companyname": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FSvFB": {"email": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}, "geocode": {"maxScore": 1.51}}, "FSvFC": {"address": {"weight": 0}, "email": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}, "geocode": {"maxScore": 1.51}}, "FSvFM": {"address": {"weight": 0}, "email": {"maxScore": 0}, "image": {"weight": 1}, "mobilenumber": {"weight": 0}, "name": {"maxScore": 0}, "geocode": {"maxScore": 1.51}}, "FSvIN": {"name": {"weight": 0, "maxScore": 0}}, "FSvKL": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FSvNS": {"address": {"weight": 0}, "email": {"maxScore": 0}, "mobilenumber": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FSvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FSvVD": {"address": {"weight": 0}, "image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "FSvWP": {"email": {"maxScore": 0}, "mobilenumber": {"weight": 0}, "weight": 0}, "FSvXN": {"name": {"maxScore": 0}, "weight": 0}, "GPvFB": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "gender": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "GPvFC": {"email": {"maxScore": 0}, "gender": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "GPvFS": {"email": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "GPvIN": {"name": {"weight": 0, "maxScore": 0}}, "GPvLI": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "GPvKL": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "GPvNS": {"email": {"maxScore": 0}, "name": {"maxScore": 0}, "weight": 0}, "GPvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "GPvVD": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "GPvWP": {"email": {"maxScore": 0}, "weight": 0}, "GPvXN": {"name": {"maxScore": 0}, "weight": 0}, "KLvIN": {"name": {"weight": 0, "maxScore": 0}}, "KLvNS": {"name": {"maxScore": 0}, "weight": 0}, "KLvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "KLvVD": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "KLvXN": {"name": {"maxScore": 0}, "weight": 0}, "LIvFB": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "LIvFC": {"email": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "LIvFM": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "LIvFS": {"email": {"maxScore": 0}, "image": {"weight": 1}, "name": {"maxScore": 0}}, "LIvIN": {"name": {"weight": 0, "maxScore": 0}}, "LIvKL": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "LIvNS": {"email": {"maxScore": 0}, "name": {"maxScore": 0}, "weight": 0}, "LIvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "LIvVD": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "LIvWP": {"email": {"maxScore": 0}, "weight": 0}, "LIvXN": {"name": {"maxScore": 0}, "weight": 0}, "NSvIN": {"name": {"weight": 0, "maxScore": 0}}, "PBvFB": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "gender": {"maxScore": 0}, "image": {"weight": 0}, "name": {"maxScore": 0}, "username": {"weight": 0}}, "PBvFC": {"address": {"weight": 0}, "companyname": {"maxScore": 3.01, "weight": 3.01}, "dob": {"weight": 0}, "email": {"maxScore": 1.51, "weight": 1.51}, "gender": {"maxScore": 1.51, "weight": 1.51}, "geocode": {"maxScore": 1.51, "weight": 0}, "image": {"weight": 0}, "name": {"maxScore": 1.51, "weight": 1.51}, "username": {"weight": 0}}, "PBvFM": {"address": {"weight": 0}, "companyname": {"maxScore": 1.51, "weight": 1.51}, "dob": {"maxScore": 2.25, "weight": 2.25}, "email": {"maxScore": 1.51, "weight": 1.51}, "gender": {"maxScore": 0}, "geocode": {"maxScore": 1.51, "weight": 0}, "image": {"weight": 0}, "mobilenumber": {"maxScore": 1.51, "weight": 1.51}, "name": {"maxScore": 0}, "username": {"weight": 0}}, "PBvFS": {"address": {"weight": 0}, "email": {"maxScore": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"maxScore": 0}}, "PBvGP": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "gender": {"maxScore": 0}, "image": {"weight": 0}, "name": {"maxScore": 0}}, "PBvIN": {"companyname": {"weight": 0}, "name": {"weight": 0, "maxScore": 0}}, "PBvTW": {"image": {"weight": 0}, "name": {"maxScore": 0}, "username": {"maxScore": 2.25, "weight": 2.25}}, "PBvLI": {"dob": {"weight": 0}, "email": {"maxScore": 0}, "image": {"weight": 0}, "name": {"maxScore": 0}}, "PBvKL": {"image": {"weight": 0}, "name": {"maxScore": 0}}, "PBvNS": {"address": {"weight": 0}, "email": {"maxScore": 0}, "mobilenumber": {"weight": 0}, "name": {"maxScore": 0}}, "PBvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}}, "PBvVD": {"address": {"weight": 0}, "companyname": {"weight": 0}, "image": {"weight": 0}, "name": {"maxScore": 0}, "username": {"weight": 0}}, "PBvWP": {"email": {"maxScore": 0}, "mobilenumber": {"weight": 0}}, "PBvXN": {"companyname": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "PNvNS": {"name": {"maxScore": 0}, "weight": 0}, "PNvIN": {"name": {"weight": 0, "maxScore": 0}}, "PNvVD": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "PNvXN": {"name": {"maxScore": 0}, "weight": 0}, "TWvFB": {"image": {"weight": 1}, "name": {"maxScore": 0}, "username": {"weight": 0}, "weight": 4.01}, "TWvFC": {"image": {"weight": 1}, "name": {"maxScore": 0}, "username": {"weight": 0}}, "TWvFS": {"image": {"weight": 0}, "name": {"maxScore": 0}}, "TWvGP": {"image": {"weight": 1}, "name": {"maxScore": 0}}, "TWvIN": {"name": {"weight": 0, "maxScore": 0}}, "TWvLI": {"image": {"weight": 1}, "name": {"maxScore": 0}}, "TWvKL": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "TWvNS": {"name": {"maxScore": 0}, "weight": 0}, "TWvPN": {"image": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "TWvVD": {"image": {"weight": 0}, "name": {"maxScore": 0}, "username": {"weight": 0}, "weight": 0}, "TWvXN": {"name": {"maxScore": 0}, "weight": 0}, "VDvIN": {"companyname": {"weight": 0}, "name": {"weight": 0, "maxScore": 0}}, "VDvNS": {"address": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "VDvXN": {"companyname": {"weight": 0}, "name": {"maxScore": 0}, "weight": 0}, "WPvNS": {"email": {"maxScore": 0}, "mobilenumber": {"weight": 0}, "weight": 0}, "XNvIN": {"companyname": {"weight": 0}, "name": {"weight": 0, "maxScore": 0}}, "XNvNS": {"name": {"maxScore": 0}, "weight": 0}, "confidence": 0.075, "maxScore": 1.51, "minScore": -1.51, "weight": 1.51}, "osb_score": {"li_pn_tw": {"LIANL.100507": 0.5, "TWANL.100507": 0.25, "PNANL.100507": 0.25}, "li_pn": {"LIANL.100507": 0.66, "PNANL.100507": 0.33}, "li_tw": {"LIANL.100507": 0.66, "TWANL.100507": 0.33}, "pn_tw": {"TWANL.100507": 0.5, "PNANL.100507": 0.5}, "li": {"LIANL.100507": 1}, "pn": {"PNANL.100507": 1}, "tw": {"TWANL.100507": 1}, "default": 0.5}, "authscore_plus": {"sigmoid": {"us": {"a": 2.55, "b": 0.37}, "int": {"a": 2.3, "b": 0.39}}, "logistic": {"0to3_5": {"A": 0, "U": 5, "k": 6, "beta": 0.428, "t0": 3.5}, "3_5to5": {"A": 0, "U": 5, "k": 1.75, "beta": 0.428, "t0": 3.5}, "5to6_5": {"A": 5, "U": 10, "k": 1.75, "beta": 2.333, "t0": 6.5}, "6_5to10": {"A": 5, "U": 10, "k": 3, "beta": 2.333, "t0": 6.5}}}, "confidence": 0}, "reasoncode_descriptions": {"address": {"I701": "Address is commercial mail drop or general delivery", "I702": "Address is PO Box", "I703": "Address is PO Box throwback", "I704": "Address is multi-unit or high rise", "I705": "Address is single unit", "I706": "Address is commercial or dual purpose", "I707": "Address is residential", "I708": "Name correlates to address", "R701": "Address not active, not receiving mail or vacant", "R703": "Address does not exist or not valid", "R704": "Address is correctional facility", "R705": "Name does not correlate to address"}, "email": {"I551": "Email address is <180 days old", "I552": "Email address is between 180 days and 2 years old", "I553": "Email address is >2 years old", "I554": "Email domain is <180 days old", "I555": "Email domain is >180 days old", "I556": "Name correlates to email", "R551": "Email address unresponsive", "R552": "Email domain does not exist", "R553": "Email username does not exist", "R557": "Email address is disposable", "R558": "Email address is auto-generated", "R559": "Registered name for email address does not match input name"}, "image": {"I776": "Perfect profile image match found", "I777": "Partial profile image match found", "I778": "No profile image match found"}, "kyc": {"I901": "Entered phone number is mobile number", "I902": "Entered name missing", "I903": "Entered address missing", "I904": "Entered SSN/TIN missing or incomplete", "I905": "Entered phone missing or incomplete", "I906": "Entered date of birth missing or incomplete", "I907": "Entered SSN issued to a non-US citizen", "I908": "Entered ZIP code is military-only ZIP code", "I909": "Entered first name and last name possibly flipped", "I910": "Entered address may be previous address", "I911": "Primary input address is PO Box", "I912": "Entered SSN possibly randomly issued by the SSA", "I913": "Entered address is vacant", "R901": "SSN cannot be resolved to an entity", "R902": "KYC record validation failed on Name", "R903": "KYC record validation failed on Date of Birth", "R904": "KYC record validation failed on Address", "R905": "KYC record validation failed on Mobile Number", "R906": "KYC record validation failed on Driver License", "R907": "Entered SSN reported as deceased", "R908": "Some ID elements not verified", "R909": "Entered identity reported as deceased", "R911": "Entered SSN issued prior to entered date of birth", "R912": "Entered last name and SSN verified but not with entered address and phone", "R913": "SSN improperly formatted or cannot exist", "R914": "Entered phone number may be disconnected", "R915": "Entered phone number potentially invalid", "R916": "Entered address may be invalid according to postal specifications", "R917": "Entered phone number and input ZIP code combination invalid", "R918": "Unable to verify name, address, SSN/TIN and phone", "R919": "Unable to verify address", "R920": "SSN/TIN cannot be resolved to an entity", "R921": "Unable to verify phone number", "R922": "Unable to verify date of birth", "R923": "Entered SSN/TIN possibly miskeyed", "R924": "Entered address possibly miskeyed", "R925": "Entered phone number possibly miskeyed", "R926": "Entered name on OFAC watchlist", "R927": "Unable to verify name", "R928": "Entered SSN associated with multiple last names", "R929": "Entered driver license number invalid for entered driver license state", "R930": "Unable to verify first name", "R931": "Entered phone and address geographically distant (>10 miles)", "R932": "Entered address matches prison address", "R933": "Entered last name not associated with entered SSN", "R934": "Entered first name not associated with entered SSN", "R935": "Entered home phone and work phone geographically distant (>100 miles)", "R936": "Entered work phone potentially invalid", "R937": "Entered work phone potentially disconnected", "R938": "Entered address returns different phone number", "R939": "Entered SSN associated with different last name and same first name", "R940": "Entered SSN not found in public records", "R941": "Entered SSN associated with different name and address", "R942": "Entered phone number associated with different name and address", "R943": "Entered name and address associated with an unlisted/non-published phone number", "R944": "Entered name poss<PERSON>y miskeyed", "R945": "Entered name and address return different phone number", "R946": "Entered date of birth possibly miskeyed", "R947": "Entered SSN not primary SSN for entered identity", "R948": "Address mismatch between city/state and ZIP code", "R949": "Different driver license number found for applicant", "R950": "Entered driver license number valid but not on record", "R951": "Entered driver license number possibly miskeyed", "R952": "Unable to verify driver license number", "R953": "Entered SSN possibly randomly issued by SSA but invalid when first associated with entered identity", "R954": "Entered SSN is an ITIN", "R955": "Multiple identities associated with input SSN", "R956": "Multiple SSNs reported with applicant", "R957": "No date of birth reported for entered identity", "R958": "Entered address State differs from best associated State for input identity  ", "R960": "Entered name match on non-OFAC global watchlists", "R961": "Unable to verify ZIP code", "R962": "Entered phone number is pager number", "R963": "Entered ZIP code belongs to PO Box", "R964": "Entered address is transient commercial or institutional address", "R965": "Entered phone number matches transient commercial or institutional address", "R966": "Entered SSN recently issued", "R967": "Entered phone area code changing", "R968": "Entered work phone is pager number", "R969": "Entered work phone is mobile number", "R970": "Entered SSN issued within last three years", "R971": "Entered SSN issued after age five, post-1990", "R972": "Primary input address is Commercial Mail Receiving Agency", "R973": "Entered ZIP code is corporate-only", "I914": "First name, surname and address match KYC data from NAP correlation analysis", "I915": "First name, surname and phone match KYC data from NAP correlation analysis", "I916": "First name, surname, address and phone match KYC data from NAP correlation analysis", "I917": "First name, surname and address match KYC data from NAS correlation analysis", "I918": "First name, surname and SSN match KYC data from NAS correlation analysis", "I919": "First name, surname, address and SSN match KYC data from NAS correlation analysis", "I920": "Emerging Identity Indicator"}, "watchlist": {"I196": "No watchlist match", "R186": "Watchlist match"}, "others": {"R350": "Date of birth suggests <16 years old or >100 years old", "R352": "Online sources could not validate date of birth"}, "phone": {"I601": "Phone is landline", "I602": "Phone is mobile", "I603": "Phone is fixed VoIP or traditional VoIP", "I604": "Phone is toll-free", "I605": "Phone is premium", "I606": "Phone is non-fixed-VoIP or over-the-top VoIP", "I607": "Phone is voicemail", "I608": "Phone type is commercial or dual purpose", "I609": "Phone type is consumer/residential", "I610": "Phone is prepaid", "I611": "Phone is associated with a major US carrier", "I612": "Phone is not associated with a major US carrier", "I614": "Phone in service >90 days", "I615": "Phone in service <90 days", "I616": "Phone is associated with an MVNO", "I618": "Name correlates to phone", "R601": "Address does not correlate to phone", "R602": "Country code of address associated with phone does not match input country", "R603": "Phone disconnected, delisted, disabled, not active or not in service", "R604": "Phone service discontinued <90 days", "R605": "Phone service discontinued >90 days", "R606": "Phone not actively used", "R607": "Address associated with phone only partially matches input address", "R608": "Name does not correlate to phone", "R609": "Phone time zone different from input address time zone", "R611": "Phone number is not allowed for consumers", "R702": "Postal state code associated with phone different from input address"}, "ip": {"I617": "IP is known proxy", "R610": "IP address geolocation >100 miles from input address"}, "social_network_profile": {"I121": "Social network(s) match", "R106": "Identity element in Alert List", "I126": "Name part of email", "I127": "External source(s) match", "I129": "Entity resolution succeeded", "I130": "Facebook account abandoned", "I230": "Facebook account match", "I131": "Facebook insufficient account history", "I132": "Facebook connections are unnatural", "I232": "Facebook connections are good", "I133": "Facebook activity is unnatural", "I233": "Facebook activity is good", "I134": "Facebook account unmaintained", "I135": "Different Facebook IDs found", "I136": "Facebook account suspended", "I140": "Twitter account abandoned", "I240": "Twitter account match", "I141": "Twitter insufficient account history", "I241": "Twitter good account history", "I142": "Twitter connections are unnatural", "I242": "Twitter connections are good", "I143": "Twitter activity is unnatural", "I243": "Twitter activity is good", "I144": "Twitter account unmaintained", "I244": "Twitter account is well maintained", "I145": "Different Twitter IDs found", "I146": "Twitter account suspended", "I250": "LinkedIn account match", "I152": "LinkedIn connections are unnatural", "I252": "LinkedIn connections are good", "I153": "LinkedIn activity is unnatural", "I253": "LinkedIn activity is good", "I154": "LinkedIn account unmaintained", "I254": "LinkedIn account is well maintained", "I355": "Different LinkedIn IDs found", "I356": "LinkedIn account suspended", "I160": "GooglePlus account abandoned", "I260": "GooglePlus account match", "I161": "GooglePlus insufficient account history", "I261": "GooglePlus good account history", "I162": "GooglePlus connections are unnatural", "I262": "GooglePlus connections are good", "I163": "GooglePlus activity is unnatural", "I263": "GooglePlus activity is good", "I164": "GooglePlus account unmaintained", "I264": "GooglePlus account is well maintained", "I165": "Different GooglePlus IDs found", "I166": "GooglePlus account suspended", "I155": "LinkedIn headline is empty", "I156": "LinkedIn number of skills >10", "I157": "LinkedIn profile currently employed", "I158": "LinkedIn duration of most recent experience >30 days", "I159": "LinkedIn profile has industry", "I255": "LinkedIn days since last update >180", "I256": "LinkedIn at least one education degree", "I257": "LinkedIn number of connections >5", "I171": "Pinterest days since last update >180", "I172": "Pinterest number of followers >5", "I173": "Pinterest number of following >5", "I174": "Pinterest number of social networks >1", "I175": "Pinterest number of Likes >1", "I176": "Pinterest number of Pins >1", "I177": "Pinterest number of Boards >1", "I178": "Pinterest profile has image", "I180": "Pinterest account match", "I181": "Pinterest connections are unnatural", "I182": "Pinterest connections are good", "I183": "Pinterest activity is unnatural", "I184": "Pinterest activity is good", "I185": "Pinterest account unmaintained", "I186": "Pinterest account is well maintained", "I317": "Age could not be determined", "I318": "Age of person <21 years old", "I319": "Age of person >80 years old", "I322": "At least one secondary phone associated with profile >30 days old", "I325": "More than 1 Educational Degree found associated with profile", "I326": "More than 1 job found associated with profile", "I327": "More than 1 Phone found associated with profile", "I329": "No Educational Degree found associated with profile", "I330": "No employing organization found", "I331": "No job title found", "I332": "No profile image found", "I333": "One or more relationships found associated with profile", "I334": "Origin country different from primary address country", "I270": "Viadeo account abandoned", "I271": "Viadeo insufficient account history", "I272": "Viadeo connections are unnatural", "I273": "Viadeo activity is unnatural", "I274": "Viadeo account unmaintained", "I275": "Viadeo account active within 180 days", "I276": "Viadeo account updated within 180 days", "I277": "Viadeo profile has valid name", "I278": "Viadeo profile has image", "I279": "Viadeo profile has resume", "I280": "Viadeo profile shows employment history", "I281": "Viadeo profile current job <90 days old", "I282": "Viadeo profile is connected to groups", "I283": "Viadeo number of contacts is normal", "I284": "Viadeo profile is a Premium member", "I290": "Xing account abandoned", "I291": "Xing account history insufficient", "I292": "Xing connections are unnatural", "I293": "Xing activity is unnatural", "I294": "Xing account unmaintained", "I295": "Xing account active within 180 days", "I296": "Xing account updated within 180 days", "I297": "Xing profile has valid name", "I298": "Xing profile shows employment history", "I299": "Xing profile shows current employment", "R113": "Entity resolution failed for provided gender"}, "validation": {"V101": "State must be two characters", "V102": "Country code must meet ISO 3166-1 alpha-2 character standard", "V103": "State permitted only when country=US", "V104": "Physical address requires at least ZIP/country or city/country", "V105": "Physical address and city in US requires at least ZIP or state", "V106": "Country code required unless physical address, city, state, and ZIP ", "V107": "Supported date of birth formats are yyyyMMdd, yyyy-MM-dd, and yyyy/MM/dd", "V108": "IP address must meet IPV4 or IPV6 standard", "V109": "Invalid geocode format", "V110": "Supported phone number formats are +*********** and +**************", "V111": "Missing Required fields: firstname/surname", "V112": "Missing Required field: email", "V113": "Missing Required field: provider", "V114": "Missing Required field: <PERSON><PERSON><PERSON>", "V115": "Missing Required field: accesstokensecret", "V116": "National ID permitted only when country=US", "V117": "Invalid driver license format", "V118": "Invalid National ID format", "V119": "Invalid Image file", "E101": "Data source lookup timed out"}}, "error_codes": {"701": "Missing Required field: email", "702": "Invalid geocode format", "703": "Supported date of birth formats are yyyyMMdd, yyyy-MM-dd, and yyyy/MM/dd", "704": "Country code required unless physical address, city, state, and zip are specified", "705": "IP address must meet IPV or IPV standard", "706": "State is only permitted when country=US", "707": "State must be two characters", "708": "Supported phone number formats are + and +   ", "801": "Possible blacklist reasons are <PERSON><PERSON>, Violated ToS, Suspected of fraud, Committed fraud, Chargeback <PERSON><PERSON>, First Payment Default <PERSON>, Identity Fraud and Fake ID.", "802": "Possible blacklist reasons are <PERSON><PERSON>, <PERSON>ted ToS, Suspected of fraud, ..", "803": "The user identity associated to this access token is not in any blacklist as a potential or known fraudulent identity. If you believe this is in error, <NAME_EMAIL> for resolution.", "804": "No user authorization found", "805": "Unable to find user.", "806": "The user identity associated to this access token is not in any blacklist as a potential or known fraudolent identity. If you believe this is in error, <NAME_EMAIL> for resolution.", "807": "Already added to blacklist", "808": "The user identity is blacklisted as <PERSON><PERSON>. If you believe this is in error, <NAME_EMAIL> for resolution.", "809": "Required Reason Code input missing", "810": "Your account has been created successfully. A Socure sales representative will be in touch with you shortly.", "811": "An account with this email address already exists", "813": "Api v1 is deprecated please use api v2.5 https://service.socure.com/api/2.5", "901": "InternalServerException", "902": "MissingRequiredParametersException", "903": "MissingParameterOptionsException", "904": "InsufficientPermissionException", "905": "DomainNotAuthorizedException", "906": "Fraudscore internal Error", "907": "StillProcessingException", "908": "InvalidRequestParametersException", "909": "AccountNotActivatedException", "910": "NoAccountAssociatedException", "911": "InvalidTokenException", "912": "Missing Required fields: firstname/surname", "913": "Missing Required field: <PERSON><PERSON><PERSON>", "914": "Commonwealth/Territory and Military states are not supported", "915": "Driver License State is required for Driver License validation", "916": "Driver License state must be the 2 letter state abbreviation", "917": "Invalid date format yyyy-MM-dd, yyyy/MM/dd or yyyyMMdd", "918": "Invalid email address", "919": "Invalid State/Province Code", "920": "InvalidArgumentException", "921": "KYC is deprecated for v2.0. Use v2.5 for KYC queries.", "922": "KYC or Watchlist processing error. Please retry transaction using forcerefresh=true", "923": "Missing Required field: country", "924": "Missing Required field: firstname/surname", "926": "Please use only ISO 3166-1 alpha-2 codes for country", "927": "UnknownProviderException", "928": "Accounts with PII Masking cannot use blacklist functions"}}