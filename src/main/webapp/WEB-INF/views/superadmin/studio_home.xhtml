<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>

    <style>

        .topcorner{
            position:absolute;
            top:210px;
            right: 25px;
        }

        .pad {
          margin-top: 10px;
          margin-bottom: 10px;
        }

        .createrulecodedialog{
            margin-top: 10px;
            margin-bottom: 10px;
            margin-left: 20px;
            padding: 20px;
        }

        .button-align {
          margin-left: 40px;
        }

        th, td {
            padding: 10px;
        }

        #sideNav a {
          position: absolute;
          left: -80px;
          transition: 0.3s;
          padding: 15px;
          width: 100px;
          text-decoration: none;
          font-size: 20px;
          color: white;
          border-radius: 0 5px 5px 0;
        }

        #sideNav a:hover {
          left: 0;
        }

        #studioSideNav {
          top: 300px;
          background-color: #555;
        }

        #testSideNav {
          top: 380px;
          background-color: #555;
        }

        #popupbox{
            margin: 0;
            margin-left: 40%;
            margin-right: 40%;
            margin-top: 25%;
            padding-top: 10px;
            width:500px;
            height:300px;
            z-index: 2;
            font-family: arial;
            visibility: hidden;
        }

        #text{

          top: 50%;
          left: 50%;
          font-size: 50px;
          color: white;
          transform: translate(-50%,-50%);
          -ms-transform: translate(-50%,-50%);
        }

        #overlay {
          position: fixed;
          display: none;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0,0,0,0.5);
          z-index: 2;
          cursor: pointer;
        }

        /* The popup form - hidden by default */
        .form-popup-rulecode {
          display: none;
          position: fixed;
          border: 3px solid #f1f1f1;
          left: 400px;
          top: 150px;
          width:500px;
          height:200px;
          z-index: 9;
        }

        .form-popup {
          display: none;
          position: fixed;
          border: 3px solid #f1f1f1;
          left: 600px;
          top: 200px;
          width:500px;
          height:200px;
          z-index: 9;
        }

        .form-popup-dependency {
          display: none;
          position: fixed;
          border: 3px solid #f1f1f1;
          left: 400px;
          top: 150px;
          width:500px;
          height:700px;
          z-index: 9;
          overflow-y: scroll;
        }

        /* Add styles to the form container */
        .form-container {
          padding: 10px;
          background-color: white;
          height:500px;
          overflow-y: scroll;
        }

        /* Add styles to the form container */
        .form-container-bulk {
          height:250px;
        }

        /* Full-width input fields */
        .form-container input[type=text], .form-container select {
          width: 100%;
          padding: 5px;
          margin: 5px 0 22px 0;
          border: none;
          background: #f1f1f1;
        }

        /* When the inputs get focus, do something */
        .form-container input[type=text]:focus, .form-container select:focus {
          background-color: #ddd;
          outline: none;
        }

        /* Set a style for the submit/login button */
        .form-container .btn {
          background-color: #555;
          color: white;
          padding: 16px 20px;
          border: none;
          cursor: pointer;
          width: 50%;
          margin-bottom:10px;
          opacity: 0.8;

        }

        .form-container .link-button {
            background-color: #555;
            border: none;
            color: white;
            display: inline-block;
            width: 50%;
            margin-bottom:10px;
            opacity: 0.8;
            padding: 16px 20px;
            cursor: pointer;
        }

        /* Add a red background color to the cancel button */
        .form-container .cancel {
          background-color: #555;
        }

        /* Add some hover effects to buttons */
        .form-container .btn:hover, .open-button:hover {
          opacity: 1;
        }

        .button-pad{
            margin-left :50px;
        }


    </style>
</h:head>
<h:body>

    <div id="studioHomePage">
        <br/>
        <button id="pullConfigButton" onclick="pullConfig()" >Deploy Lastest config</button>
        <button id="create_rulecode_popup" class="button-pad" onclick="openCreateRuleCodeForm('')" > Create New Rulecode </button>
        <div class="form-popup-rulecode" id="create_rulecode_container">
            <form action="" class="form-container">
                <label><b>Rulecode Name</b></label>
                <input type="text" id="rulecodeName"/>

                <label><b>Operator</b></label>
                <label id="operatorDesc"></label>
                <select id="operator" onchange="selectOperator(this)">
                    <option value="default" selected="selected">Choose operator</option>
                </select>

                <label><b>Inputs</b></label>
                <label id="inputDesc"></label>
                <input type="text" id="inputs"/>
                <label></label>

                <div id="operatorConfig" >
                    <b>Add Operator Config (Optional)</b> &nbsp;&nbsp;&nbsp; <button id ="addOperatorConfigBtn" type="button" onclick="addOperatorConfig()">+</button>
                </div>

                <div>
                    <br/>
                    <button type="button" class="btn" id="create_submit_btn" value="" onclick="createRulecode()" >Submit</button>
                    <button type="button" class="btn cancel" onclick="closeCreateRulecodeForm()">Close</button>
                </div>
            </form>
        </div>

        <div class="form-popup-rulecode" id="add_vendor_config_container">
            <form action="" class="form-container">

                <label><b>Prefix</b></label>
                <select id="prefix"></select>

                <div id="vendorConfig" >
                    <b>Add Vendor Config (Optional)</b> &nbsp;&nbsp;&nbsp; <button id ="addVendorConfigBtn" type="button" onclick="addVendorConfig()">+</button>
                </div>

                <button type="button" class="btn" id="add_vendor_config_submit" value="" onclick="submitVendorConfig()" >Add Config</button>
                <button type="button" class="btn cancel" onclick="closeAddVendorConfigForm()">Close</button>
            </form>
        </div>

        <button id="create_bulk_rulecode" class="button-pad" onclick="openCreateBulkRuleCodeForm()" > Bulk Create Rulecode </button>
        <div class="form-popup-rulecode" id="create_rulecode_bulk_container">
            <form action="" class="form-container form-container-bulk">
                <label><b>FileName</b></label>
                <input type="text" id="bulkFileName"/>
                <button type="button" class="btn" id="create_bulk" value="" onclick="bulkcreateRulecodes()" >Submit</button>
                <button type="button" class="btn cancel" onclick="closeCreateBulkRulecodeForm()">Close</button>
            </form>
        </div>


        <table id="rulecodes" border="2" align="center">
            <thead>
            <tr> <th>Rulecode</th> <th>Operation Name</th>  <th>Inputs</th> <th>Operator Config</th> <th>Deployment Status</th>  <th>Edit</th> </tr>
            </thead>
            <tbody>
            </tbody>
        </table>

    </div>

</h:body>
</html>
