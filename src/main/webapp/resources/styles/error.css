@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\0000317}}
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video { margin: 0; padding: 0; border: 0; font: inherit; font-size: 100%; vertical-align: baseline; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\0000322}}
html { line-height: 1; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\0000324}}
ol, ul { list-style: none; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\0000326}}
table { border-collapse: collapse; border-spacing: 0; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\0000328}}
caption, th, td { text-align: left; font-weight: normal; vertical-align: middle; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\0000330}}
q, blockquote { quotes: none; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\00003103}}
q:before, q:after, blockquote:before, blockquote:after { content: ""; content: none; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\0000332}}
a img { border: none; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Library\/Ruby\/Gems\/1\.8\/gems\/compass-0\.12\.2\/frameworks\/compass\/stylesheets\/compass\/reset\/_utilities\.scss}line{font-family:\00003116}}
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary { display: block; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000329}}
body { font: 100% "Droid Sans", Helvetica, Arial, sans-serif; color: #222; background: #FFF; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000335}}
a { color: #808080; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000337}}
.no-touch a { text-decoration: none; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000340}}
a:hover { color: #222; text-decoration: underline; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000346}}
h2 { font-size: 1.75em; line-height: 130%; letter-spacing: -0.04em; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000352}}
h3 { margin-bottom: 30px; font-weight: bold; font-size: 1.125em; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000358}}
h4 { font-weight: bold; margin-bottom: 0.5em; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000363}}
p { margin-bottom: 1em; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000369}}
#logo a { text-indent: 100%; white-space: nowrap; width: 148px; height: 36px; background: url('../assets/images/shared/logo.png?1365578719') no-repeat 0 0; display: block; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000382}}
#socialConnect ul { width: 504px; margin: 0 auto; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000311}}
#socialConnect ul:before, #socialConnect ul:after { content: "\0020"; display: block; height: 0; overflow: hidden; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000317}}
#socialConnect ul:after { clear: both; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000388}}
#socialConnect ul li { float: left; width: 90px; margin: 0 18px; -webkit-transition-property: opacity; -moz-transition-property: opacity; -o-transition-property: opacity; transition-property: opacity; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\0000398}}
#socialConnect ul li:hover strong { border-color: #C9C9C9; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003102}}
#socialConnect ul li:hover span { color: #333; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003109}}
#socialConnect ul li.disabled, #socialConnect ul li[disabled] { filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40); opacity: 0.4; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003113}}
#socialConnect ul li a { text-decoration: none; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003117}}
#socialConnect ul li .so-icon { display: block; width: 88px; height: 88px; background: #FFF; border: 1px solid #cccccc; -webkit-border-radius: 45px; -moz-border-radius: 45px; -ms-border-radius: 45px; -o-border-radius: 45px; border-radius: 45px; -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.2); -moz-box-shadow: 0 0 3px rgba(0, 0, 0, 0.2); box-shadow: 0 0 3px rgba(0, 0, 0, 0.2); }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003126}}
#socialConnect ul li .so-icon.loading { background: url('../assets/images/shared/icons/loading.gif?1365578719') no-repeat center center; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003136}}
#socialConnect ul .so-fb .so-icon { background: url('../assets/images/shared/icons/social/big/fb.png?1365578719') no-repeat center center; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003136}}
#socialConnect ul .so-tw .so-icon { background: url('../assets/images/shared/icons/social/big/tw.png?1365578719') no-repeat center center; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003136}}
#socialConnect ul .so-in .so-icon { background: url('../assets/images/shared/icons/social/big/in.png?1366705573') no-repeat center center; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003136}}
#socialConnect ul .so-gp .so-icon { background: url('../assets/images/shared/icons/social/big/gp.png?1365578719') no-repeat center center; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003145}}
#footer { text-align: center; padding: 30px 0; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/base\.scss}line{font-family:\00003150}}
#footer p { font-size: 0.813em; color: #888; display: inline-block; padding: 10px 20px; border: 1px solid #dddddd; -webkit-border-radius: 3px; -moz-border-radius: 3px; -ms-border-radius: 3px; -o-border-radius: 3px; border-radius: 3px; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/error\.scss}line{font-family:\000033}}
body { background: url('../assets/images/404/crowd.jpg?1365578719') no-repeat center top; }

@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/error\.scss}line{font-family:\000037}}
#error404 { text-align: center; width: 320px; padding: 30px 30px 35px; margin: 80px auto 0; background: #FFF; border: 1px solid #cccccc; -webkit-border-radius: 3px; -moz-border-radius: 3px; -ms-border-radius: 3px; -o-border-radius: 3px; border-radius: 3px; -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); -moz-box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/error\.scss}line{font-family:\0000318}}
#error404 #logo { width: 148px; margin: 0 auto 20px; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/error\.scss}line{font-family:\0000323}}
#error404 h2 { font-size: 1.5em; font-weight: bold; padding: 30px 0 20px; border-top: 1px solid #dddddd; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/error\.scss}line{font-family:\0000330}}
#error404 p { font-size: 0.875em; line-height: 130%; padding-bottom: 5px; }
@media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/error\.scss}line{font-family:\0000336}}
#error404 p a { color: #900; }

@media screen and (max-width: 500px) { @media -sass-debug-info{filename{font-family:file\:\/\/\/Users\/<USER>\/socure-repo\/web-app-ui\/scss\/error\.scss}line{font-family:\0000346}}
  #error404 { width: auto; margin: 40px 20px; } }
