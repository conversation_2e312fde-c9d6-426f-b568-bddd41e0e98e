package me.socure.sponsorbank

import me.socure.account.sponsorbank.{SponsorBankClient, SponsorBankClientFactory}
import me.socure.common.hmac.factory.HMACEncrypterFactory
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}

import scala.concurrent.ExecutionContext

class SponsorBankClientFactory extends FactoryBean[SponsorBankClient]{
  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  override def getObject: SponsorBankClient = {

    val hmacEncrypter = HMACEncrypterFactory.get(
      secretKey = conf("hmac.secret.key"),
      keyStrength = conf("hmac.strength").toInt
    )

    val client = SponsorBankClientFactory.create(
      realm = conf("hmac.realm"),
      version = conf("hmac.version"),
      endpoint = conf("endpoint"),
      encrypter = hmacEncrypter
    )(executionContext)

    client
  }

  private def conf(prop: String): String = {
    envUtil.getProperty(s"account.service.$prop")
  }

  override def getObjectType: Class[SponsorBankClient] = classOf[SponsorBankClient]

  override def isSingleton: Boolean = true
}
