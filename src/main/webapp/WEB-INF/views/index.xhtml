<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Socure Dashboard</title>

	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		media="print" />
	<script>
		bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		appenv = "#{bucket.appEnv}";
	</script>

	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>

<h:body class="">
	<header id="DashboardNavbar"
		class="navbar navbar-fixed-top navbar-notch">
	<div class="navbar-inner">
		<a id="SocureLogo" href="#{request.contextPath}/" class="brand">SOCURE</a>

		<nav class="nav-notch-wrapper">
		<ul id="DashboardNav" class="nav nav-notch">
			<li class="active"><h:panelGroup layout="block"
					rendered="#{request.isUserInRole('ROLE_ADMIN') or request.isUserInRole('ROLE_REPORTS')}">
					<a href="#{request.contextPath}/">Reports </a>
				</h:panelGroup></li>
			<li><h:panelGroup layout="block"
					rendered="#{request.isUserInRole('ROLE_ADMIN') or request.isUserInRole('ROLE_SETTINGS')}">
					<a href="#{request.contextPath}/settings">Settings</a>
				</h:panelGroup></li>
			<li><h:panelGroup layout="block"
					rendered="#{request.isUserInRole('ROLE_ADMIN') or request.isUserInRole('ROLE_DEVELOPER')}">
					<a href="#{request.contextPath}/developer">Developer</a>
				</h:panelGroup></li>
			<li><h:panelGroup layout="block"
					rendered="#{request.isUserInRole('ROLE_ADMIN') or request.isUserInRole('ROLE_FEEDBACK')}">
					<a href="#{request.contextPath}/feedback">Feedback</a>
				</h:panelGroup></li>
				<li><h:panelGroup layout="block"
					rendered="#{request.isUserInRole('ROLE_ADMIN') or request.isUserInRole('ROLE_TRAINING')}">
					<a href="#{request.contextPath}/training">Training</a>
				</h:panelGroup></li>
			<!-- <li><a href="#{request.contextPath}/settings">Settings</a></li>
			<li><a href="#{request.contextPath}/developer">Developer</a></li> -->
			<li><div style="width: 350px;"></div></li>
		</ul>
		<span class="notch"> <span class="notch-img"></span>
		</span> </nav>
		<ul id="AccountNav" class="nav pull-right">
			<li><h:panelGroup layout="block"
					rendered="#{request.isUserInRole('ROLE_ADMIN')}">
					<a id="DelegateAdmin" class="icn-multi-user"
						href="javascript:list_delegate_admin();"></a>
				</h:panelGroup></li>
			<li><a id="UserName" href="#AccountModal" data-toggle="modal"
				data-target="#AccountModal"></a></li>
			<li><a id="LogInOut" href="#{request.contextPath}/logout">Log
					Out</a></li>
		</ul>
	</div>
	</header>

	<section id="Reports" class="dashboard"> <header
		id="FilterNavbar" class="navbar navbar-inverse navbar-static-top">
	<div class="navbar-inner">

		<div class="navbar-block controls-date">
			<span class="navbar-label">Report for</span> <span
				class="navbar-controls view"> <span class="date"><time
						id="StartDate"></time> to <time id="EndDate"></time></span> <a href="#">Change
					Dates</a>
			</span> <span class="navbar-controls edit"> <span
				class="datetime-input"><input type="datetime"
					class="datepicker startTime"></input></span> to <span
				class="datetime-input"><input type="datetime"
					class="datepicker endTime"></input></span> <a
				class="btn btn-dark-on-light" href="#">Run Report</a>
			</span>
		</div>

		<span class="divider-vertical"></span>

		<div class="navbar-block controls-segments">
			<span class="navbar-label">View report by</span>
			<ul id="FilterNav" class="nav">
				<li><a class="btn active" data-segment="network" href="#">Network</a></li>
				<li><a class="btn" data-segment="age" href="#">Age Group</a></li>
				<li><a class="btn" data-segment="gender" href="#">Gender</a></li>
				<li><a class="btn" data-segment="location" href="#">Location</a></li>
			</ul>
		</div>

		<span class="divider-vertical"></span>

		<div class="navbar-block controls-confidence">
			<span class="navbar-label">Confidence level</span>
			<ul class="nav">
				<li><a class="btn active" data-confidence="all" href="#">All</a></li>
				<li><a class="btn" data-confidence="high" href="#">High</a></li>
				<li><a class="btn" data-confidence="low" href="#">Low</a></li>
			</ul>
		</div>

		<span class="divider-vertical"></span>

		<div class="navbar-block controls-export">
			<ul class="nav">
				<li><a href="#" id="PrintLink" class="icn-print" title="Print"></a></li>
				<li><a href="#" id="ExportLink" class="icn-export"
					title="Export"></a></li>
			</ul>
		</div>

		<span class="divider-vertical last"></span>
	</div>
	</header>

	<div class="dashboard-row row-fluid">
		<div class="row-inner">
			<div id="AvgSide" class="dashboard-panel side span3">
				<div class="panel-inner">
					<div id="AvgScorePanel" class="panel-content overall active">

						<h4>Average Score</h4>
						<figure id="AvgScoreOverall" class="chart circle score"
							data-key="score"> <span class="value"></span> </figure>

						<h4>Total Profiles</h4>
						<figure id="TotalProfiles" class="chart number" data-key="total"></figure>

						<h4>Total Transactions</h4>
						<figure id="TotalTransactions" class="chart number"
							data-key="total_calls"></figure>

						<a id="ScoreGroupsBtn" class="btn panel-switch" href="#">View
							Score Groups <span class="icn-notch-right"></span>
						</a>

					</div>
					<div id="GroupsPanel" class="panel-content groups">

						<h4>Number of Profiles by Score Group</h4>
						<div class="score-group-row">
							<span id="GroupCircleLow" class="group-score"><span
								class="value">0-4</span></span>
							<figure id="GroupTotalLow" class="chart number group"
								data-key="range0_4"> <span class="value"></span> <small>profiles</small>
							</figure>
						</div>
						<div class="score-group-row">
							<span id="GroupCircleMid" class="group-score"><span
								class="value">5</span></span>
							<figure id="GroupTotalMid" class="chart number group"
								data-key="range5_7"> <span class="value"></span> <small>profiles</small>
							</figure>
						</div>
						<div class="score-group-row">
							<span id="GroupCircleHigh" class="group-score"><span
								class="value">6-10</span></span>
							<figure id="GroupTotalHigh" class="chart number group"
								data-key="range8_10"> <span class="value"></span> <small>profiles</small>
							</figure>
						</div>

						<a class="btn panel-switch" href="#"><span
							class="icn-notch-left"></span> Back to Average Score</a>

					</div>
					<div id="CitiesPanel" class="panel-content cities">

						<h4>Cities by Score Group</h4>
						<div class="score-group-row">
							<span id="CitiesCircleLow" class="group-score"><span class="value">0-4</span></span>
							<figure id="CitiesTotalLow" class="chart number group"
								data-key="lowTotal"> <span class="value"></span> <small>cities</small>
							</figure>
						</div>
						<div class="score-group-row">
							<span id="CitiesCircleMid" class="group-score"><span
								class="value">5</span></span>
							<figure id="CitiesTotalMid" class="chart number group"
								data-key="midTotal"> <span class="value"></span> <small>cities</small>
							</figure>
						</div>
						<div class="score-group-row">
							<span id="CitiesCircleHigh" class="group-score"><span
								class="value">6-10</span></span>
							<figure id="CitiesTotalHigh" class="chart number group"
								data-key="highTotal"> <span class="value"></span> <small>cities</small>
							</figure>
						</div>

					</div>
				</div>
			</div>

			<div id="AvgMain" class="dashboard-panel main span9">
				<div class="panel-inner">
					<h3>
						<span class="mode">Average Score</span> <small>by <span
							class="segment">Network</span></small>
					</h3>
					<label class="toggle-gender"><input id="GenderBreakdown"
						type="checkbox" />Break Down by Gender</label>
					<div class="panel-content network active">
						<ul class="legend groups-legend">
							<li><span class="swatch low"></span> 0-4</li>
							<li><span class="swatch mid"></span> 5</li>
							<li><span class="swatch high"></span> 6-10</li>
						</ul>
						<figure id="AvgScoreByNetwork" class="chart bar"
							data-segment="network"> <span class="axis-label y">Score</span>
						<span class="axis-label x">Network</span> </figure>
					</div>
					<div class="panel-content age">
						<ul class="legend groups-legend">
							<li><span class="swatch low"></span> 0-4</li>
							<li><span class="swatch mid"></span> 5</li>
							<li><span class="swatch high"></span> 6-10</li>
						</ul>
						<figure id="AvgScoreByAge" class="chart bar" data-segment="age">
						<span class="axis-label y">Score</span> <span class="axis-label x">Age
							Groups</span> </figure>
					</div>
					<div id="AvgScoreByGenderPanel" class="panel-content gender">
						<div class="panel-section">
							<ul class="legend groups-legend">
								<li><span class="swatch low"></span> 0-4</li>
								<li><span class="swatch mid"></span> 5</li>
								<li><span class="swatch high"></span> 6-10</li>
							</ul>
							<h4>Males</h4>
							<figure class="chart circle score" data-gender="male"
								data-key="score"> <span class="value"></span> </figure>
							<small># of Profiles</small>
							<figure class="chart number" data-gender="male" data-key="total"></figure>
						</div>
						<div class="panel-section">
							<h4>Females</h4>
							<figure class="chart circle score" data-gender="female"
								data-key="score"> <span class="value"></span> </figure>
							<small># of Profiles</small>
							<figure class="chart number" data-gender="female"
								data-key="total"></figure>
						</div>
						<div class="panel-section">
							<h4>Unknown</h4>
							<figure class="chart circle score" data-gender="unknown"
								data-key="score"> <span class="value"></span> </figure>
							<small># of Profiles</small>
							<figure class="chart number" data-gender="unknown"
								data-key="total"></figure>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="dashboard-row row-fluid">
		<div class="row-inner">
			<div id="AuthSide" class="dashboard-panel side span3">
				<div class="panel-inner">
					<div class="panel-content active">
						<h4>Authenticity</h4>
						<figure id="AuthenticityOverall" class="chart circle percent"
							data-key="realPercentage"> <span class="value"></span> </figure>

						<h4>Total Real Profiles</h4>
						<figure id="TotalRealProfiles" class="chart number number-total"
							data-key="real" data-sub-key="total"></figure>
					</div>
					<figure id="ResponseTime" class="chart response"
						data-key="res_time">API Call Response Time: <span
						class="value"></span> ms</figure>
				</div>
			</div>

			<div id="AuthMain" class="dashboard-panel main span9">
				<div class="panel-inner">
					<h3>
						Authenticity <small>by <span class="segment">Network</span></small>
					</h3>
					<div class="panel-content network">
						<ul class="legend">
							<li><span class="swatch fb"></span> Facebook</li>
							<li><span class="swatch tw"></span> Twitter</li>
							<li><span class="swatch in"></span> LinkedIn</li>
							<li><span class="swatch gp"></span> Google+</li>
							<li><span class="swatch email"></span> Email</li> 
						</ul>
						<figure id="AuthByNetwork" class="chart line"> <span
							class="axis-label y"># of Fake Profiles</span> <span
							class="axis-label x">Time</span> </figure>
					</div>
					<div class="panel-content age">
						<ul class="legend">
							<li><span class="swatch real"></span> Real Profiles</li>
							<li><span class="swatch fake"></span> Fake Profiles</li>
						</ul>
						<figure id="AuthByAge" class="chart bar"> <span
							class="axis-label y"># of Profiles</span> <span
							class="axis-label x">Age Groups</span> </figure>
					</div>
					<div id="AuthByGenderPanel" class="panel-content gender">
						<div class="panel-section">
							<h4>Males</h4>
							<figure class="chart circle percent" data-gender="male"
								data-key="realPercentage"> <span class="value"></span>
							</figure>
							<small>Real Profiles</small>
							<figure class="chart number" data-gender="male" data-key="real"></figure>
						</div>
						<div class="panel-section">
							<h4>Females</h4>
							<figure class="chart circle percent" data-gender="female"
								data-key="realPercentage"> <span class="value"></span>
							</figure>
							<small>Real Profiles</small>
							<figure class="chart number" data-gender="female" data-key="real"></figure>
						</div>
						<div class="panel-section">
							<h4>Unknown</h4>
							<figure class="chart circle percent" data-gender="unknown"
								data-key="realPercentage"> <span class="value"></span>
							</figure>
							<small>Real Profiles</small>
							<figure class="chart number" data-gender="unknown"
								data-key="real"></figure>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div id="Location"></div>
	</section>

	<section id="Loading" class="overlay show">
	<p>Loading Dashboard...</p>
	<div class="loader">
		<img src="#{bucket.resourceURL}/assets/images/socure-loader.svg" /> <span
			class="dot"></span>
	</div>
	</section>

	<section id="Intro" class="overlay"> <img
		class="intro-pointer"
		src="#{bucket.resourceURL}/assets/images/intro-pointer.svg" />
	<div class="intro-page active">
		<h2>
			Welcome, <span class="username">USER</span>
		</h2>
		<h3>Here’s what you need to do to get your account set up:</h3>
		<ul class="intro-steps">
			<h:panelGroup layout="block"
				rendered="#{request.isUserInRole('ROLE_ADMIN') or request.isUserInRole('ROLE_SUPER_ADMIN') or request.isUserInRole('ROLE_SETTINGS')}">
				<li><a class="intro-hover"
					href="#{request.contextPath}/settings"> <img
						src="#{bucket.resourceURL}/assets/images/intro-orange.png"
						alt="Settings" />
				</a>
					<p>
						Configure your accounts on the <a
							href="#{request.contextPath}/settings">Settings page.</a>
					</p></li>
			</h:panelGroup>

			<h:panelGroup layout="block"
				rendered="#{!request.isUserInRole('ROLE_ADMIN') and !request.isUserInRole('ROLE_SUPER_ADMIN') and !request.isUserInRole('ROLE_SETTINGS')}">
				<li><a class="intro-hover" href="#"> <img
						src="#{bucket.resourceURL}/assets/images/intro-orange.png"
						alt="Settings" />
				</a>
					<p style="visibility: hidden;">
						Configure your accounts on the <a href="#">Settings page.</a>
					</p></li>
			</h:panelGroup>
			<li><img
				src="#{bucket.resourceURL}/assets/images/intro-green.png"
				alt="Gather Data" />
				<p>Wait for users to start connecting with Socure!</p></li>
			<h:panelGroup layout="block"
				rendered="#{request.isUserInRole('ROLE_ADMIN') or request.isUserInRole('ROLE_SUPER_ADMIN') or request.isUserInRole('ROLE_DEVELOPER')}">
				<li><a class="intro-hover"
					href="#{request.contextPath}/developer"> <img
						src="#{bucket.resourceURL}/assets/images/intro-navy.png"
						alt="Developer" />
				</a>
					<p>
						Test requests to our API in the <a
							href="#{request.contextPath}/developer">Developer</a> section.
					</p></li>
			</h:panelGroup>

			<h:panelGroup layout="block"
				rendered="#{!request.isUserInRole('ROLE_ADMIN') and !request.isUserInRole('ROLE_SUPER_ADMIN') and !request.isUserInRole('ROLE_DEVELOPER')}">
				<li><a class="intro-hover" href="#"> <img
						src="#{bucket.resourceURL}/assets/images/intro-navy.png"
						alt="Developer" />
				</a>
					<p style="visibility: hidden;">
						Test requests to our API in the <a href="#">Developer</a> section.
					</p></li>
					
				</h:panelGroup>

			
		</ul>
		<a class="btn intro-btn btn-dark-on-light intro-next">Next</a>
	</div>
	<div class="intro-page">
		<h3>Once you start using Socure's APIs, you'll get a clear view
			of your enterprise's fraud footprint with segmentation details.</h3>
		<a class="btn intro-btn btn-dark-on-light intro-close">Close and
			Go To My Dashboard</a>
		<p>
			<img src="#{bucket.resourceURL}/assets/images/intro-screenshot.jpg" />
		</p>
	</div>
	</section>

	<div id="AccountModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">My Account</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="modal-section">
						<p>
							<span class="label">Email</span><span id="UserEmail"></span>
						</p>
					</div>
					<div class="modal-section">
						<p class="change-password-link">
							<span class="label">Password</span><a href="#" id="ChangePassword">Change
							Password</a>
						</p>
						<div class="change-password">
							<label>Current Password <input id="CurrentPassword"
														   type="password" /></label>
							<label>New Password <input
								id="NewPassword" type="password" /></label>
						</div>
						<p class="password-msg"></p>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary save-new-password">Save changes</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<div id="MessageModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="information" style="padding-top: 15px;">
						<label id="delete_msg"></label>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="delete_delegate_admin(false);">Delete</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div id="DelegatedAdminModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="modal-section">
						<div class="accordion" id="accordion2"></div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" data-toggle="modal"
							data-target="#AddDelegatedAdminModal" data-dismiss="modal">Add New</button>
					<button type="button" class="btn btn-secondary" onclick="show_delete_dialog(false);" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<div id="AddDelegatedAdminModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="message" class="alert alert-error"></div>
					<form>
						<div class="form-group row">
							<label for="firstNameLabel" class="col-sm-2 col-form-label">First Name</label>
							<div class="col-sm-10">
								<input id="firstName" type="text" />
							</div>
						</div>
						<div class="form-group row">
							<label for="lastNameLabel" class="col-sm-2 col-form-label">Last Name</label>
							<div class="col-sm-10">
								<input id="lastName" type="text" />
							</div>
						</div>
						<div class="form-group row">
							<label for="contactNumberLabel" class="col-sm-2 col-form-label">Contact Number</label>
							<div class="col-sm-10">
								<input id="contactNumber" type="text" />
							</div>
						</div>
						<div class="form-group row">
							<label for="emailLabel" class="col-sm-2 col-form-label">Email</label>
							<div class="col-sm-10">
								<input id="email" type="text" />
							</div>
						</div>
						<div class="form-group row">
							<label for="passwordLabel" class="col-sm-2 col-form-label">Password</label>
							<div class="col-sm-10">
								<input id="password" type="text" />
							</div>
						</div>
						<div class="form-group row">
							<label for="rolesLabel" class="col-sm-2 col-form-label">Roles</label>
							<div class="col-sm-10">
								<div class="form-check">
									<input class="form-check-input permission" name="gridRadios" id="gridRadios1" value="reports" type="checkbox" />
									<label class="form-check-label" for="gridRadios1">
										Reports
									</label>
								</div>
								<div class="form-check">
									<input class="form-check-input permission" name="gridRadios" id="gridRadios2" value="settings" type="checkbox" />
									<label class="form-check-label" for="gridRadios2">
										Settings
									</label>
								</div>
								<div class="form-check">
									<input class="form-check-input permission" name="gridRadios" id="gridRadios3" value="developer" type="checkbox" />
									<label class="form-check-label" for="gridRadios3">
										Developer
									</label>
								</div>
								<div class="form-check">
									<input class="form-check-input permission" name="gridRadios" id="gridRadios4" value="feedback" type="checkbox" />
									<label class="form-check-label" for="gridRadios4">
										Feedback
									</label>
								</div>
								<div class="form-check">
									<input class="form-check-input permission" name="gridRadios" id="gridRadios5" value="training" type="checkbox" />
									<label class="form-check-label" for="gridRadios5">
										Training
									</label>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="save_delegate_admin(null,false);">Save</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/lib.js"></script>

	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
	<script type="text/javascript">
    if (typeof(Zenbox) !== "undefined") {
            Zenbox.init({
                dropboxID: "20179186",
                url: "https://socure.zendesk.com",
                tabTooltip: "Support",
                tabImageURL: "https://assets.zendesk.com/external/zenbox/images/tab_support.png",
                tabColor: "#f59024",
                tabPosition: "Left"
            });
        }
    </script>
    <!-- Pingdom Real User Monitoring(RUM) Script -->
	<script>
		var _prum = [['id', '535b00b5abe53d2b2e841ad0'],
		             ['mark', 'firstbyte', (new Date()).getTime()]];
		(function() {
		    var s = document.getElementsByTagName('script')[0]
		      , p = document.createElement('script');
		    p.async = 'async';
		    p.src = '//rum-static.pingdom.net/prum.min.js';
		    s.parentNode.insertBefore(p, s);
		})();
	</script>
</h:body>
</html>