<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
    <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="resources/styles/socure-dashboard.css" />
    <script src="resources/scripts/jquery.min.js"></script>
    <script src="resources/scripts/bootstrap.min.js"></script>
    <script src="resources/scripts/spin.min.js" type="text/javascript"></script>
    <script src="resources/scripts/jquery.spin.js" type="text/javascript"></script>
    <script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
    <script src="#{bucket.resourceURL}/scripts/au10tix_profile_image_extractor.js"></script>
    <script src="#{bucket.resourceURL}/scripts/vkbeautify.0.99.00.beta.js"></script>
    <script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>


    <script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
        loggedinUsername = "#{loggedinUsername}";
        appenv = "#{bucket.appEnv}";
    </script>

    <meta charset="UTF-8"/>

    <style>
        div.accordion-inner textarea.beautified-value {
            min-height: 400px !important;
        }
        #stats {
                padding: 10px 0;
        }

        #stats .accordion-inner {
            padding: 9px 15px;
            border-top: 1px solid #e5e5e5;
        }

        #stats .accordion-heading {
            padding: 5px;
            border-bottom: 0;
            border-top: 0;
            border-left: 0;
            border-right: 0;
        }

        #stats .accordion-group {
            margin-bottom: 2px;
            border: 1px solid #e5e5e5;
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px;
            padding: 0;
        }

        .trns-table td{
            max-width:500px;
        }

        .casemgmt-table td{
            max-width: 130%;
        }

        .td-nowrap{
            white-space: nowrap;
            padding-right: 10px;
        }

        #transactionResult textarea[readonly] {
            background-color: transparent;
            border: none;
            resize: none;
            box-shadow: none;
        }

        #transactionResult textarea[readonly] {
            background-color: transparent;
            border: none;
            resize: none;
        }

        .pad {
          margin-top: 10px;
          margin-bottom: 10px;
        }

        input[type="radio"]{
          margin: 0 10px 0 10px;
        }
        .refresh{
            float:right;
        }

        .loading-overlay-show {
          display: block;
          opacity: 1;
          filter: alpha(opacity=100);
          position: fixed;
          width:100%;
          height:100%
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(255, 255, 255, 0.9);
          z-index: 2000;
          top: 0;
          bottom: -75px;
          text-align: center;
          -webkit-transition: opacity 750ms;
          -moz-transition: opacity 750ms;
          -o-transition: opacity 750ms;
          transition: opacity 750ms;
        }

        .loading-overlay-hide {
          display: none;
          opacity: 0;
          filter: alpha(opacity=0);
        }

        #LoadingRefresh p {
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          text-align: center;
          font-size: 2em;
        }
        #LoadingRefresh .refresh-loader {
          -webkit-box-sizing: border-box;
          -moz-box-sizing: border-box;
          box-sizing: border-box;
          width: 60px;
          height: 60px;
          position: absolute;
          bottom: 50%;
          left: 50%;
          margin-bottom: 25px;
          margin-left: -30px;
          -webkit-animation: rotate 1250ms linear infinite;
          -moz-animation: rotate 1250ms linear infinite;
          -ms-animation: rotate 1250ms linear infinite;
          -o-animation: rotate 1250ms linear infinite;
          animation: rotate 1250ms linear infinite;
        }
        #LoadingRefresh .refresh-loader img {
          width: 100%;
          height: 100%;
        }
        #LoadingRefresh .refresh-loader .dot {
          width: 20px;
          height: 20px;
          position: absolute;
          left: 50%;
          top: 50%;
          margin-left: -10px;
          margin-top: -10px;
          background: #f8ae4f;
          -webkit-border-radius: 100%;
          -moz-border-radius: 100%;
          border-radius: 100%;
          -webkit-animation: blink 625ms ease-in-out infinite alternate;
          -moz-animation: blink 625ms ease-in-out infinite alternate;
          -ms-animation: blink 625ms ease-in-out infinite alternate;
          -o-animation: blink 625ms ease-in-out infinite alternate;
          animation: blink 625ms ease-in-out infinite alternate;
        }

        .underline-hr {
            border: 1px solid black;
            padding: 0;
            margin: 0;
        }

        .accordion { list-style-type: none; padding: 0; margin: 0 0 30px; border: 1px solid #17a; border-top: none; border-left: none; }
        .accordion ul { padding: 0; margin: 0; float: left; display: block; width: 100%; }
        .accordion li { cursor: pointer; list-style-type: none; padding: 0; margin: 0; float: left; display: block; width: 100%;}

        .accordion li div { padding: 20px;  display: block; clear: both; float: left;}
        .accordion a { text-decoration: none; border-bottom: 1px solid #4df; font: bold 1.1em/2em Arial, sans-serif;  padding: 0 10px; display: block; cursor: pointer;}

        /*  .active{background:red!important}*/
        /* Level 2 */
        .accordion li ul li { padding: 1px 20px; font-size: 0.9em; }
    </style>

    <title>Troubleshooting</title>

    <script language="javascript" src="#{bucket.resourceURL}/scripts/troubleshooting.js"></script>
</h:head>
<h:body>

    <iframe width="1" height="1" frameborder="0" src="" id="downloadHelper" style="display:none;"></iframe>

    <div align="center">
        <font color="blue"><h3>
            <h:outputText value="#{param['message']}" />
        </h3> </font>
    </div>
    <br/>
    <br/>
    <div align="right">
        <h5>
            <h:outputLink value="industry">Manage Industry</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="active_users">Active Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="inactive_users">Inactive Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;


            <h:outputLink value="logout">Logout</h:outputLink>
            &nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
    </div>
    <br/>
    <br/>
    <center><h3>Troubleshooting</h3></center>
    <hr/>

    <div class="container" id="spin_container">
        <div class="row">
            <div class="span12">


                <div align="center" id="radiobuttons">
                    <input type="radio" name="type" value="txn" checked="checked" style="padding 25px 25px 25px 25px" onclick="clearPage()" >Transaction Id</input>
                    <input type="radio" name="type" value="ref" class="pad" onclick="clearPage()">StepUp Event ID</input>
                    <input type="radio" name="type" value="ref" class="pad" onclick="clearPage()">Watchlist Monitoring</input>
                    <input type="radio" name="type" value="ref" class="pad" onclick="clearPage()">Document UUID</input>
                    <input type="radio" name="type" value="ref" class="pad" onclick="clearPage()">Case Management</input>
                    <input type="radio" name="type" value="ref" class="pad" onclick="clearPage()">DocV 3.0 Transaction</input>
                </div>
                <br/>
                <div align="center" id="stepUpEventRadioButtons" style="display: none;">
                    <input type="radio" name="stepUpType" value="stepUpEvent" class="pad" onclick="clearPage()">Webhook Events Details</input>
                    <input type="radio" name="stepUpType" value="stepUpProcess" class="pad" onclick="clearPage()">StepUp Process Details</input>
                    <input type="radio" name="stepUpType" value="stepUpProcesses" class="pad" onclick="clearPage()">StepUp Processes by DocVTransactionToken</input>
                </div>
                <br/>
                <div align="center" id="caseMgmtRadioButtons" style="display: none;">
                    <input type="radio" name="caseMgmtType" value="byCaseId" class="pad" onclick="clearPage()">Case Id</input>
                    <input type="radio" name="caseMgmtType" value="byTxnId" class="pad" onclick="clearPage()">Transaction Id</input>
                </div>


                <div id="idInputCont" class="pad" align="center">
                    Enter  ID  <input id="Id" type="text"/>
                </div>
                <div id="idInput2Cont" class="pad" align="center" style="display: none">
                    <div class="pad">Document UUID  <input id="uuid" type="text"/></div>
                    <div class="pad">Account ID  <input id="inputId2" oninput="this.value = this.value.replace(/[^0-9]/g, '')" type="text"/></div>
                </div>

                <div align="center"><input type="button" value="Search" id="txnsubmit" /> </div>
                <div id="modelExplainer" align="center"  class="pad">
                    <a href="#" id="modelExplainerLink" target="_blank">Model Explainer</a>
                </div>
                <div id="txnStatusMsg"></div>

                <div id="txnresult" class="pad">
                    <table id="transactionResult" border="1" width="125%" class="trns-table">
                        <tr><th width="20%">Name</th><th width="80%">Details</th></tr>

                        <tr><td><span id="name">ID</span></td>
                            <td><span id="resultId"></span></td>
                        </tr>

                        <tr><td><span id="name">Transaction ID</span></td>
                            <td><span id="resultTxnId"></span></td>
                        </tr>

                        <tr><td><span id="name">Environment</span></td>
                            <td><span id="resultEnvironment"></span></td>
                        </tr>

                        <tr><td><span id="name">Transaction Date</span></td>
                            <td><span id="resultDate"></span></td>
                        </tr>

                        <tr><td><span id="name">Process Time</span></td>
                            <td><span id="resultTime"></span></td>
                        </tr>

                        <tr><td><span id="name">API</span></td>
                            <td><span id="resultApi"></span></td>
                        </tr>

                        <tr><td><span id="name">Parameters</span></td>
                            <td><span ><textarea id="resultParam"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr><td><span id="name">Resolved Parameters (DocV)</span></td>
                            <td><span ><textarea id="resolvedParameters"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr><td><span id="name">DocV 3.0 Troubleshooting</span></td>
                            <td><span id="dvLink"></span></td>
                        </tr>

                        <tr style="display: none"><td><span id="name">Document(s)</span></td>
                            <td><span id="docDownload"></span></td>
                        </tr>

                        <tr style="display: none"><td><span id="captureChannel">Capture Channel</span></td>
                            <td><span style="text-transform: capitalize;" id="captureChannelValue"></span></td>
                        </tr>

                        <tr style="display: none"><td><span id="name">Profile Image</span></td>
                            <td><span><a href="#" id="profileImage" target="_blank">Download</a></span></td>
                        </tr>

                        <tr style="display: none"><td><span id="name">Extracted Profile Image</span></td>
                            <td><span><a href="#" id="extractedProfileImage" target="_blank">Download</a></span></td>
                        </tr>

                        <tr><td><span id="name">Response</span></td>
                            <td><span><textarea id="resultResponse"  style="width:700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr id="decision" style="display: none"><td><span>Decision</span></td>
                            <td><span id="decisionBlock"></span></td>
                        </tr>

                        <tr><td><span id="name">InternalWorkLogs</span></td>
                            <td><span><textarea id="internalWorkLogsResponse"  style="width:700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr><td><span id="name">Error</span></td>
                            <td><span id="resultError"></span></td>
                        </tr>

                        <tr><td><span id="name">Error Message</span></td>
                            <td><span id="resultErrorMsg"></span></td>
                        </tr>

                        <tr><td><span id="name">Account IP Address</span></td>
                            <td><span id="resultIp"></span></td>
                        </tr>

                        <tr><td><span id="name">Origin of Invocation</span></td>
                            <td><span id="resultInvo"></span></td>
                        </tr>

                        <tr><td><span id="name">Geo Code</span></td>
                            <td><span id="resultGeo"></span></td>
                        </tr>

                        <tr><td><span id="name">API Key</span></td>
                            <td><span id="resultApiKey"></span></td>
                        </tr>

                        <tr><td><span id="name">Account ID</span></td>
                            <td><span id="resultaccountId"></span></td>
                        </tr>

                        <tr><td><span id="name">API Name</span></td>
                            <td><span id="resultApiName"></span></td>
                        </tr>

                        <tr><td><span id="name">Caches Used</span></td>
                            <td><span id="resultCache"></span></td>
                        </tr>

                        <tr><td><span id="name">Debug</span></td>
                            <td><span><textarea id="resultDebug"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr><td><span id="name">Details</span></td>
                            <td><span><textarea id="resultDetails"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr><td><span id="name">Auth Score</span></td>
                            <td><span id="resultScore"></span></td>
                        </tr>

                        <tr><td><span id="name">Confidence</span></td>
                            <td><span id="resultConf"></span></td>
                        </tr>

                        <tr><td><span id="name">Reason Codes</span></td>
                            <td><span><textarea id="resultCode"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr><td><span id="name">Rish Code</span></td>
                            <td><span id="resultRisk"></span></td>
                        </tr>

                        <tr><td><span id="name">Customer ID</span></td>
                            <td><span id="resultCustomer"></span></td>
                        </tr>

                        <tr><td><span id="name">Run ID</span></td>
                            <td><span id="resultRun"></span></td>
                        </tr>

                        <tr><td><span id="name">Request URI</span></td>
                            <td><span><textarea id="resultUri"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr><td><span id="name">Unique UID</span></td>
                            <td><span id="resultUid"></span></td>
                        </tr>

                    </table>
                </div>

                <br/>
                <div id="thirdPartyHeader"><h4>ThirdParty Audits</h4> <hr class="underline-hr" /></div>

                <div id="refresh" class="refresh pad"><button class="btn-primary" id="refreshButton">Refresh ThirdParty Calls</button></div>
                <br/><br/>
                <div id="stats"></div>

                <div id="eventresult">

                    <table id="events" border='2'>
                        <thead>
                        <tr>
                            <th>id</th>
                            <th>eventReferenceId</th>
                            <th>idPlusReferenceId</th>
                            <th>accountId</th>
                            <th>eventType</th>
                            <th>environmentType</th>
                            <th>responseTime</th>
                            <th>httpStatus</th>
                            <th>timeStamp</th>

                        </tr>
                        </thead>
                        <tbody id="eventsbody">

                        </tbody>
                    </table>

                </div>

                <div id="uuidResultCont" style="text-align: center; margin: 10px 0px">
                    <div id="uuidResultViewFiles"></div>
                </div>

                <div id="eventDetailResult" style="position:fixed;display:none">
                    <button style="display: block;" onclick="closeEventDetail()">Close</button>
                    <button id="eventDownloadButton" style="display:none" onclick="download()">Download</button>
                    <!--                    <span ><textarea id="eventDetailResultText" style="width: 700px;" readonly="readonly"></textarea></span>-->
                    <textarea id="eventDetailResultText" style="width: 700px;" readonly="readonly"></textarea>

                </div>

                <div class="modal" tabindex="-1" role="dialog" id="imageDisplayDialog" style="display: none;position: fixed; top: 5%; left: 5%; background-color: white;width: 1000px; height: 800px; overflow-y: scroll;">
                    <button id="closeImageButton" onclick="closeImageDiv()">Close</button>
                    <div id="imagesList">
                    </div>
                    <img id="docImage" alt="Loading Image...." src="" style="max-width: 100%; max-height: 100%; background: url('#{bucket.resourceURL}/assets/images/select2-spinner.gif') no-repeat center;"/>

                </div>

                <section id="LoadingRefresh" class="loading-overlay-show">
                    <p>Loading ...</p>
                    <div class="refresh-loader">
                        <img src="#{bucket.resourceURL}/assets/images/socure-loader.svg" /> <span
                            class="dot"></span>
                    </div>
                </section>


                <div id="stepUpProcessResult" class="pad">
                    <table id="stepUpResult" border="1" width="125%" class="trns-table">
                        <tr><th width="20%">Name</th><th width="80%">Details</th></tr>

                        <tr>
                            <td><span id="stepUpEventIdname"><b>Event ID</b></span></td>
                            <td><span id="stepUpEventId"></span></td>
                        </tr>

                        <tr>
                            <td><span id="stepUpEventAccountIdName"><b>Account ID</b></span></td>
                            <td><span id="stepUpAccountId"></span></td>
                        </tr>

                        <tr>
                            <td><span id="stepUpPublicAccountIdName"><b>Public Account ID</b></span></td>
                            <td><span id="stepUpPublicAccountId"></span></td>
                        </tr>

                        <tr>
                            <td><span id="stepUpEventVerificationLevel"><b>Verification Level</b></span></td>
                            <td><span id="stepUpVerificationLevel"></span></td>
                        </tr>

                        <tr>
                            <td><span id="stepUpEventFlowConfiguration"><b>Flow Configuration</b></span></td>
                            <td><textarea id="stepUpFlowConfiguration"  style="width: 700px;" readonly="readonly" ></textarea></td>
                        </tr>

                        <tr>
                            <td><span id="name"><b>Upload Response</b></span></td>
                            <td id="uploadDetailsWithDownloadOption">
                                <table id="uploadAudits" border='2'>
                                    <thead>
                                    <tr>
                                        <th>Success</th>
                                        <th>ReferenceID</th>
                                        <th>UUID</th>
                                        <th>Created At</th>
                                        <th>Document(s)</th>
                                        <th>Capture Metrics</th>
                                    </tr>
                                    </thead>
                                    <tbody id="uploadAuditTableBody">

                                    </tbody>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td><span id="name"><b>Event Statuses</b></span></td>
                            <td><span><textarea id="stepUpStatusDetails"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr>
                            <td><span id="name"><b>ID+ Response</b></span></td>
                            <td><span><textarea id="stepUpIdPlusDetails"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr>
                            <td><span id="name"><b>Process Config</b></span></td>
                            <td><span><textarea id="stepUpConfigDetails"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr>
                            <td><span id="name"><b>Payload Details</b></span></td>
                            <td><span><textarea id="stepUpPayloadDetails"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>


                    </table>
                </div>


                <div id="caseMgmtTroubleShootingResult" class="pad" style="display: none;">
                    <table id="caseDetailsTable" border="1" class="casemgmt-table" style="margin-left: -50px;">
                        <tr><th width="20%">Name</th><th width="80%">Details</th></tr>

                        <tr>
                            <td class="td-nowrap"><span id="caseIdName"><b>Case ID</b></span></td>
                            <td><span id="caseId"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="transactionIdName"><b>Txn ID</b></span></td>
                            <td><span id="caseTransactionId"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="caseTypeName"><b>Case Type</b></span></td>
                            <td><span id="caseType"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="caseAccountIdName"><b>Account Id</b></span></td>
                            <td><span id="caseAccountId"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="caseEnvironmentTypeIdName"><b>Environment</b></span></td>
                            <td><span id="caseEnvironmentTypeId"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="caseWorkflowStatusName"><b>Case Status</b></span></td>
                            <td><span id="caseWorkflowStatus"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="caseAssignedToName"><b>Analyst</b></span></td>
                            <td><span id="caseAssignedTo"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="caseReferredToname"><b>Supervisor</b></span></td>
                            <td><span id="caseReferredTo"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="caseInvestigateByname"><b>Case Officer</b></span></td>
                            <td><span id="caseInvestigateBy"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="createdAtName"><b>Created At</b></span></td>
                            <td><span id="caseCreatedAt"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="updatedAtName"><b>Updated At</b></span></td>
                            <td><span id="caseUpdatedAt"></span></td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="name"><b>Entity Details</b></span></td>
                            <td id="entityDetails" style="padding: 20px;">
                                <table id="entityDetailsTable" border='2'>
                                    <thead>
                                    <tr>
                                        <th>Entity Id</th>
                                        <th>Disposition Status</th>
                                        <th>Approval Status</th>
                                        <th>Escalation Status</th>
                                        <th>Event Type</th>
                                        <th>Last Updated At</th>
                                    </tr>
                                    </thead>
                                    <tbody id="entityDetailsBody">

                                    </tbody>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td class="td-nowrap"><span id="name"><b>Case Audit Trail</b></span></td>
                            <td id="auditDetails" style="padding: 20px;">
                                <table id="auditDetailsTable" border='2'>
                                    <thead>
                                    <tr>
                                        <th>Context</th>
                                        <th>Case / EntityId</th>
                                        <th>Updated Value</th>
                                        <th>User</th>
                                        <th>Action Time</th>
                                    </tr>
                                    </thead>
                                    <tbody id="auditDetailsBody">

                                    </tbody>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td><span id="caseEntityDetailsName"><b>Entity Meta Data</b></span></td>
                            <td><span><textarea id="caseEntityDetails"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                        <tr>
                            <td><span id="caseAllCommentsDetailsName"><b>All Comments Info</b></span></td>
                            <td><span><textarea id="caseAllCommentsDetails"  style="width: 700px;" readonly="readonly" > </textarea></span></td>
                        </tr>

                    </table>
                </div>

                <div id="caseListResponseResult" class="pad" style="display: none;">
                    <table id="caseListResponseTable" border='2'>
                        <thead>
                        <tr>
                            <th>Case Id</th>
                            <th>Status</th>
                            <th>Assigned To</th>
                            <th>Referred To</th>
                            <th>Created At</th>
                            <th>Updated At</th>
                        </tr>
                        </thead>
                        <tbody id="caseListResponseTableBody">

                        </tbody>
                    </table>
                </div>



                <span id="referenceId" style="display:none"></span>
                <span id="transactionDate" style="display:none"></span>



            </div>
        </div>
    </div>
</h:body>
</html>
