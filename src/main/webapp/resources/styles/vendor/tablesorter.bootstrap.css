/*************
  Bootstrap theme
 *************/
/* j<PERSON><PERSON><PERSON> Theme */
.tablesorter-bootstrap {
	width: 100%;
}
.tablesorter-bootstrap .tablesorter-header,
.tablesorter-bootstrap tfoot th,
.tablesorter-bootstrap tfoot td {
	font: bold 14px/20px Arial, Sans-serif;
	position: relative;
	padding: 8px;
	margin: 0 0 18px;
	list-style: none;
	background-color: #FBFBFB;
	background-image: -moz-linear-gradient(top, white, #efefef);
	background-image: -ms-linear-gradient(top, white, #efefef);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(white), to(#efefef));
	background-image: -webkit-linear-gradient(top, white, #efefef);
	background-image: -o-linear-gradient(top, white, #efefef);
	background-image: linear-gradient(to bottom, white, #efefef);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#efefef', GradientType=0);
	background-repeat: repeat-x;
	-webkit-box-shadow: inset 0 1px 0 white;
	-moz-box-shadow: inset 0 1px 0 #ffffff;
	box-shadow: inset 0 1px 0 white;
}

.tablesorter-bootstrap .tablesorter-header {
	cursor: pointer;
}

.tablesorter-bootstrap .tablesorter-header-inner {
	position: relative;
	padding: 4px 18px 4px 4px;
}

/* bootstrap uses <i> for icons */
.tablesorter-bootstrap .tablesorter-header i {
	position: absolute;
	right: 2px;
	top: 50%;
	margin-top: -7px; /* half the icon height; older IE doesn't like this */
	width: 14px;
	height: 14px;
	background-repeat: no-repeat;
	line-height: 14px;
	display: inline-block;
}
.tablesorter-bootstrap .bootstrap-icon-unsorted {
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAOCAYAAAD5YeaVAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAWVJREFUeNqUUL9Lw2AUTGP8mqGlpBQkNeCSRcckEBcHq1jImMElToKuDvpHFMGhU0BQcHBwLji6CE1B4uB/INQsDi4d2jQ/fPeZxo764OV6915f7lLJ81xot9tCURXqdVEUr7IsO6ffH9Q5BlEUCaLwWxWqTcbYnaIoh0Dw4gAvcWlxq1qt9hqNxg6hUGAP+uIPUrGs0qXLer2+v/pTX6QpxLtkc2U2m53ACb8sSdIDXerSEms2m6+DweAICA4d89KGbduf9MpEVdXQ9/2LVqv1CASHjjn3iq/x1xKFfxQPqGnada1W86bT6SiO42OS3qk3KPStLMvbk8nkfjwen/LLuq6blFymMB0KdUPSGhAcOualjX6/f0bCiC7NaWGPQr0BwaFjzn0gYJqmLAiCA8/zni3LmhuGkQPBoWPOPwQeaPIqD4fDruu6L6Zp5kBw6IudchmdJAkLw3DXcZwnIPjy/FuAAQCiqqWWCAFKcwAAAABJRU5ErkJggg==);
}

/* since bootstrap (table-striped) uses nth-child(), we just use this to add a zebra stripe color */
.tablesorter-bootstrap tr.odd td {
	background-color: #f9f9f9;
}
.tablesorter-bootstrap tbody > .odd:hover > td,
.tablesorter-bootstrap tbody > .even:hover > td {
	background-color: #f5f5f5;
}
.tablesorter-bootstrap tr.even td {
	background-color: #fff;
}

/* processing icon */
.tablesorter-bootstrap .tablesorter-processing {
	background-image: url('data:image/gif;base64,R0lGODlhFAAUAKEAAO7u7lpaWgAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQBCgACACwAAAAAFAAUAAACQZRvoIDtu1wLQUAlqKTVxqwhXIiBnDg6Y4eyx4lKW5XK7wrLeK3vbq8J2W4T4e1nMhpWrZCTt3xKZ8kgsggdJmUFACH5BAEKAAIALAcAAAALAAcAAAIUVB6ii7jajgCAuUmtovxtXnmdUAAAIfkEAQoAAgAsDQACAAcACwAAAhRUIpmHy/3gUVQAQO9NetuugCFWAAAh+QQBCgACACwNAAcABwALAAACE5QVcZjKbVo6ck2AF95m5/6BSwEAIfkEAQoAAgAsBwANAAsABwAAAhOUH3kr6QaAcSrGWe1VQl+mMUIBACH5BAEKAAIALAIADQALAAcAAAIUlICmh7ncTAgqijkruDiv7n2YUAAAIfkEAQoAAgAsAAAHAAcACwAAAhQUIGmHyedehIoqFXLKfPOAaZdWAAAh+QQFCgACACwAAAIABwALAAACFJQFcJiXb15zLYRl7cla8OtlGGgUADs=');
	position: absolute;
	z-index: 1000;
}

/* filter widget */
.tablesorter-bootstrap .tablesorter-filter-row .tablesorter-filter {
	width: 98%;
	height: inherit;
	margin: 0 auto;
	padding: 4px 6px;
	background-color: #fff;
	color: #333;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-transition: height 0.1s ease;
	-moz-transition: height 0.1s ease;
	-o-transition: height 0.1s ease;
	transition: height 0.1s ease;
}
.tablesorter-bootstrap .tablesorter-filter-row td {
	background: #eee;
	line-height: normal;
	text-align: center;
	padding: 4px 6px;
	vertical-align: middle;
	-webkit-transition: line-height 0.1s ease;
	-moz-transition: line-height 0.1s ease;
	-o-transition: line-height 0.1s ease;
	transition: line-height 0.1s ease;
}
/* hidden filter row */
.tablesorter-bootstrap .tablesorter-filter-row.hideme td {
	padding: 2px; /* change this to modify the thickness of the closed border row */
	margin: 0;
	line-height: 0;
}
.tablesorter-bootstrap .tablesorter-filter-row.hideme .tablesorter-filter {
	height: 1px;
	min-height: 0;
	border: 0;
	padding: 0;
	margin: 0;
	/* don't use visibility: hidden because it disables tabbing */
	opacity: 0;
	filter: alpha(opacity=0);
}

/* pager plugin */
.tablesorter-bootstrap .tablesorter-pager select {
  padding: 4px 6px;
}
.tablesorter-bootstrap .tablesorter-pager .pagedisplay {
	border: 0;
}