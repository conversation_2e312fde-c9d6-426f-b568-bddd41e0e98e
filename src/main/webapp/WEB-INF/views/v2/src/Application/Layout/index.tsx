import { App<PERSON><PERSON>, <PERSON>, Drawer, Grid, <PERSON><PERSON><PERSON><PERSON>on, Too<PERSON><PERSON>, Typography } from "@mui/material";
import MenuIcon from '@mui/icons-material/Menu';
import React, { useMemo, useState, Fragment } from "react";
import { SimpleTreeView, TreeItem } from '@mui/x-tree-view';
import { GlobalPageList } from "./data";
import { goToLink } from "../../Utilities";

const Layout: React.FC<any> = () => {

    const [isDrawerOpen, setDrawerOpen] = useState<boolean>(false);

    const renderDummyAppBar = useMemo(() => {
        return <AppBar sx={{ zIndex: -10 }} color="transparent" elevation={0} position="sticky" key="dummy-toolbar">
            <Toolbar></Toolbar>
        </AppBar>;
    }, []);

    const renderAppBar = useMemo(() => {
        return <AppBar color="warning" position="fixed" key="actual-toolbar">
            <Grid container alignItems={"center"} spacing={1} direction="row">
                <Grid item key="menu-btn">
                    <IconButton
                        size="large"
                        edge={false}
                        color="inherit"
                        onClick={() => setDrawerOpen(true)}
                    >
                        <MenuIcon fontSize="large" />
                    </IconButton>
                </Grid>
                <Grid item key="title">
                    <Typography
                        variant="h5"
                        component="div"
                    >
                        {"Socure Super Admin"}
                    </Typography>
                </Grid>
            </Grid>
        </AppBar >;
    }, [setDrawerOpen]);

    const renderDrawer = useMemo(() => {
        return <Drawer open={isDrawerOpen} onClose={() => setDrawerOpen(false)}>
            <Box sx={{ width: "19.5vw", paddingLeft: "1vw", paddingTop: "4vh" }}>
                <SimpleTreeView sx={{ width: "18vw" }}>
                    {GlobalPageList.map(pg => {
                        return <TreeItem itemId={pg.key} key={pg.key} label={pg.display}>
                            {pg.pages.map(p => {

                                return <TreeItem
                                    itemId={p.key}
                                    label={p.display}
                                    key={p.key}
                                    onClick={goToLink.bind(null, (p.type === "v2" ? `/v2${(p.linkRoute || p.route)}` : p.route), false)}
                                />;
                            })}
                        </TreeItem>
                    })}
                </SimpleTreeView>
            </Box>
        </Drawer>;
    }, [isDrawerOpen, setDrawerOpen]);

    return <Fragment>
        {renderAppBar}
        {renderDummyAppBar}
        {renderDrawer}
    </Fragment>;
}

export default Layout;