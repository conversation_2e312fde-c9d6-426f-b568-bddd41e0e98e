import ch.qos.logback.classic.PatternLayout
import ch.qos.logback.classic.boolex.GEventEvaluator
import ch.qos.logback.classic.encoder.PatternLayoutEncoder
import ch.qos.logback.classic.filter.ThresholdFilter
import ch.qos.logback.core.ConsoleAppender
import ch.qos.logback.core.filter.EvaluatorFilter
import ch.qos.logback.core.rolling.FixedWindowRollingPolicy
import ch.qos.logback.core.rolling.RollingFileAppender
import ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy
import me.socure.logback.MailgunAppender

import static ch.qos.logback.classic.Level.*
import static ch.qos.logback.core.spi.FilterReply.ACCEPT
import static ch.qos.logback.core.spi.FilterReply.DENY

def LOG_PATTERN = "[%d] [%5p] [%X{TRANSACTION_ID}] [%X{INSTANCE_ID}] [%X{akkaSource}] [%X{akkaTimestamp}] [%t] \\(%F:%M:%L\\) %m%n"

appender("console", ConsoleAppender) {
    encoder(PatternLayoutEncoder) {
        pattern = LOG_PATTERN
    }
}

appender("s3logger", RollingFileAppender) {
    append = true
    file = "logs/audit-log.log"
    encoder(PatternLayoutEncoder) {
        pattern = LOG_PATTERN
    }
    filter(ThresholdFilter) {
        level = TRACE
    }
    rollingPolicy(FixedWindowRollingPolicy) {
        maxIndex = 5
        fileNamePattern = "logs/audit-log.log.%i"
    }
    triggeringPolicy(SizeBasedTriggeringPolicy) {
        maxFileSize = "5MB"
    }
}
appender("perf", RollingFileAppender) {
    file = "logs/perf-stats.log"
    append = true
    encoder(PatternLayoutEncoder) {
        pattern = LOG_PATTERN
    }
    filter(ThresholdFilter) {
        level = TRACE
    }
    rollingPolicy(FixedWindowRollingPolicy) {
        maxIndex = 5
        fileNamePattern = "logs/perf-stats.log.%i"
    }
    triggeringPolicy(SizeBasedTriggeringPolicy) {
        maxFileSize = "5MB"
    }
}


logger("org.perf4j.TimingLogger", INFO, ["perf"], false)
logger("me.socure.audit.util.UsageTrackerWorker", INFO, ["s3logger"], false)
logger("me.socure.common.hmac", DEBUG, ["console"], false)
logger("me.socure.transactionauditing.rest.client", DEBUG, ["console"], false)
root(INFO, ["console", "mail"])

