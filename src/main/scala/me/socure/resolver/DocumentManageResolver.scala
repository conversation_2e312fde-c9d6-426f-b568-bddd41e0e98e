package me.socure.resolver

import me.socure.document.manager.client.DocumentManagerClient
import me.socure.document.manager.model.doctype.{DtoDocumentType, DtoDocumentTypeAccount, DtoProvisionAccount}
import me.socure.model.ErrorResponse
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component

import scala.concurrent.Await
import scala.concurrent.duration._

@Component
class DocumentManageResolver {

  private val logger = LoggerFactory.getLogger(getClass)

  @Autowired
  @Qualifier("documentManagerClient")
  var documentManagerClient: DocumentManagerClient = _

  def listDocTypes() :  Either[ErrorResponse, List[DtoDocumentType]] = {
    val response = documentManagerClient.listDocTypes()
    Await.result(response, 35 seconds)
  }

  def createDocumentType(dtoDocumentType: DtoDocumentType): Either[ErrorResponse, DtoDocumentType] = {
    val response = documentManagerClient.createDocumentType(dtoDocumentType);
    Await.result(response, 35 seconds)
  }

  def provisionDocumentType(provisionDetails: DtoProvisionAccount) : Either[ErrorResponse, DtoDocumentTypeAccount] = {
    val response = documentManagerClient.provisionDocumentType(provisionDetails);
    Await.result(response, 35 seconds)
  }

  def provisionAllDocumentType(accountPublicId: String) : Either[ErrorResponse, String] = {
    val response = documentManagerClient.provisionAllDocumentTypes(accountPublicId);
    Await.result(response, 35 seconds)
  }


  def deProvisionDocumentType(provisionDetails: DtoProvisionAccount): Either[ErrorResponse, String]=  {
    val response = documentManagerClient.deProvisionDocumentType(provisionDetails);
    Await.result(response, 35 seconds)

  }

  def listDocTypeByAccountPublicId(accountPublicId: String): Either[ErrorResponse, Seq[DtoDocumentType]] = {
    val response = documentManagerClient.listDocTypeByAccountPublicId(accountPublicId);
    Await.result(response, 35 seconds)
  }

}


