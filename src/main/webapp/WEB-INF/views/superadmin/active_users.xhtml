<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<link rel="stylesheet" type="text/css"
		  href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css"
		  href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		  href="#{bucket.resourceURL}/styles/datepicker.css" />
	<script src="resources/scripts/bootstrap.min.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/ZeroClipboard.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/TableTools.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/bootstrap-datepicker.js"></script>
	<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		  media="print" />
	<script>
		bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
        canAccessControlCentreAccessFlag = "#{canAccessControlCentre}";
		controlCenterAccessType = {
			fullAccess: "Full Access",
			viewOnly: "View Only",
			noAccess: "No Access"
		};
		totalRecords = "#{active_user_count}";
		loggedinUsername = "#{loggedinUsername}";
		appenv = "#{bucket.appEnv}";
	</script>
	<script type="text/javascript" src="#{bucket.resourceURL}/scripts/active_users.js" />
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>

<h:body>
	<div align="center">
		<font color="blue"><h3 id="notification">
			<h:outputText value="#{param['message']}" />
		</h3> </font>
	</div>
	<div align="left" class="page_container">

		<h:outputLink value="leaderboard">Leaderboard</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account/lockout/list">Locked Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="fraudmodel">Manage Fraud Model</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="troubleshooting">Troubleshooting</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="rulecode">Rulecode</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="audit_statistics">Audit Statistics</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="update_domain">Update Domains</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="fraudmanager">Fraud Model Manager</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="v2/fraud-model-manager/model-listing">Fraud Model Manager Reimagined</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="rate_limit">Rate Limit Manager</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account_manager">Manage Account</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="update_dashboard_domain">Update Dashboard Domains</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="decrypt_with_pgp_keys">Decrypt with PGP</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="pgp_keys">PGP Keys</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="pgp_signature_public_keys">PGP Signature Public Key Management</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="sftp_users">SFTP Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<!--h:outputLink value="invite_primary_user">Invite Primary User</h:outputLink-->
		<h:outputLink value="saml2_metadata">SAML 2.0 Metadata Management</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="alertlist_consortium_import">AlertList Consortium Import</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="json-editor">JSON Editor</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="watchlist_source">Watchlist Sources</h:outputLink>
		|
		<h:outputLink value="document_manager">Document Manager</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="docv_strategies">Docv Strategies</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="jobdefinition_config">Batch Job Central</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="sandbox_config_mgmt">Sandbox Configuration Management</h:outputLink>
		&nbsp; | &nbsp; &nbsp;<span id="control_center_span">
		<h:outputLink value="control_center">Control Center</h:outputLink>
	    &nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="new_control_center">New Control Center</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		</span>
		<h:outputLink value="password_reset">Password Reset</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="batch_jobs">Batch Jobs</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="v2">V2</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="v2/kyc/simple-record-correction">Record Correction</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>

	<div id="user_content" class="page_container">
		<fieldset>
			<legend>Active Users</legend>

			<div class="tab-content">
				<div class="tab-pane active" id="tab_inactive_users">
					<div id="example_wrapper" class="dataTables_wrapper form-inline"
						 role="grid">
						<table cellpadding="0" cellspacing="0" border="0"
							   class="table table-striped table-bordered dataTable"
							   id="tbl_active_users"
							   aria-describedby="example_info">
							<thead>
							<tr role="row">
								<th class="sorting_asc" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-sort="ascending"
									aria-label="AccountId: activate to sort column descending"
									style="width: 163px;">Account ID</th>
								<th class="sorting_asc" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-sort="ascending"
									aria-label="FirstName: activate to sort column descending"
									style="width: 163px;">First Name</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="LastName: activate to sort column ascending"
									style="width: 237px;">LastName</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="Company: activate to sort column ascending"
									style="width: 217px;">Company</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="ContactNumber: activate to sort column ascending"
									style="width: 139px;">Contact Number</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="Email: activate to sort column ascending"
									style="width: 98px;">Email</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="ApiKey: activate to sort column ascending"
									style="width: 98px;">ApiKey</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="Role: activate to sort column ascending"
									style="width: 98px;">Role</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="IsInternal: activate to sort column ascending"
									style="width: 98px;">Is Internal</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="RegisteredOn: activate to sort column ascending"
									style="width: 98px;">Registered On</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="Domains: activate to sort column ascending"
									style="width: 98px;">Authorized Domains</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="EncyptionEnabled: activate to sort column ascending"
									style="width: 98px;">Encryption Enabled</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="EncyptionEnabled: activate to sort column ascending"
									style="width: 98px;">PII Mask Enabled</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="PublicAccountId: activate to sort column ascending"
									style="width: 98px;">Public Account ID</th>
								<th class="sorting_asc" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-sort="ascending"
									aria-label="ExternalAccountId: activate to sort column descending"
									style="width: 163px;">External Account ID</th>
							</tr>
							</thead>
						</table>
					</div>
				</div>
			</div>

		</fieldset>
	</div>
	<script src="#{bucket.resourceURL}/scripts/lib.js"></script>

	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
</h:body>
</html>
