import React, { useCallback, useMemo } from 'react';

const Button = ({
  type,
  text,
  disabled,
  onClick
}) => {
  const onButtonClick = useCallback(() => {
    !disabled && onClick && onClick();
  }, [ disabled, onClick ])

  const classNameValue = useMemo(() => {
    let className = 'socure-c-button '
    disabled && (className += ' disabled')
    switch(type) {
      case 'link':
        return className + ' socure-c-button-link';
      case 'secondary':
        return className + ' socure-c-button-secondary';
      case 'primary':
        return className + ' socure-c-button-primary';
      default:
        return className;
    }
  }, [ type, disabled ])

  return <div
    className={classNameValue}
    onClick={onButtonClick} >
    {text}
  </div>
}

export default Button;