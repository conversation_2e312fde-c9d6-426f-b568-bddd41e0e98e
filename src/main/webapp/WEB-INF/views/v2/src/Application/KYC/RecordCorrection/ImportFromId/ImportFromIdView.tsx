import { <PERSON>ton, <PERSON>alog, <PERSON>alogA<PERSON>, DialogContent, DialogTitle, Divider, FormControl, Grid, InputLabel, MenuItem, Select, TextField } from '@mui/material';
import { Clear as ClearIcon, Done as DoneIcon } from '@mui/icons-material';
import React, { useMemo } from 'react';
import { IRetrievedIdData } from './ImportFromIdTypes';

export interface IImportFromIdViewPropsBase {
    type: number;
    setType: React.Dispatch<React.SetStateAction<number>>;
    id: string;
    setId: React.Dispatch<React.SetStateAction<string>>;
    account: number;
    setAccount: React.Dispatch<React.SetStateAction<number>>;
    locked: boolean;
    setLocked: React.Dispatch<React.SetStateAction<boolean>>;
    loadCluserIDs: () => Promise<void>;
    retrievedData: IRetrievedIdData | null;
    cluster: string | null;
    setCluster: React.Dispatch<React.SetStateAction<string | null>>;
    clearData: () => void;
}

export interface IImportFromIdViewProps extends IImportFromIdViewPropsBase {
    isOpen: boolean;
    onClose: () => void;
    apply: (vendor: string, recordId: string, sid:string) => void;
}

const ImportFromIdView: React.FC<IImportFromIdViewProps> = ({
    type, setType, id, setId, account, setAccount, locked, setLocked,
    loadCluserIDs, retrievedData, cluster, setCluster, clearData,
    isOpen, onClose, apply
}) => {
    const renderTypeSelection = useMemo(() => {
        return <Grid item key="type" sm={4}>
            <FormControl disabled={locked} fullWidth>
                <InputLabel id="kyc-es-record-correction-id-type-selector-label">{"Type"}</InputLabel>
                <Select
                    labelId="kyc-es-record-correction-id-type-selector-label"
                    fullWidth value={type}
                    onChange={e => setType(e.target.value as number)}
                    label="Type"
                    size="small"
                >
                    <MenuItem value={1} key={1}>{"Transaction ID"}</MenuItem>
                    <MenuItem value={2} key={2}>{"Universal Socure ID"}</MenuItem>
                    <MenuItem value={3} key={3}>{"Account Specific Socure ID"}</MenuItem>
                </Select>
            </FormControl>
        </Grid>
    }, [type, setType, locked]);

    const renderIdInput = useMemo(() => {
        return <Grid key="ID" item sm={5}>
            <TextField
                disabled={locked}
                fullWidth
                label="ID"
                value={id}
                onChange={e => setId(e.target.value)}
                size="small"
            />
        </Grid>;
    }, [id, setId, locked])

    const renderDivider = useMemo(() => <Grid key="divider-01" item sm={12}>
        <Divider />
    </Grid>, []);

    const renderClusterSelect = useMemo(() => {
        if (cluster && retrievedData) {
            return <React.Fragment>
                <Grid item key="type" sm={8}>
                    <FormControl fullWidth>
                        <InputLabel id="kyc-es-record-correction-id-cluster-selector-label">{"Cluster ID"}</InputLabel>
                        <Select
                            labelId="kyc-es-record-correction-id-cluster-selector-label"
                            fullWidth value={cluster}
                            onChange={e => setCluster(e.target.value)}
                            label="Cluster ID"
                            size="small"
                        >
                            {retrievedData.clusters.map(d => {
                                const value = `${d.cluster_id}:${d.source}`
                                const display = d.cluster_id
                                return <MenuItem value={value} key={value}>{display}</MenuItem>
                            })}
                        </Select>
                    </FormControl>
                </Grid>
                <Grid key="divider-02" item sm={12}>
                    <Divider />
                </Grid>
            </React.Fragment>;
        } else {
            return null;
        }
    }, [cluster, retrievedData, setCluster])

    const renderAccount = useMemo(() => {
        return <Grid item key="account" sm={3}>
            {
                type === 3 ?
                    <TextField
                        disabled={locked}
                        fullWidth
                        label="Account"
                        value={account}
                        onChange={e => {
                            const v = e.target.value;
                            if (v) {
                                setAccount(Math.round(parseInt(v)));
                            } else {
                                setAccount(0);
                            }
                        }}
                        size="small"
                    /> :
                    null
            }
        </Grid>;
    }, [account, setAccount, locked, type]);

    const renderDialogActions = useMemo(() => {
        return <Grid container spacing={1} direction="row-reverse">
            <Grid item key={"Apply"}>
                <Button
                    disabled={!locked || cluster === null || retrievedData === null}
                    variant="contained"
                    color="primary"
                    onClick={() => {
                        const [record, vendor] = (cluster as string).split(":");
                        apply(vendor, record, (retrievedData as IRetrievedIdData).socureID);
                    }}
                >
                    {"Apply"}
                </Button>
            </Grid>
            <Grid item key={"Cancel"}>
                <Button
                    onClick={onClose}
                    variant="contained"
                    color="error"
                >
                    {"Cancel"}
                </Button>
            </Grid>
        </Grid>
    }, [locked, onClose, apply, cluster, retrievedData]);

    const renderRetrieveButtons = useMemo(() => {
        return <React.Fragment>
            <Grid item sm={2} key="btnClear">
                <Button onClick={clearData} fullWidth startIcon={<ClearIcon />} color="warning" variant="contained">
                    {"Clear"}
                </Button>
            </Grid>
            <Grid item sm={2} key="btnGo">
                <Button onClick={loadCluserIDs} fullWidth disabled={locked} startIcon={<DoneIcon />} color="success" variant="contained">
                    {"Go"}
                </Button>
            </Grid>
        </React.Fragment>
    }, [locked, loadCluserIDs, clearData]);

    return <Dialog maxWidth="md" fullWidth onClose={onClose} open={isOpen}>
        <DialogTitle>
            {"Get Cluster from IDs"}
        </DialogTitle>
        <DialogContent style={{ paddingTop: "1%", paddingBottom: "1%" }}>
            <Grid container direction="row" spacing={1}>
                {renderTypeSelection}
                {renderIdInput}
                {renderAccount}
                {renderRetrieveButtons}
                {renderDivider}
                {renderClusterSelect}
            </Grid>
        </DialogContent>
        <DialogActions>
            {renderDialogActions}
        </DialogActions>
    </Dialog>;
}

export default ImportFromIdView;