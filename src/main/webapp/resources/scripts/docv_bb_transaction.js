const VerificationTypes = {
  ImageSubjectDocumentFront: "document_front",
  ImageSubjectDocumentBack: "document_back",
  ImageSubjectBarcode: "document_barcode",
  ImageSubjectSelfieFront: "selfie_front",
  ImageSubjectSelfieLeft: "selfie_left",
  ImageSubjectSelfieRight: "selfie_right",
};

const ExtractorTypes = {
  TMIConservative: "TMIConservative",
  TMIAggressive: "TMIAggressive",
  PDF417: "PDF417",
  MRZ: "MRZInference",
};

// names for upload types used throughout the page
const VerificationNames = {
  fronts: {
    verification: "Photo ID",
    stepCompleted: "Front Uploaded",
    title: "Front of Document",
    category: "document-front",
  },
  backs: {
    verification: "Photo ID",
    stepCompleted: "Back Uploaded",
    title: "Back of Document",
    category: "document-back",
  },
  barcodes: {
    verification: "ID Scan",
    stepCompleted: "Barcode Uploaded",
    title: "Barcode",
    category: "document-barcode",
  },
  selfies: {
    verification: "Selfie",
    stepCompleted: "Selfie Uploaded",
    title: "Selfie",
    category: "selfie",
  },
};

function loadDocVBBData() {
  const urlParams = new URLSearchParams(window.location.search);
  const txnId = urlParams.get("txnId");

  $.ajax({
    url:
      contextUrl + "/superadmin/1/troubleshooting/docv-bb-transaction/" + txnId,
    type: "GET",
    error: function (err) {
      let errMsg;
      try {
        errMsg = JSON.parse(err.responseText);
        alert(`Error: ${errMsg.readable}`);
      } catch {
        errMsg = err.responseText;
        alert(`Error: ${errMsg}`);
      }
    },
    success: function (resp) {
      handleDocVBBResponse(resp);
      $("#docv-bb-txn").css({ display: "block" });
    },
  });
}

function handleDocVBBResponse(data) {
  if (!data) {
    alert("No data found");
    return;
  }

  let parsedData;
  try {
    parsedData = JSON.parse(data);
  } catch (err) {
    alert(`Could not parse transaction data: ${err}`);
  }

  makeTransactionOverviewSection(parsedData);
  makeVerificationsSection(parsedData);
  makePIIAPISection(parsedData);
}

function makeTransactionOverviewSection(parsedData) {
  // transaction id
  $("#docv-bb-txn-overview-info-id").text(`• ID: ${parsedData.transaction_id}`);

  // completed verifications
  const completedVerifications = compileCompletedVerifications(
    parsedData.image_uploads
  );
  const verificationNames = getUploadTypeNames(completedVerifications).map(
    (n) => n.verification
  );
  $("#docv-bb-txn-overview-info-completed-verifications").text(
    `• Completed Verifications: ${strArrayToString([
      // convert to Set to remove duplicates
      ...new Set(verificationNames),
    ])}`
  );

  // photo ID Data viewer
  const collapsible = document.getElementById(
    "docv-bb-txn-overview-additional-photo-id-data-collapsible"
  );
  const photoIDData = compileData(parsedData.fields);
  addNewJSONViewer(collapsible, photoIDData);

  $("#docv-bb-txn-overview-additional-photo-id-data").on("click", function () {
    $(collapsible).css("display") === "block"
      ? $(collapsible).css({ display: "none" })
      : $(collapsible).css({ display: "block" });
  });

  const stepNames = getUploadTypeNames(completedVerifications).map(
    (n) => n.stepCompleted
  );
  $("#docv-bb-txn-overview-additional-steps-completed-steps").text(
    strArrayToString(stepNames)
  );
}

function makeVerificationsSection(parsedData) {
  const verifications = compileCompletedVerifications(parsedData.image_uploads);

  createPhotoDisplay(verifications, parsedData.image_uploads);
  createVerificationTabs(verifications);
  createVerificationToggle(verifications);
  fillVerificationTabData(verifications);
}

function makePIIAPISection(parsedData) {
  const prefix = "#docv-bb-txn-pii-and-api";
  const piiTabID = `${prefix}-pii-tab`;
  const apiTabID = `${prefix}-api-tab`;
  const piiDataID = `${prefix}-pii-data`;
  const apiDataID = `${prefix}-api-data`;
  const tabContainer = `${prefix}-tabs`;
  const dataContainer = `${prefix}-data-container`;

  const container = document.getElementById("docv-bb-txn-pii-and-api-api-data");
  addNewJSONViewer(container, parsedData);

  fillPiiTabTable(parsedData.fields);

  $(piiTabID).on("click", () => {
    selectTab(piiTabID, tabContainer, piiDataID, dataContainer);
  });
  $(apiTabID).on("click", () =>
    selectTab(apiTabID, tabContainer, apiDataID, dataContainer)
  );
}

function createPhotoDisplay(verifications) {
  Object.keys(verifications).forEach((vk) => {
    if (verifications[vk].length) {
      const category = verifications[vk][0].subject.replace(/_/g, "-");
      const container = document.createElement("div");
      $(container).attr({
        id: `docv-bb-txn-verifications-photo-container-${category}`,
        class: "docv-bb-txn-verifications-photo-container",
      });

      const title = document.createElement("h4");
      $(title).attr({
        id: `docv-bb-txn-verifications-photo-container-${category}-title`,
        class: "docv-bb-txn-verifications-photo-container-title",
      });
      $(title).text(VerificationNames[vk].title);

      $(container).append(title);
      $("#docv-bb-txn-verifications-photo-container-container").append(
        container
      );

      addVerificationPhotoContainer(container, verifications[vk]);
    }
  });
}

function addVerificationPhotoContainer(container, verificationsArr) {
  if (!verificationsArr.length) {
    return;
  }

  const photoInfoContainer = document.createElement("div");
  $(photoInfoContainer).attr({
    id: `docv-bb-txn-verifications-photo-container-verification-photo-info-${verificationsArr[0].upload_id}`,
    class: `docv-bb-txn-verifications-photo-container-verification-photo-info`,
  });

  const photoImageContainer = document.createElement("div");
  $(photoImageContainer).attr({
    id: `docv-bb-txn-verifications-photo-container-verification-photo-info-image-container-${verificationsArr[0].upload_id}`,
    class: `docv-bb-txn-verifications-photo-container-verification-photo-info-image-container`,
  });
  $(photoInfoContainer).append(photoImageContainer);

  const arrowContainer = document.createElement("div");
  $(arrowContainer).attr({
    class: "docv-bb-txn-verifications-photo-container-arrow-container",
  });

  // set up the first verification
  addPhotoContainer(
    photoImageContainer,
    getAllImageUploadImages(verificationsArr[0])
  );

  const warning = document.createElement("div");
  $(warning).attr({
    id: `docv-bb-txn-verifications-photo-container-warning-${verificationsArr[0].upload_id}`,
    class: "docv-bb-txn-verifications-photo-container-warning",
  });
  $(warning).text(makeWarningText(verificationsArr[0].created_at));

  $(photoImageContainer).append(arrowContainer);
  addArrowIcon(arrowContainer, "left", () => changeVerification(-1));
  addArrowIcon(arrowContainer, "right", () => changeVerification(1));

  // change verification
  function changeVerification(distance) {
    const currentUploadId = $(photoInfoContainer)
      .attr("id")
      .replace(
        "docv-bb-txn-verifications-photo-container-verification-photo-info-",
        ""
      );
    const position = verificationsArr.findIndex(
      (s) => s.upload_id === currentUploadId
    );
    const newVerification =
      verificationsArr[
        (position + verificationsArr.length + distance) %
          verificationsArr.length
      ];

    $(photoImageContainer).children("img").remove();
    addPhotoContainer(
      photoImageContainer,
      getAllImageUploadImages(newVerification)
    );
    $(photoInfoContainer).attr({
      id: `docv-bb-txn-verifications-photo-container-verification-photo-info-${newVerification.upload_id}`,
    });

    const oldWarningId = `#docv-bb-txn-verifications-photo-container-warning-${currentUploadId}`;
    $(oldWarningId).attr({
      id: `docv-bb-txn-verifications-photo-container-warning-${newVerification.upload_id}`,
    });
    const newWarningId = `#docv-bb-txn-verifications-photo-container-warning-${newVerification.upload_id}`;
    $(newWarningId).text(makeWarningText(newVerification.created_at));
  }

  $(photoInfoContainer).append(warning);
  $(container).append(photoInfoContainer);
}

function addPhotoContainer(container, photoUrlArr) {
  const img = document.createElement("img");
  $(img).attr({
    src: photoUrlArr[0],
  });

  $(img).on("click", function () {
    const currentSrc = $(this).attr("src");
    const position = photoUrlArr.findIndex((s) => s === currentSrc);

    if (position !== -1) {
      $(this).attr({
        src: photoUrlArr[(position + 1) % photoUrlArr.length],
      });
    }
  });

  $(container).append(img);
}

function addArrowIcon(container, direction, handler) {
  const circle = document.createElement("div");
  $(circle).attr({
    class: "docv-bb-txn-verifications-photo-container-arrow-icon",
  });

  const arrow = document.createElement("img");
  $(arrow).attr({
    src: `resources/assets/images/arrow-${direction}.svg`,
  });

  $(circle).append(arrow);

  $(circle).on("click", handler);
  $(container).append(circle);
}

function getAllImageUploadImages(verification) {
  const fullImgUrl = verification.signed_url;
  let derivedImageUrls = [];
  if (verification.derived_images) {
    derivedImageUrls = verification.derived_images.map((d) => d.signed_url);
  }

  return [fullImgUrl, ...derivedImageUrls];
}

function makeWarningText(dateStr) {
  return `THIS IMAGE CONTAINS SENSITIVE INFORMATION\n${formatToUTCDate(
    dateStr
  )}\nALL ACCESS LOGGED - DO NOT SAVE OR SHARE`;
}

function createVerificationTabs() {
  const tabContainerID = "#docv-bb-txn-verifications-data-tab-container > div";
  const dataContainerID = "#docv-bb-txn-verifications-data-container-data";

  $(".docv-bb-txn-verifications-data-tab").on("click", (evt) => {
    const tabID = `#${evt.target.id}`;
    const dataID = tabID.replace("-tab", "");

    selectTab(tabID, tabContainerID, dataID, dataContainerID);
  });
}

function createVerificationToggle(verifications) {
  const toggleID = "#docv-bb-txn-verifications-data-toggle";

  getUploadTypeNames(verifications).forEach((v) => {
    const button = document.createElement("button");
    $(button).attr("id", `docv-bb-txn-verifications-toggle-${v.category}`);
    $(button).attr("class", `docv-bb-txn-verifications-toggle-type`);
    $(button).text(v.title);
    $(button).on("click", function () {
      // reset all toggle styles
      $(`${toggleID} *`).css({
        "background-color": "white",
        color: "black",
      });

      // highlight toggle
      $(this).css({ "background-color": "black", color: "white" });

      // display correct data
      const tabContainerID = "#docv-bb-txn-verifications-data-tab-container";
      const tabSetID = `${tabContainerID}-${v.category}`;

      $(`${tabContainerID} > div`).css({ display: "none" });
      $(tabSetID).css({ display: "block" });
      $(`${tabSetID} > button`).first().trigger("click");
    });

    $(toggleID).append(button);
  });

  // preselect the first toggle
  $(`${toggleID} > button`).first().trigger("click");
}

function fillVerificationTabData(verifications) {
  fillFrontPayloads(verifications["fronts"]);
  fillBackPayloads([...verifications["backs"], ...verifications["barcodes"]]);
  fillSelfiePayloads(verifications["selfies"]);
}

function fillFrontPayloads(fronts) {
  const fullPayloadName = "full_payload.json";
  const exifName = "exif.json";

  const payloads = [];
  const exifs = [];
  const predictions = [];
  const ocrs = [];

  if (!fronts) {
    return;
  }

  fronts.forEach((f) => {
    const fullPayload = f.payloads.find((p) => p.name === fullPayloadName);
    if (fullPayload) {
      getPayloadFromSignedUrl(fullPayload, payloads);
    }

    const exif = f.payloads.find((p) => p.name === exifName);
    if (exif) {
      getPayloadFromSignedUrl(exif, exifs);
    }

    const prediction = f.model_scores;
    if (prediction) {
      predictions.push(prediction);
    }

    const ocrConservative = {};
    const ocrAggressive = {};

    if (f.extractions) {
      f.extractions.forEach((e) => {
        if (e.source === ExtractorTypes.TMIConservative) {
          ocrConservative[e.name] = e.value;
        }
        if (e.source === ExtractorTypes.TMIAggressive) {
          ocrAggressive[e.name] = e.value;
        }
      });

      ocrs.push({
        conservative: ocrConservative,
        aggressive: ocrAggressive,
      });
    }
  });

  const payloadContainer = document.getElementById(
    "docv-bb-txn-verifications-data-payload-document-front"
  );
  const exifContainer = document.getElementById(
    "docv-bb-txn-verifications-data-exif-document-front"
  );
  const predictionContainer = document.getElementById(
    "docv-bb-txn-verifications-data-prediction-document-front"
  );
  const ocrContainer = document.getElementById(
    "docv-bb-txn-verifications-data-ocr-document-front"
  );

  addNewJSONViewerForSignedUrlPayloads(payloadContainer, payloads);
  addNewJSONViewerForSignedUrlPayloads(exifContainer, exifs);
  addNewJSONViewer(predictionContainer, predictions);
  addNewJSONViewer(ocrContainer, ocrs);
}

function fillBackPayloads(backs) {
  const fullPayloadName = "ocr";
  const exifName = "exif.json";
  const barcode1dName = "barcode1d";
  const pdf417Name = "pdf417";

  const payloads = [];
  const exifs = [];
  const predictions = [];
  const barcode1ds = [];
  const pdf417s = [];
  const parsed = [];

  if (!backs) {
    return;
  }

  backs.forEach((b) => {
    // ocr is the the raw payload for the back
    const ocr = b.payloads.find((p) => p.name === fullPayloadName);
    if (ocr) {
      getPayloadFromSignedUrl(ocr, payloads);
    }

    const exif = b.payloads.find((p) => p.name === exifName);
    if (exif) {
      getPayloadFromSignedUrl(exif, exifs);
    }

    const prediction = b.model_scores;
    if (prediction) {
      predictions.push(prediction);
    }

    const barcode1d = b.payloads.find((p) => p.name === barcode1dName);
    if (barcode1d) {
      getPayloadFromNonJSONSignedUrl(barcode1d, barcode1ds);
    }

    const pdf417 = b.payloads.find((p) => p.name === pdf417Name);
    if (pdf417) {
      getPayloadFromSignedUrl(pdf417, pdf417s);
    }

    const extraction = {};

    if (b.extractions) {
      b.extractions.forEach((e) => {
        if (e.source === ExtractorTypes.PDF417) {
          extraction[e.name] = e.value;
        }
      });

      parsed.push(extraction);
    }
  });

  const payloadContainer = document.getElementById(
    "docv-bb-txn-verifications-data-payload-document-back"
  );
  const exifContainer = document.getElementById(
    "docv-bb-txn-verifications-data-exif-document-back"
  );
  const predictionContainer = document.getElementById(
    "docv-bb-txn-verifications-data-prediction-document-back"
  );
  const barcode1dContainer = document.getElementById(
    "docv-bb-txn-verifications-data-barcode-1d-document-back"
  );
  const pdf417Container = document.getElementById(
    "docv-bb-txn-verifications-data-pdf417-document-back"
  );
  const parsedContainer = document.getElementById(
    "docv-bb-txn-verifications-data-parsed-document-back"
  );

  addNewJSONViewerForSignedUrlPayloads(payloadContainer, payloads);
  addNewJSONViewerForSignedUrlPayloads(exifContainer, exifs);
  addNewJSONViewer(predictionContainer, predictions);
  addNewJSONViewerForSignedUrlPayloads(barcode1dContainer, barcode1ds);
  addNewJSONViewerForSignedUrlPayloads(pdf417Container, pdf417s);
  addNewJSONViewer(parsedContainer, parsed);
}

function fillSelfiePayloads(selfies) {
  const faceName = "face";

  const payloads = [];
  const predictions = [];

  if (!selfies) {
    return;
  }

  selfies.forEach((s) => {
    const face = s.payloads.find((p) => p.name === faceName);
    if (face) {
      getPayloadFromSignedUrl(face, payloads);
    }

    const prediction = s.model_scores;
    if (prediction) {
      predictions.push(prediction);
    }
  });

  const payloadContainer = document.getElementById(
    "docv-bb-txn-verifications-data-payload-selfie"
  );
  const predictionContainer = document.getElementById(
    "docv-bb-txn-verifications-data-prediction-selfie"
  );

  addNewJSONViewerForSignedUrlPayloads(payloadContainer, payloads);
  addNewJSONViewer(predictionContainer, predictions);
}

function selectTab(selectedTabID, tabContainer, selectedDataID, dataContainer) {
  const selectedStyles = {
    tab: {
      borderBottom: "2px solid black",
      color: "black",
    },
    data: {
      display: "block",
    },
  };

  const unselectedStyles = {
    tab: {
      borderBottom: "2px solid white",
      color: "#237791",
    },
    data: {
      display: "none",
    },
  };

  $(`${tabContainer} > button`).css({
    "border-bottom": unselectedStyles.tab.borderBottom,
    color: unselectedStyles.tab.color,
  });
  $(`${dataContainer} > div`).css({
    display: unselectedStyles.data.display,
  });

  $(selectedTabID).css({
    "border-bottom": selectedStyles.tab.borderBottom,
    color: selectedStyles.tab.color,
  });
  $(selectedDataID).css("display", selectedStyles.data.display);
}

function compileCompletedVerifications(imageUploads) {
  if (!imageUploads || !imageUploads.length) {
    return;
  }

  return {
    fronts: imageUploads.filter(
      (upload) => upload.subject === VerificationTypes.ImageSubjectDocumentFront
    ),
    backs: imageUploads.filter(
      (upload) => upload.subject === VerificationTypes.ImageSubjectDocumentBack
    ),
    barcodes: imageUploads.filter(
      (upload) => upload.subject === VerificationTypes.ImageSubjectBarcode
    ),
    selfies: imageUploads.filter(
      (upload) =>
        upload.subject === VerificationTypes.ImageSubjectSelfieFront ||
        upload.subject === VerificationTypes.ImageSubjectSelfieLeft ||
        upload.subject === VerificationTypes.ImageSubjectSelfieRight
    ),
  };
}

function fillPiiTabTable(fields) {
  const extractedPIIColClass =
    ".docv-bb-txn-pii-and-api-pii-data-table-extracted-pii-col";
  const ocrConsColClass =
    ".docv-bb-txn-pii-and-api-pii-data-table-ocr-cons-col";
  const ocrAggrColClass =
    ".docv-bb-txn-pii-and-api-pii-data-table-ocr-aggr-col";
  const barcodeColClass = ".docv-bb-txn-pii-and-api-pii-data-table-barcode-col";
  const mrzColClass = ".docv-bb-txn-pii-and-api-pii-data-table-mrz-col";

  const photoIDData = compileData(fields);
  fillColumnWithData(photoIDData, extractedPIIColClass);

  const ocrConservative = compileData(fields, ExtractorTypes.TMIConservative);
  const ocrAggressive = compileData(fields, ExtractorTypes.TMIAggressive);
  fillColumnWithData(ocrConservative, ocrConsColClass);
  fillColumnWithData(ocrAggressive, ocrAggrColClass);

  const barcodeData = compileData(fields, ExtractorTypes.PDF417);
  if (!Object.keys(barcodeData).length) {
    $(barcodeColClass).css({ display: "none" });
  } else {
    fillColumnWithData(barcodeData, barcodeColClass);
  }

  const mrzData = compileData(fields, ExtractorTypes.MRZ);
  if (!Object.keys(mrzData).length) {
    $(mrzColClass).css({ display: "none" });
  } else {
    fillColumnWithData(mrzData, mrzColClass);
  }
}

function compileData(fields, extractorType) {
  const compiled = {};
  const fieldNames = Object.keys(fields);
  for (let i = 0; i < fieldNames.length; i++) {
    const key = fieldNames[i];
    if (!extractorType) {
      compiled[key] = fields[key].value;
    } else {
      source = fields[key].sources.find((s) => s.type === extractorType);

      if (source) {
        compiled[key] = source.value;
      }
    }
  }

  return compiled;
}

function fillColumnWithData(data, colClass) {
  $("#docv-bb-txn-pii-and-api-pii-data-table-body-name")
    .find(colClass)
    .text(makeName(data.given_name, data.middle_name, data.family_name));

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-dob")
    .find(colClass)
    .text(dobWithAge(data.date_of_birth));

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-address")
    .find(colClass)
    .text(
      makeAddress(
        data.address_street,
        data.address_city,
        data.address_subdivision,
        data.address_postal_code,
        data.address_country
      )
    );

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-doc-type")
    .find(colClass)
    .text(idTypeToReadable(data.id_type));

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-country")
    .find(colClass)
    .text(data.address_country);

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-state")
    .find(colClass)
    .text(data.address_subdivision);

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-id-number")
    .find(colClass)
    .text(data.id_number);

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-issue-date")
    .find(colClass)
    .text(data.id_issue_date);

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-expiry")
    .find(colClass)
    .text(data.id_expiry_date);

  $("#docv-bb-txn-pii-and-api-pii-data-table-body-issuer-country")
    .find(colClass)
    .text(data.id_issuer ? data.id_issuer.split("-")[0] : "");
}

function getPayloadFromSignedUrl(payloadInfo, payloadPromisesArr) {
  payloadPromisesArr.push(
    $.ajax({
      url: payloadInfo.signed_url,
      type: "GET",
    })
      .then((resp) => JSON.parse(resp))
      .catch((err) =>
        alert(`could not get ${payloadInfo.name} payload, ${err}`)
      )
  );
}

function getPayloadFromNonJSONSignedUrl(payloadInfo, payloadPromisesArr) {
  payloadPromisesArr.push(
    $.ajax({
      url: payloadInfo.signed_url,
      type: "GET",
    })
      .then((resp) => resp)
      .catch((err) =>
        alert(`could not get ${payloadInfo.name} payload, ${err}`)
      )
  );
}

function addNewJSONViewer(container, json) {
  return new JSONEditor(container, { mode: "view" }, json);
}

function addNewJSONViewerForSignedUrlPayloads(container, payloadPromisesArr) {
  Promise.all(payloadPromisesArr).then((resolvedPayloads) => {
    addNewJSONViewer(container, resolvedPayloads);
  });
}

function createOrAddToArr(item, array) {
  return array ? [...array, item] : [item];
}

function makeName(given, middle, family) {
  if (!given && !middle && !family) {
    return "";
  }

  let name = "";

  if (family) {
    name += family;
  }

  if (given && name) {
    name += `, ${given}`;
  }

  if (middle && name) {
    if (given) {
      name += ` ${middle}`;
    } else {
      name += `, ${middle}`;
    }
  }

  return name;
}

function dobWithAge(dateString) {
  if (!dateString) {
    return "";
  }

  const dob = new Date(dateString);
  const now = new Date();

  let age = now.getFullYear() - dob.getFullYear();

  let isBeforeBirthday = false;
  if (now.getMonth() < dob.getMonth()) {
    isBeforeBirthday = true;
  }

  if (now.getMonth() === dob.getMonth() && now.getDate() < dob.getDate()) {
    isBeforeBirthday = true;
  }

  if (isBeforeBirthday) {
    age -= 1;
  }

  return `${dob.getUTCDate()} ${numberToMonthAbbreviation(
    dob.getUTCMonth()
  )}, ${dob.getUTCFullYear()} (Age ${age})`;
}

function makeAddress(street, city, subdivision, postalcode, country) {
  let address = "";

  if (street) {
    address += street;
  }

  if (city) {
    if (address) {
      address += ", ";
    }
    address += city;
  }

  if (subdivision) {
    if (address) {
      address += ", ";
    }
    address += subdivision;
  }

  if (postalcode) {
    if (address) {
      address += ", ";
    }
    address += postalcode;
  }

  if (country) {
    if (address) {
      address += ", ";
    }
    address += country;
  }

  return address;
}

function numberToMonthAbbreviation(monthNumber) {
  const abbreviations = {
    0: "Jan",
    1: "Feb",
    2: "Mar",
    3: "Apr",
    4: "May",
    5: "Jun",
    6: "Jul",
    7: "Aug",
    8: "Sep",
    9: "Oct",
    10: "Nov",
    11: "Dec",
  };

  return abbreviations[monthNumber];
}

function idTypeToReadable(id) {
  const idType = {
    Passport: "P",
    IDCard: "ID",
    DriversLicense: "DL",
    Visa: "V",
    PermanentResidentCard: "PRC",
    PassportCard: "PC",
    ElectionCard: "E",
    HealthCard: "H",
    CitizenshipCard: "C",
    FirearmLicense: "F",
    NexusCard: "N",
    CertificateOfIndianStatus: "IS",
    Unknown: "UNK",
    DND404DL: "DND404",
    MXResidentCard: "MRC",
    LimitedTermDL: "LTDL",
  };

  const idTypeToLabel = {
    [idType.Passport]: "Passport",
    [idType.IDCard]: "ID card",
    [idType.DriversLicense]: "Driver's license",
    [idType.Visa]: "Visa",
    [idType.PermanentResidentCard]: "Permanent resident card",
    [idType.PassportCard]: "Passport card",
    [idType.Unknown]: "Unknown",
    [idType.ElectionCard]: "Election card",
    [idType.HealthCard]: "Health card",
    [idType.CitizenshipCard]: "Citizenship card",
    [idType.FirearmLicense]: "Firearm license",
    [idType.NexusCard]: "Nexus card",
    [idType.CertificateOfIndianStatus]: "Certificate of Indian status",
    [idType.DND404DL]: "DND 404 Driver's License",
    [idType.MXResidentCard]: "Resident card",
    [idType.LimitedTermDL]: "Limited Term Driver's License",
  };

  return idTypeToLabel[id] || id;
}

function strArrayToString(items) {
  return items.reduce(
    (acc, curr) => (acc === "None" ? "" + curr : acc + ", " + curr),
    "None"
  );
}

function getUploadTypeNames(uploadTypes) {
  // this is so that we always go front -> back -> barcodes -> selfies
  const names = [];

  if (uploadTypes.fronts.length) {
    names.push(VerificationNames.fronts);
  }

  if (uploadTypes.backs.length) {
    names.push(VerificationNames.backs);
  }

  if (uploadTypes.barcodes.length) {
    names.push(VerificationNames.barcodes);
  }

  if (uploadTypes.selfies.length) {
    names.push(VerificationNames.selfies);
  }

  return names;
}

function formatToUTCDate(str, options) {
  return new Date(str)
    .toLocaleDateString(
      "en-us",
      options
        ? options
        : {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "numeric",
            minute: "numeric",
            second: "numeric",
            hourCycle: "h24",
            timeZone: "UTC",
          }
    )
    .replace(",", "");
}

$(document).ready(loadDocVBBData);
