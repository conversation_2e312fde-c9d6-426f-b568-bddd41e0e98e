import React, { useCallback, useEffect, useMemo, useState } from 'react'
import Button from './Button'
import FlagEditor from './FlagEditor'
import Textbox from './Textbox'
import EachFieldsRow from './EachFieldsRow'
import Popup from './Popup'
import Switch from './Switch'

const EachConfig = ({ val }) => {
  return <div className='flex flex-row'>
    <div>{val.name} :</div>
    <div className='ml-16'>{val.value}</div>
  </div>
}

const convertChildToParent = (flag) => {
  const { name, ...rest } = flag;
  return {
    name,
    allEnv : rest
  }
}

const EachFlag = ({ idx, flag, handleFlagClick, handleChildFlagClick, onChange }) => {
  const defaultConfigs = useMemo(() => {
    return flag?.allEnv?.defaultConfigs?.configs;
  }, [ flag ]);

  const editDisabled = useMemo(() => flag?.allEnv?.active !== 'true', [ flag ])

  const handleStatusChange = useCallback((id) => (val) => {
    onChange && onChange(id, -1, flag.name, {...flag.allEnv, active : val.toString()})
  }, [ flag, onChange ])

  const handleChildStatusChange = useCallback((pIndex, cIndex) => (val) => {
    const childrenFlags = [...flag.allEnv.childrenFlags];
    childrenFlags[cIndex].active = val.toString();
    onChange && onChange(pIndex, -1, flag.name, {...flag.allEnv, childrenFlags });
  }, [ flag, onChange ])

  const childFlagCount = useMemo(() => {
    return flag?.allEnv?.childrenFlags?.length || 0;
  }, [ flag ])

  const onChildFlagClick = useCallback((pIndex, cIndex) => () => {
    !editDisabled && handleChildFlagClick && handleChildFlagClick(pIndex, cIndex);
  }, [ editDisabled, handleChildFlagClick ])

  return <>
    <tr>
      <td className='text-center' colSpan={2}><div onClick={handleFlagClick(idx)} key={idx} className='socure-c-button socure-c-button-link'>{flag.name}</div></td>
      <td rowSpan={childFlagCount + 1}>{defaultConfigs
      ? defaultConfigs.map((val, idx) => 
        <EachConfig key={idx} val={val} />)
      : '-'
      }</td>
      <td className='text-center' rowSpan={childFlagCount + 1}>
        <Switch id={`switch_${idx}`} isChecked={flag?.allEnv?.active === 'true'} onChange={handleStatusChange(idx)} />
      </td>
    </tr>
    {flag?.allEnv?.childrenFlags?.map((cf, index) => <tr key={index}>
      <td className='text-center'>
        <div onClick={onChildFlagClick(idx, index)} className={`socure-c-button socure-c-button-link ${editDisabled ? 'disabled' : ''}`}>
          {cf.name}
        </div>
      </td>
      <td className='text-center'>
        <Switch
          id={`switch_cf_${index}`}
          isChecked={cf.active === 'true'}
          disabled={editDisabled}
          onChange={handleChildStatusChange(idx, index)} />
      </td>
    </tr>)}
  </>
}


const JsonViewer = ({ obj }) => {
  const handleClick = useCallback((e) => {
    e.stopPropagation();
  }, [])

  return <div className='popup-container json-viewer' onClick={handleClick}>
    <pre className='p-24'>
      {JSON.stringify(obj, null, 3)}
    </pre>
  </div>
}

const getNextVersion = (v) => {
  if(v) {
    v = v.split('.');
    const suffix = v[1] ? parseInt(v[1]) + 1 : 1;
    v = v[0] + '.' + suffix;
    return v;
  } else {
    return '1.0';
  }
}

const santizeConfig = (config) => {
  return {...config,
    version: getNextVersion(config.version),
    flags : config.flags?.filter(flag => !!flag.name)};
}

const ConfigEditor = ({ config, onCancel, onSave }) => {

  const [ currentConfig, setCurrentConfig ] = useState(config);
  const [ currentFlagIdx, setCurrentFlagIdx ] = useState(-1);
  const [ childFlagIdx, setChildFlagIdx ] = useState(-1);
  const [ currenFlag, setCurrentFlag ] = useState({});
  const [ currentFlagName, setCurrentFlagName ] = useState('');
  const [ showPopup, setShowPopup ] = useState(false);
  const [ showFlagEditor, setShowFlagEditor ] = useState(false);
  const [ parentName, setParentName ] = useState('');

  useEffect(() => {
    setCurrentConfig(config);
    setCurrentFlagIdx(-1);
    setChildFlagIdx(-1);
  }, [ config ])

  const title = useMemo(() => {
    return config.name
      ? 'Edit Config'
      : 'Add New Config' 
  }, [ config ])

  const onConfigNameChange = useCallback((val) => {
    setCurrentConfig(conf => ({...conf,  name : val}))
  }, [])

  const onPopupClose = useCallback(() => {
    setShowPopup(false);
  }, [])

  const onConfigSave = useCallback(() => {
    onSave && onSave(santizeConfig(currentConfig))
  }, [ currentConfig, onSave ])

  const getEmptyFlagIdx = useCallback(() => {
    return currentConfig?.flags?.findIndex(flag => !flag.name);
  }, [ currentConfig ]);

  const handleNewFlag = useCallback(() => {
    const emptyFlagIndex = getEmptyFlagIdx();
    if(emptyFlagIndex >= 0) {
      setCurrentFlagIdx(emptyFlagIndex);
    } else {
      setCurrentConfig(conf => {
        const flags = conf.flags || [];
        setCurrentFlagIdx(flags.length);
        return {...conf, flags: [...flags, { name: '', allEnv : {
          active: 'false'
        }}]}
      })
    }
    setCurrentFlag({ active : 'false' })
    setCurrentFlagName('');
    setShowFlagEditor(true)
    setChildFlagIdx(-1);
    setParentName('')
  }, [ getEmptyFlagIdx ])

  const handleFlagChanges = useCallback((pIndex, cIndex, name, allEnv, removedFlags) => {
    removedFlags = removedFlags || [];
    setCurrentConfig(conf => {
      return {...conf, flags : conf.flags.map((val, idx) => {
        if(pIndex === idx) {
          if(cIndex > -1) {
            return {...val, allEnv : {...val.allEnv, childrenFlags: val.allEnv.childrenFlags.map((cVal, cIdx) => {
              return cIdx === cIndex
                ? {...allEnv, name}
                : cVal;
            })}}
          } else {
            return { name, allEnv }
          }
        } else {
          return val;
        }
      }).filter(val => removedFlags.indexOf(val.name) < 0)}
    })
  }, [])

  const onUnlink = useCallback((pIndex, cIndex) => {
    setCurrentConfig(conf => {
      const newChildrenFlags = [...conf?.flags[pIndex].allEnv.childrenFlags]
      const independentFlag = convertChildToParent(newChildrenFlags[cIndex]);
      newChildrenFlags.splice(cIndex, 1)
      const newConf = {...conf, flags: conf.flags.map((val, idx) =>
        pIndex === idx
        ? {...val, allEnv : {...val.allEnv, childrenFlags : newChildrenFlags}}
        : val
      )};
      return {...newConf, flags : [...newConf.flags, independentFlag]};
    })
  }, [])

  const handleViewJson = useCallback(() => {
    setShowPopup(true);
  }, [])

  const handleFlagClick = useCallback((idx) => () => {
    setCurrentFlagIdx(idx);
    setChildFlagIdx(-1);
    setCurrentFlag(currentConfig?.flags[idx].allEnv || { active: 'false' });
    setCurrentFlagName(currentConfig?.flags[idx].name);
    setParentName('');
    setShowFlagEditor(true);
  }, [ currentConfig ])

  const handleChildFlagClick = useCallback((pIndex, cIndex) => {
    setCurrentFlagIdx(pIndex);
    setChildFlagIdx(cIndex);
    setCurrentFlag(currentConfig?.flags[pIndex].allEnv.childrenFlags[cIndex]);
    setCurrentFlagName(currentConfig?.flags[pIndex].allEnv.childrenFlags[cIndex].name);
    setParentName(currentConfig?.flags[pIndex].name);
    setShowFlagEditor(true);
  }, [ currentConfig ])

  const closeFlagEditor = useCallback(() => {
    setShowFlagEditor(false);
  }, [])

  const getConfigNameField = useCallback(() => {
    return config.name
      ? <div>{config.name}</div>
      : <Textbox value={currentConfig?.name || ''} placeholder='Config Name' onChange={onConfigNameChange} />
  }, [ config, currentConfig, onConfigNameChange])

  const flagList = useMemo(() => {
    return currentConfig?.flags?.reduce((acc, flag) => {
      let newAcc = [...acc, flag.name]
      if(flag?.allEnv?.childrenFlags?.length > 0) {
        newAcc = newAcc.concat(flag.allEnv.childrenFlags.map(val => val.name))
      }
      return newAcc;
    }, []) || [];
  }, [ currentConfig ])

  const parentFlagsWithoutChild = useMemo(() => {
    return currentConfig?.flags?.filter(flag => flag.name && !flag.allEnv?.childrenFlags?.length > 0) || [];
  }, [ currentConfig ])

  return <div className='config-editor'>
    <div className='flex flex-row align-center'>
      <h2>{title}</h2>
      <div className='socure-c-button socure-c-button-link ml-32' onClick={handleViewJson}>Troubleshoot</div> 
    </div>
    <EachFieldsRow label='Name' children={getConfigNameField()} />

    <EachFieldsRow label='Version' children={currentConfig.version || '1.0'} />

    <h2>Flags</h2>
    <table className='flags-list w-100'>
      <thead>
        <tr>
          <th colSpan={2}>Name</th>
          <th>Config</th>
          <th>Active</th>
        </tr>
      </thead>
      <tbody>
        {currentConfig?.flags && currentConfig.flags.map((flag, idx) =>
          !!flag.name
            && <EachFlag
              flag={flag}
              key={idx}
              idx={idx}
              handleFlagClick={handleFlagClick}
              handleChildFlagClick={handleChildFlagClick}
              onChange={handleFlagChanges} />
        )}
      </tbody>
    </table>
    <div className='mt-16'>
      <Button text='+ Add Flag' type='link' onClick={handleNewFlag} />
    </div>

    <div className='mt-32 flex flex-row'>
      <Button text='Save' type='primary' onClick={onConfigSave} />
      <div className='ml-32'>
        <Button text='Cancel' type='secondary' onClick={onCancel} />
      </div>
    </div>

    {showPopup && <Popup onClose={onPopupClose} children={<JsonViewer obj={santizeConfig(currentConfig)} />} />}
    {showFlagEditor
      && <Popup children={
        <FlagEditor
          id={currentFlagIdx}
          childFlagId={childFlagIdx}
          value={currenFlag}
          name={currentFlagName}
          flagList={flagList}
          parentName={parentName}
          parentFlagsWithoutChild={parentFlagsWithoutChild}
          onUnlink={onUnlink}
          onChange={handleFlagChanges}
          onCancel={closeFlagEditor} />
        } />}
  </div>
}

export default ConfigEditor;