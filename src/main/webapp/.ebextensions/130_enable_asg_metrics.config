Resources:
  AWSEBAutoScalingGroup:
    Type: "AWS::AutoScaling::AutoScalingGroup"
    Properties: {
        "MetricsCollection": [ {
            "Granularity": "1Minute",
            "Metrics": [
               GroupMinSize,
               GroupMaxSize,
               GroupDesiredCapacity,
               GroupInServiceInstances,
               GroupPendingInstances,
               GroupStandbyInstances,
               GroupTerminatingInstances,
               GroupTotalInstances
             ]
         } ]
    }
