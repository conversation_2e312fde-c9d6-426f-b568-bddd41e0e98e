import React from 'react';
import ImportFromIdController from './ImportFromIdController';
import ImportFromIdView from './ImportFromIdView';

interface IImportFromIdContainerProps {
    isOpen: boolean;
    onClose: () => void;
    apply: (vendor:string, recordId: string, sid:string) => void;
}

const ImportFromIdContainer: React.FC<IImportFromIdContainerProps> = ({ isOpen, onClose, apply }: IImportFromIdContainerProps) => {

    return <ImportFromIdController>
        {
            props => <ImportFromIdView
                isOpen={isOpen}
                onClose={onClose}
                apply={apply}
                {...props}
            />
        }
    </ImportFromIdController>
}

export default ImportFromIdContainer;