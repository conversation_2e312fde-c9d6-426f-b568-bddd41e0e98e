package me.socure.superadmin.authentication

import com.socure.common.Random
import me.socure.model.BusinessUserRoles
import org.scalatest.{FreeSpec, Matchers}
import org.springframework.security.core.authority.SimpleGrantedAuthority

class SuperAdminUserDetailsServiceImplTest extends FreeSpec with Matchers {

  val service = new SuperAdminUserDetailsServiceImpl

  "should create a new user details object with given username and SUPER-ADMIN role" in {
    val username = Random.alphaNumeric(50)
    val user = service.loadUserByUsername(username)
    user.getUsername shouldBe username
    user.getPassword shouldBe null
    user.getAuthorities shouldBe java.util.Arrays.asList(
      new SimpleGrantedAuthority(BusinessUserRoles.SUPER_ADMIN.name)
    )
  }
}
