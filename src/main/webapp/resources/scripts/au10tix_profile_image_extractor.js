
function parse_xml(resp_str) {
    resp_str = resp_str.trim();
    if(resp_str === '' || resp_str.startsWith('\r\n')) {
        //empty or old response format, discard it.
        return null;
    }
    try {
        return $($.parseXML(resp_str));
    } catch(err) {
        console.log('Unable to parse Document Verification response', err);
        return null;
    }
}

function try_extract_profile_image(stats) {
    var doc_ver_stats = stats['Document Verification'];
    if(doc_ver_stats) {
        for (var req_key in doc_ver_stats) {
            var req_val = doc_ver_stats[req_key];
            if(req_val && req_val.hasOwnProperty('Response')) {
                var resp_text = req_val['Response'];
                var resp_xml = parse_xml(resp_text);
                if(resp_xml !== null) {
                    var img_base64 = resp_xml.find('b\\:DocumentPartImage:first ImageData').text().trim()
                    if(img_base64 !== '') {
                        return img_base64;
                    }
                }
            }
        }
    }
    return null;
}
