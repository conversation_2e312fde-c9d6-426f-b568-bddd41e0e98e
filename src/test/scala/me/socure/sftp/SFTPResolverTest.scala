package me.socure.sftp

import dispatch.Future
import me.socure.account.client.sftp.AccountSftpUserClientImpl
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.model.account.AccountSftpUser
import org.joda.time.DateTime
import org.mockito.Mockito.{never, when}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}
import me.socure.util.EnvUtil
import org.mockito.{Mockito, Matchers => MMatchers}
import software.amazon.awssdk.services.transfer.TransferClient
import software.amazon.awssdk.services.transfer.model.{CreateUserRequest, CreateUserResponse}

import java.util.Properties


class SFTPResolverTest extends FunSuite with Matchers with BeforeAndAfter{

  val accountSFTPUserClient: AccountSftpUserClientImpl = Mockito.mock(classOf[AccountSftpUserClientImpl])
  val envUtil: EnvUtil = Mockito.mock(classOf[EnvUtil])
  val transferClient = Mockito.mock(classOf[TransferClient])

  val props: Properties = new Properties() {
    put("role", "role")
    put("homeDirectoryType", "/client/")
    put("region", "us-east-1")
    put("serverId", "5")
    put("target", "target")
    put("entry", "/client")
  }

  val resolver = new SFTPResolver
  resolver.accountSftpUserClient = accountSFTPUserClient
  resolver.envUtil = envUtil
  resolver.transferClient = transferClient
  when(envUtil.getPropertiesPlain(MMatchers.anyString)) thenReturn props
  resolver.initializeSftpConfig()

  before {
    Mockito.reset(transferClient, accountSFTPUserClient)
  }

  test("should list SFTP Users") {
    val expected = Seq(AccountSftpUser(1, 1, "test", "sftp-user", DateTime.now(), DateTime.now()))
    when(accountSFTPUserClient.listAccountSftpUsers()) thenReturn Future.successful(Right(expected))
    val response = resolver.listSFTPUsers
    response.fold(_ => fail, _ shouldBe expected)
  }

  test("should fail to list SFTP Users") {
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(accountSFTPUserClient.listAccountSftpUsers()) thenReturn Future.successful(Left(expected))
    val response = resolver.listSFTPUsers
    response.fold(_ shouldBe ExceptionCodes.UnknownError.description, _ => fail)
  }

  test("should create SFTP User") {
    val accountId = 1L
    val sftpUser = "guestuser"
    val publicAccountId = "publicaccountid1"
    val sshkey = "sshkey"
    val expected = true
    val createUserResponse = CreateUserResponse.builder()
      .userName(sftpUser)
      .serverId("5")
      .build()

    when(transferClient.createUser(MMatchers.any[CreateUserRequest])) thenReturn createUserResponse
    when(accountSFTPUserClient.saveAccountSftpUser(accountId, sftpUser)) thenReturn Future.successful(Right(expected))
    val response = resolver.createSFTPUser(accountId, publicAccountId, sftpUser, sshkey)
    response.fold(_ => fail, _ shouldBe expected)
    Mockito.verify(transferClient).createUser(MMatchers.any[CreateUserRequest])
    Mockito.verify(accountSFTPUserClient).saveAccountSftpUser(accountId, sftpUser)
  }

  test("should fail to create SFTP Users") {
    val accountId = 1L
    val sftpUser = "guestuser"
    val publicAccountId = "publicaccountid1"
    val sshkey = "sshkey"
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    val createUserResponse = CreateUserResponse.builder()
      .userName(sftpUser)
      .serverId("5")
      .build()
    when(transferClient.createUser(MMatchers.any[CreateUserRequest])) thenReturn createUserResponse
    when(accountSFTPUserClient.saveAccountSftpUser(accountId, sftpUser)) thenReturn Future.successful(Left(expected))
    val response = resolver.createSFTPUser(accountId, publicAccountId, sftpUser, sshkey)
    response.fold(_ => expected, _ => fail)
    Mockito.verify(transferClient).createUser(MMatchers.any[CreateUserRequest])
    Mockito.verify(accountSFTPUserClient).saveAccountSftpUser(accountId, sftpUser)
  }

  test("should fail to create SFTP Users, failed to create IAM User") {
    val accountId = 1L
    val sftpUser = "guestuser"
    val publicAccountId = "publicaccountid1"
    val sshkey = "sshkey"
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    val createUserResponse = CreateUserResponse.builder()
      .build()
    when(transferClient.createUser(MMatchers.any[CreateUserRequest])) thenReturn createUserResponse
    when(accountSFTPUserClient.saveAccountSftpUser(accountId, sftpUser)) thenReturn Future.successful(Left(expected))
    val response = resolver.createSFTPUser(accountId, publicAccountId, sftpUser, sshkey)
    response.fold(_ => expected, _ => fail)
    Mockito.verify(transferClient).createUser(MMatchers.any[CreateUserRequest])
    Mockito.verify(accountSFTPUserClient, never).saveAccountSftpUser(accountId, sftpUser)
  }

  test("should fail to create SFTP Users, Invalid SFTP username provided") {
    val accountId = 1L
    val sftpUser = "*&^%"
    val publicAccountId = "publicaccountid1"
    val sshkey = "sshkey"
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    val createUserResponse = CreateUserResponse.builder()
      .userName(sftpUser)
      .serverId("5")
      .build()
    when(transferClient.createUser(MMatchers.any[CreateUserRequest])) thenReturn createUserResponse
    when(accountSFTPUserClient.saveAccountSftpUser(accountId, sftpUser)) thenReturn Future.successful(Left(expected))
    val response = resolver.createSFTPUser(accountId, publicAccountId, sftpUser, sshkey)
    response.fold(_ => expected, _ => fail)
    Mockito.verify(transferClient, never).createUser(MMatchers.any[CreateUserRequest])
    Mockito.verify(accountSFTPUserClient, never).saveAccountSftpUser(accountId, sftpUser)
  }
}
