<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
<link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
<script src="resources/scripts/jquery.min.js"></script>
<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
<script src="resources/scripts/bootstrap.min.js"></script>
	<script>
        bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		appenv = "#{bucket.appEnv}";
    </script>
<meta charset="UTF-8"/>
<title>Test endpoints</title>

<script language="javascript">
		$(function() {
			
			var authScoreBase = '#{bucket.authScoreBase}';
			window.socure = window.socure || {};
			window.socure.apiKey = null;
			window.socure.apiKeyFetchFailed = false;
			
			function getApiKey(){
				if(!window.socure.apiKeyFetchFailed &amp;&amp; window.socure.apiKey == null){
					$.ajax({
						url : "superadmin/1/my_api_key",
						//dataType : 'jsonp',
						type : 'GET',
						async : false,
						success : function(data) {
							if(typeof(data) != 'undefined' &amp;&amp; data != null &amp;&amp; data.trim() != ''){
								window.socure.apiKey = data;
							}
						}
					});
				}
				return window.socure.apiKey;
			}

			// add multiple select / deselect functionality
			$('#pattern').click(function() {
				$('#pat-result').text("Processing...");
				var network =$('#network').val();
				 
				var socureKey=getApiKey();
				if(socureKey == null || socureKey.trim() == ''){
					alert('Unable to fetch API Key. Please make sure you are logged in and try refreshing page.');
				} else {
					$.ajax({
						url : authScoreBase + "/test/pat.jsonp",
						dataType : 'jsonp',
						data : {
							network : network,
							socureKey:socureKey
							
						},
						type : 'GET',
						success : function(data) {
							$('#pat-result').css('white-space','pre');
							$('#pat-result').text(JSON.stringify(data,null,4));
							
						}
	
					});
				}
			});
			
			// add multiple select / deselect functionality
            $('#loglevelsubmit').click(function() {
                $('#loglevelresult').text("Processing...");
                var levelLog =$('#loglevelselect').val();

                var socureKey=getApiKey();
                
                var dat = {
                        logLevel : levelLog,
                        socurekey:socureKey
                    };
                var logger = $("#loglevellogger").val().trim();
                if(logger.length > 0) {
                	dat.logger = logger;
                }
                
                if(socureKey == null || socureKey.trim() == ''){
                    alert('Unable to fetch API Key. Please make sure you are logged in and try refreshing page.');
                } else {
                    $.ajax({
                        url : authScoreBase + "/log/level/change.jsonp",
                        dataType : 'jsonp',
                        data : dat,
                        type : 'GET',
                        success : function(data) {
                            $('#loglevelresult').css('white-space','pre');
                            $('#loglevelresult').text(data);
                        }

                    });
                }
            });

            $('#txnsubmit').click(function() {
                $('#txnresult').text("Processing...");
                var txnID =$('#txnId').val();

                if(txnID == "" ){
                	alert("Transaction ID can't be empty.");
                	$('#txnId').focus();
                } else {
                	var dat = {
                            txnID : txnID
                        };
                                $.ajax({
                                url : "http://localhost:9090/socure-admin" + "/superadmin/1/txn_search.jsonp",
                                dataType : 'jsonp',
                                data : dat,
                                type : 'GET',
                                success : function(data) {
                                    $('#txnresult').css('white-space','pre');

                                    $("#tnxresult").show();
                                    $('#resultId').text(data.id);
                                    $('#resultTxnId').text(data.transactionId);
                                    $('#resultDate').text(data.transactionDate);
                                    $('#resultTime').text(data.processingTime);
                                    $('#resultApi').text(data.api);
                                    $('#resultParam').text(data.parameters);
                                    $('#resultResponse').text(data.response);
                                    $('#resultError').text(data.error);
                                    $('#resultErrorMsg').text(data.errorMsg);
                                    $('#resultIp').text(data.accountIpAddress);
                                    $('#resultInvo').text(data.originOfInvocation);
                                    $('#resultGeo').text(data.geocode);
                                    $('#resultApiKey').text(data.apiKey);
                                    $('#resultaccountId').text(data.accountId);
                                    $('#resultApiName').text(data.apiName);
                                    $('#resultCache').text(data.cachesUsed);
                                    $('#resultDebug').text(data.debug);
                                    $('#resultDetails').text(data.details);
                                    $('#resultScore').text(data.authScore);
                                    $('#resultConf').text(data.confidence);
                                    $('#resultCode').text(data.reasonCodes);
                                    $('#resultRisk').text(data.hasRiskCode);
                                    $('#resultCustomer').text(data.customerUserId);
                                    $('#resultRun').text(data.runId);
                                    $('#resultUri').text(data.requestURI);
                                    $('#resultUid').text(data.uuid);
                                }

                            });
                }
                
            });


			$('#twitter').click(function() {
                
	          $('#twitter-data').text("Processing...");
	          var socureKey=getApiKey();
				if(socureKey == null || socureKey.trim() == ''){
					alert('Unable to fetch API Key. Please make sure you are logged in and try refreshing page.');
				} else {
					$.ajax({
						url : authScoreBase + "/test/twitter/ratelimits.jsonp",
						dataType : 'jsonp',
						data : {
							socureKey:socureKey
							},
						type : 'GET',
						success : function(data) {
							//$('#twitter-data').css('white-space','pre');
							$('#twitter-data').text(JSON.stringify(data));
						}
	
					});
				}
			});
			
		});



	</script>
</h:head>
<h:body>

<iframe width="1" height="1" frameborder="0" src="" id="downloadHelper" style="display:none;"></iframe>

<div align="center">
		<font color="blue"><h3>
				<h:outputText value="#{param['message']}" />
			</h3> </font>
	</div>
	<br/>
	<br/>
	<div align="right">
	   <h5>
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		
	
		<h:outputLink value="logout">Logout</h:outputLink>
		&nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
	</div>
	<br/>
	<br/>
      <center><h3>Fraud Model Management</h3></center>
     <hr/>
        
		
            <div class="container">
                <div class="row">
                	<h:outputLink value="fraudmodel">Manage Fraud Models</h:outputLink>
                </div>
				<div class="row" id="managedefaultmodeltab">
					<h:outputLink value="defaultmodel">Manage Default Models</h:outputLink>
				</div>
				<div class="row" style="display: none;">
                	<h:outputLink value="fraudmapping">Fraud Model Mapping</h:outputLink>
                </div>
				<div class="row">
					<h:outputLink value="fraud_mapping_v2">Fraud Model Mapping V2</h:outputLink>
				</div>
            </div>
	<script>
		$(function(){
   			function callback() {
      			if (!app.defaultModel) {
        			document.getElementById("managedefaultmodeltab").style.display = "none";
  				};
   			}
   			app.subscribe(callback);
		})

	</script>
</h:body>
</html>