package me.socure.interceptor

import javax.servlet.http.{HttpServletRequest, HttpServletResponse}
import me.socure.common.servlet.HttpMethod
import me.socure.control.center.constants.ControlCenterPermission
import org.mockito.Mockito
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{FreeSpec, Matchers}


class ControlCenterAuthorisationInterceptorTest extends FreeSpec with MockitoSugar with Matchers {


  val httpReq  = mock[HttpServletRequest]
  val httpRes  = mock[HttpServletResponse]

  "should return True" in {
    val controlCenterAuthorisationInterceptor = new ControlCenterAuthorisationInterceptor()
    val spy = Mockito.spy(controlCenterAuthorisationInterceptor)

    Mockito.doReturn(ControlCenterPermission.FULL_ACCESS).when(spy).getPermissionInSession()
    Mockito.when(httpReq.getMethod).thenReturn(HttpMethod.GET.name())

    val res = spy.preHandle(httpReq,httpRes,new Object())

    assert(res == true)
  }

  "should return false" in {
    val controlCenterAuthorisationInterceptor = new ControlCenterAuthorisationInterceptor()
    val spy = Mockito.spy(controlCenterAuthorisationInterceptor)

    Mockito.doReturn(ControlCenterPermission.NO_ACCESS).when(spy).getPermissionInSession()
    Mockito.when(httpReq.getMethod).thenReturn(HttpMethod.GET.name())

    val res = spy.preHandle(httpReq,httpRes,new Object())

    assert(res == false)
  }

  "should return true - POST" in {
    val controlCenterAuthorisationInterceptor = new ControlCenterAuthorisationInterceptor()
    val spy = Mockito.spy(controlCenterAuthorisationInterceptor)

    Mockito.doReturn(ControlCenterPermission.FULL_ACCESS).when(spy).getPermissionInSession()
    Mockito.when(httpReq.getMethod).thenReturn(HttpMethod.POST.name())

    val res = spy.preHandle(httpReq,httpRes,new Object())

    assert(res == true)
  }

  "should return false - POST" in {
    val controlCenterAuthorisationInterceptor = new ControlCenterAuthorisationInterceptor()
    val spy = Mockito.spy(controlCenterAuthorisationInterceptor)

    Mockito.doReturn(ControlCenterPermission.VIEW_ONLY).when(spy).getPermissionInSession()
    Mockito.when(httpReq.getMethod).thenReturn(HttpMethod.POST.name())

    val res = spy.preHandle(httpReq,httpRes,new Object())

    assert(res == false)

  }

  "should return true - HEAD" in {
    val controlCenterAuthorisationInterceptor = new ControlCenterAuthorisationInterceptor()
    val spy = Mockito.spy(controlCenterAuthorisationInterceptor)

    Mockito.doReturn(ControlCenterPermission.VIEW_ONLY).when(spy).getPermissionInSession()
    Mockito.when(httpReq.getMethod).thenReturn(HttpMethod.HEAD.name())

    val res = spy.preHandle(httpReq,httpRes,new Object())

    assert(res == true)
  }

}
