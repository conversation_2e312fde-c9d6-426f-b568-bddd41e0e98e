<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
	<link rel="stylesheet" href="resources/styles/socure-dashboard.css" ></link>
	<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
	<script src="#{bucket.resourceURL}/scripts/jquery.min.js" ></script>
	<script src="#{bucket.resourceURL}/scripts/bootstrap.min.js"></script>
    <script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>

	<meta charset="UTF-8"/>
	<title>Decrypt withPGP Keys</title>

	<script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
        loggedinUsername = "#{loggedinUsername}";
        appenv = "#{bucket.appEnv}";
	</script>

</h:head>
<h:body>

	<div align="center">
		<font color="blue"><h3>
			<h:outputText value="#{param['message']}" />
		</h3> </font>
	</div>
	<br/>
	<br/>
	<div align="right">
		<h5>
			<h:outputLink value="industry">Manage Industry</h:outputLink>
			&nbsp;&nbsp; | &nbsp; &nbsp;
			<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
			&nbsp;&nbsp; | &nbsp; &nbsp;
			<h:outputLink value="active_users">Active Users</h:outputLink>
			&nbsp;&nbsp; | &nbsp; &nbsp;
			<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
			&nbsp;&nbsp; | &nbsp; &nbsp;
			<h:outputLink value="logout">Logout</h:outputLink>
			&nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
	</div>
	<br/>
	<br/>

	<center><h3>Decrypt File</h3></center>
	<hr/>
	<div class="container">
		<div class="row">
			<div class="span12">
				<div class="accordion" id="myaccordion">
					<div class="accordion-group">
						<form action="#{request.contextPath}/superadmin/1/decrypt_input_file" method="post" enctype="multipart/form-data">
							<div class="form-group row">
								<label for="accountid" class="col-sm-2 col-form-label">Select Account</label>
								<div class="col-sm-10">
									<select required="true" id="accountid" name="accountid">
										<option value="NONE"> -- Select Account -- </option>
										<ui:repeat var="name" value="#{accountname}" varStatus="status" >
											<option value="#{name.accountId}">#{name.accountId} - #{name.accountName}</option>
										</ui:repeat>
									</select>
								</div>
							</div>

							<div class="form-group row">
								<label for="file" class="col-sm-2 col-form-label">File</label>
								<div class="col-sm-10">
									<input type="file" id="file" name="file" required="true"/>
								</div>
							</div>
							<input type="submit" class="btn btn-primary" value="Submit"/>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script>
	    window.onload = function() {
	      if(DD_RUM) {
            DD_RUM.startView({name: "/decrypt_with_pgp_keys"});
            DD_RUM.setUser({ "id": loggedinUsername});
          }
        }

        $("form").submit(function(evt){
            var selectedAccount =$('#accountid').val();
			if(selectedAccount == 'NONE'){
			    alert("Please select an account");
                $('#accountid').focus();
                evt.preventDefault();
                return false;
			}
			DD_RUM.track("Decrypt with PGP keys", { accountId: selectedAccount });
        });
	</script>
</h:body>
</html>