@font-face {
  font-family: 'Proxima Nova';
  src: url(../assets/fonts/proximanova-regular.eot);
  src: url(../assets/fonts/proximanova-regular.eot?#iefix) format('embedded-opentype'), url(../assets/fonts/proximanova-regular.woff) format('woff'), url(../assets/fonts/proximanova-regular.ttf) format('truetype'), url(../assets/fonts/proximanova-regular.svg#Proxima-Nova-Regular) format('svg');
  font-weight: 400;
}
@font-face {
  font-family: 'Proxima Nova';
  src: url(../assets/fonts/proximanova-semibold.eot);
  src: url(../assets/fonts/proximanova-semibold.eot?#iefix) format('embedded-opentype'), url(../assets/fonts/proximanova-semibold.woff) format('woff'), url(../assets/fonts/proximanova-semibold.ttf) format('truetype'), url(../assets/fonts/proximanova-semibold.svg#Proxima-Nova-Semibold) format('svg');
  font-weight: 700;
}
@font-face {
  font-family: 'Proxima Nova';
  src: url(../assets/fonts/proximanova-bold.eot);
  src: url(../assets/fonts/proximanova-bold.eot?#iefix) format('embedded-opentype'), url(../assets/fonts/proximanova-bold.woff) format('woff'), url(../assets/fonts/proximanova-bold.ttf) format('truetype'), url(../assets/fonts/proximanova-bold.svg#Proxima-Nova-Bold) format('svg');
  font-weight: 900;
}@font-face {
  font-family: 'Proxima Nova';
  src: url(../assets/fonts/proximanova-regular.eot);
  src: url(../assets/fonts/proximanova-regular.eot?#iefix) format('embedded-opentype'), url(../assets/fonts/proximanova-regular.woff) format('woff'), url(../assets/fonts/proximanova-regular.ttf) format('truetype'), url(../assets/fonts/proximanova-regular.svg#Proxima-Nova-Regular) format('svg');
  font-weight: 400;
}
@font-face {
  font-family: 'Proxima Nova';
  src: url(../assets/fonts/proximanova-semibold.eot);
  src: url(../assets/fonts/proximanova-semibold.eot?#iefix) format('embedded-opentype'), url(../assets/fonts/proximanova-semibold.woff) format('woff'), url(../assets/fonts/proximanova-semibold.ttf) format('truetype'), url(../assets/fonts/proximanova-semibold.svg#Proxima-Nova-Semibold) format('svg');
  font-weight: 700;
}
@font-face {
  font-family: 'Proxima Nova';
  src: url(../assets/fonts/proximanova-bold.eot);
  src: url(../assets/fonts/proximanova-bold.eot?#iefix) format('embedded-opentype'), url(../assets/fonts/proximanova-bold.woff) format('woff'), url(../assets/fonts/proximanova-bold.ttf) format('truetype'), url(../assets/fonts/proximanova-bold.svg#Proxima-Nova-Bold) format('svg');
  font-weight: 900;
}
@font-face {
  font-family: 'Dashboard Icons';
  src: url('../assets/fonts/dashboard-icons.eot');
  src: url('../assets/fonts/dashboard-icons.eot?#iefix') format('embedded-opentype'), url('../assets/fonts/dashboard-icons.woff') format('woff'), url('../assets/fonts/dashboard-icons.ttf') format('truetype'), url('../assets/fonts/dashboard-icons.svg#Dashboard-Icons') format('svg');
  font-weight: normal;
  font-style: normal;
}
.icn-export,
.icn-print,
.icn-calendar,
.icn-check,
.icn-notch-right,
.icn-notch-left,
.icn-notch-down,
.icn-ridges,
.icn-fb,
.icn-tw,
.icn-in,
.icn-gp,
.icn-user,
.icn-multi-user {
  font-family: 'Dashboard Icons';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
}
.icn-export:before {
  content: "\e001";
}
.icn-print:before {
  content: "\e002";
}
.icn-calendar:before {
  content: "\e003";
}
.icn-check:before {
  content: "\e004";
}
.icn-notch-right:before {
  content: "\e005";
}
.icn-notch-left:before {
  content: "\e006";
}
.icn-notch-down:before {
  content: "\e007";
}
.icn-ridges:before {
  content: "\e008";
}
.icn-fb:before {
  content: "\e009";
}
.icn-tw:before {
  content: "\e00a";
}
.icn-in:before {
  content: "\e00b";
}
.icn-gp:before {
  content: "\e00c";
}
.icn-user:before {
  content: "\e000";
}
.icn-multi-user:before {
  content: "\e00d";
}

/*!
 * Bootstrap v2.3.2
 *
 * Copyright 2012 Twitter, Inc
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Designed and built with all the love in the world @twitter by @mdo and @fat.
 */
.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  line-height: 0;
}
.clearfix:after {
  clear: both;
}
.hide-text {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section {
  display: block;
}
audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
audio:not([controls]) {
  display: none;
}
html {
  font-size: 100%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}
a:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
a:hover,
a:active {
  outline: 0;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  /* Responsive images (ensure images don't scale beyond their parents) */
  max-width: 100%;
  /* Part 1: Set a maxium relative to the parent */
  width: auto\9;
  /* IE7-8 need help adjusting responsive images */
  height: auto;
  /* Part 2: Scale the height according to the width, otherwise you get stretching */
  vertical-align: middle;
  border: 0;
  -ms-interpolation-mode: bicubic;
}
#map_canvas img,
.google-maps img {
  max-width: none;
}
button,
input,
select,
textarea {
  margin: 0;
  font-size: 100%;
  vertical-align: middle;
}
button,
input {
  *overflow: visible;
  line-height: normal;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
label,
select,
button,
input[type="button"],
input[type="reset"],
input[type="submit"],
input[type="radio"],
input[type="checkbox"] {
  cursor: pointer;
}
input[type="search"] {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  -webkit-appearance: textfield;
}
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea {
  overflow: auto;
  vertical-align: top;
}
@media print {
  * {
    text-shadow: none !important;
    color: #000 !important;
    background: transparent !important;
    box-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  .ir a:after,
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  @page {
    margin: 0.5cm;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
}
body {
  margin: 0;
  font-family: "Proxima Nova", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 20px;
  color: #333333;
  background-color: #ffffff;
}
body a {
  color: #ee8e3c;
  text-decoration: none;
}
body a:hover,
body a:focus {
  color: #f18f30;
  text-decoration: underline;
}
.img-rounded {
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
}
.img-polaroid {
  padding: 4px;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.img-circle {
  -webkit-border-radius: 500px;
  -moz-border-radius: 500px;
  border-radius: 500px;
}
.row {
  margin-left: -20px;
  *zoom: 1;
}
.row:before,
.row:after {
  display: table;
  content: "";
  line-height: 0;
}
.row:after {
  clear: both;
}
[class*="span"] {
  float: left;
  min-height: 1px;
  margin-left: 20px;
}
.container,
.navbar-static-top .container,
.navbar-fixed-top .container,
.navbar-fixed-bottom .container {
  width: 940px;
}
.span12 {
  width: 940px;
}
.span11 {
  width: 860px;
}
.span10 {
  width: 780px;
}
.span9 {
  width: 700px;
}
.span8 {
  width: 620px;
}
.span7 {
  width: 540px;
}
.span6 {
  width: 460px;
}
.span5 {
  width: 380px;
}
.span4 {
  width: 300px;
}
.span3 {
  width: 220px;
}
.span2 {
  width: 140px;
}
.span1 {
  width: 60px;
}
.offset12 {
  margin-left: 980px;
}
.offset11 {
  margin-left: 900px;
}
.offset10 {
  margin-left: 820px;
}
.offset9 {
  margin-left: 740px;
}
.offset8 {
  margin-left: 660px;
}
.offset7 {
  margin-left: 580px;
}
.offset6 {
  margin-left: 500px;
}
.offset5 {
  margin-left: 420px;
}
.offset4 {
  margin-left: 340px;
}
.offset3 {
  margin-left: 260px;
}
.offset2 {
  margin-left: 180px;
}
.offset1 {
  margin-left: 100px;
}
.row-fluid {
  width: 100%;
  *zoom: 1;
}
.row-fluid:before,
.row-fluid:after {
  display: table;
  content: "";
  line-height: 0;
}
.row-fluid:after {
  clear: both;
}
.row-fluid [class*="span"] {
  display: block;
  width: 100%;
  min-height: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  float: left;
  margin-left: 0;
  *margin-left: -0.05319148936170213%;
}
.row-fluid [class*="span"]:first-child {
  margin-left: 0;
}
.row-fluid .controls-row [class*="span"] + [class*="span"] {
  margin-left: 0;
}
.row-fluid .span12 {
  width: 99.99999999999999%;
  *width: 99.94680851063828%;
}
.row-fluid .span11 {
  width: 91.66666666666666%;
  *width: 91.61347517730495%;
}
.row-fluid .span10 {
  width: 83.33333333333331%;
  *width: 83.2801418439716%;
}
.row-fluid .span9 {
  width: 74.99999999999999%;
  *width: 74.94680851063828%;
}
.row-fluid .span8 {
  width: 66.66666666666666%;
  *width: 66.61347517730495%;
}
.row-fluid .span7 {
  width: 58.33333333333333%;
  *width: 58.28014184397163%;
}
.row-fluid .span6 {
  width: 49.99999999999999%;
  *width: 49.94680851063829%;
}
.row-fluid .span5 {
  width: 41.66666666666666%;
  *width: 41.613475177304956%;
}
.row-fluid .span4 {
  width: 33.33333333333333%;
  *width: 33.28014184397163%;
}
.row-fluid .span3 {
  width: 24.999999999999996%;
  *width: 24.946808510638295%;
}
.row-fluid .span2 {
  width: 16.666666666666664%;
  *width: 16.613475177304963%;
}
.row-fluid .span1 {
  width: 8.333333333333332%;
  *width: 8.28014184397163%;
}
.row-fluid .offset12 {
  margin-left: 99.99999999999999%;
  *margin-left: 99.89361702127657%;
}
.row-fluid .offset12:first-child {
  margin-left: 99.99999999999999%;
  *margin-left: 99.89361702127657%;
}
.row-fluid .offset11 {
  margin-left: 91.66666666666666%;
  *margin-left: 91.56028368794324%;
}
.row-fluid .offset11:first-child {
  margin-left: 91.66666666666666%;
  *margin-left: 91.56028368794324%;
}
.row-fluid .offset10 {
  margin-left: 83.33333333333331%;
  *margin-left: 83.2269503546099%;
}
.row-fluid .offset10:first-child {
  margin-left: 83.33333333333331%;
  *margin-left: 83.2269503546099%;
}
.row-fluid .offset9 {
  margin-left: 74.99999999999999%;
  *margin-left: 74.89361702127657%;
}
.row-fluid .offset9:first-child {
  margin-left: 74.99999999999999%;
  *margin-left: 74.89361702127657%;
}
.row-fluid .offset8 {
  margin-left: 66.66666666666666%;
  *margin-left: 66.56028368794324%;
}
.row-fluid .offset8:first-child {
  margin-left: 66.66666666666666%;
  *margin-left: 66.56028368794324%;
}
.row-fluid .offset7 {
  margin-left: 58.33333333333333%;
  *margin-left: 58.226950354609926%;
}
.row-fluid .offset7:first-child {
  margin-left: 58.33333333333333%;
  *margin-left: 58.226950354609926%;
}
.row-fluid .offset6 {
  margin-left: 49.99999999999999%;
  *margin-left: 49.89361702127659%;
}
.row-fluid .offset6:first-child {
  margin-left: 49.99999999999999%;
  *margin-left: 49.89361702127659%;
}
.row-fluid .offset5 {
  margin-left: 41.66666666666666%;
  *margin-left: 41.560283687943254%;
}
.row-fluid .offset5:first-child {
  margin-left: 41.66666666666666%;
  *margin-left: 41.560283687943254%;
}
.row-fluid .offset4 {
  margin-left: 33.33333333333333%;
  *margin-left: 33.226950354609926%;
}
.row-fluid .offset4:first-child {
  margin-left: 33.33333333333333%;
  *margin-left: 33.226950354609926%;
}
.row-fluid .offset3 {
  margin-left: 24.999999999999996%;
  *margin-left: 24.893617021276594%;
}
.row-fluid .offset3:first-child {
  margin-left: 24.999999999999996%;
  *margin-left: 24.893617021276594%;
}
.row-fluid .offset2 {
  margin-left: 16.666666666666664%;
  *margin-left: 16.56028368794326%;
}
.row-fluid .offset2:first-child {
  margin-left: 16.666666666666664%;
  *margin-left: 16.56028368794326%;
}
.row-fluid .offset1 {
  margin-left: 8.333333333333332%;
  *margin-left: 8.22695035460993%;
}
.row-fluid .offset1:first-child {
  margin-left: 8.333333333333332%;
  *margin-left: 8.22695035460993%;
}
[class*="span"].hide,
.row-fluid [class*="span"].hide {
  display: none;
}
[class*="span"].pull-right,
.row-fluid [class*="span"].pull-right {
  float: right;
}
.container {
  margin-right: auto;
  margin-left: auto;
  *zoom: 1;
}
.container:before,
.container:after {
  display: table;
  content: "";
  line-height: 0;
}
.container:after {
  clear: both;
}
.container-fluid {
  padding-right: 20px;
  padding-left: 20px;
  *zoom: 1;
}
.container-fluid:before,
.container-fluid:after {
  display: table;
  content: "";
  line-height: 0;
}
.container-fluid:after {
  clear: both;
}
p {
  margin: 0 0 10px;
}
.lead {
  margin-bottom: 20px;
  font-size: 21px;
  font-weight: 200;
  line-height: 30px;
}
small {
  font-size: 85%;
}
strong {
  font-weight: bold;
}
em {
  font-style: italic;
}
cite {
  font-style: normal;
}
.muted {
  color: #999999;
}
a.muted:hover,
a.muted:focus {
  color: #808080;
}
.text-warning {
  color: #c09853;
}
a.text-warning:hover,
a.text-warning:focus {
  color: #a47e3c;
}
.text-error {
  color: #b94a48;
}
a.text-error:hover,
a.text-error:focus {
  color: #953b39;
}
.text-info {
  color: #3a87ad;
}
a.text-info:hover,
a.text-info:focus {
  color: #2d6987;
}
.text-success {
  color: #468847;
}
a.text-success:hover,
a.text-success:focus {
  color: #356635;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 10px 0;
  font-family: inherit;
  font-weight: bold;
  line-height: 20px;
  color: inherit;
  text-rendering: optimizelegibility;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small {
  font-weight: normal;
  line-height: 1;
  color: #999999;
}
h1,
h2,
h3 {
  line-height: 40px;
}
h1 {
  font-size: 38.5px;
}
h2 {
  font-size: 31.5px;
}
h3 {
  font-size: 24.5px;
}
h4 {
  font-size: 17.5px;
}
h5 {
  font-size: 14px;
}
h6 {
  font-size: 11.9px;
}
h1 small {
  font-size: 24.5px;
}
h2 small {
  font-size: 17.5px;
}
h3 small {
  font-size: 14px;
}
h4 small {
  font-size: 14px;
}
.page-header {
  padding-bottom: 9px;
  margin: 20px 0 30px;
  border-bottom: 1px solid #eeeeee;
}
ul,
ol {
  padding: 0;
  margin: 0 0 10px 25px;
}
ul ul,
ul ol,
ol ol,
ol ul {
  margin-bottom: 0;
}
li {
  line-height: 20px;
}
ul.unstyled,
ol.unstyled {
  margin-left: 0;
  list-style: none;
}
ul.inline,
ol.inline {
  margin-left: 0;
  list-style: none;
}
ul.inline > li,
ol.inline > li {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  padding-left: 5px;
  padding-right: 5px;
}
dl {
  margin-bottom: 20px;
}
dt,
dd {
  line-height: 20px;
}
dt {
  font-weight: bold;
}
dd {
  margin-left: 10px;
}
.dl-horizontal {
  *zoom: 1;
}
.dl-horizontal:before,
.dl-horizontal:after {
  display: table;
  content: "";
  line-height: 0;
}
.dl-horizontal:after {
  clear: both;
}
.dl-horizontal dt {
  float: left;
  width: 160px;
  clear: left;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dl-horizontal dd {
  margin-left: 180px;
}
hr {
  margin: 20px 0;
  border: 0;
  border-top: 1px solid #eeeeee;
  border-bottom: 1px solid #ffffff;
}
abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #999999;
}
abbr.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
blockquote {
  padding: 0 0 0 15px;
  margin: 0 0 20px;
  border-left: 5px solid #eeeeee;
}
blockquote p {
  margin-bottom: 0;
  font-size: 17.5px;
  font-weight: 300;
  line-height: 1.25;
}
blockquote small {
  display: block;
  line-height: 20px;
  color: #999999;
}
blockquote small:before {
  content: '\2014 \00A0';
}
blockquote.pull-right {
  float: right;
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eeeeee;
  border-left: 0;
}
blockquote.pull-right p,
blockquote.pull-right small {
  text-align: right;
}
blockquote.pull-right small:before {
  content: '';
}
blockquote.pull-right small:after {
  content: '\00A0 \2014';
}
q:before,
q:after,
blockquote:before,
blockquote:after {
  content: "";
}
address {
  display: block;
  margin-bottom: 20px;
  font-style: normal;
  line-height: 20px;
}
code,
pre {
  padding: 0 3px 2px;
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
  font-size: 12px;
  color: #333333;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
code {
  padding: 2px 4px;
  color: #d14;
  background-color: #f7f7f9;
  border: 1px solid #e1e1e8;
  white-space: nowrap;
}
pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 20px;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre;
  white-space: pre-wrap;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
pre.prettyprint {
  margin-bottom: 20px;
}
pre code {
  padding: 0;
  color: inherit;
  white-space: pre;
  white-space: pre-wrap;
  background-color: transparent;
  border: 0;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
form {
  margin: 0 0 20px;
}
fieldset {
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: 40px;
  color: #333333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
legend small {
  font-size: 15px;
  color: #999999;
}
label,
input,
button,
select,
textarea {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
}
input,
button,
select,
textarea {
  font-family: "Proxima Nova", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
label {
  display: block;
  margin-bottom: 5px;
}
/** select,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
  display: inline-block;
  height: 20px;
  padding: 4px 6px;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 20px;
  color: #555555;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  vertical-align: middle;
}
*/
input,
textarea,
.uneditable-input {
  width: 206px;
}
textarea {
  height: auto;
}
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border linear .2s, box-shadow linear .2s;
  -moz-transition: border linear .2s, box-shadow linear .2s;
  -o-transition: border linear .2s, box-shadow linear .2s;
  transition: border linear .2s, box-shadow linear .2s;
}
textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
.uneditable-input:focus {
  border-color: rgba(82, 168, 236, 0.8);
  outline: 0;
  outline: thin dotted \9;
  /* IE6-9 */
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(82,168,236,.6);
  -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(82,168,236,.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(82,168,236,.6);
}
input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  *margin-top: 0;
  /* IE7 */
  margin-top: 1px \9;
  /* IE8-9 */
  line-height: normal;
}
input[type="file"],
input[type="image"],
input[type="submit"],
input[type="reset"],
input[type="button"],
input[type="radio"],
input[type="checkbox"] {
  width: auto;
}
select,
input[type="file"] {
  height: 30px;
  /* In IE7, the height of the select element cannot be changed by height, only font-size */
  *margin-top: 4px;
  /* For IE7, add top margin to align select with labels */
  line-height: 30px;
}
select {
  width: 220px;
  border: 1px solid #cccccc;
  background-color: #ffffff;
}
select[multiple],
select[size] {
  height: auto;
}
select:focus,
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.uneditable-input,
.uneditable-textarea {
  color: #999999;
  background-color: #fcfcfc;
  border-color: #cccccc;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.025);
  -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.025);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.025);
  cursor: not-allowed;
}
.uneditable-input {
  overflow: hidden;
  white-space: nowrap;
}
.uneditable-textarea {
  width: auto;
  height: auto;
}
input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #999999;
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #999999;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999999;
}
.radio,
.checkbox {
  min-height: 20px;
  padding-left: 20px;
}
.radio input[type="radio"],
.checkbox input[type="checkbox"] {
  float: left;
  margin-left: -20px;
}
.controls > .radio:first-child,
.controls > .checkbox:first-child {
  padding-top: 5px;
}
.radio.inline,
.checkbox.inline {
  display: inline-block;
  padding-top: 5px;
  margin-bottom: 0;
  vertical-align: middle;
}
.radio.inline + .radio.inline,
.checkbox.inline + .checkbox.inline {
  margin-left: 10px;
}
.input-mini {
  width: 60px;
}
.input-small {
  width: 90px;
}
.input-medium {
  width: 150px;
}
.input-large {
  width: 210px;
}
.input-xlarge {
  width: 270px;
}
.input-xxlarge {
  width: 530px;
}
input[class*="span"],
select[class*="span"],
textarea[class*="span"],
.uneditable-input[class*="span"],
.row-fluid input[class*="span"],
.row-fluid select[class*="span"],
.row-fluid textarea[class*="span"],
.row-fluid .uneditable-input[class*="span"] {
  float: none;
  margin-left: 0;
}
.input-append input[class*="span"],
.input-append .uneditable-input[class*="span"],
.input-prepend input[class*="span"],
.input-prepend .uneditable-input[class*="span"],
.row-fluid input[class*="span"],
.row-fluid select[class*="span"],
.row-fluid textarea[class*="span"],
.row-fluid .uneditable-input[class*="span"],
.row-fluid .input-prepend [class*="span"],
.row-fluid .input-append [class*="span"] {
  display: inline-block;
}
input,
textarea,
.uneditable-input {
  margin-left: 0;
}
.controls-row [class*="span"] + [class*="span"] {
  margin-left: 20px;
}
input.span12,
textarea.span12,
.uneditable-input.span12 {
  width: 926px;
}
input.span11,
textarea.span11,
.uneditable-input.span11 {
  width: 846px;
}
input.span10,
textarea.span10,
.uneditable-input.span10 {
  width: 766px;
}
input.span9,
textarea.span9,
.uneditable-input.span9 {
  width: 686px;
}
input.span8,
textarea.span8,
.uneditable-input.span8 {
  width: 606px;
}
input.span7,
textarea.span7,
.uneditable-input.span7 {
  width: 526px;
}
input.span6,
textarea.span6,
.uneditable-input.span6 {
  width: 446px;
}
input.span5,
textarea.span5,
.uneditable-input.span5 {
  width: 366px;
}
input.span4,
textarea.span4,
.uneditable-input.span4 {
  width: 286px;
}
input.span3,
textarea.span3,
.uneditable-input.span3 {
  width: 206px;
}
input.span2,
textarea.span2,
.uneditable-input.span2 {
  width: 126px;
}
input.span1,
textarea.span1,
.uneditable-input.span1 {
  width: 46px;
}
.controls-row {
  *zoom: 1;
}
.controls-row:before,
.controls-row:after {
  display: table;
  content: "";
  line-height: 0;
}
.controls-row:after {
  clear: both;
}
.controls-row [class*="span"],
.row-fluid .controls-row [class*="span"] {
  float: left;
}
.controls-row .checkbox[class*="span"],
.controls-row .radio[class*="span"] {
  padding-top: 5px;
}
input[disabled],
select[disabled],
textarea[disabled],
input[readonly],
select[readonly],
textarea[readonly] {
  cursor: not-allowed;
  background-color: #eeeeee;
}
input[type="radio"][disabled],
input[type="checkbox"][disabled],
input[type="radio"][readonly],
input[type="checkbox"][readonly] {
  background-color: transparent;
}
.control-group.warning .control-label,
.control-group.warning .help-block,
.control-group.warning .help-inline {
  color: #c09853;
}
.control-group.warning .checkbox,
.control-group.warning .radio,
.control-group.warning input,
.control-group.warning select,
.control-group.warning textarea {
  color: #c09853;
}
.control-group.warning input,
.control-group.warning select,
.control-group.warning textarea {
  border-color: #c09853;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.control-group.warning input:focus,
.control-group.warning select:focus,
.control-group.warning textarea:focus {
  border-color: #a47e3c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc59e;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc59e;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc59e;
}
.control-group.warning .input-prepend .add-on,
.control-group.warning .input-append .add-on {
  color: #c09853;
  background-color: #fcf8e3;
  border-color: #c09853;
}
.control-group.error .control-label,
.control-group.error .help-block,
.control-group.error .help-inline {
  color: #b94a48;
}
.control-group.error .checkbox,
.control-group.error .radio,
.control-group.error input,
.control-group.error select,
.control-group.error textarea {
  color: #b94a48;
}
.control-group.error input,
.control-group.error select,
.control-group.error textarea {
  border-color: #b94a48;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.control-group.error input:focus,
.control-group.error select:focus,
.control-group.error textarea:focus {
  border-color: #953b39;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
}
.control-group.error .input-prepend .add-on,
.control-group.error .input-append .add-on {
  color: #b94a48;
  background-color: #f2dede;
  border-color: #b94a48;
}
.control-group.success .control-label,
.control-group.success .help-block,
.control-group.success .help-inline {
  color: #468847;
}
.control-group.success .checkbox,
.control-group.success .radio,
.control-group.success input,
.control-group.success select,
.control-group.success textarea {
  color: #468847;
}
.control-group.success input,
.control-group.success select,
.control-group.success textarea {
  border-color: #468847;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.control-group.success input:focus,
.control-group.success select:focus,
.control-group.success textarea:focus {
  border-color: #356635;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7aba7b;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7aba7b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7aba7b;
}
.control-group.success .input-prepend .add-on,
.control-group.success .input-append .add-on {
  color: #468847;
  background-color: #dff0d8;
  border-color: #468847;
}
.control-group.info .control-label,
.control-group.info .help-block,
.control-group.info .help-inline {
  color: #3a87ad;
}
.control-group.info .checkbox,
.control-group.info .radio,
.control-group.info input,
.control-group.info select,
.control-group.info textarea {
  color: #3a87ad;
}
.control-group.info input,
.control-group.info select,
.control-group.info textarea {
  border-color: #3a87ad;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.control-group.info input:focus,
.control-group.info select:focus,
.control-group.info textarea:focus {
  border-color: #2d6987;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7ab5d3;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7ab5d3;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7ab5d3;
}
.control-group.info .input-prepend .add-on,
.control-group.info .input-append .add-on {
  color: #3a87ad;
  background-color: #d9edf7;
  border-color: #3a87ad;
}
input:focus:invalid,
textarea:focus:invalid,
select:focus:invalid {
  color: #b94a48;
  border-color: #ee5f5b;
}
input:focus:invalid:focus,
textarea:focus:invalid:focus,
select:focus:invalid:focus {
  border-color: #e9322d;
  -webkit-box-shadow: 0 0 6px #f8b9b7;
  -moz-box-shadow: 0 0 6px #f8b9b7;
  box-shadow: 0 0 6px #f8b9b7;
}
.form-actions {
  padding: 19px 20px 20px;
  margin-top: 20px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-top: 1px solid #e5e5e5;
  *zoom: 1;
}
.form-actions:before,
.form-actions:after {
  display: table;
  content: "";
  line-height: 0;
}
.form-actions:after {
  clear: both;
}
.help-block,
.help-inline {
  color: #595959;
}
.help-block {
  display: block;
  margin-bottom: 10px;
}
.help-inline {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  vertical-align: middle;
  padding-left: 5px;
}
.input-append,
.input-prepend {
  display: inline-block;
  margin-bottom: 10px;
  vertical-align: middle;
  font-size: 0;
  white-space: nowrap;
}
.input-append input,
.input-prepend input,
.input-append select,
.input-prepend select,
.input-append .uneditable-input,
.input-prepend .uneditable-input,
.input-append .dropdown-menu,
.input-prepend .dropdown-menu,
.input-append .popover,
.input-prepend .popover {
  font-size: 14px;
}
.input-append input,
.input-prepend input,
.input-append select,
.input-prepend select,
.input-append .uneditable-input,
.input-prepend .uneditable-input {
  position: relative;
  margin-bottom: 0;
  *margin-left: 0;
  vertical-align: top;
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}
.input-append input:focus,
.input-prepend input:focus,
.input-append select:focus,
.input-prepend select:focus,
.input-append .uneditable-input:focus,
.input-prepend .uneditable-input:focus {
  z-index: 2;
}
.input-append .add-on,
.input-prepend .add-on {
  display: inline-block;
  width: auto;
  height: 20px;
  min-width: 16px;
  padding: 4px 5px;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  text-align: center;
  text-shadow: 0 1px 0 #ffffff;
  background-color: #eeeeee;
  border: 1px solid #ccc;
}
.input-append .add-on,
.input-prepend .add-on,
.input-append .btn,
.input-prepend .btn,
.input-append .btn-group > .dropdown-toggle,
.input-prepend .btn-group > .dropdown-toggle {
  vertical-align: top;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.input-append .active,
.input-prepend .active {
  background-color: #a9dba9;
  border-color: #46a546;
}
.input-prepend .add-on,
.input-prepend .btn {
  margin-right: -1px;
}
.input-prepend .add-on:first-child,
.input-prepend .btn:first-child {
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}
.input-append input,
.input-append select,
.input-append .uneditable-input {
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}
.input-append input + .btn-group .btn:last-child,
.input-append select + .btn-group .btn:last-child,
.input-append .uneditable-input + .btn-group .btn:last-child {
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}
.input-append .add-on,
.input-append .btn,
.input-append .btn-group {
  margin-left: -1px;
}
.input-append .add-on:last-child,
.input-append .btn:last-child,
.input-append .btn-group:last-child > .dropdown-toggle {
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}
.input-prepend.input-append input,
.input-prepend.input-append select,
.input-prepend.input-append .uneditable-input {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.input-prepend.input-append input + .btn-group .btn,
.input-prepend.input-append select + .btn-group .btn,
.input-prepend.input-append .uneditable-input + .btn-group .btn {
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}
.input-prepend.input-append .add-on:first-child,
.input-prepend.input-append .btn:first-child {
  margin-right: -1px;
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}
.input-prepend.input-append .add-on:last-child,
.input-prepend.input-append .btn:last-child {
  margin-left: -1px;
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}
.input-prepend.input-append .btn-group:first-child {
  margin-left: 0;
}
input.search-query {
  padding-right: 14px;
  padding-right: 4px \9;
  padding-left: 14px;
  padding-left: 4px \9;
  /* IE7-8 doesn't have border-radius, so don't indent the padding */
  margin-bottom: 0;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
}
/* Allow for input prepend/append in search forms */
.form-search .input-append .search-query,
.form-search .input-prepend .search-query {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.form-search .input-append .search-query {
  -webkit-border-radius: 14px 0 0 14px;
  -moz-border-radius: 14px 0 0 14px;
  border-radius: 14px 0 0 14px;
}
.form-search .input-append .btn {
  -webkit-border-radius: 0 14px 14px 0;
  -moz-border-radius: 0 14px 14px 0;
  border-radius: 0 14px 14px 0;
}
.form-search .input-prepend .search-query {
  -webkit-border-radius: 0 14px 14px 0;
  -moz-border-radius: 0 14px 14px 0;
  border-radius: 0 14px 14px 0;
}
.form-search .input-prepend .btn {
  -webkit-border-radius: 14px 0 0 14px;
  -moz-border-radius: 14px 0 0 14px;
  border-radius: 14px 0 0 14px;
}
.form-search input,
.form-inline input,
.form-horizontal input,
.form-search textarea,
.form-inline textarea,
.form-horizontal textarea,
.form-search select,
.form-inline select,
.form-horizontal select,
.form-search .help-inline,
.form-inline .help-inline,
.form-horizontal .help-inline,
.form-search .uneditable-input,
.form-inline .uneditable-input,
.form-horizontal .uneditable-input,
.form-search .input-prepend,
.form-inline .input-prepend,
.form-horizontal .input-prepend,
.form-search .input-append,
.form-inline .input-append,
.form-horizontal .input-append {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  margin-bottom: 0;
  vertical-align: middle;
}
.form-search .hide,
.form-inline .hide,
.form-horizontal .hide {
  display: none;
}
.form-search label,
.form-inline label,
.form-search .btn-group,
.form-inline .btn-group {
  display: inline-block;
}
.form-search .input-append,
.form-inline .input-append,
.form-search .input-prepend,
.form-inline .input-prepend {
  margin-bottom: 0;
}
.form-search .radio,
.form-search .checkbox,
.form-inline .radio,
.form-inline .checkbox {
  padding-left: 0;
  margin-bottom: 0;
  vertical-align: middle;
}
.form-search .radio input[type="radio"],
.form-search .checkbox input[type="checkbox"],
.form-inline .radio input[type="radio"],
.form-inline .checkbox input[type="checkbox"] {
  float: left;
  margin-right: 3px;
  margin-left: 0;
}
.control-group {
  margin-bottom: 10px;
}
legend + .control-group {
  margin-top: 20px;
  -webkit-margin-top-collapse: separate;
}
.form-horizontal .control-group {
  margin-bottom: 20px;
  *zoom: 1;
}
.form-horizontal .control-group:before,
.form-horizontal .control-group:after {
  display: table;
  content: "";
  line-height: 0;
}
.form-horizontal .control-group:after {
  clear: both;
}
.form-horizontal .control-label {
  float: left;
  width: 160px;
  padding-top: 5px;
  text-align: right;
}
.form-horizontal .controls {
  *display: inline-block;
  *padding-left: 20px;
  margin-left: 180px;
  *margin-left: 0;
}
.form-horizontal .controls:first-child {
  *padding-left: 180px;
}
.form-horizontal .help-block {
  margin-bottom: 0;
}
.form-horizontal input + .help-block,
.form-horizontal select + .help-block,
.form-horizontal textarea + .help-block,
.form-horizontal .uneditable-input + .help-block,
.form-horizontal .input-prepend + .help-block,
.form-horizontal .input-append + .help-block {
  margin-top: 10px;
}
.form-horizontal .form-actions {
  padding-left: 180px;
}
table {
  max-width: 100%;
  background-color: transparent;
  border-collapse: collapse;
  border-spacing: 0;
}
.table {
  width: 100%;
  margin-bottom: 20px;
}
.table th,
.table td {
  padding: 8px;
  line-height: 20px;
  text-align: left;
  vertical-align: top;
  border-top: 1px solid #dddddd;
}
.table th {
  font-weight: bold;
}
.table thead th {
  vertical-align: bottom;
}
.table caption + thead tr:first-child th,
.table caption + thead tr:first-child td,
.table colgroup + thead tr:first-child th,
.table colgroup + thead tr:first-child td,
.table thead:first-child tr:first-child th,
.table thead:first-child tr:first-child td {
  border-top: 0;
}
.table tbody + tbody {
  border-top: 2px solid #dddddd;
}
.table .table {
  background-color: #ffffff;
}
.table-condensed th,
.table-condensed td {
  padding: 4px 5px;
}
.table-bordered {
  border: 1px solid #dddddd;
  border-collapse: collapse;
  border-left: 0;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.table-bordered th,
.table-bordered td {
  border-left: 1px solid #dddddd;
}
.table-bordered caption + thead tr:first-child th,
.table-bordered caption + tbody tr:first-child th,
.table-bordered caption + tbody tr:first-child td,
.table-bordered colgroup + thead tr:first-child th,
.table-bordered colgroup + tbody tr:first-child th,
.table-bordered colgroup + tbody tr:first-child td,
.table-bordered thead:first-child tr:first-child th,
.table-bordered tbody:first-child tr:first-child th,
.table-bordered tbody:first-child tr:first-child td {
  border-top: 0;
}
.table-bordered thead:first-child tr:first-child > th:first-child,
.table-bordered tbody:first-child tr:first-child > td:first-child,
.table-bordered tbody:first-child tr:first-child > th:first-child {
  -webkit-border-top-left-radius: 4px;
  -moz-border-radius-topleft: 4px;
  border-top-left-radius: 4px;
}
.table-bordered thead:first-child tr:first-child > th:last-child,
.table-bordered tbody:first-child tr:first-child > td:last-child,
.table-bordered tbody:first-child tr:first-child > th:last-child {
  -webkit-border-top-right-radius: 4px;
  -moz-border-radius-topright: 4px;
  border-top-right-radius: 4px;
}
.table-bordered thead:last-child tr:last-child > th:first-child,
.table-bordered tbody:last-child tr:last-child > td:first-child,
.table-bordered tbody:last-child tr:last-child > th:first-child,
.table-bordered tfoot:last-child tr:last-child > td:first-child,
.table-bordered tfoot:last-child tr:last-child > th:first-child {
  -webkit-border-bottom-left-radius: 4px;
  -moz-border-radius-bottomleft: 4px;
  border-bottom-left-radius: 4px;
}
.table-bordered thead:last-child tr:last-child > th:last-child,
.table-bordered tbody:last-child tr:last-child > td:last-child,
.table-bordered tbody:last-child tr:last-child > th:last-child,
.table-bordered tfoot:last-child tr:last-child > td:last-child,
.table-bordered tfoot:last-child tr:last-child > th:last-child {
  -webkit-border-bottom-right-radius: 4px;
  -moz-border-radius-bottomright: 4px;
  border-bottom-right-radius: 4px;
}
.table-bordered tfoot + tbody:last-child tr:last-child td:first-child {
  -webkit-border-bottom-left-radius: 0;
  -moz-border-radius-bottomleft: 0;
  border-bottom-left-radius: 0;
}
.table-bordered tfoot + tbody:last-child tr:last-child td:last-child {
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-bottomright: 0;
  border-bottom-right-radius: 0;
}
.table-bordered caption + thead tr:first-child th:first-child,
.table-bordered caption + tbody tr:first-child td:first-child,
.table-bordered colgroup + thead tr:first-child th:first-child,
.table-bordered colgroup + tbody tr:first-child td:first-child {
  -webkit-border-top-left-radius: 4px;
  -moz-border-radius-topleft: 4px;
  border-top-left-radius: 4px;
}
.table-bordered caption + thead tr:first-child th:last-child,
.table-bordered caption + tbody tr:first-child td:last-child,
.table-bordered colgroup + thead tr:first-child th:last-child,
.table-bordered colgroup + tbody tr:first-child td:last-child {
  -webkit-border-top-right-radius: 4px;
  -moz-border-radius-topright: 4px;
  border-top-right-radius: 4px;
}
.table-striped tbody > tr:nth-child(odd) > td,
.table-striped tbody > tr:nth-child(odd) > th {
  background-color: #f9f9f9;
}
.table-hover tbody tr:hover > td,
.table-hover tbody tr:hover > th {
  background-color: #f5f5f5;
}
table td[class*="span"],
table th[class*="span"],
.row-fluid table td[class*="span"],
.row-fluid table th[class*="span"] {
  display: table-cell;
  float: none;
  margin-left: 0;
}
.table td.span1,
.table th.span1 {
  float: none;
  width: 44px;
  margin-left: 0;
}
.table td.span2,
.table th.span2 {
  float: none;
  width: 124px;
  margin-left: 0;
}
.table td.span3,
.table th.span3 {
  float: none;
  width: 204px;
  margin-left: 0;
}
.table td.span4,
.table th.span4 {
  float: none;
  width: 284px;
  margin-left: 0;
}
.table td.span5,
.table th.span5 {
  float: none;
  width: 364px;
  margin-left: 0;
}
.table td.span6,
.table th.span6 {
  float: none;
  width: 444px;
  margin-left: 0;
}
.table td.span7,
.table th.span7 {
  float: none;
  width: 524px;
  margin-left: 0;
}
.table td.span8,
.table th.span8 {
  float: none;
  width: 604px;
  margin-left: 0;
}
.table td.span9,
.table th.span9 {
  float: none;
  width: 684px;
  margin-left: 0;
}
.table td.span10,
.table th.span10 {
  float: none;
  width: 764px;
  margin-left: 0;
}
.table td.span11,
.table th.span11 {
  float: none;
  width: 844px;
  margin-left: 0;
}
.table td.span12,
.table th.span12 {
  float: none;
  width: 924px;
  margin-left: 0;
}
.table tbody tr.success > td {
  background-color: #dff0d8;
}
.table tbody tr.error > td {
  background-color: #f2dede;
}
.table tbody tr.warning > td {
  background-color: #fcf8e3;
}
.table tbody tr.info > td {
  background-color: #d9edf7;
}
.table-hover tbody tr.success:hover > td {
  background-color: #d0e9c6;
}
.table-hover tbody tr.error:hover > td {
  background-color: #ebcccc;
}
.table-hover tbody tr.warning:hover > td {
  background-color: #faf2cc;
}
.table-hover tbody tr.info:hover > td {
  background-color: #c4e3f3;
}
[class^="icon-"],
[class*=" icon-"] {
  display: inline-block;
  width: 14px;
  height: 14px;
  *margin-right: .3em;
  line-height: 14px;
  vertical-align: text-top;
  background-image: url("../assets/images/glyphicons-halflings.png");
  background-position: 14px 14px;
  background-repeat: no-repeat;
  margin-top: 1px;
}
/* White icons with optional class, or on hover/focus/active states of certain elements */
.icon-white,
.nav-pills > .active > a > [class^="icon-"],
.nav-pills > .active > a > [class*=" icon-"],
.nav-list > .active > a > [class^="icon-"],
.nav-list > .active > a > [class*=" icon-"],
.navbar-inverse .nav > .active > a > [class^="icon-"],
.navbar-inverse .nav > .active > a > [class*=" icon-"],
.dropdown-menu > li > a:hover > [class^="icon-"],
.dropdown-menu > li > a:focus > [class^="icon-"],
.dropdown-menu > li > a:hover > [class*=" icon-"],
.dropdown-menu > li > a:focus > [class*=" icon-"],
.dropdown-menu > .active > a > [class^="icon-"],
.dropdown-menu > .active > a > [class*=" icon-"],
.dropdown-submenu:hover > a > [class^="icon-"],
.dropdown-submenu:focus > a > [class^="icon-"],
.dropdown-submenu:hover > a > [class*=" icon-"],
.dropdown-submenu:focus > a > [class*=" icon-"] {
  background-image: url("../assets/images/glyphicons-halflings-white.png");
}
.icon-glass {
  background-position: 0      0;
}
.icon-music {
  background-position: -24px 0;
}
.icon-search {
  background-position: -48px 0;
}
.icon-envelope {
  background-position: -72px 0;
}
.icon-heart {
  background-position: -96px 0;
}
.icon-star {
  background-position: -120px 0;
}
.icon-star-empty {
  background-position: -144px 0;
}
.icon-user {
  background-position: -168px 0;
}
.icon-film {
  background-position: -192px 0;
}
.icon-th-large {
  background-position: -216px 0;
}
.icon-th {
  background-position: -240px 0;
}
.icon-th-list {
  background-position: -264px 0;
}
.icon-ok {
  background-position: -288px 0;
}
.icon-remove {
  background-position: -312px 0;
}
.icon-zoom-in {
  background-position: -336px 0;
}
.icon-zoom-out {
  background-position: -360px 0;
}
.icon-off {
  background-position: -384px 0;
}
.icon-signal {
  background-position: -408px 0;
}
.icon-cog {
  background-position: -432px 0;
}
.icon-trash {
  background-position: -456px 0;
}
.icon-home {
  background-position: 0 -24px;
}
.icon-file {
  background-position: -24px -24px;
}
.icon-time {
  background-position: -48px -24px;
}
.icon-road {
  background-position: -72px -24px;
}
.icon-download-alt {
  background-position: -96px -24px;
}
.icon-download {
  background-position: -120px -24px;
}
.icon-upload {
  background-position: -144px -24px;
}
.icon-inbox {
  background-position: -168px -24px;
}
.icon-play-circle {
  background-position: -192px -24px;
}
.icon-repeat {
  background-position: -216px -24px;
}
.icon-refresh {
  background-position: -240px -24px;
}
.icon-list-alt {
  background-position: -264px -24px;
}
.icon-lock {
  background-position: -287px -24px;
}
.icon-flag {
  background-position: -312px -24px;
}
.icon-headphones {
  background-position: -336px -24px;
}
.icon-volume-off {
  background-position: -360px -24px;
}
.icon-volume-down {
  background-position: -384px -24px;
}
.icon-volume-up {
  background-position: -408px -24px;
}
.icon-qrcode {
  background-position: -432px -24px;
}
.icon-barcode {
  background-position: -456px -24px;
}
.icon-tag {
  background-position: 0 -48px;
}
.icon-tags {
  background-position: -25px -48px;
}
.icon-book {
  background-position: -48px -48px;
}
.icon-bookmark {
  background-position: -72px -48px;
}
.icon-print {
  background-position: -96px -48px;
}
.icon-camera {
  background-position: -120px -48px;
}
.icon-font {
  background-position: -144px -48px;
}
.icon-bold {
  background-position: -167px -48px;
}
.icon-italic {
  background-position: -192px -48px;
}
.icon-text-height {
  background-position: -216px -48px;
}
.icon-text-width {
  background-position: -240px -48px;
}
.icon-align-left {
  background-position: -264px -48px;
}
.icon-align-center {
  background-position: -288px -48px;
}
.icon-align-right {
  background-position: -312px -48px;
}
.icon-align-justify {
  background-position: -336px -48px;
}
.icon-list {
  background-position: -360px -48px;
}
.icon-indent-left {
  background-position: -384px -48px;
}
.icon-indent-right {
  background-position: -408px -48px;
}
.icon-facetime-video {
  background-position: -432px -48px;
}
.icon-picture {
  background-position: -456px -48px;
}
.icon-pencil {
  background-position: 0 -72px;
}
.icon-map-marker {
  background-position: -24px -72px;
}
.icon-adjust {
  background-position: -48px -72px;
}
.icon-tint {
  background-position: -72px -72px;
}
.icon-edit {
  background-position: -96px -72px;
}
.icon-share {
  background-position: -120px -72px;
}
.icon-check {
  background-position: -144px -72px;
}
.icon-move {
  background-position: -168px -72px;
}
.icon-step-backward {
  background-position: -192px -72px;
}
.icon-fast-backward {
  background-position: -216px -72px;
}
.icon-backward {
  background-position: -240px -72px;
}
.icon-play {
  background-position: -264px -72px;
}
.icon-pause {
  background-position: -288px -72px;
}
.icon-stop {
  background-position: -312px -72px;
}
.icon-forward {
  background-position: -336px -72px;
}
.icon-fast-forward {
  background-position: -360px -72px;
}
.icon-step-forward {
  background-position: -384px -72px;
}
.icon-eject {
  background-position: -408px -72px;
}
.icon-chevron-left {
  background-position: -432px -72px;
}
.icon-chevron-right {
  background-position: -456px -72px;
}
.icon-plus-sign {
  background-position: 0 -96px;
}
.icon-minus-sign {
  background-position: -24px -96px;
}
.icon-remove-sign {
  background-position: -48px -96px;
}
.icon-ok-sign {
  background-position: -72px -96px;
}
.icon-question-sign {
  background-position: -96px -96px;
}
.icon-info-sign {
  background-position: -120px -96px;
}
.icon-screenshot {
  background-position: -144px -96px;
}
.icon-remove-circle {
  background-position: -168px -96px;
}
.icon-ok-circle {
  background-position: -192px -96px;
}
.icon-ban-circle {
  background-position: -216px -96px;
}
.icon-arrow-left {
  background-position: -240px -96px;
}
.icon-arrow-right {
  background-position: -264px -96px;
}
.icon-arrow-up {
  background-position: -289px -96px;
}
.icon-arrow-down {
  background-position: -312px -96px;
}
.icon-share-alt {
  background-position: -336px -96px;
}
.icon-resize-full {
  background-position: -360px -96px;
}
.icon-resize-small {
  background-position: -384px -96px;
}
.icon-plus {
  background-position: -408px -96px;
}
.icon-minus {
  background-position: -433px -96px;
}
.icon-asterisk {
  background-position: -456px -96px;
}
.icon-exclamation-sign {
  background-position: 0 -120px;
}
.icon-gift {
  background-position: -24px -120px;
}
.icon-leaf {
  background-position: -48px -120px;
}
.icon-fire {
  background-position: -72px -120px;
}
.icon-eye-open {
  background-position: -96px -120px;
}
.icon-eye-close {
  background-position: -120px -120px;
}
.icon-warning-sign {
  background-position: -144px -120px;
}
.icon-plane {
  background-position: -168px -120px;
}
.icon-calendar {
  background-position: -192px -120px;
}
.icon-random {
  background-position: -216px -120px;
  width: 16px;
}
.icon-comment {
  background-position: -240px -120px;
}
.icon-magnet {
  background-position: -264px -120px;
}
.icon-chevron-up {
  background-position: -288px -120px;
}
.icon-chevron-down {
  background-position: -313px -119px;
}
.icon-retweet {
  background-position: -336px -120px;
}
.icon-shopping-cart {
  background-position: -360px -120px;
}
.icon-folder-close {
  background-position: -384px -120px;
  width: 16px;
}
.icon-folder-open {
  background-position: -408px -120px;
  width: 16px;
}
.icon-resize-vertical {
  background-position: -432px -119px;
}
.icon-resize-horizontal {
  background-position: -456px -118px;
}
.icon-hdd {
  background-position: 0 -144px;
}
.icon-bullhorn {
  background-position: -24px -144px;
}
.icon-bell {
  background-position: -48px -144px;
}
.icon-certificate {
  background-position: -72px -144px;
}
.icon-thumbs-up {
  background-position: -96px -144px;
}
.icon-thumbs-down {
  background-position: -120px -144px;
}
.icon-hand-right {
  background-position: -144px -144px;
}
.icon-hand-left {
  background-position: -168px -144px;
}
.icon-hand-up {
  background-position: -192px -144px;
}
.icon-hand-down {
  background-position: -216px -144px;
}
.icon-circle-arrow-right {
  background-position: -240px -144px;
}
.icon-circle-arrow-left {
  background-position: -264px -144px;
}
.icon-circle-arrow-up {
  background-position: -288px -144px;
}
.icon-circle-arrow-down {
  background-position: -312px -144px;
}
.icon-globe {
  background-position: -336px -144px;
}
.icon-wrench {
  background-position: -360px -144px;
}
.icon-tasks {
  background-position: -384px -144px;
}
.icon-filter {
  background-position: -408px -144px;
}
.icon-briefcase {
  background-position: -432px -144px;
}
.icon-fullscreen {
  background-position: -456px -144px;
}
.dropup,
.dropdown {
  position: relative;
}
.dropdown-toggle {
  *margin-bottom: -3px;
}
.dropdown-toggle:active,
.open .dropdown-toggle {
  outline: 0;
}
.caret {
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: top;
  border-top: 4px solid #000000;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  content: "";
}
.dropdown .caret {
  margin-top: 8px;
  margin-left: 2px;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  *border-right-width: 2px;
  *border-bottom-width: 2px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
}
.dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.dropdown-menu .divider {
  *width: 100%;
  height: 1px;
  margin: 9px 1px;
  *margin: -5px 0 5px;
  overflow: hidden;
  background-color: #e5e5e5;
  border-bottom: 1px solid #ffffff;
}
.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 20px;
  color: #333333;
  white-space: nowrap;
}
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus,
.dropdown-submenu:hover > a,
.dropdown-submenu:focus > a {
  text-decoration: none;
  color: #ffffff;
  background-color: #ed8933;
  background-image: -moz-linear-gradient(top, #ee8e3c, #ec8025);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ee8e3c), to(#ec8025));
  background-image: -webkit-linear-gradient(top, #ee8e3c, #ec8025);
  background-image: -o-linear-gradient(top, #ee8e3c, #ec8025);
  background-image: linear-gradient(to bottom, #ee8e3c, #ec8025);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffee8e3c', endColorstr='#ffec8025', GradientType=0);
}
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
  color: #ffffff;
  text-decoration: none;
  outline: 0;
  background-color: #ed8933;
  background-image: -moz-linear-gradient(top, #ee8e3c, #ec8025);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ee8e3c), to(#ec8025));
  background-image: -webkit-linear-gradient(top, #ee8e3c, #ec8025);
  background-image: -o-linear-gradient(top, #ee8e3c, #ec8025);
  background-image: linear-gradient(to bottom, #ee8e3c, #ec8025);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffee8e3c', endColorstr='#ffec8025', GradientType=0);
}
.dropdown-menu > .disabled > a,
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  color: #999999;
}
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: default;
}
.open {
  *z-index: 1000;
}
.open > .dropdown-menu {
  display: block;
}
.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990;
}
.pull-right > .dropdown-menu {
  right: 0;
  left: auto;
}
.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  border-top: 0;
  border-bottom: 4px solid #000000;
  content: "";
}
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 1px;
}
.dropdown-submenu {
  position: relative;
}
.dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  -webkit-border-radius: 0 6px 6px 6px;
  -moz-border-radius: 0 6px 6px 6px;
  border-radius: 0 6px 6px 6px;
}
.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}
.dropup .dropdown-submenu > .dropdown-menu {
  top: auto;
  bottom: 0;
  margin-top: 0;
  margin-bottom: -2px;
  -webkit-border-radius: 5px 5px 5px 0;
  -moz-border-radius: 5px 5px 5px 0;
  border-radius: 5px 5px 5px 0;
}
.dropdown-submenu > a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #cccccc;
  margin-top: 5px;
  margin-right: -10px;
}
.dropdown-submenu:hover > a:after {
  border-left-color: #ffffff;
}
.dropdown-submenu.pull-left {
  float: none;
}
.dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
  -webkit-border-radius: 6px 0 6px 6px;
  -moz-border-radius: 6px 0 6px 6px;
  border-radius: 6px 0 6px 6px;
}
.dropdown .dropdown-menu .nav-header {
  padding-left: 20px;
  padding-right: 20px;
}
.typeahead {
  z-index: 1051;
  margin-top: 2px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
/*
.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  -moz-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
.fade.in {
  opacity: 1;
}
.collapse {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition: height 0.35s ease;
  -moz-transition: height 0.35s ease;
  -o-transition: height 0.35s ease;
  transition: height 0.35s ease;
}
.collapse.in {
  height: auto;
}
*/
.btn {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  border-radius: 100px;
  *margin-left: .3em;
}
.btn:first-child {
  *margin-left: 0;
}
.btn:hover,
.btn:focus {
  color: #333333;
  text-decoration: none;
}
.btn:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.btn.active,
.btn:active {
  background-image: none;
  outline: 0;
}
.btn.disabled,
.btn[disabled] {
  cursor: default;
  background-image: none;
  opacity: 0.65;
  filter: alpha(opacity=65);
}
.btn-large {
  padding: 11px 19px;
  font-size: 17.5px;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  border-radius: 100px;
}
.btn-large [class^="icon-"],
.btn-large [class*=" icon-"] {
  margin-top: 4px;
}
.btn-small {
  padding: 2px 10px;
  font-size: 11.9px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.btn-small [class^="icon-"],
.btn-small [class*=" icon-"] {
  margin-top: 0;
}
.btn-mini [class^="icon-"],
.btn-mini [class*=" icon-"] {
  margin-top: -1px;
}
.btn-mini {
  padding: 0 6px;
  font-size: 10.5px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.btn-block {
  display: block;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%;
}
.btn-primary.active,
.btn-warning.active,
.btn-danger.active,
.btn-success.active,
.btn-info.active,
.btn-inverse.active {
  color: rgba(255, 255, 255, 0.75);
}
.btn-primary {
  background: #ee8e3c;
  *background-color: #eec93c;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.btn-primary.disabled,
.btn-primary[disabled] {
  color: #ffffff;
  background-color: #eec93c;
  *background-color: #ecc325;
}
.btn-primary:active,
.btn-primary.active {
  background-color: #e3b814 \9;
}
.btn-warning {
  background: #fbb450;
  *background-color: #f89406;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active,
.btn-warning.disabled,
.btn-warning[disabled] {
  color: #ffffff;
  background-color: #f89406;
  *background-color: #df8505;
}
.btn-warning:active,
.btn-warning.active {
  background-color: #c67605 \9;
}
.btn-danger {
  background: #ee5f5b;
  *background-color: #bd362f;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active,
.btn-danger.disabled,
.btn-danger[disabled] {
  color: #ffffff;
  background-color: #bd362f;
  *background-color: #a9302a;
}
.btn-danger:active,
.btn-danger.active {
  background-color: #942a25 \9;
}
.btn-success {
  background: #62c462;
  *background-color: #51a351;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.btn-success.disabled,
.btn-success[disabled] {
  color: #ffffff;
  background-color: #51a351;
  *background-color: #499249;
}
.btn-success:active,
.btn-success.active {
  background-color: #408140 \9;
}
.btn-info {
  background: #5bc0de;
  *background-color: #2f96b4;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active,
.btn-info.disabled,
.btn-info[disabled] {
  color: #ffffff;
  background-color: #2f96b4;
  *background-color: #2a85a0;
}
.btn-info:active,
.btn-info.active {
  background-color: #24748c \9;
}
.btn-inverse {
  background: #444444;
  *background-color: #222222;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.btn-inverse:hover,
.btn-inverse:focus,
.btn-inverse:active,
.btn-inverse.active,
.btn-inverse.disabled,
.btn-inverse[disabled] {
  color: #ffffff;
  background-color: #222222;
  *background-color: #151515;
}
.btn-inverse:active,
.btn-inverse.active {
  background-color: #080808 \9;
}
button.btn,
input[type="submit"].btn {
  *padding-top: 3px;
  *padding-bottom: 3px;
}
button.btn::-moz-focus-inner,
input[type="submit"].btn::-moz-focus-inner {
  padding: 0;
  border: 0;
}
button.btn.btn-large,
input[type="submit"].btn.btn-large {
  *padding-top: 7px;
  *padding-bottom: 7px;
}
button.btn.btn-small,
input[type="submit"].btn.btn-small {
  *padding-top: 3px;
  *padding-bottom: 3px;
}
button.btn.btn-mini,
input[type="submit"].btn.btn-mini {
  *padding-top: 1px;
  *padding-bottom: 1px;
}
.btn-link,
.btn-link:active,
.btn-link[disabled] {
  background-color: transparent;
  background-image: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn-link {
  border-color: transparent;
  cursor: pointer;
  color: #ee8e3c;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.btn-link:hover,
.btn-link:focus {
  color: #f18f30;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover,
.btn-link[disabled]:focus {
  color: #333333;
  text-decoration: none;
}
.btn-group {
  position: relative;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  font-size: 0;
  vertical-align: middle;
  white-space: nowrap;
  *margin-left: .3em;
}
.btn-group:first-child {
  *margin-left: 0;
}
.btn-group + .btn-group {
  margin-left: 5px;
}
.btn-toolbar {
  font-size: 0;
  margin-top: 10px;
  margin-bottom: 10px;
}
.btn-toolbar > .btn + .btn,
.btn-toolbar > .btn-group + .btn,
.btn-toolbar > .btn + .btn-group {
  margin-left: 5px;
}
.btn-group > .btn {
  position: relative;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.btn-group > .btn + .btn {
  margin-left: -1px;
}
.btn-group > .btn,
.btn-group > .dropdown-menu,
.btn-group > .popover {
  font-size: 14px;
}
.btn-group > .btn-mini {
  font-size: 10.5px;
}
.btn-group > .btn-small {
  font-size: 11.9px;
}
.btn-group > .btn-large {
  font-size: 17.5px;
}
.btn-group > .btn:first-child {
  margin-left: 0;
  -webkit-border-top-left-radius: 4px;
  -moz-border-radius-topleft: 4px;
  border-top-left-radius: 4px;
  -webkit-border-bottom-left-radius: 4px;
  -moz-border-radius-bottomleft: 4px;
  border-bottom-left-radius: 4px;
}
.btn-group > .btn:last-child,
.btn-group > .dropdown-toggle {
  -webkit-border-top-right-radius: 4px;
  -moz-border-radius-topright: 4px;
  border-top-right-radius: 4px;
  -webkit-border-bottom-right-radius: 4px;
  -moz-border-radius-bottomright: 4px;
  border-bottom-right-radius: 4px;
}
.btn-group > .btn.large:first-child {
  margin-left: 0;
  -webkit-border-top-left-radius: 100px;
  -moz-border-radius-topleft: 100px;
  border-top-left-radius: 100px;
  -webkit-border-bottom-left-radius: 100px;
  -moz-border-radius-bottomleft: 100px;
  border-bottom-left-radius: 100px;
}
.btn-group > .btn.large:last-child,
.btn-group > .large.dropdown-toggle {
  -webkit-border-top-right-radius: 100px;
  -moz-border-radius-topright: 100px;
  border-top-right-radius: 100px;
  -webkit-border-bottom-right-radius: 100px;
  -moz-border-radius-bottomright: 100px;
  border-bottom-right-radius: 100px;
}
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active {
  z-index: 2;
}
.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0;
}
.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px;
  -webkit-box-shadow: inset 1px 0 0 rgba(255,255,255,.125), inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
  -moz-box-shadow: inset 1px 0 0 rgba(255,255,255,.125), inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
  box-shadow: inset 1px 0 0 rgba(255,255,255,.125), inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
  *padding-top: 5px;
  *padding-bottom: 5px;
}
.btn-group > .btn-mini + .dropdown-toggle {
  padding-left: 5px;
  padding-right: 5px;
  *padding-top: 2px;
  *padding-bottom: 2px;
}
.btn-group > .btn-small + .dropdown-toggle {
  *padding-top: 5px;
  *padding-bottom: 4px;
}
.btn-group > .btn-large + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px;
  *padding-top: 7px;
  *padding-bottom: 7px;
}
.btn-group.open .dropdown-toggle {
  background-image: none;
  -webkit-box-shadow: inset 0 2px 4px rgba(0,0,0,.15), 0 1px 2px rgba(0,0,0,.05);
  -moz-box-shadow: inset 0 2px 4px rgba(0,0,0,.15), 0 1px 2px rgba(0,0,0,.05);
  box-shadow: inset 0 2px 4px rgba(0,0,0,.15), 0 1px 2px rgba(0,0,0,.05);
}
.btn-group.open .btn.dropdown-toggle {
  background-color: #e9e9eb;
}
.btn-group.open .btn-primary.dropdown-toggle {
  background-color: #eec93c;
}
.btn-group.open .btn-warning.dropdown-toggle {
  background-color: #f89406;
}
.btn-group.open .btn-danger.dropdown-toggle {
  background-color: #bd362f;
}
.btn-group.open .btn-success.dropdown-toggle {
  background-color: #51a351;
}
.btn-group.open .btn-info.dropdown-toggle {
  background-color: #2f96b4;
}
.btn-group.open .btn-inverse.dropdown-toggle {
  background-color: #222222;
}
.btn .caret {
  margin-top: 8px;
  margin-left: 0;
}
.btn-large .caret {
  margin-top: 6px;
}
.btn-large .caret {
  border-left-width: 5px;
  border-right-width: 5px;
  border-top-width: 5px;
}
.btn-mini .caret,
.btn-small .caret {
  margin-top: 8px;
}
.dropup .btn-large .caret {
  border-bottom-width: 5px;
}
.btn-primary .caret,
.btn-warning .caret,
.btn-danger .caret,
.btn-info .caret,
.btn-success .caret,
.btn-inverse .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.btn-group-vertical {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
}
.btn-group-vertical > .btn {
  display: block;
  float: none;
  max-width: 100%;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.btn-group-vertical > .btn + .btn {
  margin-left: 0;
  margin-top: -1px;
}
.btn-group-vertical > .btn:first-child {
  -webkit-border-radius: 4px 4px 0 0;
  -moz-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}
.btn-group-vertical > .btn:last-child {
  -webkit-border-radius: 0 0 4px 4px;
  -moz-border-radius: 0 0 4px 4px;
  border-radius: 0 0 4px 4px;
}
.btn-group-vertical > .btn-large:first-child {
  -webkit-border-radius: 100px 100px 0 0;
  -moz-border-radius: 100px 100px 0 0;
  border-radius: 100px 100px 0 0;
}
.btn-group-vertical > .btn-large:last-child {
  -webkit-border-radius: 0 0 100px 100px;
  -moz-border-radius: 0 0 100px 100px;
  border-radius: 0 0 100px 100px;
}
.alert {
  padding: 8px 35px 8px 14px;
  margin-bottom: 20px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  background-color: #fcf8e3;
  border: 1px solid #fbeed5;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.alert,
.alert h4 {
  color: #c09853;
}
.alert h4 {
  margin: 0;
}
.alert .close {
  position: relative;
  top: -2px;
  right: -21px;
  line-height: 20px;
}
.alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
  color: #468847;
}
.alert-success h4 {
  color: #468847;
}
.alert-danger,
.alert-error {
  background-color: #f2dede;
  border-color: #eed3d7;
  color: #b94a48;
}
.alert-danger h4,
.alert-error h4 {
  color: #b94a48;
}
.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #3a87ad;
}
.alert-info h4 {
  color: #3a87ad;
}
.alert-block {
  padding-top: 14px;
  padding-bottom: 14px;
}
.alert-block > p,
.alert-block > ul {
  margin-bottom: 0;
}
.alert-block p + p {
  margin-top: 5px;
}
body .nav {
  margin-left: 0;
  margin-bottom: 20px;
  list-style: none;
}
body .nav > li > a {
  display: block;
}
body .nav > li > a:hover,
body .nav > li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
body .nav > li > a > img {
  max-width: none;
}
body .nav > .pull-right {
  float: right;
}
body .nav-header {
  display: block;
  padding: 3px 15px;
  font-size: 11px;
  font-weight: bold;
  line-height: 20px;
  color: #999999;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
}
body .nav li + .nav-header {
  margin-top: 9px;
}
body .nav-list {
  padding-left: 15px;
  padding-right: 15px;
  margin-bottom: 0;
}
body .nav-list > li > a,
body .nav-list .nav-header {
  margin-left: -15px;
  margin-right: -15px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}
body .nav-list > li > a {
  padding: 3px 15px;
}
body .nav-list > .active > a,
body .nav-list > .active > a:hover,
body .nav-list > .active > a:focus {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.2);
  background-color: #ee8e3c;
}
body .nav-list [class^="icon-"],
body .nav-list [class*=" icon-"] {
  margin-right: 2px;
}
body .nav-list .divider {
  *width: 100%;
  height: 1px;
  margin: 9px 1px;
  *margin: -5px 0 5px;
  overflow: hidden;
  background-color: #e5e5e5;
  border-bottom: 1px solid #ffffff;
}
body body .nav-tabs,
body .nav-pills {
  *zoom: 1;
}
body .nav-tabs:before,
body .nav-pills:before,
body .nav-tabs:after,
.nav-pills:after {
  display: table;
  content: "";
  line-height: 0;
}
body .nav-tabs:after,
.nav-pills:after {
  clear: both;
}
body .nav-tabs > li,
.nav-pills > li {
  float: left;
}
body .nav-tabs > li > a,
body .nav-pills > li > a {
  padding-right: 12px;
  padding-left: 12px;
  margin-right: 2px;
  line-height: 14px;
}
body .nav-tabs {
  border-bottom: 1px solid #ddd;
}
body .nav-tabs > li {
  margin-bottom: -1px;
}
body .nav-tabs > li > a {
  padding-top: 8px;
  padding-bottom: 8px;
  line-height: 20px;
  border: 1px solid transparent;
  -webkit-border-radius: 4px 4px 0 0;
  -moz-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}
body .nav-tabs > li > a:hover,
body .nav-tabs > li > a:focus {
  border-color: #eeeeee #eeeeee #dddddd;
}
body .nav-tabs > .active > a,
body .nav-tabs > .active > a:hover,
body .nav-tabs > .active > a:focus {
  color: #555555;
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
  cursor: default;
}
body .nav-pills > li > a {
  padding-top: 8px;
  padding-bottom: 8px;
  margin-top: 2px;
  margin-bottom: 2px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}
body .nav-pills > .active > a,
body .nav-pills > .active > a:hover,
body .nav-pills > .active > a:focus {
  color: #ffffff;
  background-color: #ee8e3c;
}
body .nav-stacked > li {
  float: none;
}
body .nav-stacked > li > a {
  margin-right: 0;
}
body .nav-tabs.nav-stacked {
  border-bottom: 0;
}
body .nav-tabs.nav-stacked > li > a {
  border: 1px solid #ddd;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
body .nav-tabs.nav-stacked > li:first-child > a {
  -webkit-border-top-right-radius: 4px;
  -moz-border-radius-topright: 4px;
  border-top-right-radius: 4px;
  -webkit-border-top-left-radius: 4px;
  -moz-border-radius-topleft: 4px;
  border-top-left-radius: 4px;
}
body .nav-tabs.nav-stacked > li:last-child > a {
  -webkit-border-bottom-right-radius: 4px;
  -moz-border-radius-bottomright: 4px;
  border-bottom-right-radius: 4px;
  -webkit-border-bottom-left-radius: 4px;
  -moz-border-radius-bottomleft: 4px;
  border-bottom-left-radius: 4px;
}
body .nav-tabs.nav-stacked > li > a:hover,
body .nav-tabs.nav-stacked > li > a:focus {
  border-color: #ddd;
  z-index: 2;
}
body .nav-pills.nav-stacked > li > a {
  margin-bottom: 3px;
}
body .nav-pills.nav-stacked > li:last-child > a {
  margin-bottom: 1px;
}
body .nav-tabs .dropdown-menu {
  -webkit-border-radius: 0 0 6px 6px;
  -moz-border-radius: 0 0 6px 6px;
  border-radius: 0 0 6px 6px;
}
body .nav-pills .dropdown-menu {
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
}
body .nav .dropdown-toggle .caret {
  border-top-color: #ee8e3c;
  border-bottom-color: #ee8e3c;
  margin-top: 6px;
}
body .nav .dropdown-toggle:hover .caret,
body .nav .dropdown-toggle:focus .caret {
  border-top-color: #f18f30;
  border-bottom-color: #f18f30;
}
/* move down carets for tabs */
body .nav-tabs .dropdown-toggle .caret {
  margin-top: 8px;
}
.nav .active .dropdown-toggle .caret {
  border-top-color: #fff;
  border-bottom-color: #fff;
}
body .nav-tabs .active .dropdown-toggle .caret {
  border-top-color: #555555;
  border-bottom-color: #555555;
}
body .nav > .dropdown.active > a:hover,
body .nav > .dropdown.active > a:focus {
  cursor: pointer;
}
body .nav-tabs .open .dropdown-toggle,
body .nav-pills .open .dropdown-toggle,
body .nav > li.dropdown.open.active > a:hover,
body .nav > li.dropdown.open.active > a:focus {
  color: #ffffff;
  background-color: #999999;
  border-color: #999999;
}
body .nav li.dropdown.open .caret,
body .nav li.dropdown.open.active .caret,
body .nav li.dropdown.open a:hover .caret,
body .nav li.dropdown.open a:focus .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
  opacity: 1;
  filter: alpha(opacity=100);
}
.tabs-stacked .open > a:hover,
.tabs-stacked .open > a:focus {
  border-color: #999999;
}
.tabbable {
  *zoom: 1;
}
.tabbable:before,
.tabbable:after {
  display: table;
  content: "";
  line-height: 0;
}
.tabbable:after {
  clear: both;
}
.tab-content {
  overflow: auto;
}
body .tabs-below > .nav-tabs,
body .tabs-right > .nav-tabs,
body .tabs-left > .nav-tabs {
  border-bottom: 0;
}
body .tab-content > .tab-pane,
body .pill-content > .pill-pane {
  display: none;
}
body .tab-content > .active,
body .pill-content > .active {
  display: block;
}
.tabs-below > .nav-tabs {
  border-top: 1px solid #ddd;
}
.tabs-below > .nav-tabs > li {
  margin-top: -1px;
  margin-bottom: 0;
}
.tabs-below > .nav-tabs > li > a {
  -webkit-border-radius: 0 0 4px 4px;
  -moz-border-radius: 0 0 4px 4px;
  border-radius: 0 0 4px 4px;
}
.tabs-below > .nav-tabs > li > a:hover,
.tabs-below > .nav-tabs > li > a:focus {
  border-bottom-color: transparent;
  border-top-color: #ddd;
}
.tabs-below > .nav-tabs > .active > a,
.tabs-below > .nav-tabs > .active > a:hover,
.tabs-below > .nav-tabs > .active > a:focus {
  border-color: transparent #ddd #ddd #ddd;
}
.tabs-left > .nav-tabs > li,
.tabs-right > .nav-tabs > li {
  float: none;
}
.tabs-left > .nav-tabs > li > a,
.tabs-right > .nav-tabs > li > a {
  min-width: 74px;
  margin-right: 0;
  margin-bottom: 3px;
}
.tabs-left > .nav-tabs {
  float: left;
  margin-right: 19px;
  border-right: 1px solid #ddd;
}
.tabs-left > .nav-tabs > li > a {
  margin-right: -1px;
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}
.tabs-left > .nav-tabs > li > a:hover,
.tabs-left > .nav-tabs > li > a:focus {
  border-color: #eeeeee #dddddd #eeeeee #eeeeee;
}
.tabs-left > .nav-tabs .active > a,
.tabs-left > .nav-tabs .active > a:hover,
.tabs-left > .nav-tabs .active > a:focus {
  border-color: #ddd transparent #ddd #ddd;
  *border-right-color: #ffffff;
}
.tabs-right > .nav-tabs {
  float: right;
  margin-left: 19px;
  border-left: 1px solid #ddd;
}
.tabs-right > .nav-tabs > li > a {
  margin-left: -1px;
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}
.tabs-right > .nav-tabs > li > a:hover,
.tabs-right > .nav-tabs > li > a:focus {
  border-color: #eeeeee #eeeeee #eeeeee #dddddd;
}
.tabs-right > .nav-tabs .active > a,
.tabs-right > .nav-tabs .active > a:hover,
.tabs-right > .nav-tabs .active > a:focus {
  border-color: #ddd #ddd #ddd transparent;
  *border-left-color: #ffffff;
}
body .nav > .disabled > a {
  color: #999999;
}
.nav > .disabled > a:hover,
.nav > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  cursor: default;
}
.navbar {
  overflow: visible;
  margin-bottom: 20px;
  *position: relative;
  *z-index: 2;
}
.navbar-inner {
  min-height: 43px;
  padding-left: 20px;
  padding-right: 20px;
  background-color: #1c1d29;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  *zoom: 1;
}
.navbar-inner:before,
.navbar-inner:after {
  display: table;
  content: "";
  line-height: 0;
}
.navbar-inner:after {
  clear: both;
}
.navbar .container {
  width: auto;
}
.nav-collapse.collapse {
  height: auto;
  overflow: visible;
}
.navbar .brand {
  float: left;
  display: block;
  padding: 11.5px 20px 11.5px;
  margin-left: -20px;
  font-size: 20px;
  font-weight: 200;
  color: #7d7d85;
}
.navbar .brand:hover,
.navbar .brand:focus {
  text-decoration: none;
}
.navbar-text {
  margin-bottom: 0;
  line-height: 43px;
  color: #7d7d85;
}
.navbar-link {
  color: #7d7d85;
}
.navbar-link:hover,
.navbar-link:focus {
  color: #ffffff;
}
.navbar .divider-vertical {
  height: 43px;
  margin: 0 9px;
  border-left: 1px solid #1c1d29;
  border-right: 1px solid #1c1d29;
}
.navbar .btn,
.navbar .btn-group {
  margin-top: 6.5px;
}
.navbar .btn-group .btn,
.navbar .input-prepend .btn,
.navbar .input-append .btn,
.navbar .input-prepend .btn-group,
.navbar .input-append .btn-group {
  margin-top: 0;
}
.navbar-form {
  margin-bottom: 0;
  *zoom: 1;
}
.navbar-form:before,
.navbar-form:after {
  display: table;
  content: "";
  line-height: 0;
}
.navbar-form:after {
  clear: both;
}
.navbar-form input,
.navbar-form select,
.navbar-form .radio,
.navbar-form .checkbox {
  margin-top: 6.5px;
}
.navbar-form input,
.navbar-form select,
.navbar-form .btn {
  display: inline-block;
  margin-bottom: 0;
}
.navbar-form input[type="image"],
.navbar-form input[type="checkbox"],
.navbar-form input[type="radio"] {
  margin-top: 3px;
}
.navbar-form .input-append,
.navbar-form .input-prepend {
  margin-top: 5px;
  white-space: nowrap;
}
.navbar-form .input-append input,
.navbar-form .input-prepend input {
  margin-top: 0;
}
.navbar-search {
  position: relative;
  float: left;
  margin-top: 6.5px;
  margin-bottom: 0;
}
.navbar-search .search-query {
  margin-bottom: 0;
  padding: 4px 14px;
  font-family: "Proxima Nova", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  font-weight: normal;
  line-height: 1;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
}
.navbar-static-top {
  position: static;
  margin-bottom: 0;
}
.navbar-static-top .navbar-inner {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
  margin-bottom: 0;
}
.navbar-fixed-top .navbar-inner,
.navbar-static-top .navbar-inner {
  border-width: 0 0 1px;
}
.navbar-fixed-bottom .navbar-inner {
  border-width: 1px 0 0;
}
.navbar-fixed-top .navbar-inner,
.navbar-fixed-bottom .navbar-inner {
  padding-left: 0;
  padding-right: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.navbar-static-top .container,
.navbar-fixed-top .container,
.navbar-fixed-bottom .container {
  width: 940px;
}
.navbar-fixed-top {
  top: 0;
}
.navbar-fixed-bottom {
  bottom: 0;
}
.navbar .nav {
  position: relative;
  left: 0;
  display: block;
  float: left;
  margin: 0 10px 0 0;
}
.navbar .nav.pull-right {
  float: right;
  margin-right: 0;
}
.navbar .nav > li {
  float: left;
}
.navbar .nav > li > a {
  float: none;
  padding: 11.5px 15px 11.5px;
  color: #7d7d85;
  text-decoration: none;
}
.navbar .nav .dropdown-toggle .caret {
  margin-top: 8px;
}
.navbar .nav > li > a:focus,
.navbar .nav > li > a:hover {
  background-color: transparent;
  color: #ffffff;
  text-decoration: none;
}
.navbar .nav > .active > a,
.navbar .nav > .active > a:hover,
.navbar .nav > .active > a:focus {
  color: #ffffff;
  text-decoration: none;
  background-color: transparent;
}
.navbar .btn-navbar {
  display: none;
  float: right;
  padding: 7px 10px;
  margin-left: 5px;
  margin-right: 5px;
  background: #12121a;
  *background-color: #12121a;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 0 rgba(255,255,255,.075);
  -moz-box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 0 rgba(255,255,255,.075);
  box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 0 rgba(255,255,255,.075);
}
.navbar .btn-navbar:hover,
.navbar .btn-navbar:focus,
.navbar .btn-navbar:active,
.navbar .btn-navbar.active,
.navbar .btn-navbar.disabled,
.navbar .btn-navbar[disabled] {
  color: #ffffff;
  background-color: #12121a;
  *background-color: #07080b;
}
.navbar .btn-navbar:active,
.navbar .btn-navbar.active {
  background-color: #000000 \9;
}
.navbar .btn-navbar .icon-bar {
  display: block;
  width: 18px;
  height: 2px;
  background-color: #f5f5f5;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);
}
.btn-navbar .icon-bar + .icon-bar {
  margin-top: 3px;
}
.navbar .nav > li > .dropdown-menu:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ccc;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  top: -7px;
  left: 9px;
}
.navbar .nav > li > .dropdown-menu:after {
  content: '';
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  position: absolute;
  top: -6px;
  left: 10px;
}
.navbar-fixed-bottom .nav > li > .dropdown-menu:before {
  border-top: 7px solid #ccc;
  border-top-color: rgba(0, 0, 0, 0.2);
  border-bottom: 0;
  bottom: -7px;
  top: auto;
}
.navbar-fixed-bottom .nav > li > .dropdown-menu:after {
  border-top: 6px solid #ffffff;
  border-bottom: 0;
  bottom: -6px;
  top: auto;
}
.navbar .nav li.dropdown > a:hover .caret,
.navbar .nav li.dropdown > a:focus .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.navbar .nav li.dropdown.open > .dropdown-toggle,
.navbar .nav li.dropdown.active > .dropdown-toggle,
.navbar .nav li.dropdown.open.active > .dropdown-toggle {
  background-color: transparent;
  color: #ffffff;
}
.navbar .nav li.dropdown > .dropdown-toggle .caret {
  border-top-color: #7d7d85;
  border-bottom-color: #7d7d85;
}
.navbar .nav li.dropdown.open > .dropdown-toggle .caret,
.navbar .nav li.dropdown.active > .dropdown-toggle .caret,
.navbar .nav li.dropdown.open.active > .dropdown-toggle .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.navbar .pull-right > li > .dropdown-menu,
.navbar .nav > li > .dropdown-menu.pull-right {
  left: auto;
  right: 0;
}
.navbar .pull-right > li > .dropdown-menu:before,
.navbar .nav > li > .dropdown-menu.pull-right:before {
  left: auto;
  right: 12px;
}
.navbar .pull-right > li > .dropdown-menu:after,
.navbar .nav > li > .dropdown-menu.pull-right:after {
  left: auto;
  right: 13px;
}
.navbar .pull-right > li > .dropdown-menu .dropdown-menu,
.navbar .nav > li > .dropdown-menu.pull-right .dropdown-menu {
  left: auto;
  right: 100%;
  margin-left: 0;
  margin-right: -1px;
  -webkit-border-radius: 6px 0 6px 6px;
  -moz-border-radius: 6px 0 6px 6px;
  border-radius: 6px 0 6px 6px;
}
.navbar-inverse {
  /*.nav > li > a:focus,
  .nav > li > a:hover {
    background-color: @navbarInverseLinkBackgroundHover;
    color: @navbarInverseLinkColorHover;
  }

  .nav .active > a,
  .nav .active > a:hover,
  .nav .active > a:focus {
    color: @navbarInverseLinkColorActive;
    background-color: @navbarInverseLinkBackgroundActive;
  }*/
}
.navbar-inverse .navbar-inner {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  min-height: 75px;
  background-color: #ffffff;
  border-color: #dcdcde;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.navbar-inverse .brand,
.navbar-inverse .nav > li > a {
  color: #6d6e76;
}
.navbar-inverse .brand:hover,
.navbar-inverse .nav > li > a:hover,
.navbar-inverse .brand:focus,
.navbar-inverse .nav > li > a:focus {
  color: #f38f1e;
}
.navbar-inverse .brand {
  padding: 27.5px 20px 27.5px;
  color: #6d6e76;
}
.navbar-inverse .navbar-text {
  line-height: 75px;
  color: #929299;
}
.navbar-inverse .navbar-link {
  color: #6d6e76;
}
.navbar-inverse .navbar-link:hover,
.navbar-inverse .navbar-link:focus {
  color: #f38f1e;
}
.navbar-inverse .divider-vertical {
  display: inline-block;
  height: 75px;
  border-left-color: #dcdcde;
  border-right: none;
  vertical-align: top;
}
.navbar-inverse .nav > li > a {
  padding: 27.5px 15px 26.5px;
}
.navbar-inverse .nav li.dropdown.open > .dropdown-toggle,
.navbar-inverse .nav li.dropdown.active > .dropdown-toggle,
.navbar-inverse .nav li.dropdown.open.active > .dropdown-toggle {
  background-color: #ffffff;
  color: #f38f1e;
}
.navbar-inverse .nav li.dropdown > a:hover .caret,
.navbar-inverse .nav li.dropdown > a:focus .caret {
  border-top-color: #f38f1e;
  border-bottom-color: #f38f1e;
}
.navbar-inverse .nav li.dropdown > .dropdown-toggle .caret {
  border-top-color: #6d6e76;
  border-bottom-color: #6d6e76;
}
.navbar-inverse .nav li.dropdown.open > .dropdown-toggle .caret,
.navbar-inverse .nav li.dropdown.active > .dropdown-toggle .caret,
.navbar-inverse .nav li.dropdown.open.active > .dropdown-toggle .caret {
  border-top-color: #f38f1e;
  border-bottom-color: #f38f1e;
}
.navbar-inverse .navbar-search .search-query {
  color: #ffffff;
  background-color: #ffffff;
  border-color: #ffffff;
  -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,.1), 0 1px 0 rgba(255,255,255,.15);
  -moz-box-shadow: inset 0 1px 2px rgba(0,0,0,.1), 0 1px 0 rgba(255,255,255,.15);
  box-shadow: inset 0 1px 2px rgba(0,0,0,.1), 0 1px 0 rgba(255,255,255,.15);
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
}
.navbar-inverse .navbar-search .search-query:-moz-placeholder {
  color: #cccccc;
}
.navbar-inverse .navbar-search .search-query:-ms-input-placeholder {
  color: #cccccc;
}
.navbar-inverse .navbar-search .search-query::-webkit-input-placeholder {
  color: #cccccc;
}
.navbar-inverse .navbar-search .search-query:focus,
.navbar-inverse .navbar-search .search-query.focused {
  padding: 5px 15px;
  color: #333333;
  text-shadow: 0 1px 0 #ffffff;
  background-color: #ffffff;
  border: 0;
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
  outline: 0;
}
.navbar-inverse .btn-navbar {
  background: #f2f2f2;
  *background-color: #f2f2f2;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.navbar-inverse .btn-navbar:hover,
.navbar-inverse .btn-navbar:focus,
.navbar-inverse .btn-navbar:active,
.navbar-inverse .btn-navbar.active,
.navbar-inverse .btn-navbar.disabled,
.navbar-inverse .btn-navbar[disabled] {
  color: #ffffff;
  background-color: #f2f2f2;
  *background-color: #e5e5e5;
}
.navbar-inverse .btn-navbar:active,
.navbar-inverse .btn-navbar.active {
  background-color: #d9d9d9 \9;
}
/*
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1028;
  background-color: #1d1e2a;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop,
.modal-backdrop.fade.in {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.modal {
  position: fixed;
  top: 10%;
  left: 50%;
  z-index: 1029;
  width: 376px;
  margin-left: -188px;
  background-color: #ffffff;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
  box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding-box;
  background-clip: padding-box;
  outline: none;
}
.modal.fade {
  -webkit-transition: opacity .3s linear, top .3s ease-out;
  -moz-transition: opacity .3s linear, top .3s ease-out;
  -o-transition: opacity .3s linear, top .3s ease-out;
  transition: opacity .3s linear, top .3s ease-out;
  top: -25%;
}
.modal.fade.in {
  top: 10%;
}
.modal .confirm {
  margin-left: 5px;
}
.modal.modal-lg {
  width: 800px;
  margin-left: -400px;
}
.modal-header {
  padding: 9px 15px;
  background-color: #3d3e48;
  color: white;
  -webkit-border-top-right-radius: 6px;
  -moz-border-radius-topright: 6px;
  border-top-right-radius: 6px;
  -webkit-border-top-left-radius: 6px;
  -moz-border-radius-topleft: 6px;
  border-top-left-radius: 6px;
}
.modal-header .close {
  color: white;
  float: right;
  text-decoration: none;
}
.modal-header h3 {
  margin: 0;
  font-size: 12px;
  line-height: 16px;
  text-transform: uppercase;
}
.modal-body {
  position: relative;
  overflow-y: auto;
  padding: 0 15px;
}
.modal-form {
  margin-bottom: 0;
}
.modal-section {
  border-bottom: 1px solid #dddddf;
}
.modal-section p {
  margin: 0;
  padding: 25px 0;
}
.modal-section .label {
  display: inline-block;
  width: 80px;
  margin-right: 20px;
  text-align: right;
}
.modal-footer {
  padding: 14px 15px 15px;
  margin-bottom: 0;
  -webkit-border-radius: 0 0 6px 6px;
  -moz-border-radius: 0 0 6px 6px;
  border-radius: 0 0 6px 6px;
  *zoom: 1;
}
.modal-footer:before,
.modal-footer:after {
  display: table;
  content: "";
  line-height: 0;
}
.modal-footer:after {
  clear: both;
}
.modal-footer .btn + .btn {
  margin-left: 5px;
  margin-bottom: 0;
}
.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}*/
.tooltip {
  position: absolute;
  z-index: 1030;
  display: block;
  visibility: visible;
  font-size: 13px;
  line-height: 1.4;
  font-weight: light;
  opacity: 0;
  filter: alpha(opacity=0);
}
.tooltip.in {
  opacity: 1;
  filter: alpha(opacity=100);
}
.tooltip.top {
  margin-top: -3px;
  padding: 5px 0;
}
.tooltip.right {
  margin-left: 3px;
  padding: 0 5px;
}
.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0;
}
.tooltip.left {
  margin-left: -3px;
  padding: 0 5px;
}
.tooltip .value {
  font-weight: bold;
}
.tooltip-leftalign {
  text-align: left;
  *zoom: 1;
}
.tooltip-leftalign:before,
.tooltip-leftalign:after {
  display: table;
  content: "";
  line-height: 0;
}
.tooltip-leftalign:after {
  clear: both;
}
.tooltip-leftalign * {
  float: left;
  clear: both;
  text-align: left;
}
.tooltip-inner {
  max-width: 200px;
  padding: 10px 12px;
  color: #ffffff;
  text-align: center;
  text-decoration: none;
  white-space: pre-wrap;
  background-color: #3a3b46;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.19999999999999996);
  -moz-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.19999999999999996);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.19999999999999996);
  overflow: hidden;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.tooltip-arrow:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  margin: inherit;
  border-color: transparent;
  border-style: solid;
  border-width: inherit;
  z-index: -1;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #3a3b46;
}
.tooltip.top .tooltip-arrow:after {
  bottom: -2px;
  border-top-color: rgba(0, 0, 0, 0.19999999999999996);
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #3a3b46;
}
.tooltip.right .tooltip-arrow:after {
  left: 0px;
  bottom: -7px;
  border-right-color: rgba(0, 0, 0, 0.19999999999999996);
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #3a3b46;
}
.tooltip.left .tooltip-arrow:after {
  right: -2px;
  border-left-color: rgba(0, 0, 0, 0.19999999999999996);
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #3a3b46;
}
.tooltip.bottom .tooltip-arrow:after {
  top: -2px;
  border-bottom-color: rgba(0, 0, 0, 0.19999999999999996);
}
.tooltip p {
  margin: 0;
}
.tooltip .group {
  font-weight: bold;
}
.tooltip .low {
  color: #f36741;
}
.tooltip .mid {
  color: #33abe3;
}
.tooltip .high {
  color: #78c45e;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1010;
  display: none;
  max-width: 276px;
  padding: 1px;
  text-align: left;
  background-color: #ffffff;
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  white-space: normal;
}
.popover.top {
  margin-top: -10px;
}
.popover.right {
  margin-left: 10px;
}
.popover.bottom {
  margin-top: 10px;
}
.popover.left {
  margin-left: -10px;
}
.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
}
.popover-title:empty {
  display: none;
}
.popover-content {
  padding: 9px 14px;
}
.popover .arrow,
.popover .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover .arrow {
  border-width: 11px;
}
.popover .arrow:after {
  border-width: 10px;
  content: "";
}
.popover.top .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px;
}
.popover.top .arrow:after {
  bottom: 1px;
  margin-left: -10px;
  border-bottom-width: 0;
  border-top-color: #ffffff;
}
.popover.right .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.popover.right .arrow:after {
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #ffffff;
}
.popover.bottom .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}
.popover.bottom .arrow:after {
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #ffffff;
}
.popover.left .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.popover.left .arrow:after {
  right: 1px;
  border-right-width: 0;
  border-left-color: #ffffff;
  bottom: -10px;
}
/*!
 * Datepicker for Bootstrap
 *
 * Copyright 2012 Stefan Petre
 * Improvements by Andrew Rowls
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
.datepicker {
  padding: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  direction: ltr;
  /*.dow {
		border-top: 1px solid #ddd !important;
	}*/
}
.datepicker-inline {
  width: 220px;
}
.datepicker.datepicker-rtl {
  direction: rtl;
}
.datepicker.datepicker-rtl table tr td span {
  float: right;
}
.datepicker-dropdown {
  top: 0;
  left: 0;
}
.datepicker-dropdown:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ccc;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  top: -7px;
  left: 6px;
}
.datepicker-dropdown:after {
  content: '';
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  position: absolute;
  top: -6px;
  left: 7px;
}
.datepicker > div {
  display: none;
}
.datepicker.days div.datepicker-days {
  display: block;
}
.datepicker.months div.datepicker-months {
  display: block;
}
.datepicker.years div.datepicker-years {
  display: block;
}
.datepicker table {
  margin: 0;
}
.datepicker td,
.datepicker th {
  text-align: center;
  width: 20px;
  height: 20px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border: none;
}
.table-striped .datepicker table tr td,
.table-striped .datepicker table tr th {
  background-color: transparent;
}
.datepicker table tr td.day:hover {
  background: #eeeeee;
  cursor: pointer;
}
.datepicker table tr td.old,
.datepicker table tr td.new {
  color: #999999;
}
.datepicker table tr td.disabled,
.datepicker table tr td.disabled:hover {
  background: none;
  color: #999999;
  cursor: default;
}
.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
  background: #fdd49a;
  *background-color: #fdf59a;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  color: #000;
}
.datepicker table tr td.today:hover,
.datepicker table tr td.today:hover:hover,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today:focus,
.datepicker table tr td.today:hover:focus,
.datepicker table tr td.today.disabled:focus,
.datepicker table tr td.today.disabled:hover:focus,
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:hover.active,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today:hover.disabled,
.datepicker table tr td.today.disabled.disabled,
.datepicker table tr td.today.disabled:hover.disabled,
.datepicker table tr td.today[disabled],
.datepicker table tr td.today:hover[disabled],
.datepicker table tr td.today.disabled[disabled],
.datepicker table tr td.today.disabled:hover[disabled] {
  color: #ffffff;
  background-color: #fdf59a;
  *background-color: #fcf282;
}
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:hover.active {
  background-color: #fbf069 \9;
}
.datepicker table tr td.today:hover:hover {
  color: #000;
}
.datepicker table tr td.today.active:hover {
  color: #fff;
}
.datepicker table tr td.range,
.datepicker table tr td.range:hover,
.datepicker table tr td.range.disabled,
.datepicker table tr td.range.disabled:hover {
  background: #eeeeee;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.datepicker table tr td.range.today,
.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today.disabled:hover {
  background: #f3c17a;
  *background-color: #f3e97a;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today:hover:hover,
.datepicker table tr td.range.today.disabled:hover,
.datepicker table tr td.range.today.disabled:hover:hover,
.datepicker table tr td.range.today:focus,
.datepicker table tr td.range.today:hover:focus,
.datepicker table tr td.range.today.disabled:focus,
.datepicker table tr td.range.today.disabled:hover:focus,
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:hover.active,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today:hover.disabled,
.datepicker table tr td.range.today.disabled.disabled,
.datepicker table tr td.range.today.disabled:hover.disabled,
.datepicker table tr td.range.today[disabled],
.datepicker table tr td.range.today:hover[disabled],
.datepicker table tr td.range.today.disabled[disabled],
.datepicker table tr td.range.today.disabled:hover[disabled] {
  color: #ffffff;
  background-color: #f3e97a;
  *background-color: #f1e663;
}
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:hover.active {
  background-color: #efe24b \9;
}
.datepicker table tr td.selected,
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected.disabled:hover {
  background: #b3b3b3;
  *background-color: #808080;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected:hover:hover,
.datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected.disabled:hover:hover,
.datepicker table tr td.selected:focus,
.datepicker table tr td.selected:hover:focus,
.datepicker table tr td.selected.disabled:focus,
.datepicker table tr td.selected.disabled:hover:focus,
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:hover.active,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected:hover.disabled,
.datepicker table tr td.selected.disabled.disabled,
.datepicker table tr td.selected.disabled:hover.disabled,
.datepicker table tr td.selected[disabled],
.datepicker table tr td.selected:hover[disabled],
.datepicker table tr td.selected.disabled[disabled],
.datepicker table tr td.selected.disabled:hover[disabled] {
  color: #ffffff;
  background-color: #808080;
  *background-color: #737373;
}
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:hover.active {
  background-color: #666666 \9;
}
.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover {
  background: #ee8e3c;
  *background-color: #eec93c;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:focus,
.datepicker table tr td.active:hover:focus,
.datepicker table tr td.active.disabled:focus,
.datepicker table tr td.active.disabled:hover:focus,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active[disabled],
.datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active.disabled:hover[disabled] {
  color: #ffffff;
  background-color: #eec93c;
  *background-color: #ecc325;
}
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active {
  background-color: #e3b814 \9;
}
.datepicker table tr td span {
  display: block;
  width: 23%;
  height: 54px;
  line-height: 54px;
  float: left;
  margin: 1%;
  cursor: pointer;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.datepicker table tr td span:hover {
  background: #eeeeee;
}
.datepicker table tr td span.disabled,
.datepicker table tr td span.disabled:hover {
  background: none;
  color: #999999;
  cursor: default;
}
.datepicker table tr td span.active,
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active.disabled:hover {
  background: #ee8e3c;
  *background-color: #eec93c;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:focus,
.datepicker table tr td span.active:hover:focus,
.datepicker table tr td span.active.disabled:focus,
.datepicker table tr td span.active.disabled:hover:focus,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active[disabled],
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active.disabled:hover[disabled] {
  color: #ffffff;
  background-color: #eec93c;
  *background-color: #ecc325;
}
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active {
  background-color: #e3b814 \9;
}
.datepicker table tr td span.old,
.datepicker table tr td span.new {
  color: #999999;
}
.datepicker th.datepicker-switch {
  width: 145px;
}
.datepicker thead tr:first-child th,
.datepicker tfoot tr th {
  cursor: pointer;
}
.datepicker thead tr:first-child th:hover,
.datepicker tfoot tr th:hover {
  background: #eeeeee;
}
.datepicker .cw {
  font-size: 10px;
  width: 12px;
  padding: 0 2px 0 5px;
  vertical-align: middle;
}
.datepicker thead tr:first-child th.cw {
  cursor: default;
  background-color: transparent;
}
.input-append.date .add-on i,
.input-prepend.date .add-on i {
  display: block;
  cursor: pointer;
  width: 16px;
  height: 16px;
}
.input-daterange input {
  text-align: center;
}
.input-daterange input:first-child {
  -webkit-border-radius: 3px 0 0 3px;
  -moz-border-radius: 3px 0 0 3px;
  border-radius: 3px 0 0 3px;
}
.input-daterange input:last-child {
  -webkit-border-radius: 0 3px 3px 0;
  -moz-border-radius: 0 3px 3px 0;
  border-radius: 0 3px 3px 0;
}
.input-daterange .add-on {
  display: inline-block;
  width: auto;
  min-width: 16px;
  height: 20px;
  padding: 4px 5px;
  font-weight: normal;
  line-height: 20px;
  text-align: center;
  text-shadow: 0 1px 0 #ffffff;
  vertical-align: middle;
  background-color: #eeeeee;
  border: 1px solid #ccc;
  margin-left: -5px;
  margin-right: -5px;
}
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
.hide {
  display: none;
}
.show {
  display: block;
}
.invisible {
  visibility: hidden;
}
.affix {
  position: fixed;
}
/*
Version: 3.4.2 Timestamp: Mon Aug 12 15:04:12 PDT 2013
*/
.select2-container {
  margin: 0;
  position: relative;
  display: inline-block;
  /* inline-block for ie7 */
  zoom: 1;
  *display: inline;
  vertical-align: middle;
}
.select2-container,
.select2-drop,
.select2-search,
.select2-search input {
  /*
    Force border-box so that % widths fit the parent
    container without overlap because of margin/padding.

    More Info : http://www.quirksmode.org/css/box.html
  */
  -webkit-box-sizing: border-box;
  /* webkit */
  -moz-box-sizing: border-box;
  /* firefox */
  box-sizing: border-box;
  /* css3 */
}
.select2-container .select2-choice {
  display: block;
  height: 26px;
  padding: 0 0 0 8px;
  overflow: hidden;
  position: relative;
  border: 1px solid #cccccc;
  white-space: nowrap;
  line-height: 26px;
  color: #444;
  text-decoration: none;
  border-radius: 4px;
  background-clip: padding-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #fff;
}
.select2-container.select2-drop-above .select2-choice {
  border-bottom-color: #cccccc;
  border-radius: 0 0 4px 4px;
}
.select2-container.select2-allowclear .select2-choice .select2-chosen {
  margin-right: 42px;
}
.select2-container .select2-choice > .select2-chosen {
  margin-right: 26px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.select2-container .select2-choice abbr {
  display: none;
  width: 12px;
  height: 12px;
  position: absolute;
  right: 24px;
  top: 8px;
  font-size: 1px;
  text-decoration: none;
  border: 0;
  background: url('/assets/images/select2.png') right top no-repeat;
  cursor: pointer;
  outline: 0;
}
.select2-container.select2-allowclear .select2-choice abbr {
  display: inline-block;
}
.select2-container .select2-choice abbr:hover {
  background-position: right -11px;
  cursor: pointer;
}
.select2-drop-mask {
  border: 0;
  margin: 0;
  padding: 0;
  position: fixed;
  left: 0;
  top: 0;
  min-height: 100%;
  min-width: 100%;
  height: auto;
  width: auto;
  z-index: 9998;
  /* styles required for IE to work */
  background-color: #fff;
  opacity: 0;
  filter: alpha(opacity=0);
}
.select2-drop {
  width: 100%;
  margin-top: -1px;
  position: absolute;
  z-index: 9999;
  top: 100%;
  background: #fff;
  color: #000;
  border: 1px solid #cccccc;
  border-top: 0;
  border-radius: 0 0 4px 4px;
  -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
}
.select2-drop-auto-width {
  border-top: 1px solid #cccccc;
  width: auto;
}
.select2-drop-auto-width .select2-search {
  padding-top: 4px;
}
.select2-drop.select2-drop-above {
  margin-top: 1px;
  border-top: 1px solid #cccccc;
  border-bottom: 0;
  border-radius: 4px 4px 0 0;
  -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, 0.15);
  box-shadow: 0 -4px 5px rgba(0, 0, 0, 0.15);
}
.select2-drop-active {
  border: 1px solid rgba(82, 168, 236, 0.6);
  border-top: none;
}
.select2-drop.select2-drop-above.select2-drop-active {
  border-top: 1px solid rgba(82, 168, 236, 0.6);
}
.select2-container .select2-choice .select2-arrow {
  display: inline-block;
  width: 18px;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  border-left: 1px solid #cccccc;
  border-radius: 0 4px 4px 0;
  background-clip: padding-box;
  background: #ccc;
}
.select2-container .select2-choice .select2-arrow b {
  display: block;
  width: 100%;
  height: 100%;
  background: url('/assets/images/select2.png') no-repeat 0 1px;
}
.select2-search {
  display: inline-block;
  width: 100%;
  min-height: 26px;
  margin: 0;
  padding-left: 4px;
  padding-right: 4px;
  position: relative;
  z-index: 10000;
  white-space: nowrap;
}
.select2-search input {
  width: 100%;
  height: auto !important;
  min-height: 26px;
  padding: 4px 20px 4px 5px;
  margin: 0;
  outline: 0;
  font-family: sans-serif;
  font-size: 1em;
  border: 1px solid #cccccc;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: #ffffff url('/assets/images/select2.png') no-repeat 100% -22px;
}
.select2-drop.select2-drop-above .select2-search input {
  margin-top: 4px;
}
.select2-search input.select2-active {
  background: #ffffff url('/assets/images/select2-spinner.gif') no-repeat 100%;
}
.select2-container-active .select2-choice,
.select2-container-active .select2-choices {
  border: 1px solid rgba(82, 168, 236, 0.6);
  outline: none;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}
.select2-dropdown-open .select2-choice {
  border-bottom-color: transparent;
  -webkit-box-shadow: 0 1px 0 #fff inset;
  box-shadow: 0 1px 0 #fff inset;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background-color: #eee;
}
.select2-dropdown-open.select2-drop-above .select2-choice,
.select2-dropdown-open.select2-drop-above .select2-choices {
  border: 1px solid rgba(82, 168, 236, 0.6);
  border-top-color: transparent;
}
.select2-dropdown-open .select2-choice .select2-arrow {
  background: transparent;
  border-left: none;
  filter: none;
}
.select2-dropdown-open .select2-choice .select2-arrow b {
  background-position: -18px 1px;
}
/* results */
.select2-results {
  max-height: 200px;
  padding: 0 0 0 4px;
  margin: 4px 4px 4px 0;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.select2-results ul.select2-result-sub {
  margin: 0;
  padding-left: 0;
}
.select2-results ul.select2-result-sub > li .select2-result-label {
  padding-left: 20px;
}
.select2-results ul.select2-result-sub ul.select2-result-sub > li .select2-result-label {
  padding-left: 40px;
}
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label {
  padding-left: 60px;
}
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label {
  padding-left: 80px;
}
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label {
  padding-left: 100px;
}
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label {
  padding-left: 110px;
}
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label {
  padding-left: 120px;
}
.select2-results li {
  list-style: none;
  display: list-item;
  background-image: none;
}
.select2-results li.select2-result-with-children > .select2-result-label {
  font-weight: bold;
}
.select2-results .select2-result-label {
  padding: 3px 7px 4px;
  margin: 0;
  cursor: pointer;
  min-height: 1em;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.select2-results .select2-highlighted {
  background: #3875d7;
  color: #fff;
}
.select2-results li em {
  background: #feffde;
  font-style: normal;
}
.select2-results .select2-highlighted em {
  background: transparent;
}
.select2-results .select2-highlighted ul {
  background: #fff;
  color: #000;
}
.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
  background: #f4f4f4;
  display: list-item;
}
/*
disabled look for disabled choices in the results dropdown
*/
.select2-results .select2-disabled.select2-highlighted {
  color: #666;
  background: #f4f4f4;
  display: list-item;
  cursor: default;
}
.select2-results .select2-disabled {
  background: #f4f4f4;
  display: list-item;
  cursor: default;
}
.select2-results .select2-selected {
  display: none;
}
.select2-more-results.select2-active {
  background: #f4f4f4 url('/assets/images/select2-spinner.gif') no-repeat 100%;
}
.select2-more-results {
  background: #f4f4f4;
  display: list-item;
}
/* disabled styles */
.select2-container.select2-container-disabled .select2-choice {
  background-color: #f4f4f4;
  background-image: none;
  border: 1px solid #ddd;
  cursor: default;
}
.select2-container.select2-container-disabled .select2-choice .select2-arrow {
  background-color: #f4f4f4;
  background-image: none;
  border-left: 0;
}
.select2-container.select2-container-disabled .select2-choice abbr {
  display: none;
}
/* multiselect */
.select2-container-multi .select2-choices {
  height: auto !important;
  height: 1%;
  margin: 0;
  padding: 0;
  position: relative;
  border: 1px solid #cccccc;
  cursor: text;
  overflow: hidden;
  background-color: #fff;
}
.select2-locked {
  padding: 3px 5px 3px 5px !important;
}
.select2-container-multi .select2-choices {
  min-height: 26px;
}
.select2-container-multi.select2-container-active .select2-choices {
  border: 1px solid rgba(82, 168, 236, 0.6);
  outline: none;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}
.select2-container-multi .select2-choices li {
  float: left;
  list-style: none;
}
.select2-container-multi .select2-choices .select2-search-field {
  margin: 0;
  padding: 0;
  white-space: nowrap;
}
.select2-container-multi .select2-choices .select2-search-field input {
  padding: 5px;
  margin: 1px 0;
  font-family: sans-serif;
  font-size: 100%;
  color: #666;
  outline: 0;
  border: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent !important;
}
.select2-container-multi .select2-choices .select2-search-field input.select2-active {
  background: #ffffff url('/assets/images/select2-spinner.gif') no-repeat 100% !important;
}
.select2-default {
  color: #999 !important;
}
.select2-container-multi .select2-choices .select2-search-choice {
  padding: 3px 5px 3px 18px;
  margin: 3px 0 3px 5px;
  position: relative;
  line-height: 13px;
  color: #333;
  cursor: default;
  border: 1px solid #cccccc;
  border-radius: 3px;
  -webkit-box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  background-clip: padding-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #e4e4e4;
}
.select2-container-multi .select2-choices .select2-search-choice .select2-chosen {
  cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice-focus {
  background: #d4d4d4;
}
.select2-search-choice-close {
  display: block;
  width: 12px;
  height: 13px;
  position: absolute;
  right: 3px;
  top: 4px;
  font-size: 1px;
  outline: none;
  background: url('/assets/images/select2.png') right top no-repeat;
}
.select2-container-multi .select2-search-choice-close {
  left: 3px;
}
.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
  background-position: right -11px;
}
.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {
  background-position: right -11px;
}
/* disabled styles */
.select2-container-multi.select2-container-disabled .select2-choices {
  background-color: #f4f4f4;
  background-image: none;
  border: 1px solid #ddd;
  cursor: default;
}
.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
  padding: 3px 5px 3px 5px;
  border: 1px solid #ddd;
  background-image: none;
  background-color: #f4f4f4;
}
.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {
  display: none;
  background: none;
}
/* end multiselect */
.select2-result-selectable .select2-match,
.select2-result-unselectable .select2-match {
  text-decoration: underline;
}
.select2-offscreen,
.select2-offscreen:focus {
  clip: rect(0 0 0 0) !important;
  width: 1px !important;
  height: 1px !important;
  border: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  outline: 0 !important;
  left: 0px !important;
  top: 0px !important;
}
.select2-display-none {
  display: none;
}
.select2-measure-scrollbar {
  position: absolute;
  top: -10000px;
  left: -10000px;
  width: 100px;
  height: 100px;
  overflow: scroll;
}
/* Retina-ize icons */
@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 144dpi) {
  .select2-search input,
  .select2-search-choice-close,
  .select2-container .select2-choice abbr,
  .select2-container .select2-choice .select2-arrow b {
    background-image: url('select2x2.png') !important;
    background-repeat: no-repeat !important;
    background-size: 60px 40px !important;
  }
  .select2-search input {
    background-position: 100% -21px !important;
  }
}
/*************
  Bootstrap theme
 *************/
/* jQuery Bootstrap Theme */
.tablesorter-bootstrap {
  width: 100%;
}
.tablesorter-bootstrap .tablesorter-header,
.tablesorter-bootstrap tfoot th,
.tablesorter-bootstrap tfoot td {
  font: bold 14px/20px Arial, Sans-serif;
  position: relative;
  padding: 8px;
  margin: 0 0 18px;
  list-style: none;
  background-color: #FBFBFB;
  background-image: -moz-linear-gradient(top, #ffffff, #efefef);
  background-image: -ms-linear-gradient(top, #ffffff, #efefef);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#efefef));
  background-image: -webkit-linear-gradient(top, #ffffff, #efefef);
  background-image: -o-linear-gradient(top, #ffffff, #efefef);
  background-image: linear-gradient(to bottom, #ffffff, #efefef);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#efefef', GradientType=0);
  background-repeat: repeat-x;
  -webkit-box-shadow: inset 0 1px 0 white;
  -moz-box-shadow: inset 0 1px 0 #ffffff;
  box-shadow: inset 0 1px 0 white;
}
.tablesorter-bootstrap .tablesorter-header {
  cursor: pointer;
}
.tablesorter-bootstrap .tablesorter-header-inner {
  position: relative;
  padding: 4px 18px 4px 4px;
}
/* bootstrap uses <i> for icons */
.tablesorter-bootstrap .tablesorter-header i {
  position: absolute;
  right: 2px;
  top: 50%;
  margin-top: -7px;
  /* half the icon height; older IE doesn't like this */
  width: 14px;
  height: 14px;
  background-repeat: no-repeat;
  line-height: 14px;
  display: inline-block;
}
.tablesorter-bootstrap .bootstrap-icon-unsorted {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAOCAYAAAD5YeaVAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAWVJREFUeNqUUL9Lw2AUTGP8mqGlpBQkNeCSRcckEBcHq1jImMElToKuDvpHFMGhU0BQcHBwLji6CE1B4uB/INQsDi4d2jQ/fPeZxo764OV6915f7lLJ81xot9tCURXqdVEUr7IsO6ffH9Q5BlEUCaLwWxWqTcbYnaIoh0Dw4gAvcWlxq1qt9hqNxg6hUGAP+uIPUrGs0qXLer2+v/pTX6QpxLtkc2U2m53ACb8sSdIDXerSEms2m6+DweAICA4d89KGbduf9MpEVdXQ9/2LVqv1CASHjjn3iq/x1xKFfxQPqGnada1W86bT6SiO42OS3qk3KPStLMvbk8nkfjwen/LLuq6blFymMB0KdUPSGhAcOualjX6/f0bCiC7NaWGPQr0BwaFjzn0gYJqmLAiCA8/zni3LmhuGkQPBoWPOPwQeaPIqD4fDruu6L6Zp5kBw6IudchmdJAkLw3DXcZwnIPjy/FuAAQCiqqWWCAFKcwAAAABJRU5ErkJggg==);
}
/* since bootstrap (table-striped) uses nth-child(), we just use this to add a zebra stripe color */
.tablesorter-bootstrap tr.odd td {
  background-color: #f9f9f9;
}
.tablesorter-bootstrap tbody > .odd:hover > td,
.tablesorter-bootstrap tbody > .even:hover > td {
  background-color: #f5f5f5;
}
.tablesorter-bootstrap tr.even td {
  background-color: #fff;
}
/* processing icon */
.tablesorter-bootstrap .tablesorter-processing {
  background-image: url('data:image/gif;base64,R0lGODlhFAAUAKEAAO7u7lpaWgAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQBCgACACwAAAAAFAAUAAACQZRvoIDtu1wLQUAlqKTVxqwhXIiBnDg6Y4eyx4lKW5XK7wrLeK3vbq8J2W4T4e1nMhpWrZCTt3xKZ8kgsggdJmUFACH5BAEKAAIALAcAAAALAAcAAAIUVB6ii7jajgCAuUmtovxtXnmdUAAAIfkEAQoAAgAsDQACAAcACwAAAhRUIpmHy/3gUVQAQO9NetuugCFWAAAh+QQBCgACACwNAAcABwALAAACE5QVcZjKbVo6ck2AF95m5/6BSwEAIfkEAQoAAgAsBwANAAsABwAAAhOUH3kr6QaAcSrGWe1VQl+mMUIBACH5BAEKAAIALAIADQALAAcAAAIUlICmh7ncTAgqijkruDiv7n2YUAAAIfkEAQoAAgAsAAAHAAcACwAAAhQUIGmHyedehIoqFXLKfPOAaZdWAAAh+QQFCgACACwAAAIABwALAAACFJQFcJiXb15zLYRl7cla8OtlGGgUADs=');
  position: absolute;
  z-index: 1000;
}
/* filter widget */
.tablesorter-bootstrap .tablesorter-filter-row .tablesorter-filter {
  width: 98%;
  height: inherit;
  margin: 0 auto;
  padding: 4px 6px;
  background-color: #fff;
  color: #333;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: height 0.1s ease;
  -moz-transition: height 0.1s ease;
  -o-transition: height 0.1s ease;
  transition: height 0.1s ease;
}
.tablesorter-bootstrap .tablesorter-filter-row td {
  background: #eee;
  line-height: normal;
  text-align: center;
  padding: 4px 6px;
  vertical-align: middle;
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
/* hidden filter row */
.tablesorter-bootstrap .tablesorter-filter-row.hideme td {
  padding: 2px;
  /* change this to modify the thickness of the closed border row */
  margin: 0;
  line-height: 0;
}
.tablesorter-bootstrap .tablesorter-filter-row.hideme .tablesorter-filter {
  height: 1px;
  min-height: 0;
  border: 0;
  padding: 0;
  margin: 0;
  /* don't use visibility: hidden because it disables tabbing */
  opacity: 0;
  filter: alpha(opacity=0);
}
/* pager plugin */
.tablesorter-bootstrap .tablesorter-pager select {
  padding: 4px 6px;
}
.tablesorter-bootstrap .tablesorter-pager .pagedisplay {
  border: 0;
}
#admin_message,
#message {
  margin-top: 15px;
  margin-bottom: 0;
}
#admin_message:empty,
#message:empty {
  display: none;
}
#DelegateAdmin {
  line-height: 20px;
  margin-top: -1px;
}
#DelegatedAdminModal table {
  width: 100%;
}
#DelegatedAdminModal thead {
  border-bottom: 1px solid #ccc;
}
#DelegatedAdminModal th {
  text-align: left;
  line-height: 60px;
  padding: 0 20px;
}
#DelegatedAdminModal tbody *:not(.btn) {
  color: #445 !important;
}
#DelegatedAdminModal .hiddenRow {
  padding: 0;
}
#DelegatedAdminModal .hiddenRow .accordion-body {
  background: #fff;
}
#DelegatedAdminModal .hiddenRow .accordion-body.in {
  background: #eee;
}
#DelegatedAdminModal .accordion > table > tbody > tr {
  line-height: 50px;
  background: #fff;
  cursor: pointer;
}
#DelegatedAdminModal .accordion > table > tbody > tr:nth-child(odd) > td {
  padding: 0 20px;
}
#DelegatedAdminModal .accordion > table > tbody > tr:nth-child(odd) > td:nth-child(2) {
  position: relative;
}
#DelegatedAdminModal .accordion > table > tbody > tr:nth-child(odd) > td:nth-child(2):before {
  position: absolute;
  left: 0;
  content: "▶";
}
#DelegatedAdminModal .accordion > table > tbody > tr.open {
  background: #eee;
}
#DelegatedAdminModal .accordion > table > tbody > tr.open:nth-child(odd) > td:nth-child(2):before {
  content: "▼";
}
#DelegatedAdminModal .accordion > table > tbody > tr:hover {
  background: #eee;
}
#DelegatedAdminModal input {
  margin: 0;
}
#DelegatedAdminModal fieldset {
  padding: 30px;
  border-top: 1px dotted #ccc;
}
#DelegatedAdminModal fieldset > form {
  margin: 0;
}
#DelegatedAdminModal fieldset td {
  width: 50%;
  text-align: right;
}
#DelegatedAdminModal fieldset td input {
  margin-left: 10px;
}
#DelegatedAdminModal .submit {
  float: right;
}
#DelegatedAdminModal .roles {
  position: relative;
  text-align: left;
}
#DelegatedAdminModal .roles label {
  display: inline-block;
  margin: 0;
}
#DelegatedAdminModal .roles > label {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  text-align: right;
  padding-right: 240px;
}
#DelegatedAdminModal .roles > div {
  display: inline-block;
  margin-left: -240px;
}
#DelegatedAdminModal .roles > div label {
  margin: 0 20px;
  font-weight: normal;
}
#DelegatedAdminModal .roles > div label input {
  margin-top: -3px;
}
#DelegatedAdminModal label {
  font-weight: bold;
}

#AddDelegatedAdminModal table {
  width: 100%;
}
#AddDelegatedAdminModal table tr {
  border-bottom: 1px dotted #ccc;
}
#AddDelegatedAdminModal table td {
  width: 50%;
  text-align: right;
  padding-top: 30px;
  padding-bottom: 20px;
}
#AddDelegatedAdminModal table td input {
  margin-left: 10px;
}
#AddDelegatedAdminModal .roles input,
#AddDelegatedAdminModal .roles label {
  display: inline-block;
  vertical-align: middle;
}
#AddDelegatedAdminModal .roles div {
  display: inline-block;
}
#AddDelegatedAdminModal .roles div label {
  display: inline-block;
  font-weight: normal;
  margin: 0 20px;
}
#AddDelegatedAdminModal .roles div label input {
  margin: 0;
  margin-top: -3px;
}
#AddDelegatedAdminModal .roles td {
  padding-right: 240px;
}
#AddDelegatedAdminModal .roles td + td {
  position: relative;
  text-align: left;
  padding-right: 0;
  left: -240px;
}
#Feedback > .table {
  background: #fff;
}
#Feedback > .table td {
  vertical-align: middle;
}
#Feedback #update-feedback-table .searchBox {
  margin: 0;
  width: 300px;
}
#Feedback .datetime-input:after {
  right: 11px;
}
#feedback-table {
  border: 1px solid #dddddd;
  border-radius: 4px;
  margin: 0;
}
#feedback-table .expandable > th:first-child,
#feedback-table .expandable > td:first-child {
  padding-left: 30px;
}
#feedback-table .expandable > th:last-child,
#feedback-table .expandable > td:last-child {
  padding-right: 30px;
}
.tablesorter-headerRow th {
  line-height: 50px;
}
.tablesorter-header:not(.sorter-false) {
  background-image: url(data:image/gif;base64,R0lGODlhFQAJAIAAACMtMP///yH5BAEAAAEALAAAAAAVAAkAAAIXjI+AywnaYnhUMoqt3gZXPmVg94yJVQAAOw==);
  background-position: center right;
  background-repeat: no-repeat;
  cursor: pointer;
  white-space: normal;
}
.tablesorter thead .headerSortUp,
.tablesorter thead .tablesorter-headerSortUp,
.tablesorter thead .tablesorter-headerAsc {
  background-image: url(data:image/gif;base64,R0lGODlhFQAEAIAAACMtMP///yH5BAEAAAEALAAAAAAVAAQAAAINjI8Bya2wnINUMopZAQA7);
  border-bottom: #f89406 2px solid;
}
.tablesorter thead .headerSortDown,
.tablesorter thead .tablesorter-headerSortDown,
.tablesorter thead .tablesorter-headerDesc {
  background-image: url(data:image/gif;base64,R0lGODlhFQAEAIAAACMtMP///yH5BAEAAAEALAAAAAAVAAQAAAINjB+gC+jP2ptn0WskLQA7);
  border-bottom: #f89406 2px solid;
}
.expandable {
  -webkit-animation: fadeInRow 500ms;
  -moz-animation: fadeInRow 500ms;
  -ms-animation: fadeInRow 500ms;
  -o-animation: fadeInRow 500ms;
  animation: fadeInRow 500ms;
}
.expandable:hover,
.expandable:hover + tr {
  cursor: pointer;
  background-color: #eee;
}
.expandable > td:first-child {
  position: relative;
  padding-left: 22px;
  padding-right: 0;
}
.expandable > td:first-child:after {
  content: '▶';
  font-size: 10px;
  display: block;
  position: absolute;
  top: 14px;
  left: 10px;
}
.expandable.expanded > td:first-child:after {
  content: '▼';
  font-size: 12px;
}
#loading-row td {
  text-align: center;
  border-top: 1px solid #ddd;
}
td.no-data {
  text-align: center;
}
.more-information {
  display: none;
  border-top: none;
}
.more-information > td {
  display: none;
  padding: 15px 30px;
  background: #eeeeee;
}
.feedback-data {
  text-align: justify;
}
.feedback-data:after {
  display: inline-block;
  content: '';
  width: 100%;
}
.feedback-data > * {
  display: inline-block;
}
.feedback-data article {
  width: 35%;
  vertical-align: top;
  text-align: left;
}
.feedback-data article.blacklist {
  width: 25%;
}
.feedback-data h5 {
  font-weight: normal;
  font-size: 16px;
}
.feedback-data table {
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  width: 100%;
}
.feedback-data table th,
.feedback-data table td {
  border-left: 1px solid #ddd;
  padding: 0;
  overflow: auto;
}
.feedback-data table th span,
.feedback-data table td span {
  padding: 10px 15px;
  height: 100%;
  display: inline-block;
  max-width: 300px;
}
.feedback-data table th {
  font-size: 13px;
  font-weight: bold;
  width: 150px;
}
.feedback-data table td {
  font-size: 14px;
  color: #555555;
}
.blacklist-form {
  text-align: justify;
}
.blacklist-form:after {
  display: inline-block;
  content: '';
  width: 100%;
}
.blacklist-form > * {
  display: inline-block;
}
.blacklist-form input {
  width: 100%;
  box-sizing: border-box;
  height: 28px;
}
.blacklist-form label {
  display: inline-block;
  vertical-align: top;
  text-align: left;
  margin-bottom: 15px;
  width: 55%;
}
.blacklist-form label:first-child {
  width: 42%;
}
.blacklist-form label input {
  width: 100%;
}
.blacklist-form .select2-container {
  display: block;
}
.blacklist-form .label-text {
  font-size: 13px;
  font-weight: bold;
  display: inline-block;
  margin-bottom: 5px;
}
.disagree {
  padding: 4px 12px;
}
.disagreed,
.disagreed:hover {
  background-color: #e63;
  color: white;
}
.disagreed-row,
.disagreed-row:hover,
.disagreed-row + tr,
.disagreed-row:hover + tr,
.blacklisted-row,
.blacklisted-row:hover,
.blacklisted-row + tr,
.blacklisted-row:hover + tr {
  background-color: #efefef;
  color: #aaa;
}
.blacklisted-row .isBlacklisted {
  display: block;
}
.paginate-link {
  -webkit-animation: fadeInRow 500ms;
  -moz-animation: fadeInRow 500ms;
  -ms-animation: fadeInRow 500ms;
  -o-animation: fadeInRow 500ms;
  animation: fadeInRow 500ms;
  text-align: center;
  height: 45px;
  line-height: 45px;
  background-color: white;
  border: 1px solid #ddd;
  border-top: 0;
  display: none;
}
.paginate-link:hover {
  color: #333;
  background-color: #eee;
  text-decoration: none;
}
.tablesorter-default + .paginate-link:not(.hidden) {
  display: block;
}
.paginate-link.loading {
  position: relative;
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
}
.paginate-link.loading:after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 16px;
  height: 16px;
  margin: auto;
  background: url('/assets/images/select2-spinner.gif');
}
.paginate-link.hidden {
  -webkit-transition: opacity 250ms;
  -moz-transition: opacity 250ms;
  -o-transition: opacity 250ms;
  transition: opacity 250ms;
  opacity: 0;
  filter: alpha(opacity=0);
  pointer-events: none;
}
@-webkit-keyframes fadeInRow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-moz-keyframes fadeInRow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-ms-keyframes fadeInRow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-o-keyframes fadeInRow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fadeInRow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
#Training * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
#Training .btn {
  border: none;
  outline: none;
}
#Training.loading .training-section {
  opacity: 0;
}
.training-section {
  margin-top: 30px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border: 1px solid #cdcdd0;
  background: white;
}
.training-section > header {
  *zoom: 1;
  cursor: pointer;
  padding: 0 25px;
}
.training-section > header:before,
.training-section > header:after {
  display: table;
  content: "";
  line-height: 0;
}
.training-section > header:after {
  clear: both;
}
.training-section > header > * {
  line-height: 60px;
  margin-top: 0;
  margin-bottom: 0;
}
.training-section > header h3 {
  float: left;
}
.training-section > header .template-link {
  float: right;
  padding-right : 20px;
  margin: 0 10px;
}
.training-section > header .collapse-icon {
  float: right;
}
.training-section > header .collapse-icon:after {
  content: "−";
  font-size: 25px;
  line-height: 57px;
  font-weight: bold;
  color: #bdbec1;
}
.training-section > header .collapse-icon.out:after {
  content: "+";
  font-size: 30px;
  line-height: 60px;
}
.training-section > header:hover .collapse-icon:after {
  color: #7c7d83;
}
.training-inner {
  *zoom: 1;
  position: relative;
  padding-left: 25px;
}
.training-inner:before,
.training-inner:after {
  display: table;
  content: "";
  line-height: 0;
}
.training-inner:after {
  clear: both;
}
.training-inner .add-btn {
  margin-bottom: 25px;
  margin-top: 15px;
}
.training-inner .download-btn {
  width: 100%;
  margin-top: 15px;
}
.training-inner .download-btn[disabled] {
  opacity: 0.1;
}
.csv-section {
  width: 50%;
  float: left;
  margin-bottom: 25px;
  padding-right: 25px;
}
.csv-section:nth-child(odd) {
  clear: left;
}
.csv-section .csv-section-inner {
  padding: 15px;
  border: 1px solid #cdcdd0;
  border-radius: 4px;
}
.csv-section .csv-row {
  *zoom: 1;
  position: relative;
}
.csv-section .csv-row:before,
.csv-section .csv-row:after {
  display: table;
  content: "";
  line-height: 0;
}
.csv-section .csv-row:after {
  clear: both;
}
.csv-section .csv-row > span {
  min-height: 40px;
  line-height: 40px;
}
.csv-section .csv-row > .left {
  position: relative;
  display: block;
  width: 25%;
  float: left;
  font-weight: bold;
}
.csv-section .csv-row > .right {
  position: relative;
  display: block;
  width: 75%;
  float: right;
}
.csv-section .csv-row > .right .progress-bar {
  display: block;
  position: relative;
  width: 100%;
  margin-top: 10px;
  height: 20px;
  background: #eeeeee;
}
.csv-section .csv-row > .right .progress-bar .completed {
  display: block;
  position: relative;
  width: 0%;
  height: 100%;
  background: #99d120;
  -webkit-transition: width 150ms;
  -moz-transition: width 150ms;
  -o-transition: width 150ms;
  transition: width 150ms;
}
.csv-section .csv-row > .right .exceptions {
  display: block;
  margin-top: 10px;
  height: 20px;
  line-height: 20px;
  border-bottom: 1px solid #cdcdd0;
}
.csv-section .file-wrapper {
  position: relative;
  display: inline-block;
}
.csv-section .file-wrapper .file-wrapper-label {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #eeeeee;
  border: 1px dotted #93939a;
  line-height: 30px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.csv-section .file-wrapper input {
  opacity: 0;
  background: white;
  cursor: pointer;
}
html {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  height: 100%;
}
html.show-filters {
  padding-bottom: 75px;
}
html body {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  height: 100%;
  min-width: 1200px;
  padding-top: 43px;
  background-color: #f6f6f7;
}
body.loading {
  visibility: hidden;
}
body.locked {
  overflow: hidden;
}
#SocureLogo {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
  height: 43px;
  padding-top: 0;
  padding-bottom: 0;
  width: 107px;
  background-color: #1c1d29;
  background-image: url('../assets/images/socure-logo.svg');
  background-position: 50% 50%;
  background-repeat: no-repeat;
}
.btn-dark {
  color: #ffffff;
  background: #474752;
  -webkit-transition: 500ms;
  -moz-transition: 500ms;
  -o-transition: 500ms;
  transition: 500ms;
  -webkit-transition-property: 'color, background';
  -moz-transition-property: 'color, background';
  -o-transition-property: 'color, background';
  transition-property: 'color, background';
}
.btn-dark:hover,
.btn-dark:focus,
.btn-dark.disabled,
.btn-dark[disabled] {
  color: #474752;
  background-color: #ffffff;
  -webkit-transition-duration: 150ms;
  -moz-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
.btn-dark:active,
.btn-dark.active {
  color: #474752;
  background-color: #ffffff;
}
.btn-dark-on-light {
  color: #ffffff;
  background: #474752;
  -webkit-transition: 500ms;
  -moz-transition: 500ms;
  -o-transition: 500ms;
  transition: 500ms;
  -webkit-transition-property: 'color, background';
  -moz-transition-property: 'color, background';
  -o-transition-property: 'color, background';
  transition-property: 'color, background';
}
.btn-dark-on-light:hover,
.btn-dark-on-light:focus,
.btn-dark-on-light.disabled,
.btn-dark-on-light[disabled] {
  color: #ffffff;
  background-color: #f89406;
  -webkit-transition-duration: 150ms;
  -moz-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}

.btn-dark-on-light:active,
.btn-dark-on-light.active {
  color: #ffffff;
  background-color: #f89406;
}
.btn-dark-on-light.disabled {
  background-color: rgba(71, 71, 82, 0.9);
}
.btn-orange {
  color: #ffffff;
  background: #f89406;
  -webkit-transition: 500ms;
  -moz-transition: 500ms;
  -o-transition: 500ms;
  transition: 500ms;
  -webkit-transition-property: 'color, background';
  -moz-transition-property: 'color, background';
  -o-transition-property: 'color, background';
  transition-property: 'color, background';
}
.btn-orange:hover,
.btn-orange:focus,
.btn-orange.disabled,
.btn-orange[disabled] {
  color: #ffffff;
  background-color: #474752;
  -webkit-transition-duration: 150ms;
  -moz-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
.btn-orange:active,
.btn-orange.active {
  color: #ffffff;
  background-color: #474752;
}
.btn {
  border: none;
}
.btn .icn-notch-left,
.btn .icn-notch-right {
  font-size: 8px;
}
#UserName:before {
  content: "\e000";
  position: relative;
  left: -5px;
  top: 1px;
  font-family: 'Dashboard Icons';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
}
.login-msg {
  padding: 20px 0 0 !important;
}
.login-btn {
  float: right;
}
.pwd-field {
  padding-top: 0 !important;
}
.password-msg {
  padding-top: 0 !important;
}
.password-msg:empty {
  display: none;
}
#ChangePassword.active {
  display: none;
}
.change-password {
  display: none;
  text-align: right;
  padding: 25px 0;
}
.change-password.active {
  display: block;
}
.dashboard {
  position: relative;
  height: 100%;
  padding-top: 43px;
  margin-top: -43px;
}
.dashboard-row {
  position: relative;
  height: 50%;
  min-height: 450px;
  overflow: hidden;
}
.dashboard-row .row-inner {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.groups-legend {
  display: none;
}
.groups .groups-legend {
  display: block;
}
.toggle-gender {
  display: none;
  position: absolute;
  top: 35px;
  right: 40px;
}
.toggle-gender.show {
  display: inline-block;
}
.toggle-gender input {
  vertical-align: top;
  margin-right: 5px;
}
.dashboard-panel {
  display: table !important;
  position: relative;
  height: 100%;
  text-align: center;
}
.dashboard-panel.main .panel-content {
  padding-top: 60px;
}
.dashboard-panel .panel-inner {
  display: table-cell;
  position: relative;
  vertical-align: middle;
}
.dashboard-panel .panel-inner .panel-content {
  display: inline-block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  visibility: hidden;
  z-index: -1;
}
.dashboard-panel .panel-inner .panel-content.groups h4 {
  width: 75%;
  margin: auto;
}
.dashboard-panel .panel-inner .panel-content.gender {
  text-align: justify;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 150px;
  padding-top: 60px;
  max-width: 940px;
}
.dashboard-panel .panel-inner .panel-content.gender:after {
  display: inline-block;
  content: '';
  width: 100%;
}
.dashboard-panel .panel-inner .panel-content.gender > * {
  display: inline-block;
}
.dashboard-panel .panel-inner .panel-content.gender h4 {
  font-size: 16px;
  color: #262631;
  font-weight: bold;
}
.dashboard-panel .panel-inner .panel-content.gender small {
  display: inline-block;
  font-size: 14px;
  color: #6e6e6e;
  margin-top: 20px;
  margin-bottom: 10px;
}
.dashboard-panel .panel-inner .panel-content.gender .panel-section {
  text-align: center;
}
.dashboard-panel .panel-inner .active {
  position: relative;
  visibility: visible;
  z-index: 1;
}
.dashboard-panel .panel-inner .active.gender .chart.circle.groups {
  z-index: 1;
}
.dashboard-panel .panel-inner .active.gender small {
  position: relative;
  z-index: 1;
}
.dashboard-panel .panel-inner .active.gender .chart.number {
  z-index: 1;
}
.dashboard-panel .panel-inner .panel-section {
  display: inline-block;
}
.dashboard-panel h3 {
  position: absolute;
  top: 35px;
  left: 40px;
  margin: 0;
  font-size: 25px;
  font-weight: normal;
  color: #262631;
}
.dashboard-panel h3 small {
  font-size: 13px;
  color: #a2a2a2;
  vertical-align: middle;
}
.dashboard-panel h4 {
  font-size: 15px;
  line-height: 20px;
  font-weight: 100;
  color: #e6e6e6;
}
.dashboard-panel .panel-option {
  position: absolute;
  top: 35px;
  right: 40px;
}
.dashboard-panel .panel-option * {
  vertical-align: top;
}
.dashboard-panel .btn {
  color: #e9e9eb;
  background: #51525c;
  -webkit-transition: 500ms;
  -moz-transition: 500ms;
  -o-transition: 500ms;
  transition: 500ms;
  -webkit-transition-property: 'color, background';
  -moz-transition-property: 'color, background';
  -o-transition-property: 'color, background';
  transition-property: 'color, background';
}
.dashboard-panel .btn:hover,
.dashboard-panel .btn:focus,
.dashboard-panel .btn.disabled,
.dashboard-panel .btn[disabled] {
  color: #51525c;
  background-color: #e9e9eb;
  -webkit-transition-duration: 150ms;
  -moz-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
.dashboard-panel .btn:active,
.dashboard-panel .btn.active {
  color: #51525c;
  background-color: #e9e9eb;
}
.score-group-row {
  position: relative;
  width: 80%;
  max-width: 250px;
  margin: 30px auto;
  text-align: left;
}
.score-group-row .group-score {
  display: inline-block;
  width: 45px;
  height: 45px;
  line-height: 45px;
  margin-right: 10px;
  text-align: center;
}
.score-group-row .chart.number.group {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 55px;
  right: 0;
  margin: 0;
  line-height: 45px;
  text-align: center;
}
#GroupCircleLow,
#CitiesCircleLow {
  background-image: url(../assets/images/score-group-low.svg);
}
#GroupCircleMid,
#CitiesCircleMid {
  background-image: url(../assets/images/score-group-mid.svg);
}
#GroupCircleHigh,
#CitiesCircleHigh {
  background-image: url(../assets/images/score-group-high.svg);
}
.chart {
  position: relative;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  backface-visibility: hidden;
}
.chart.circle {
  position: relative;
  width: 100px;
  height: 100px;
  margin: auto;
  font-size: 50px;
  line-height: 100px;
}
.chart.circle.score {
  margin: 10px auto 30px;
}
.chart.circle.percent {
  width: 115px;
  height: 115px;
  margin: 15px auto 40px;
  font-size: 35px;
  line-height: 115px;
}
.chart.circle.groups {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  width: 200px;
  height: 200px;
  margin-bottom: -100px;
  margin-right: -100px;
  left: -50px;
  z-index: -1;
}
.chart.circle.groups .tooltips {
  -webkit-transform: rotate(-180deg);
  -moz-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  -o-transform: rotate(-180deg);
  transform: rotate(-180deg);
}
.chart.circle .value {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.chart.bar {
  margin: auto;
  width: 700px;
  height: 180px;
}
.chart.bar:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 19px;
  left: 5.555555555555555%;
  right: 5.555555555555555%;
  border-left: 1px solid #c4c4c4;
  border-bottom: 1px solid #c4c4c4;
}
.chart.line {
  margin: auto;
  width: 700px;
  height: 180px;
}
.chart.line:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 19px;
  left: 5.555555555555555%;
  right: 5.555555555555555%;
  border-left: 1px solid #c4c4c4;
  border-bottom: 1px solid #c4c4c4;
}
.chart.line .tooltips > span {
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
}
.chart.line .tooltips > span:hover {
  height: 14px !important;
  width: 14px !important;
  margin-top: -2px;
  margin-left: -2px;
}
.chart.line .tooltips > span:hover:after {
  height: 10px;
  width: 10px;
  margin-top: -5px;
  margin-left: -5px;
}
.chart.line .tooltips > span:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  height: 6px;
  width: 6px;
  margin-top: -3px;
  margin-left: -3px;
  background: white;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
}
.chart.number {
  font-size: 36px;
  line-height: 1;
  height: 36px;
  margin: 0;
  margin-bottom: 30px;
}
.chart.number small {
  margin-left: 10px;
  font-size: 13px;
  line-height: 36px;
  color: #929299;
  vertical-align: top;
}
.chart.response {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  font-size: 13px;
  color: #929299;
}
.chart.response .value {
  color: white;
}
.chart .axis-label {
  position: absolute;
  font-size: 13px;
  color: #6e6e6e;
  white-space: nowrap;
}
.chart .axis-label.y {
  right: 100%;
  top: 50%;
  -webkit-transform: rotate(-90deg) translateX(50%);
  -moz-transform: rotate(-90deg) translateX(50%);
  -ms-transform: rotate(-90deg) translateX(50%);
  -o-transform: rotate(-90deg) translateX(50%);
  transform: rotate(-90deg) translateX(50%);
}
.chart .axis-label.x {
  top: 100%;
  margin-top: 25px;
  left: 0;
  right: 0;
}
.chart .tooltips {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.chart .tooltips > span.fake {
  background: url(../assets/images/fake-profiles-pattern.gif);
}
#DashboardNavbar {
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}
#DashboardNavbar .navbar-inner {
  padding-right: 20px;
  padding-left: 20px;
  *zoom: 1;
}
#DashboardNavbar .navbar-inner:before,
#DashboardNavbar .navbar-inner:after {
  display: table;
  content: "";
  line-height: 0;
}
#DashboardNavbar .navbar-inner:after {
  clear: both;
}
@media (max-width: 768px) {
  #DashboardNavbar {
    position: absolute;
  }
}
#FilterNavbar .navbar-inner {
  text-align: justify;
  height: 75px;
  padding-right: 0;
}
#FilterNavbar .navbar-inner:after {
  display: inline-block;
  content: '';
  width: 100%;
}
#FilterNavbar .navbar-inner > * {
  display: inline-block;
}
#FilterNavbar .navbar-block {
  position: relative;
  display: inline-block;
  min-height: 100%;
  vertical-align: top;
}
#FilterNavbar .navbar-block .nav li a {
  margin: 0;
  padding: 0;
}
#FilterNavbar .navbar-block .nav li a.btn {
  margin: 0 5px;
  padding: 0 10px;
  font-size: 12px;
  line-height: 25px;
  font-weight: bold;
  text-transform: uppercase;
  color: #6e6e75;
  background: transparent;
  -webkit-transition: 500ms;
  -moz-transition: 500ms;
  -o-transition: 500ms;
  transition: 500ms;
  -webkit-transition-property: 'color, background';
  -moz-transition-property: 'color, background';
  -o-transition-property: 'color, background';
  transition-property: 'color, background';
}
#FilterNavbar .navbar-block .nav li a.btn:hover,
#FilterNavbar .navbar-block .nav li a.btn:focus,
#FilterNavbar .navbar-block .nav li a.btn.disabled,
#FilterNavbar .navbar-block .nav li a.btn[disabled] {
  color: #6e6e75;
  background-color: #e9e9eb;
  -webkit-transition-duration: 150ms;
  -moz-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
#FilterNavbar .navbar-block .nav li a.btn:active,
#FilterNavbar .navbar-block .nav li a.btn.active {
  color: #ffffff;
  background-color: #f38f1e;
}
#FilterNavbar .navbar-block .nav li a.btn:first-child {
  margin-left: 0;
}
#FilterNavbar .navbar-label {
  display: inline-block;
  font-size: 13px;
  line-height: 37.5px;
  color: #676770;
}
#FilterNavbar .navbar-controls {
  height: 18.75px;
  line-height: 18.75px;
}
#FilterNavbar .navbar-controls .date {
  font-size: 21px;
  color: #929299;
}
#FilterNavbar .navbar-controls .date time {
  font-weight: bold;
  color: #262631;
}
#FilterNavbar .navbar-controls .date + a {
  margin-left: 10px;
}
#FilterNavbar .controls-export {
  line-height: 75px;
  vertical-align: middle;
}
#FilterNavbar .controls-export .nav li a {
  font-size: 17px;
  color: #262732;
  padding: 27.5px 15px 26.5px;
  -webkit-transition: 500ms;
  -moz-transition: 500ms;
  -o-transition: 500ms;
  transition: 500ms;
  -webkit-transition-property: 'color, background';
  -moz-transition-property: 'color, background';
  -o-transition-property: 'color, background';
  transition-property: 'color, background';
}
#FilterNavbar .controls-export .nav li a:hover,
#FilterNavbar .controls-export .nav li a:active {
  color: #f18f30;
  -webkit-transition-duration: 150ms;
  -moz-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
#FilterNavbar .controls-confidence .nav,
#FilterNavbar .controls-segments .nav {
  float: none;
  margin: 0;
}
#FilterNavbar .controls-date .view {
  position: relative;
  display: block;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none;
  -webkit-transition: 250ms;
  -moz-transition: 250ms;
  -o-transition: 250ms;
  transition: 250ms;
}
#FilterNavbar .controls-date .edit {
  position: absolute;
  top: 37.5px;
  margin-top: -8px;
  left: 0;
  width: 106%;
  -webkit-transform: translateY(75px);
  -moz-transform: translateY(75px);
  -ms-transform: translateY(75px);
  -o-transform: translateY(75px);
  transform: translateY(75px);
  -webkit-transition: 250ms;
  -moz-transition: 250ms;
  -o-transition: 250ms;
  transition: 250ms;
}
#FilterNavbar .controls-date .edit > * {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
}
#FilterNavbar .controls-date .edit input {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  width: 116px;
  height: 28px;
  margin-bottom: 0;
  padding-right: 26px;
}
#FilterNavbar .controls-date .edit .btn {
  margin-left: 5px;
}
#FilterNavbar .controls-date .edit .datetime-input {
  position: relative;
  margin-right: 5px;
}
#FilterNavbar .controls-date .edit .datetime-input:after {
  content: "";
  position: absolute;
  right: 7px;
  width: 15px;
  top: 50%;
  height: 13px;
  margin-top: -6.5px;
  background: url(../assets/images/calendar.svg);
}
#FilterNavbar .controls-date .edit .endTime {
  margin-left: 5px;
}
#FilterNavbar .controls-date.edit-mode .view {
  -webkit-transform: translateY(75px);
  -moz-transform: translateY(75px);
  -ms-transform: translateY(75px);
  -o-transform: translateY(75px);
  transform: translateY(75px);
}
#FilterNavbar .controls-date.edit-mode .edit {
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none;
}
#FilterNavbar .divider-vertical.last {
  visibility: hidden;
  width: 0;
  margin: 0;
}
.datetime-input {
  position: relative;
  margin: 0;
  margin-right: 5px;
}
.datetime-input:after {
  content: "";
  position: absolute;
  right: 7px;
  width: 15px;
  top: 50%;
  height: 13px;
  margin-top: -6.5px;
  background: url(../assets/images/calendar.svg);
}
.datetime-input input {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  width: 116px;
  height: 28px;
  margin-bottom: 0;
  padding-right: 26px;
}
#AvgSide {
  color: white;
  background-color: #33343F;
}
#AvgMain {
  background-color: #F6F6F7;
}
#AvgSide:after,
#AvgMain:after {
  content: "";
  position: absolute;
  top: 100%;
  height: 2px;
  margin-top: -1px;
  left: 0;
  right: 0;
  background-color: inherit;
  z-index: 1;
}
#AuthSide {
  color: white;
  background-color: #3E3F49;
}
#AuthMain {
  background-color: #EEEEEF;
}
#AuthMain .panel-inner .gender small {
  margin-top: 0;
}
.touch #AuthSide:after,
.touch #AuthMain:after {
  content: "";
  position: absolute;
  top: 100%;
  height: 2px;
  margin-top: -1px;
  left: 0;
  right: 0;
  background-color: inherit;
  z-index: 1;
}
.navbar-notch:after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 550px;
  background-color: #1c1d29;
  z-index: -1;
}
.navbar-notch .brand {
  position: absolute;
  top: 0;
  left: 20px;
  background-color: #1c1d29;
}
.navbar-notch .navbar-inner {
  background-color: transparent;
}
.nav-notch-wrapper {
  position: relative;
  left: 0;
  display: block;
  float: left;
  margin: 0 10px 0 0;
  position: absolute;
  top: 0;
  left: 147px;
  overflow: hidden;
}
.nav-notch-wrapper .nav {
  margin: 0;
}
.nav-notch:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  left: -1px;
  right: -1px;
  bottom: 11px;
  background-color: #1c1d29;
  z-index: -1;
}
.notch {
  position: absolute;
  width: 12px;
  height: 12px;
  left: 0;
  bottom: 0;
  z-index: -1;
  -webkit-transform: translateY(6px);
  -moz-transform: translateY(6px);
  -ms-transform: translateY(6px);
  -o-transform: translateY(6px);
  transform: translateY(6px);
}
.notch:before {
  content: "";
  position: absolute;
  left: -1px;
  right: -1px;
  bottom: 100%;
  height: 15px;
  margin-bottom: -6px;
  background-color: #1c1d29;
  z-index: -1;
}
.notch .notch-img {
  position: absolute;
  width: 12px;
  height: 12px;
  background: url(../assets/images/notch.svg);
}
.notch .notch-img:before,
.notch .notch-img:after {
  content: "";
  position: absolute;
  top: -43px;
  bottom: 0;
  width: 400px;
  background-color: #1c1d29;
  z-index: -1;
}
.notch .notch-img:before {
  right: 100%;
  margin-right: -1px;
}
.notch .notch-img:after {
  left: 100%;
  margin-left: -1px;
}
#AccountNav {
  position: absolute;
  top: 0;
  right: 20px;
  left: auto;
}
.page {
  background-color: #f6f6f7;
  margin: 60px;
  padding-bottom: 60px;
}
.overlay {
  display: none;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 2000;
  top: 0;
  bottom: -75px;
  text-align: center;
  -webkit-transition: opacity 750ms;
  -moz-transition: opacity 750ms;
  -o-transition: opacity 750ms;
  transition: opacity 750ms;
}
.overlay.show {
  display: block;
  opacity: 1;
  filter: alpha(opacity=100);
  overflow: scroll;
}
#Loading p {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 2em;
}
#Loading .loader {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 60px;
  height: 60px;
  position: absolute;
  bottom: 50%;
  left: 50%;
  margin-bottom: 25px;
  margin-left: -30px;
  -webkit-animation: rotate 1250ms linear infinite;
  -moz-animation: rotate 1250ms linear infinite;
  -ms-animation: rotate 1250ms linear infinite;
  -o-animation: rotate 1250ms linear infinite;
  animation: rotate 1250ms linear infinite;
}
#Loading .loader img {
  width: 100%;
  height: 100%;
}
#Loading .loader .dot {
  width: 20px;
  height: 20px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -10px;
  margin-top: -10px;
  background: #f8ae4f;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  -webkit-animation: blink 625ms ease-in-out infinite alternate;
  -moz-animation: blink 625ms ease-in-out infinite alternate;
  -ms-animation: blink 625ms ease-in-out infinite alternate;
  -o-animation: blink 625ms ease-in-out infinite alternate;
  animation: blink 625ms ease-in-out infinite alternate;
}
@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes rotate {
  from {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-ms-keyframes rotate {
  from {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-o-keyframes rotate {
  from {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotate {
  from {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes blink {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-moz-keyframes blink {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-ms-keyframes blink {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-o-keyframes blink {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes blink {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.intro-page {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  bottom: auto;
  padding-top: 115px;
  display: none;
  overflow: hidden;
  margin: auto;
}
.intro-page.active {
  display: block;
}
.intro-page h3 {
  max-width: 800px;
  margin: auto;
}
.intro-steps {
  margin: 0;
  padding: 0;
  margin-top: 50px;
  list-style: none;
}
.intro-steps li {
  display: inline-block;
  width: 214px;
  margin: 0 36px;
}
.intro-steps p {
  padding: 20px 10px;
}
.intro-hover {
  position: relative;
  display: block;
}
.intro-hover img {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-transition: 150ms;
  -moz-transition: 150ms;
  -o-transition: 150ms;
  transition: 150ms;
}
.intro-hover:hover img {
  -webkit-transform: translateY(-5px);
  -moz-transform: translateY(-5px);
  -ms-transform: translateY(-5px);
  -o-transform: translateY(-5px);
  transform: translateY(-5px);
  -webkit-box-shadow: 0 3px 0 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 3px 0 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 3px 0 0 rgba(0, 0, 0, 0.1);
}
.intro-pointer {
  position: absolute;
  left: 50%;
  margin-left: -8.5px;
  top: 50px;
  -webkit-transition: all 150ms, left none;
  -moz-transition: all 150ms, left none;
  -o-transition: all 150ms, left none;
  transition: all 150ms, left none;
}
.intro-pointer.hidden {
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
}
.intro-close {
  margin: 20px 0 40px;
}
.settings-section {
  background-color: white;
  border: 1px solid #d7d8da;
  border-radius: 4px;
}
.settings-section.social .row-title {
  font-size: 14px;
}
.settings-section.security label {
  cursor: default;
}
.settings-section + .btn {
  margin-top: 32px;
  padding: 6px 12px;
}
.settings-row {
  position: relative;
  min-height: 104px;
}
.settings-row:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  height: 1px;
  left: 15px;
  right: 15px;
  background: #d7d8da;
}
.settings-row:first-child:before {
  content: none;
}
.settings-row .row-title {
  text-align: left;
  padding-left: 40px;
  font-size: 12px;
  font-weight: bold;
}
.settings-row label {
  position: relative;
  height: 104px;
  margin: 0;
  text-align: right;
  white-space: nowrap;
}
.settings-row label .label-text {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  right: auto;
  width: 110px;
  padding-right: 10px;
}
.settings-row label .label-text + input {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: absolute;
  left: 110px;
  right: 0;
  width: auto;
  height: 30px;
  top: 50%;
  margin-top: -15px;
}
.settings-row label .label-float {
  float: right;
}
.settings-row input.copyable {
  cursor: pointer;
}
.settings-row #domains {
  left: 55px;
  width: 100%;
}
.settings-row .action {
  padding-left: 40px;
}
.settings-row .save-changes {
  display: none;
}
.settings-row.changed .save-changes {
  display: inline-block;
}
.settings-row .row-fluid + .row-fluid {
  height: 52px;
  line-height: 52px;
}
.settings-row .row-fluid + .row-fluid label {
  height: 52px;
  line-height: 52px;
  margin-top: -20px;
}
.settings-row > *,
.settings-row .row-fluid > * {
  position: relative;
  line-height: 104px;
}
.toggle {
  position: relative;
  text-align: center;
  overflow: hidden;
  height: 104px;
}
.toggle label {
  vertical-align: text-bottom;
  display: inline-block;
  position: absolute;
  width: 68px;
  height: 28px;
  left: 50%;
  margin-left: -34px;
  top: 50%;
  margin-top: -14px;
  background: #f36741;
  border-radius: 14px;
  cursor: default;
  -webkit-transition: background 250ms ease-out;
  -moz-transition: background 250ms ease-out;
  -o-transition: background 250ms ease-out;
  transition: background 250ms ease-out;
}
.social .toggle label {
  cursor: pointer;
}
.toggle label:before,
.toggle label:after {
  position: absolute;
  top: 0;
  width: 50%;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  line-height: 30px;
  color: white;
}
.toggle label:before {
  content: "ON";
  left: 3px;
}
.toggle label:after {
  content: "OFF";
  right: 3px;
}
.toggle .switch {
  position: absolute;
  top: 3px;
  bottom: 3px;
  left: 3px;
  width: 30px;
  background-color: white;
  color: #c6c6c6;
  font-size: 9px;
  text-align: center;
  line-height: 22px;
  border-radius: 11px;
  -webkit-transition: 250ms ease-out;
  -moz-transition: 250ms ease-out;
  -o-transition: 250ms ease-out;
  transition: 250ms ease-out;
  z-index: 1;
}
.toggle input {
  position: absolute;
  visibility: hidden;
}
.toggle.toggle-on label {
  background: #78c45e;
}
.toggle.toggle-on label .switch {
  -webkit-transform: translateX(32px);
  -moz-transform: translateX(32px);
  -ms-transform: translateX(32px);
  -o-transform: translateX(32px);
  transform: translateX(32px);
}
.method-collapse-controls {
  line-height: 40px;
}
.method-collapse-controls a {
  cursor: pointer;
}
.method {
  padding: 0 25px 25px;
  margin-top: 30px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border: 1px solid #cdcdd0;
  background: white;
}
.method:first-child {
  margin-top: 0;
}
.method .alert {
  margin: 0;
}
.method .alert:empty {
  display: none;
}
.method header {
  *zoom: 1;
  position: relative;
  margin-left: -25px;
  margin-right: -25px;
  margin-bottom: 20px;
  cursor: pointer;
}
.method header:before,
.method header:after {
  display: table;
  content: "";
  line-height: 0;
}
.method header:after {
  clear: both;
}
.method header > * {
  float: left;
}
.method header .type {
  padding: 10px;
  margin-left: -1px;
  margin-top: -1px;
  margin-right: 10px;
  color: white;
  background: #f89406;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.method header .name,
.method header .endpoint {
  line-height: 40px;
  vertical-align: middle;
  margin: 0 5px;
}
.method header .name {
  font-size: 16px;
  font-weight: bold;
}
.method header .endpoint {
  color: #7c7d83;
}
.method header .endpoint:after {
  content: "+";
  position: absolute;
  right: 10px;
  top: 0;
  bottom: 0;
  line-height: 40px;
  font-size: 30px;
  font-weight: bold;
  color: #bdbec1;
}
.method header:hover .endpoint:after {
  color: #7c7d83;
}
.method.expanded header .endpoint:after {
  content: "−";
  font-size: 25px;
  top: -2px;
}
.method > p {
  font-size: 16px;
}
.method table {
  margin-top: 30px;
}
.method table tr:first-child {
  border-bottom: 1px dotted #93939a;
}
.method table tr:first-child th {
  text-align: left;
}
.method table td {
  padding-right: 20px;
  vertical-align: top;
  padding-top: 10px;
}
.method table td.param {
  font-size: 16px;
  font-weight: bold;
}
.method table td.require {
  font-family: Monaco, "Courier New", Courier, monospace;
  font-size: 12px;
  color: #51535c;
}
.method .missing {
  border: 1px solid red;
}
.method-inner {
  max-width: 960px;
}
.codebox {
  border: 1px solid #cdcdd0;
  padding: 5px;
  color: #51535c;
  font-family: Monaco, "Courier New", Courier, monospace;
  overflow: scroll;
}
.request-btn {
  margin-right: 10px;
}
.legend {
  position: absolute;
  top: 0;
  right: 40px;
  min-width: 80px;
  margin: 0;
  padding: 0;
  text-align: left;
  list-style: none;
  font-size: 13px;
  line-height: 20px;
}

.legend-alt {
  display: block;
  width: 100%;
  padding: 0;
  font-size: 21px;
  line-height: 40px;
  color: #333333;
  border: 0;
}

.legend-alt-border {
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.legend .swatch {
  display: inline-block;
  width: 15px;
  height: 15px;
  margin-right: 3px;
  margin-bottom: 5px;
  vertical-align: text-top;
}
.legend .swatch.fb {
  background: #094567;
}
.legend .swatch.tw {
  background: #35ace3;
}
.legend .swatch.in {
  background: #78c45f;
}
.legend .swatch.gp {
  background: #f3663f;
}
.legend .swatch.email {
  background: #A2A2A2;
}
.legend .swatch.low {
  background: #f36741;
}
.legend .swatch.mid {
  background: #33abe3;
}
.legend .swatch.high {
  background: #78c45e;
}
.legend .swatch.real {
  background: #f19132;
}
.legend .swatch.fake {
  background-color: white;
  background-image: url(../assets/images/fake-profiles-pattern.gif);
}
#Location {
  display: none;
  position: absolute;
  top: 118px;
  bottom: -75px;
  left: 24.999999999999996%;
  right: 1px;
  min-height: 900px;
  margin-left: -1px;
  z-index: 1;
}
#Location.active {
  display: block;
}
.marker-single {
  background-clip: padding-box;
  display: block;
  line-height: 40px;
  text-align: center;
  background: rgba(84, 85, 95, 0.9);
  color: white;
}
.marker-single:after {
  content: "";
  position: absolute;
  top: 100%;
  height: 0;
  left: 0;
  right: 0;
  border: 20px solid rgba(84, 85, 95, 0.9);
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-top: 15px solid rgba(84, 85, 95, 0.9);
}
.marker-cluster {
  background-clip: padding-box;
  border-radius: 20px;
}
.marker-cluster div {
  width: 30px;
  height: 30px;
  margin-left: 5px;
  margin-top: 5px;
  text-align: center;
  border-radius: 15px;
  color: white;
  font: 12px "Helvetica Neue", Arial, Helvetica, sans-serif;
}
.marker-cluster span {
  line-height: 30px;
}
.marker-cluster-small {
  background-color: rgba(241, 145, 50, 0.5);
}
.marker-cluster-small div {
  background-color: #f19132;
}
.marker-cluster-medium {
  background-color: rgba(241, 145, 50, 0.5);
  border-radius: 60px;
}
.marker-cluster-medium div {
  background-color: #f19132;
  width: 70px;
  height: 70px;
  border-radius: 35px;
}
.marker-cluster-medium span {
  line-height: 70px;
  font-size: 20px;
}
.marker-cluster-large {
  background-color: rgba(241, 145, 50, 0.5);
  border-radius: 90px;
}
.marker-cluster-large div {
  background-color: #f19132;
  width: 90px;
  height: 90px;
  border-radius: 45px;
}
.marker-cluster-large span {
  line-height: 90px;
  font-size: 30px;
}
.empty-msg {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 50px;
  font-size: 20px;
  line-height: 26px;
  display: none;
}
.empty .empty-msg {
  display: block;
}
.settings-section.cache {
  padding: 15px;
}
.settings-section.cache .row-fluid {
  height: 52px;
  max-width: 1575px;
}
.settings-section.cache .row-fluid > * {
  height: 100%;
  line-height: 52px;
}
.settings-section.cache label {
  margin: auto;
}
.settings-section.cache label input {
  margin: auto;
}
.settings-section.cache label .label-text {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  right: auto;
  width: 110px;
  padding-right: 10px;
}
.settings-section.cache label .label-text + input,
.settings-section.cache label .label-text + .datetime-input {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: absolute;
  left: 110px;
  width: auto;
  height: 30px;
  top: 50%;
  margin-top: -15px;
}
.settings-section.cache .date-wrapper {
  position: relative;
  text-align: right;
}
.settings-section.cache .date-wrapper label {
  float: left;
  height: 100%;
  line-height: inherit;
}
.settings-section.cache .datetime-input input {
  width: 144px;
}
.settings-section.cache .datetime-input:after {
  margin-top: -7px;
}
.settings-section.cache .checkbox-wrapper {
  padding-left: 25px;
}
.settings-section.cache .checkbox-wrapper label {
  float: left;
  height: 100%;
  line-height: inherit;
}
.settings-section.cache .checkbox-wrapper input {
  margin-right: 10px;
  margin-top: -2px;
}
#cache-table {
  margin: auto;
  margin-left: 0;
  max-width: 1048px;
}
#cache-table tr td:first-child,
#cache-table tr th:first-child {
  padding-left: 25px;
}

.ajaxLoading {
  cursor: progress !important;
}



.cancel-btn {
  color: #f89406;
  background: #ffffff;
  border: 1px solid black;
  border-style: solid;
  border-color: #f89406;
  -webkit-transition: 500ms;
  -moz-transition: 500ms;
  -o-transition: 500ms;
  transition: 500ms;
  -webkit-transition-property: 'color, background';
  -moz-transition-property: 'color, background';
  -o-transition-property: 'color, background';
  transition-property: 'color, background';
}
.cancel-btn:hover,
.cancel-btn:focus,
.cancel-btn.disabled,
.cancel-btn[disabled] {
  color: #ffffff;
  background-color: #474752;
  -webkit-transition-duration: 150ms;
  -moz-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
.cancel-btn:active,
.cancel-btn.active {
  color: #474752;
  background-color: #ffffff;
}



.pagination {
    margin: 20px 0
}

.pagination ul {
    display: inline-block;
    *display: inline;
    margin-bottom: 0;
    margin-left: 0;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    *zoom:1;-webkit-box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    -moz-box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    box-shadow: 0 1px 2px rgba(0,0,0,0.05)
}

.pagination ul>li {
    display: inline
}

.pagination ul>li>a,.pagination ul>li>span {
    float: left;
    padding: 4px 12px;
    line-height: 20px;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-left-width: 0
}

.pagination ul>li>a:hover,.pagination ul>li>a:focus,.pagination ul>.active>a,.pagination ul>.active>span {
    background-color: #f5f5f5
}

.pagination ul>.active>a,.pagination ul>.active>span {
    color: #999;
    cursor: default
}

.pagination ul>.disabled>span,.pagination ul>.disabled>a,.pagination ul>.disabled>a:hover,.pagination ul>.disabled>a:focus {
    color: #999;
    cursor: default;
    background-color: transparent
}

.pagination ul>li:first-child>a,.pagination ul>li:first-child>span {
    border-left-width: 1px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
    -webkit-border-top-left-radius: 4px;
    border-top-left-radius: 4px;
    -moz-border-radius-bottomleft: 4px;
    -moz-border-radius-topleft: 4px
}

.pagination ul>li:last-child>a,.pagination ul>li:last-child>span {
    -webkit-border-top-right-radius: 4px;
    border-top-right-radius: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
    -moz-border-radius-topright: 4px;
    -moz-border-radius-bottomright: 4px
}

.pagination-centered {
    text-align: center
}

.pagination-right {
    text-align: right
}

.pagination-large ul>li>a,.pagination-large ul>li>span {
    padding: 11px 19px;
    font-size: 17.5px
}

.pagination-large ul>li:first-child>a,.pagination-large ul>li:first-child>span {
    -webkit-border-bottom-left-radius: 6px;
    border-bottom-left-radius: 6px;
    -webkit-border-top-left-radius: 6px;
    border-top-left-radius: 6px;
    -moz-border-radius-bottomleft: 6px;
    -moz-border-radius-topleft: 6px
}

.pagination-large ul>li:last-child>a,.pagination-large ul>li:last-child>span {
    -webkit-border-top-right-radius: 6px;
    border-top-right-radius: 6px;
    -webkit-border-bottom-right-radius: 6px;
    border-bottom-right-radius: 6px;
    -moz-border-radius-topright: 6px;
    -moz-border-radius-bottomright: 6px
}

.pagination-mini ul>li:first-child>a,.pagination-small ul>li:first-child>a,.pagination-mini ul>li:first-child>span,.pagination-small ul>li:first-child>span {
    -webkit-border-bottom-left-radius: 3px;
    border-bottom-left-radius: 3px;
    -webkit-border-top-left-radius: 3px;
    border-top-left-radius: 3px;
    -moz-border-radius-bottomleft: 3px;
    -moz-border-radius-topleft: 3px
}

.pagination-mini ul>li:last-child>a,.pagination-small ul>li:last-child>a,.pagination-mini ul>li:last-child>span,.pagination-small ul>li:last-child>span {
    -webkit-border-top-right-radius: 3px;
    border-top-right-radius: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-bottom-right-radius: 3px;
    -moz-border-radius-topright: 3px;
    -moz-border-radius-bottomright: 3px
}

.pagination-small ul>li>a,.pagination-small ul>li>span {
    padding: 2px 10px;
    font-size: 11.9px
}

.pagination-mini ul>li>a,.pagination-mini ul>li>span {
    padding: 0 6px;
    font-size: 10.5px
}

body .btn-primary {
    color: #ee8e3c;
    background: #fff;
    border-color: #ee8e3c;
}

body .btn-secondary {
    background-color: #fff;
    color: #6c757d;
}

body .text-primary {
    color: #ee8e3c !important;
}

.format-selector {
  margin-top: 10px;
}

.error-msg {
  color: red;
}

.identifier-width {
  width: 350px !important;
}

.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .3s;
  transition: .3s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: -0.5px;
  bottom: 0px;
  background-color: white;
  -webkit-transition: .3s;
  transition: .3s;
}

input:checked + .slider {
  background-color: #ee8e3c;
}

input:focus + .slider {
  box-shadow: 0 0 1px #ee8e3c;
}

input:checked + .slider:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
  border: 1px solid #999;
}

.mapping-edit-button {
    color: #ee8e3c !important;
    cursor: pointer;
}

.page_container {
  width: 97%;
  margin: auto;
}

#menu td {
 width: 200px;
 white-space: nowrap;
}

#tbl_active_users td {
  white-space:break-spaces;
}

tr {
 line-height: 20px;
}