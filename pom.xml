<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>socure-super-admin</artifactId>
	<version>${revision}</version>
	<packaging>war</packaging>
	<name>socure-super-admin</name>
	<url>http://maven.apache.org</url>

	<parent>
		<artifactId>maven-root</artifactId>
		<groupId>me.socure</groupId>
		<version>0.2-SNAPSHOT</version>
	</parent>

	<properties>
		<aspectj.version>1.7.3</aspectj.version>
		<javax.servlet-api.version>2.5</javax.servlet-api.version>
		<javax.jstl-taglibs.version>1.2.3</javax.jstl-taglibs.version>
		<!-- Testing -->
		<mockito.version>1.8.5</mockito.version>
		<junit.version>4.8.2</junit.version>
		<!-- Plugins -->
		<maven.copy.plugin.version>0.2.3</maven.copy.plugin.version>
		<maven.compiler.plugin.version>2.3.2</maven.compiler.plugin.version>
		<maven.apt.plugin.version>1.0</maven.apt.plugin.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<maven.compiler.plugin.version>3.1</maven.compiler.plugin.version>
		<maven.enforcer.plugin.version>1.3.1</maven.enforcer.plugin.version>
		<maven.surefire.plugin.version>2.17</maven.surefire.plugin.version>
		<spring.version>5.3.39</spring.version>
		<spring.security.version>5.8.16</spring.security.version>
		<spring.data.jpa.version>2.1.8.RELEASE</spring.data.jpa.version>
		<spring.security.oauth.version>1.0.4.RELEASE
		</spring.security.oauth.version>
		<spring.social.version>1.1.0.RELEASE</spring.social.version>
		<spring.social.google.version>1.0.0.RELEASE
		</spring.social.google.version>
		<spring.social.linkedin.version>1.0.0.RELEASE
		</spring.social.linkedin.version>
		<socialsignin.spring.security.version>1.0.2.RELEASE
		</socialsignin.spring.security.version>
		<spring.integration.version>3.0.2.RELEASE</spring.integration.version>
		<spring.webflow.faces.version>2.5.1.RELEASE
		</spring.webflow.faces.version>
		<logback.version>1.3.15</logback.version>
		<logback.groovy.version>2.5.5</logback.groovy.version>
		<guava.version>32.0.1-jre</guava.version>
		<string.metric.version>0.27.4</string.metric.version>
		<javassit.version>3.20.0-GA</javassit.version>
		<http.client.version>4.5.1</http.client.version>
		<c3p0.version>0.9.5.2</c3p0.version>
		<sc.ver>2.11</sc.ver>

		<!-- http -->
		<netty.reactive.streams.version>2.0.0</netty.reactive.streams.version>
		<reactive.streams.version>1.0.2</reactive.streams.version>
		<dispatch.version>1.0.0-M1</dispatch.version>

		<!-- AWS -->
		<elasticache.cluster.client.version>1.1.1</elasticache.cluster.client.version>
		<aws.secretsmanager.jdbc>1.0.6</aws.secretsmanager.jdbc>
		<aws.java.sdk.v2.version>2.20.55</aws.java.sdk.v2.version>
		<http.client.version>4.5.13</http.client.version>
		<!-- AWS -->

		<socure.common.version>${revision}</socure.common.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- AWS Java SDK -->
			<dependency>
					<groupId>software.amazon.awssdk</groupId>
					<artifactId>bom</artifactId>
					<version>${aws.java.sdk.v2.version}</version>
					<type>pom</type>
					<scope>import</scope>
			</dependency>
			<dependency>
					<groupId>com.amazonaws</groupId>
					<artifactId>elasticache-java-cluster-client</artifactId>
					<version>${elasticache.cluster.client.version}</version>
					<exclusions>
							<exclusion>
									<groupId>log4j</groupId>
									<artifactId>log4j</artifactId>
							</exclusion>
							<exclusion>
									<groupId>org.slf4j</groupId>
									<artifactId>slf4j-log4j12</artifactId>
							</exclusion>
							<exclusion>
									<groupId>cglib</groupId>
									<artifactId>cglib-full</artifactId>
							</exclusion>
							<exclusion>
									<groupId>cglib</groupId>
									<artifactId>cglib-nodep</artifactId>
							</exclusion>
					</exclusions>
			</dependency>
			<dependency>
					<groupId>com.amazonaws.secretsmanager</groupId>
					<artifactId>aws-secretsmanager-jdbc</artifactId>
					<version>${aws.secretsmanager.jdbc}</version>
			</dependency>
			<dependency>
					<groupId>com.amazonaws</groupId>
					<artifactId>jmespath-java</artifactId>
					<version>${aws.java.sdk.version}</version>
			</dependency>
			<!-- AWS Java SDK -->
			<dependency>
				<groupId>org.scala-lang</groupId>
				<artifactId>jline</artifactId>
				<version>2.11.0-M3</version>
			</dependency>
			<!-- HTTP Libs - Start -->

			<dependency>
				<groupId>org.reactivestreams</groupId>
				<artifactId>reactive-streams</artifactId>
				<version>${reactive.streams.version}</version>
			</dependency>

			<dependency>
				<groupId>org.dispatchhttp</groupId>
				<artifactId>dispatch-core_${sc.ver}</artifactId>
				<version>${dispatch.version}</version>
			</dependency>
			<dependency>
				<groupId>org.dispatchhttp</groupId>
				<artifactId>dispatch-json4s-native_${sc.ver}</artifactId>
				<version>${dispatch.version}</version>
			</dependency>
			<dependency>
				<groupId>org.dispatchhttp</groupId>
				<artifactId>dispatch-json4s-jackson_${sc.ver}</artifactId>
				<version>${dispatch.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>${http.client.version}</version>
			</dependency>


			<!-- HTTP Libs - End -->
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.30</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>transfer</artifactId>
			<version>${aws.java.sdk.v2.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>sso</artifactId>
			<version>${aws.java.sdk.v2.version}</version>
		</dependency>
		<!-- Document Manager Service-->
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>document-manager-client</artifactId>
			<version>${revision}</version>
		</dependency>

		<!-- Docv Orchestra Service-->
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>docv-orchestra-client</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<artifactId>scala-compiler</artifactId>
			<groupId>org.scala-lang</groupId>
		</dependency>
		<!-- Account Service -->
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>account-service-client</artifactId>
			<version>${revision}</version>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.dataformat</groupId>
					<artifactId>jackson-dataformat-cbor</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>sandbox-client</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>socure-commons-admin</artifactId>
			<version>${revision}</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.data</groupId>
					<artifactId>spring-data-jpa</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.webflow</groupId>
					<artifactId>spring-faces</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-jpa</artifactId>
			<version>${spring.data.jpa.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.webflow</groupId>
			<artifactId>spring-faces</artifactId>
			<version>${spring.webflow.faces.version}</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<version>2.12.2</version>
		</dependency>

		<dependency>
			<groupId>javax.annotation</groupId>
			<artifactId>jsr250-api</artifactId>
			<version>1.0</version>
		</dependency>


		<!-- A seamless aspect-oriented extension to the Java programming language -->
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>${aspectj.version}</version>
		</dependency>

		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>${aspectj.version}</version>
		</dependency>

		<!-- Cglib is a powerful, high performance and quality Code Generation
			Library, It is used to extend JAVA classes and implements interfaces at runtime. -->
		<!-- CGLIB -->
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib</artifactId>
			<version>2.2.2</version>
			<scope>runtime</scope>
		</dependency>	
		<dependency>
    		<groupId>jakarta.servlet.jsp.jstl</groupId>
    		<artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
    		<version>${javax.jstl-taglibs.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.taglibs</groupId>
			<artifactId>taglibs-standard-impl</artifactId>
			<version>1.2.5</version>
		</dependency>

		<!-- Logger -->
		<!--<dependency>-->
			<!--<groupId>log4j</groupId>-->
			<!--<artifactId>log4j</artifactId>-->
			<!--<version>${log4j.version}</version>-->
			<!--<type>jar</type>-->
			<!--<scope>compile</scope>-->
		<!--</dependency>-->

		<!-- mail -->

		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.5.0-b01</version>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-engine-core</artifactId>
			<version>2.4.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.hsqldb</groupId>
					<artifactId>hsqldb</artifactId>
				</exclusion>
				<exclusion>
					<groupId>commons-collections</groupId>
					<artifactId>commons-collections</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
		</dependency>

		<!-- Inject -->
		<dependency>
			<groupId>javax.inject</groupId>
			<artifactId>javax.inject</artifactId>
			<version>1</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-oxm</artifactId>
			<version>4.3.29.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jms</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
			<version>${spring.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
			<version>${spring.security.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
			<version>${spring.security.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
			<version>${spring.security.version}</version>
		</dependency>

		<!-- Javax Servlet. This needs to be included for runtime only! -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>${javax.servlet-api.version}</version>
		</dependency>



		<!-- Testing dependencies -->
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-all</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.11</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>hamcrest-core</artifactId>
					<groupId>org.hamcrest</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-all</artifactId>
			<version>${mockito.version}</version>
			<type>jar</type>
			<scope>test</scope>
		</dependency>
		<!--<dependency>
			<groupId>hsqldb</groupId>
			<artifactId>hsqldb</artifactId>
			<version>1.8.0.10</version>
		</dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<version>2.1.210</version>
		</dependency>-->

		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>0.8.1</version>
			<scope>test</scope>
		</dependency>

		<!-- Apache Commons Upload -->
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.5</version>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.14.0</version>
		</dependency>

		<!-- reCaptcha -->
		<dependency>
			<groupId>net.tanesha.recaptcha4j</groupId>
			<artifactId>recaptcha4j</artifactId>
			<version>0.0.7</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>action-auditing-client</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>transaction-rest-client</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>decision-service-client</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>alertlist-maintainer-client</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>common-saml3</artifactId>
			<version>${socure.common.version}</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>common-config</artifactId>
			<version>${socure.common.version}</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>common-metrics</artifactId>
			<version>${socure.common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-validator</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.maven.wagon</groupId>
			<artifactId>wagon-ssh</artifactId>
			<version>3.0.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.neovisionaries</groupId>
			<artifactId>nv-i18n</artifactId>
			<version>1.18</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws.secretsmanager</groupId>
			<artifactId>aws-secretsmanager-caching-java</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>common-launchdarkly</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>me.socure</groupId>
			<artifactId>kyc-search-unique-id</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.19.0</version>
		</dependency>
	</dependencies>

	<profiles>
		<profile>
			<id>jib-fips</id>
			<activation>
				<property>
					<name>build-environment</name>
					<value>gitlab-ci-fips</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>com.google.cloud.tools</groupId>
						<artifactId>jib-maven-plugin</artifactId>
						<version>3.2.1</version>
						<configuration>
							<allowInsecureRegistries>true</allowInsecureRegistries>
							<from>
							    <image>registry.us-east-1.build.socure.link/apps/fips-tomcat9:1192202-e86af8d1-9.0.98-jre8-openjdk</image>
								<platforms>
									<platform>
										<architecture>amd64</architecture>
										<os>linux</os>
									</platform>
									<platform>
										<architecture>arm64</architecture>
										<os>linux</os>
									</platform>
								</platforms>
							</from>
							<to>
								<image>fips-registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SERVICE_NAME}:latest-fips
								</image>
								<auth>
									<username>${env.REGISTRY_USER}</username>
									<password>${env.REGISTRY_PASS}</password>
								</auth>
								<tags>
									<tag>${env.FIPS_DOCKER_IMAGE_TAG}</tag>
								</tags>
							</to>
							<container>
								<appRoot>/usr/local/tomcat/webapps/ROOT</appRoot>
								<ports>
									<port>8080</port>
								</ports>
							</container>
							<extraDirectories>
								<paths>
									<path>
										<from>target/</from>
										<into>/usr/local/tomcat/webapps/ROOT</into>
										<includes>socure-super-admin-0.2-FIPS-SNAPSHOT</includes>
									</path>
								</paths>
							</extraDirectories>
						</configuration>
						<executions>
							<execution>
								<id>build-and-push-docker-image</id>
								<phase>package</phase>
								<goals>
									<goal>build</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>jib</id>
			<activation>
				<property>
					<name>build-environment</name>
					<value>gitlab-ci</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>com.google.cloud.tools</groupId>
						<artifactId>jib-maven-plugin</artifactId>
						<version>3.2.1</version>
						<configuration>
							<from>
								<image>tomcat:8.5.82-jdk8-corretto-al2</image>
								<platforms>
									<platform>
										<architecture>amd64</architecture>
										<os>linux</os>
									</platform>
									<platform>
										<architecture>arm64</architecture>
										<os>linux</os>
									</platform>
								</platforms>
							</from>
							<to>
								<image>registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SERVICE_NAME}
								</image>
								<auth>
									<username>${env.REGISTRY_USER}</username>
									<password>${env.REGISTRY_PASS}</password>
								</auth>
								<tags>
									<tag>latest</tag>
									<tag>${env.NON_FIPS_DOCKER_IMAGE_TAG}</tag>
								</tags>
							</to>
							<container>
								<appRoot>/usr/local/tomcat/webapps/ROOT</appRoot>
								<ports>
									<port>8080</port>
								</ports>
							</container>
							<extraDirectories>
								<paths>
									<path>
										<from>target/</from>
										<into>/usr/local/tomcat/webapps/ROOT</into>
										<includes>socure-super-admin-0.2-SNAPSHOT</includes>
									</path>
								</paths>
							</extraDirectories>
						</configuration>
						<executions>
							<execution>
								<id>build-and-push-docker-image</id>
								<phase>package</phase>
								<goals>
									<goal>build</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
				<version>3.7.1</version>
				<dependencies>
					<dependency>
						<!-- add support for ssh/scp -->
						<groupId>org.apache.maven.wagon</groupId>
						<artifactId>wagon-ssh</artifactId>
						<version>1.0</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.cyclonedx</groupId>
				<artifactId>cyclonedx-maven-plugin</artifactId>
				<version>1.3.1</version>
				<inherited>false</inherited>
				<executions>
					<execution>
						<phase>verify</phase>
						<goals>
							<goal>makeAggregateBom</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<includeCompileScope>true</includeCompileScope>
					<includeProvidedScope>true</includeProvidedScope>
					<includeRuntimeScope>true</includeRuntimeScope>
					<includeSystemScope>true</includeSystemScope>
					<includeTestScope>true</includeTestScope>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>properties-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>
