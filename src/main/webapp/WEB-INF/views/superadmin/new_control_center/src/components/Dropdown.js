import React, { useCallback } from 'react';

const Dropdown = ({
  options,
  value,
  onChange
}) => {
  const onSelectionChange = useCallback((val) => {
    onChange && onChange(val);
  }, [ onChange ])

  return <select
    value={value}
    className='scoure-c-dropdown'
    onChange={e => onSelectionChange(e.target.value)}>
    {options && options.map((val, idx) =>
      <option key={idx} value={val.value}>{val.label}</option>
    )}
  </select>
}

export default Dropdown;