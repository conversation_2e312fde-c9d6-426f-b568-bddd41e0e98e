package me.socure.filter

import javax.servlet._
import javax.servlet.http.HttpServletRequest
import me.socure.action.audit.ActionAuditConfiguration
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.context.support.SpringBeanAutowiringSupport

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/5/17.
  */
class CustomLogoutFilter extends Filter {
  private val logger = LoggerFactory.getLogger(classOf[CustomLogoutFilter])
  private val metrics: Metrics = JavaMetricsFactory.get(this.getClass)
  private val excludePaths = List("/")

  override def init(filterConfig: FilterConfig): Unit = {
    SpringBeanAutowiringSupport.processInjectionBasedOnServletContext(this,
      filterConfig.getServletContext())
  }

  override def destroy(): Unit = {

  }

  @Autowired
  var actionAuditConfiguration : ActionAuditConfiguration = _
  override def doFilter(servletRequest: ServletRequest, servletResponse: ServletResponse, filterChain: FilterChain): Unit = {
    if(servletRequest.asInstanceOf[HttpServletRequest].getRequestURI.contains("logout")){
      ProcessFilter.doFilter(servletRequest, servletResponse, filterChain, actionAuditConfiguration)
    }else{
      filterChain.doFilter(servletRequest, servletResponse)
    }
  }
}
