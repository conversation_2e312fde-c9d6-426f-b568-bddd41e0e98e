@font-face {
	font-family: 'Dashboard-Icons';
	src:url('fonts/Dashboard-Icons.eot');
	src:url('fonts/Dashboard-Icons.eot?#iefix') format('embedded-opentype'),
		url('fonts/Dashboard-Icons.woff') format('woff'),
		url('fonts/Dashboard-Icons.ttf') format('truetype'),
		url('fonts/Dashboard-Icons.svg#Dashboard-Icons') format('svg');
	font-weight: normal;
	font-style: normal;
}

/* Use the following CSS code if you want to use data attributes for inserting your icons */
[data-icon]:before {
	font-family: 'Dashboard-Icons';
	content: attr(data-icon);
	speak: none;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Use the following CSS code if you want to have a class per icon */
/*
Instead of a list of all class selectors,
you can use the generic selector below, but it's slower:
[class*="icn-"] {
*/
.icn-export, .icn-print, .icn-calendar, .icn-check, .icn-notch-right, .icn-notch-left, .icn-notch-down, .icn-ridges, .icn-fb, .icn-tw, .icn-in, .icn-gp, .icn-user, .icn-multi-user {
	font-family: 'Dashboard-Icons';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
}
.icn-export:before {
	content: "\e001";
}
.icn-print:before {
	content: "\e002";
}
.icn-calendar:before {
	content: "\e003";
}
.icn-check:before {
	content: "\e004";
}
.icn-notch-right:before {
	content: "\e005";
}
.icn-notch-left:before {
	content: "\e006";
}
.icn-notch-down:before {
	content: "\e007";
}
.icn-ridges:before {
	content: "\e008";
}
.icn-fb:before {
	content: "\e009";
}
.icn-tw:before {
	content: "\e00a";
}
.icn-in:before {
	content: "\e00b";
}
.icn-gp:before {
	content: "\e00c";
}
.icn-user:before {
	content: "\e000";
}
.icn-multi-user:before {
	content: "\e00d";
}
