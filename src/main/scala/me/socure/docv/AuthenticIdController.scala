package me.socure.docv

import me.socure.common.json.bean.APIOutput
import me.socure.docv.common.dto.AuthenticIdStrategyDto
import me.socure.service.constants.IConstants
import me.socure.superadmin.factory.ObjectMapperFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.http.{HttpHeaders, HttpStatus, MediaType, ResponseEntity}
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{PathVariable, RequestBody, RequestMapping, RequestMethod}

import java.util
import scala.concurrent.ExecutionContext


@Controller
@RequestMapping(Array("/superadmin/1/docv/authenticId/*"))
class AuthenticIdController {

  @Autowired
  var authenticIdResolver : AuthenticIdResolver = _

  @Autowired
  @Qualifier("taskExecutor")
  var threadPoolTaskExecutor: ThreadPoolTaskExecutor = _
  implicit val ec: ExecutionContext = ExecutionContext.fromExecutor(threadPoolTaskExecutor)

  @RequestMapping(value = Array("/fetch/all"), method = Array(RequestMethod.GET))
  def fetchAllAuthenticIdStrategies(): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = authenticIdResolver.fetchAllAuthenticIdStrategies()
    result match {
      case Right(strategies) => {
        var strategyJava  = new util.ArrayList[StrategyDto]()
        strategies.foreach(x => strategyJava.add(getStrategyAsJava(x)));
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(strategyJava)
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    pushResponseToCaller(apiOutput)
  }

  @RequestMapping(value = Array("/add"), method = Array(RequestMethod.POST), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def addAuthenticIdStrategy(@RequestBody strategy: StrategyDto) : ResponseEntity[Object] = {
    val authStrategy = AuthenticIdStrategyDto(
      id= None,
      strategyId= None,
      name= strategy.getName,
      keyType= strategy.getKeyType,
      lenientLocationCode= strategy.getLenientLocationCode,
      strictLocationCode= strategy.getStrictLocationCode
    )
    val result = authenticIdResolver.addAuthenticIdStrategy(authStrategy)
    val apiOutput = new APIOutput()
    result match {
      case Right(strategy) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(getStrategyAsJava(strategy))
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    pushResponseToCaller(apiOutput)
  }

  @RequestMapping(value = Array("/update"), method = Array(RequestMethod.POST), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def updateAuthenticIdStrategy(@RequestBody strategy: StrategyDto) : ResponseEntity[Object] = {
    val authStrategy = AuthenticIdStrategyDto(
      id= Some(strategy.getId),
      strategyId= Some(strategy.getStrategyId),
      name= strategy.getName,
      keyType= strategy.getKeyType,
      lenientLocationCode= strategy.getLenientLocationCode,
      strictLocationCode= strategy.getStrictLocationCode
    )
    val result = authenticIdResolver.updateAuthenticIdStrategy(authStrategy)
    val apiOutput = new APIOutput()
    result match {
      case Right(strategy) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(getStrategyAsJava(strategy))
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    pushResponseToCaller(apiOutput)
  }

  @RequestMapping(value = Array("/fetch/strategy/{strategyId}"), method = Array(RequestMethod.GET))
  def fetchAuthenticIdStrategy(@PathVariable("strategyId") strategyId : String) : ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = authenticIdResolver.fetchAuthenticIdStrategy(strategyId)
    result match {
      case Right(strategy) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(getStrategyAsJava(strategy))
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    pushResponseToCaller(apiOutput)
  }

  private def pushResponseToCaller(apiOutput : APIOutput): ResponseEntity[Object]  = {
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  private def getStrategyAsJava(authenticIdStrategyDto: AuthenticIdStrategyDto) = {
    new StrategyDto().setId(authenticIdStrategyDto.id.get).setStrategyId(authenticIdStrategyDto.strategyId.get)
      .setName(authenticIdStrategyDto.name).setKeyType(authenticIdStrategyDto.keyType)
      .setLenientLocationCode(authenticIdStrategyDto.lenientLocationCode)
      .setStrictLocationCode(authenticIdStrategyDto.strictLocationCode)
  }
}
