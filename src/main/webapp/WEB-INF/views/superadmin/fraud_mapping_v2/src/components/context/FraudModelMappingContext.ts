import { createContext } from "react";
import {
  AccountAutoComplete,
  FraudModelAutoComplete,
  MappingsForAccount,
  ModelInfo,
} from "../../types";

interface iFraudModelMappingContext {
  activeAccount: AccountAutoComplete;
  accounts: AccountAutoComplete[];
  models: FraudModelAutoComplete[];
  mapping: MappingsForAccount;
  modelsAndDescriptions: ModelInfo[];

  setActiveAccount: (value: AccountAutoComplete) => void;
  setAccounts: (value: AccountAutoComplete[]) => void;
  setModels: (value: FraudModelAutoComplete[]) => void;
  setMapping: (value: MappingsForAccount) => void;
  setModelsAndDescriptions: (value: ModelInfo[]) => void;
  getAndUpdateMappingsForAccount: (
    accountId: number,
    setLoader?: boolean
  ) => void;
  setShowLoader: (value: boolean) => void;
}

export const FraudModelMappingContext =
  createContext<iFraudModelMappingContext>({} as iFraudModelMappingContext);
