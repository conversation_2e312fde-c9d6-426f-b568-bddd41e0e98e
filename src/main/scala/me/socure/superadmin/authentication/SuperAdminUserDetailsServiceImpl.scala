package me.socure.superadmin.authentication

import java.util.{Arrays => JArrays}

import me.socure.model.BusinessUserRoles
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.userdetails.{UserDetails, UserDetailsService}
import org.springframework.stereotype.Service

@Service("userDetailsServiceImpl")
class SuperAdminUserDetailsServiceImpl extends UserDetailsService {
  override def loadUserByUsername(username: String): UserDetails = {
    new UserDetailsWithoutCredentials(
      username = username,
      authorities = JArrays.asList(
        new SimpleGrantedAuthority(BusinessUserRoles.SUPER_ADMIN.name)
      )
    )
  }
}
