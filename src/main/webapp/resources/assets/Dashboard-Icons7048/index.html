<!doctype html>
<html>
<head>
<title>Your Font/Glyphs</title>
<link rel="stylesheet" href="style.css" />
<!--[if lte IE 7]><script src="lte-ie7.js"></script><![endif]-->
<style>
	section, header, footer {display: block;}
	body {
		font-family: sans-serif;
		color: #444;
		line-height: 1.5;
		font-size: 1em;
	}
	* {
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		margin: 0;
		padding: 0;
	}
	.glyph {
		font-size: 16px;
		float: left;
		text-align: center;
		background: #eee;
		padding: .75em;
		margin: .75em 1.5em .75em 0;
		width: 7.5em;
		border-radius: .25em;
		box-shadow: inset 0 0 0 1px #f8f8f8, 0 0 0 1px #CCC;
	}
	.glyph input {
		font-family: consolas, monospace;
		font-size: 13px;
		width: 100%;
		text-align: center;
		border: 0;
		box-shadow: 0 0 0 1px #ccc;
		padding: .125em;
	}
	.w-main {
		width: 80%;
	}
	.centered {
		margin-left: auto;
		margin-right: auto;
	}
	.fs1 {
		font-size: 2em;
	}
	header {
		margin: 2em 0;
		padding-bottom: .5em;
		color: #666;
		box-shadow: 0 2px #eee;
	}
	header h1 {
		font-size: 2em;
		font-weight: normal;
	}
	.clearfix:before, .clearfix:after { content: ""; display: table; }
	.clearfix:after, .clear { clear: both; }
	footer {
		margin-top: 2em;
		padding: .5em 0;
		box-shadow: 0 -2px #eee;
	}
	a, a:visited {
		color: #B35047;
		text-decoration: none;
	}
	a:hover, a:focus {color: #000;}
	.box1 {
		font-size: 16px;
		display: inline-block;
		width: 15em;
		padding: .25em .5em;
		background: #eee;
		margin: .5em 1em .5em 0;
	}
</style>
</head>
<body>
	<div class="w-main centered">
	<section class="mtm clearfix" id="glyphs">
	<header>
		<h1>Your font contains the following glyphs</h1>
		<p>The generated SVG font can be imported back to <a href="http://icomoon.io/app">IcoMoon</a> for modification.</p>
	</header>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe001;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe001;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe002;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe002;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe003;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe003;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe004;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe004;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe005;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe005;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe006;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe006;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe007;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe007;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe008;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe008;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe009;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe009;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00a;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00a;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00b;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00b;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00c;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00c;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe000;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe000;" />
	</div>
	<div class="glyph">
		<div class="fs1" aria-hidden="true" data-icon="&#xe00d;"></div>
		<input type="text" readonly="readonly" value="&amp;#xe00d;" />
	</div>
	</section>
	<div class="clear"></div>
	<section class="mtm clearfix" id="glyphs">
	<header>
		<h1>Class Names</h1>
	</header>
	<span class="box1">
		<span aria-hidden="true" class="icn-export"></span>
		&nbsp;icn-export
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-print"></span>
		&nbsp;icn-print
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-calendar"></span>
		&nbsp;icn-calendar
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-check"></span>
		&nbsp;icn-check
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-notch-right"></span>
		&nbsp;icn-notch-right
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-notch-left"></span>
		&nbsp;icn-notch-left
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-notch-down"></span>
		&nbsp;icn-notch-down
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-ridges"></span>
		&nbsp;icn-ridges
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-fb"></span>
		&nbsp;icn-fb
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-tw"></span>
		&nbsp;icn-tw
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-in"></span>
		&nbsp;icn-in
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-gp"></span>
		&nbsp;icn-gp
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-user"></span>
		&nbsp;icn-user
	</span>
	<span class="box1">
		<span aria-hidden="true" class="icn-multi-user"></span>
		&nbsp;icn-multi-user
	</span>
	</section>
	<footer>
		<p>Generated by <a href="http://icomoon.io">IcoMoon.io</a></p>
	</footer>
	</div>
	<script>
	document.getElementById("glyphs").addEventListener("click", function(e) {
		var target = e.target;
		if (target.tagName === "INPUT") {
			target.select();
		}
	});
	</script>
</body>
</html>