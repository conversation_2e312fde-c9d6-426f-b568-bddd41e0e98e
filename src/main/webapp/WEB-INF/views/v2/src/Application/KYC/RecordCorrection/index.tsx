import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { Button, ButtonGroup, Card, Checkbox, FormControl, FormControlLabel, FormGroup, Grid, InputLabel, MenuItem, Select, TextField, Typography } from '@mui/material';
import {
    Refresh as RefreshIcon,
    GetApp as GetAppIcon,
    Clear as ClearIcon,
    SystemUpdateAlt as SystemUpdateAltIcon,
    CreditCard as CreditCardIcon
} from "@mui/icons-material";
import { apiCall } from '../../../Utilities';
import { AxiosError, isAxiosError } from 'axios';
import ImportFromIdContainer from './ImportFromId/ImportFromIdContainer';
import { toaster } from '../../../Components/Toaster';

const getDataEndpoint = (v: string) => {
    const vSplit = v.split("/");
    return {
        host: `https://${vSplit[1]}`,
        region: vSplit[0],
        indexName: vSplit[2],
        dynamoDBTable: vSplit[3],
    }
}

const getErrorMessage = (e: AxiosError<any, any>): string => {
    try {
        const data: any = e.response?.data;
        return data.message || data.data.message;
    } catch (exception) {
        return e.message;
    }
}

const RecordCorrection: React.FC = function (props) {

    const [vendor, setVendor] = useState<string | null>(null);
    const [dataCombo, setDataCombo] = useState<string | null>(null);
    const [configData, setConfigData] = useState<any>(null);
    const [recordId, setRecordId] = useState<string>("");
    const [securityKey, setSecurityKeyUtil] = useState<string>(localStorage.getItem("kycRecordCorrectionSecurityKey") || "");
    const [retrievedData, setRetrievedData] = useState<any>(null);
    const [editingData, setEditingData] = useState<string>("");
    const [description, setDescription] = useState<string>("");
    const [socureTicket, setSocureTicket] = useState<string>("");
    const [vendorTicket, setVendorTicket] = useState<string>("");
    const [updateCidDeceased, setUpdateCidDeceased] = useState<boolean>(false);
    const [updateSsnDeceased, setUpdateSsnDeceased] = useState<boolean>(false);
    const [updateFullSource, setUpdateFullSource] = useState<boolean>(false);
    const [cidDeceased, setCidDeceased] = useState<boolean>(false);
    const [ssnDeceased, setSsnDeceased] = useState<boolean>(false);
    const [idDialogOpen, setIdDialogOpen] = useState<boolean>(false);
    const [socureId, setSocureId] = useState<string | null>(null);


    const applyId = useCallback((v: string, r: string, sid: string): void => {
        if (vendor !== v && configData.vendorDataComboMap[v] === undefined) {
            toaster.error("The vendor configuration corresponding to record ID does not exist.");
        } else {
            if (vendor !== v) {
                setVendor(v);
                setDataCombo(configData.vendorDataComboMap[v][0]);
            }
            setSocureId(sid);
            setRecordId(r);
            setIdDialogOpen(false);
        }
    }, [setSocureId, setVendor, setDataCombo, setRecordId, setIdDialogOpen, configData, vendor]);


    const setSecurityKey = useCallback((e: any) => {
        localStorage.setItem("kycRecordCorrectionSecurityKey", e.target.value)
        setSecurityKeyUtil(e.target.value)
    }, [setSecurityKeyUtil]);

    const resetEditingData = useCallback(() => {
        setEditingData(JSON.stringify(retrievedData, undefined, 4));
    }, [setEditingData, retrievedData]);

    const resetAllData = useCallback(() => {
        const { vendors, vendorDataComboMap } = configData;
        setVendor(vendors[0]);
        setDataCombo(vendorDataComboMap[vendors[0]][0])
        setEditingData("");
        setRetrievedData(null);
        setRecordId("");
        setDescription("")
        setSocureTicket("")
        setVendorTicket("")
        setUpdateCidDeceased(false)
        setUpdateSsnDeceased(false)
        setUpdateFullSource(false)
        setCidDeceased(false)
        setSsnDeceased(false)
        setSocureId(null);
        setIdDialogOpen(false);
    }, [
        configData,
        setVendor,
        setDataCombo,
        setEditingData,
        setRetrievedData,
        setRecordId,
        setDescription,
        setSocureTicket,
        setVendorTicket,
        setUpdateCidDeceased,
        setUpdateSsnDeceased,
        setUpdateFullSource,
        setCidDeceased,
        setSsnDeceased,
        setSocureId,
        setIdDialogOpen
    ]);

    const getData = useCallback(async () => {
        try {
            const result = await apiCall("/kyc/record_correction/get-data", {
                ...(getDataEndpoint(dataCombo as any)),
                token: securityKey,
                clusterId: recordId
            });
            if (result.status === 200) {
                setRetrievedData(result.data.attributes);
                setEditingData(JSON.stringify(result.data.attributes, undefined, 4));
                toaster.info("Successfully retrieved record data.", 5000);
            } else {
                toaster.error("The response status code was not 200!", 8000);
                console.log("The response status code was not 200!", result);
            }
        } catch (e: any) {
            if (isAxiosError(e)) {
                toaster.error(getErrorMessage(e), 8000);
                console.error("An api error occured while fetching data", e);
            } else {
                toaster.error(`An unexpected error occured ${e}`, 8000);
            }
        }
    }, [securityKey, dataCombo, recordId, setRetrievedData, setEditingData]);

    const updateData = useCallback(async () => {
        try {
            const fullSource = JSON.parse(editingData);
            const result = await apiCall("/kyc/record_correction/update-data", {
                ...(getDataEndpoint(dataCombo as any)),
                token: securityKey,
                vendor,
                description,
                correctionRequestTicket: socureTicket,
                vendorCorrectionRequestTicket: vendorTicket,
                socureId: (socureId ? socureId : undefined),
                update: {
                    clusterId: recordId,
                    ssnDeceased: (updateSsnDeceased ? (ssnDeceased ? "1" : "0") : undefined),
                    cidDeceased: (updateCidDeceased ? (cidDeceased ? "1" : "0") : undefined),
                    fullSource: (updateFullSource ? fullSource : undefined)
                }
            });
            if (result.status === 200 && result.data.status === "ok" && result.data.data.endsWith("Success")) {
                toaster.success(`Successfully updated record. ${result.data.data}`, 5000)
                getData()
            } else {
                toaster.warning("Unable to update record", 8000);
                console.warn("Unable to update record", result);
            }
        } catch (e) {
            if (isAxiosError(e)) {
                toaster.error(getErrorMessage(e), 8000);
                console.error("An api error occured while fetching data", e);
            } else {
                toaster.error(`An unexpected error occured ${e}`, 8000);
            }
        }
    }, [
        vendor,
        description,
        socureTicket,
        vendorTicket,
        ssnDeceased,
        cidDeceased,
        updateCidDeceased,
        updateSsnDeceased,
        updateFullSource,
        securityKey,
        dataCombo,
        recordId,
        editingData,
        socureId,
        getData
    ]);

    const refreshApi = useCallback(async () => {
        try {
            const result = await apiCall("/kyc/record_correction/configuration")
            if (result.status === 200) {
                const { data } = result;
                const vendors = Object.keys(data);
                const vendorDataComboMap = data;
                vendors.forEach(v => {
                    vendorDataComboMap[v] = vendorDataComboMap[v].map((_: any) => `${_.region}/${_.host}/${_.index}/${_.dynamo}`)
                });
                setConfigData({
                    vendors,
                    vendorDataComboMap
                })
                setVendor(vendors[0]);
                setDataCombo(vendorDataComboMap[vendors[0]][0]);
            } else {
                toaster.error("The response status code was not 200!", 8000);
                console.log("The response status code was not 200!", result);
            }
        } catch (e: any) {
            if (isAxiosError(e)) {
                toaster.error(getErrorMessage(e), 8000);
                console.error("An api error occured while fetching data", e);
            } else {
                toaster.error(`An unexpected error occured ${e}`, 8000);
            }
        }
    }, [setDataCombo, setConfigData]);

    useEffect(() => {
        refreshApi();
    }, []);

    const renderSocureId = useMemo(() => {
        let result = null;
        if (socureId !== null) {
            result = <TextField size="small" fullWidth label={"Socure ID"} disabled value={socureId} />;
        }
        return <Grid item key="socureId" sm={5}>{result}</Grid>;
    }, [socureId]);

    const renderDescription = useMemo(() => {
        return <Grid item sm={5} key="description">
            <TextField
                label="Description"
                size="small"
                fullWidth
                disabled={retrievedData === null}
                value={description}
                onChange={e => setDescription(e.target.value)}
            />
        </Grid>
    }, [description, retrievedData, setDescription]);

    const renderSocureTicket = useMemo(() => {
        return <Grid sm={2.5} item key="socureTicket">
            <TextField
                label="Socure Ticket"
                size="small"
                disabled={retrievedData === null}
                fullWidth
                value={socureTicket}
                onChange={e => setSocureTicket(e.target.value)}
            />
        </Grid>
    }, [socureTicket, retrievedData, setSocureTicket]);

    const renderVendorTicket = useMemo(() => {
        return <Grid sm={2.5} item key="vendorTicket">
            <TextField
                label="Vendor Ticket"
                size="small"
                disabled={retrievedData === null}
                fullWidth
                value={vendorTicket}
                onChange={e => setVendorTicket(e.target.value)}
            />
        </Grid>
    }, [vendorTicket, retrievedData, setVendorTicket]);

    const renderUpdateOptions = useMemo(() => {
        return <Grid container spacing={1} direction="column">
            <Grid item key="update-cid-deceased">
                <FormGroup>
                    <FormControlLabel
                        disabled={retrievedData === null}
                        label="Set CID Deceased"
                        control={<Checkbox checked={updateCidDeceased} onChange={e => setUpdateCidDeceased(e.target.checked)} />}
                    />
                </FormGroup>
            </Grid>
            {
                updateCidDeceased &&
                <Grid item key="cid-deceased">
                    <FormGroup>
                        <FormControlLabel
                            disabled={retrievedData === null}
                            label="CID Deceased"
                            control={<Checkbox checked={cidDeceased} onChange={e => setCidDeceased(e.target.checked)} />}
                        />
                    </FormGroup>
                </Grid>
            }
            <Grid item key="update-ssn-deceased">
                <FormGroup>
                    <FormControlLabel
                        disabled={retrievedData === null}
                        label="Set SSN Deceased"
                        control={<Checkbox checked={updateSsnDeceased} onChange={e => setUpdateSsnDeceased(e.target.checked)} />}
                    />
                </FormGroup>
            </Grid>
            {
                updateSsnDeceased &&
                <Grid item key="ssn-deceased">
                    <FormGroup>
                        <FormControlLabel
                            disabled={retrievedData === null}
                            label="SSN Deceased"
                            control={<Checkbox checked={ssnDeceased} onChange={e => setSsnDeceased(e.target.checked)} />}
                        />
                    </FormGroup>
                </Grid>
            }
            <Grid item key="Update Source">
                <FormGroup>
                    <FormControlLabel
                        disabled={retrievedData === null}
                        label="Update Source"
                        control={<Checkbox checked={updateFullSource} onChange={e => setUpdateFullSource(e.target.checked)} />}
                    />
                </FormGroup>
            </Grid>
        </Grid>;
    }, [
        updateCidDeceased,
        cidDeceased,
        updateSsnDeceased,
        ssnDeceased,
        updateFullSource,
        retrievedData,
        setUpdateCidDeceased,
        setCidDeceased,
        setUpdateSsnDeceased,
        setSsnDeceased,
        setUpdateFullSource
    ]);

    const renderSelectDataCombo = useMemo(() => {
        if (configData === null || vendor == null) {
            return null;
        }
        const vendorDataComboMap = configData.vendorDataComboMap;
        const dataCombos = vendorDataComboMap[vendor]
        return (
            <FormControl fullWidth>
                <InputLabel id="record-correction-select-data-combo-label">{"Data Combo"}</InputLabel>
                <Select
                    labelId="record-correction-select-data-combo-label"
                    value={dataCombo}
                    disabled={retrievedData !== null}
                    label="Data Combo"
                    size="small"
                    onChange={e => setDataCombo(e.target.value)}
                >
                    {dataCombos.map((_: any) => {
                        return <MenuItem value={_} key={_}>
                            {_}
                        </MenuItem>
                    })}
                </Select>
            </FormControl>
        );
    }, [dataCombo, configData, setDataCombo, retrievedData, vendor])

    const renderImportFromIdDialog = useMemo(() => {
        return <ImportFromIdContainer
            isOpen={idDialogOpen}
            onClose={() => setIdDialogOpen(false)}
            apply={applyId}
        />;
    }, [idDialogOpen, setIdDialogOpen, applyId]);

    const renderForm = useMemo(() => {
        if (configData === null) {
            return null;
        }
        return <React.Fragment>
            <Grid item sm={1.25} key="vendor">
                <FormControl fullWidth>
                    <InputLabel size="small" id="record-correction-select-vendor-label">{"Vendor"}</InputLabel>
                    <Select
                        labelId="record-correction-select-vendor-label"
                        value={vendor}
                        label="Vendor"
                        size="small"
                        disabled={retrievedData !== null || socureId !== null}
                        onChange={
                            e => {
                                const v: any = e.target.value;
                                setVendor(v);
                                setDataCombo(configData.vendorDataComboMap[v][0]);
                            }
                        }
                    >
                        {configData.vendors.map((_: any) => {
                            return <MenuItem value={_} key={_}>
                                {_}
                            </MenuItem>
                        })}
                    </Select>
                </FormControl>
            </Grid>
            <Grid item sm={9.25} key="index">
                {renderSelectDataCombo}
            </Grid>
            <Grid item key="security-key" sm={1.5}>
                <TextField disabled={retrievedData !== null} type="password" fullWidth size="small" label="Security Key" value={securityKey} onChange={setSecurityKey} />
            </Grid>
            <Grid item key="record-id" sm={2}>
                <TextField disabled={retrievedData !== null || socureId !== null} fullWidth size="small" label="Record ID" value={recordId} onChange={e => setRecordId(e.target.value)} />
            </Grid>
            {renderDescription}
            {renderSocureTicket}
            {renderVendorTicket}
        </React.Fragment>
    }, [
        configData,
        renderSelectDataCombo,
        recordId,
        socureId,
        vendor,
        securityKey,
        retrievedData,
        setVendor,
        setRecordId,
        setSecurityKey,
        setDataCombo,
        renderSocureTicket,
        renderVendorTicket,
        renderDescription
    ]);

    const renderEditingData = useMemo(() => {
        if (configData === null) {
            return null;
        }

        return <Grid container direction={"row"} spacing={1}>
            <Grid sm={2} item>
                {renderUpdateOptions}
            </Grid>
            <Grid sm={10} item key={"fullSource"}>
                {(
                    updateFullSource ?
                        <TextField
                            multiline
                            fullWidth
                            label="Full Source"
                            value={editingData}
                            size="small"
                            rows={21}
                            onChange={e => setEditingData(e.target.value)}
                        /> :
                        <Card style={{ height: "61vh", overflow: "scroll", padding: "1%" }} variant="outlined">
                            <Typography component={"pre"}>
                                {editingData}
                            </Typography>
                        </Card>
                )}
            </Grid>
        </Grid>;
    }, [editingData, updateFullSource, setEditingData, configData, renderUpdateOptions])

    const rendering = useMemo(() => (
        <React.Fragment>{renderImportFromIdDialog}
            <Grid direction="column" container spacing={1} key="Links">
                <Grid item key="form">
                    <Grid container direction="row" spacing={1}>
                        <Grid item sm={7} key="tools">
                            <ButtonGroup>
                                <Button
                                    onClick={
                                        () => {
                                            resetAllData();
                                            refreshApi();
                                        }
                                    }
                                    key="refresh"
                                    variant="contained"
                                    color="warning"
                                    startIcon={<RefreshIcon />}
                                >
                                    {"Refresh"}
                                </Button>
                                <Button
                                    disabled={recordId === "" || securityKey === ""}
                                    onClick={getData}
                                    key="retrieveData"
                                    variant="contained"
                                    color="primary"
                                    startIcon={<GetAppIcon />}
                                >
                                    {"Get Data"}
                                </Button>
                                <Button
                                    disabled={retrievedData === null}
                                    onClick={resetEditingData}
                                    key="clear-editings"
                                    variant="contained"
                                    color="error"
                                    startIcon={<ClearIcon />}
                                >
                                    {"Clear Changes"}
                                </Button>
                                <Button
                                    disabled={retrievedData === null && socureId === null}
                                    onClick={resetAllData}
                                    key="clear-all"
                                    variant="contained"
                                    color="error"
                                    startIcon={<ClearIcon />}
                                >
                                    {"Clear All"}
                                </Button>
                                <Button
                                    disabled={retrievedData === null}
                                    onClick={updateData}
                                    key="update-record"
                                    variant="contained"
                                    color="success"
                                    startIcon={<SystemUpdateAltIcon />}
                                >
                                    {"Update Record"}
                                </Button>
                                <Button
                                    onClick={() => setIdDialogOpen(true)}
                                    key="open-id-dialog-button"
                                    variant="contained"
                                    color="secondary"
                                    startIcon={<CreditCardIcon />}
                                    disabled={retrievedData !== null}
                                >
                                    {"Get record from ID"}
                                </Button>
                            </ButtonGroup>
                        </Grid>
                        {renderSocureId}
                        {renderForm}
                    </Grid>
                </Grid>
                <Grid item key="editor">
                    {renderEditingData}
                </Grid>
            </Grid>
        </React.Fragment>
    ),
        [refreshApi, getData, socureId, renderForm, recordId, retrievedData, securityKey, renderEditingData, renderImportFromIdDialog, renderSocureId, resetEditingData, resetAllData, updateData, setIdDialogOpen]
    );

    return rendering;
}

export default RecordCorrection;
