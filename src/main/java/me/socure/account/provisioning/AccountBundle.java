package me.socure.account.provisioning;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccountBundle {
    String BundleReference;
    List<BundleProducts> bundleProducts;

    public AccountBundle() {

    }

    public AccountBundle(String bundleReference) {
        BundleReference = bundleReference;
    }

    public AccountBundle(String bundleReference, List<BundleProducts> bundleProducts) {
        BundleReference = bundleReference;
        this.bundleProducts = bundleProducts;
    }

    public String getBundleReference() {
        return BundleReference;
    }

    public void setBundleReference(String bundleReference) {
        BundleReference = bundleReference;
    }

    public List<BundleProducts> getBundleProducts() {
        return bundleProducts;
    }

    public void setBundleProducts(List<BundleProducts> bundleProducts) {
        this.bundleProducts = bundleProducts;
    }
}
