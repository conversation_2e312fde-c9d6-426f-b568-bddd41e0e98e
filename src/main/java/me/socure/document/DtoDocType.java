package me.socure.document;

import java.util.List;

public class DtoDocType {

        long id = 0;
        String publicId;
        String name;
        String description;
        String category;
        String subCategory;
        List<String> tags;

        public DtoDocType setId(Long id) {
            if (id != null) {
                this.id = id;
            }
            return this;
        }

        public long getId() {
            return this.id;
        }

        public DtoDocType setPublicId(String publicId) {
            this.publicId = publicId;
            return this;
        }

        public DtoDocType setName(String name) {
            this.name = name;
            return this;
        }

        public DtoDocType setDescription(String description) {
            this.description = description;
            return this;
        }

        public DtoDocType setCategory(String category) {
            this.category = category;
            return this;
         }

         public DtoDocType setSubcategory(String subCategory) {
            this.subCategory = subCategory;
            return this;
         }

         public DtoDocType setTags(List<String> tags) {
             this.tags = tags;
             return this;
         }

         public String getPublicId() {
            return this.publicId;
         }

         public String getName() {
            return this.name;
         }

         public String getDescription() {
            return this.description;
         }

         public String getCategory() {
            return this.category;
         }

         public String getSubCategory() {
            return this.subCategory;
         }

         public List<String> getTags() {
            return tags;
         }

}
