declare global {
  interface Window {
    app: {
      host: string;
      loginUrl: string;
      apiHost: string;
      locale: string;
      modelManagement: boolean;
      defaultModel: boolean;
    };
    loggedinUsername: string;
    bucketurl: string;
  }

  const app: {
    host: string;
    loginUrl: string;
    apiHost: string;
    locale: string;
    modelManagement: boolean;
    defaultModel: boolean;
  };
  const loggedinUsername: string;
  const bucketurl: string;
}

export {};
