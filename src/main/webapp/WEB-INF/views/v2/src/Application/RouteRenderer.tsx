import React, { <PERSON> } from "react";
import { Grid, Typography } from "@mui/material";

export interface IRouteRendererProps {
    title: string;
    Component: FC;
}

const RouteRenderer: FC<IRouteRendererProps> = ({ title, Component }: IRouteRendererProps) => {
    return <Grid container spacing={1} direction="column">
        <Grid textAlign="center" item key="title">
            <Typography variant="h4">{title}</Typography>
        </Grid>
        <Grid item key="body">
            <Component />
        </Grid>
    </Grid>
};

export default RouteRenderer;