export const listResponse = `{
  "status": "Ok",
  "data": [
      "batch",
      "batch1"
  ],
  "msg": "Config List Fetched Successfully"
}`;

export const uploadResponse = `{
  "status": "Ok",
  "msg": "Config Updated Successfully for batch"
}`;

export const downloadResponse = '{"status":"Ok","data":"{\\"name\\":\\"batchjob\\",\\"flags\\":[{\\"name\\":\\"enableWSForBatchJobService\\",\\"allEnv\\":{\\"active\\":\\"true\\",\\"defaultConfigs\\":{\\"configs\\":[{\\"name\\":\\"throttlePercent\\",\\"value\\":\\"75\\"}]}}}]}","msg":"Config Fetched Successfully"}';
export const downloadResponse2 = '{"status":"Ok","data":"{\\"name\\":\\"batchjob2\\",\\"flags\\":[{\\"name\\":\\"enableWSForBatchJobService\\",\\"allEnv\\":{\\"active\\":\\"true\\",\\"defaultConfigs\\":{\\"configs\\":[{\\"name\\":\\"throttlePercent\\",\\"value\\":\\"75\\"}]}}}]}","msg":"Config Fetched Successfully"}';
