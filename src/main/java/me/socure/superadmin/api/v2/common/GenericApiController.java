package me.socure.superadmin.api.v2.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;

public abstract class GenericApiController {

    @Autowired
    @Qualifier("sslNoVerifySpringRestTemplate")
    private RestTemplate restTemplate = null;

    protected ResponseEntity<String> callRestApi(
            final String URL,
            final HttpMethod httpMethod,
            final String body,
            final HttpHeaders httpHeaders
    ) {
        RequestEntity<String> request = new RequestEntity<String>(body, httpHeaders, httpMethod, URI.create(URL));
        try {
            return restTemplate.exchange(request, String.class);
        } catch (final HttpClientErrorException e) {
            return new ResponseEntity<String>(e.getResponseBodyAsString(), e.getResponseHeaders(), e.getRawStatusCode());
        }
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ErrorResponse handleError(Exception e) {
        return ErrorResponse.builder().message(e.getMessage()).exceptionClass(e.getClass().toString()).build();
    }
}
