import React, { useCallback } from "react";
import Button from "./Button";

const EachListItem = ({ name, onClick }) => {
  return <div className="socure-config-item" onClick={onClick}>{name}</div>
}

const Leftbar = ({ list, onAddNewConfig, onSelect }) => {
  const onMenuClick = useCallback((name) => () => {
    onSelect && onSelect(name);
  }, [ onSelect ])
  return <div className="left-bar">
    <Button text='Add New Config' type='primary' onClick={onAddNewConfig} />
    <div className="mt-32">
      {list && list.map((val, idx) =>
        <EachListItem name={val} key={idx} onClick={onMenuClick(val)} />
      )}
    </div>
  </div>
}

export default Leftbar;