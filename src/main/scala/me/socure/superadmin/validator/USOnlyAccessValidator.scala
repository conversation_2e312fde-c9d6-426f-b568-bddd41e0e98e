package me.socure.superadmin.validator

import com.socure.common.struct.Countries
import me.socure.model.BusinessUserRoles
import me.socure.model.account.AccountInfoWithPermission
import me.socure.superadmin.authentication.SamlAttribute
import me.socure.superadmin.provider.SamlPayloadProvider

object USOnlyAccessValidator {

  def canEditAccountInfo(accountInfoWithPermission: AccountInfoWithPermission): Boolean = canEditAccountInfo(SamlPayloadProvider.provide(), accountInfoWithPermission)

  def canEditAccountInfo(attribute: SamlAttribute, accountInfoWithPermission: AccountInfoWithPermission): Boolean = {

    val usOnlAccessAccount: Option[Int] = accountInfoWithPermission.roles.find(_ == BusinessUserRoles.US_ONLY_ACCESS.id)
    val internal: Boolean = accountInfoWithPermission.isInternal

    if (internal) true else usOnlAccessAccount match {
      case Some(_) =>
        attribute.country.code2 == Countries.UNITED_STATES.code2
      case None => true
    }

  }

}
