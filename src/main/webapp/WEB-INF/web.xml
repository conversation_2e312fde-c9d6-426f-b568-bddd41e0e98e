<?xml version="1.0" encoding="UTF-8"?>
<!-- <web-app id="WebApp_ID" version="2.5" xmlns="http://java.sun.com/xml/ns/j2ee" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee 
	http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd"> -->
<web-app id="WebApp_ID" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLon="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	version="2.5" xmlns="http://java.sun.com/xml/ns/javaee">
	<display-name>Spring Security Tutorial</display-name>

	<listener>
		<listener-class>me.socure.dns.DnsCacheTtlSetter</listener-class>
	</listener>

	<filter>
		<filter-name>characterEncodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter
		</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>

	<filter-mapping>
		<filter-name>characterEncodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>springSecurityFilterChain</filter-name>
		<filter-class>org.springframework.web.filter.DelegatingFilterProxy
		</filter-class>
	</filter>

	<filter-mapping>
		<filter-name>springSecurityFilterChain</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath:/spring/spring-security.xml
			classpath:spring/spring-config.xml
		</param-value>
	</context-param>

	<context-param>
		<param-name>log4jConfigLocation</param-name>
		<param-value>classpath:${PARAM1}.logback.groovy</param-value>
	</context-param>
	
	<context-param>
		<param-name>webAppRootKey</param-name>
		<param-value>/socure-super-admin</param-value>
	</context-param>

	<filter>
		<filter-name>jsonpCallbackFilter</filter-name>
		<filter-class>me.socure.json.config.JsonpCallbackFilter
		</filter-class>
	</filter>

	<filter-mapping>
		<filter-name>jsonpCallbackFilter</filter-name>
		<url-pattern>*.json</url-pattern>
		<url-pattern>*.jsonp</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>cors</filter-name>
		<filter-class>me.socure.filter.CorsFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>cors</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!--<filter-mapping>
		<filter-name>superAdminAuditFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>-->
	<filter-mapping>
		<filter-name>superAdminMetricsFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<!--<filter>
		<filter-name>superAdminAuditFilter</filter-name>
		<filter-class>me.socure.filter.SuperAdminAuditFilter</filter-class>
	</filter>-->
	<filter>
		<filter-name>superAdminMetricsFilter</filter-name>
		<filter-class>me.socure.filter.SuperAdminMetricsFilter</filter-class>
	</filter>

	<listener>
		<listener-class>me.socure.util.log.Log4jConfigListener
		</listener-class>
	</listener>

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener
		</listener-class>
	</listener>

	<servlet>
		<servlet-name>spring</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet
		</servlet-class>
		<init-param>
			<param-name>dispatchOptionsRequest</param-name>
			<param-value>true</param-value>
		</init-param>
	</servlet>

	<servlet-mapping>
		<servlet-name>spring</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>

	<servlet>
		<servlet-name>Faces Servlet</servlet-name>
		<servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>

	<servlet-mapping>
		<servlet-name>Faces Servlet</servlet-name>
		<url-pattern>*.xhtml</url-pattern>
	</servlet-mapping>

	<error-page>
		<exception-type>java.lang.Exception</exception-type>
		<location>/error/handler/500</location>
	</error-page>

	<!-- Fix it -->
	<error-page>
		<error-code>404</error-code>
		<location>/WEB-INF/views/error.xhtml</location>
	</error-page>

	<error-page>
		<error-code>401</error-code>
		<location>/WEB-INF/views/error.xhtml</location>
	</error-page>


	<session-config>
		<cookie-config>
			<path>/</path>
			<http-only>true</http-only>
			<secure>true</secure>
		</cookie-config>
		<session-timeout>30</session-timeout>
	</session-config>

</web-app>

