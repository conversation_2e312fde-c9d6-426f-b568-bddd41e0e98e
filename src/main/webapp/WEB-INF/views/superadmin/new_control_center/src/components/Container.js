import React, { useCallback, useEffect, useState } from 'react'
import Leftbar from './Leftbar';
import ConfigEditor from './ConfigEditor';

const Container = ({ list, onSelect, selectedConfig, onCancel, onSave }) => {
  const [ currentConfig, setCurrentConfig ] = useState(null);

  const handleAddNewConfig = useCallback(() => {
    setCurrentConfig({})
  }, [])

  useEffect(() => {
    setCurrentConfig(selectedConfig);
  }, [ selectedConfig ])

  const onConfigSave = useCallback((config) => {
    if(!config.name) {
      alert('Inalid config name! Please fix it');
      return;
    }

    onSave && onSave(config);
  }, [ onSave ])

  return <div className='page-container'>
    <Leftbar onAddNewConfig={handleAddNewConfig} list={list} onSelect={onSelect} />
    {currentConfig && <ConfigEditor
      config={currentConfig}
      onCancel={onCancel}
      onSave={onConfigSave} /> }
  </div>
}

export default Container;