<?xml version="1.0" encoding="UTF-8"?>
<beans:beans xmlns="http://www.springframework.org/schema/security"
	xmlns:beans="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
					http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                    http://www.springframework.org/schema/security
                    http://www.springframework.org/schema/security/spring-security-5.8.xsd">

	<global-method-security pre-post-annotations="enabled"
		jsr250-annotations="enabled" />

	<http pattern="/resources" security="none" />

	<http use-expressions="true" auto-config="false"
		disable-url-rewriting="true" create-session="always">
		<headers>
			<hsts include-subdomains="true" max-age-seconds="31536000" />
			<frame-options policy="SAMEORIGIN"/>
		</headers>
		<csrf disabled="true"/>
		<intercept-url pattern="/login" access="permitAll" />
		<intercept-url pattern="/login_init" access="permitAll" />
		<intercept-url pattern="/logout" access="permitAll" />
		<intercept-url pattern="/register" access="permitAll" />
		<intercept-url pattern="/signin/**" access="permitAll" />
		<intercept-url pattern="/api/1/contact/**" access="permitAll" />
		<intercept-url pattern="/api/1/forgot_password" access="permitAll" />
		<intercept-url pattern="/api/1/activate" access="permitAll" />
		<intercept-url pattern="/recaptcha" access="permitAll" />
		<intercept-url pattern="/superadmin/application/**" access="permitAll" />

		<intercept-url pattern="/admin**" access="hasRole('ROLE_ADMIN')" />

		<intercept-url pattern="/delegated_adminapi/1/**"
			access="hasAnyRole('ROLE_ADMIN','ROLE_SUPER_ADMIN')" />


		<intercept-url pattern="/user**" access="hasRole('ROLE_USER')" />
		<intercept-url pattern="/adminapi/1/inactive_users"
			access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/adminapi/1/send_activation"
			access="hasRole('ROLE_SUPER_ADMIN')" />
			
		<intercept-url pattern="/api/1/industry/**"
			access="hasRole('ROLE_SUPER_ADMIN')" />

		<intercept-url pattern="/inactive_users" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/test_endpoints" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/send_activation" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/activate" access="hasRole('ROLE_SUPER_ADMIN')" />

		<intercept-url pattern="/active_users" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/delete" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/add_details_permission" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/remove_details_permission" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/api/1/fraudmodel/**" access="hasRole('ROLE_SUPER_ADMIN')"/>
		<intercept-url pattern="/fraud_model/mapping/**" access="hasRole('ROLE_SUPER_ADMIN')"/>
		<intercept-url pattern="/fraudmodel" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/defaultmodel" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/fraudmapping" access="hasRole('ROLE_SUPER_ADMIN') and false" />
		<intercept-url pattern="/account_manager" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/adminapi/1/parent_accounts" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/pgp_signature_public_keys" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/saml2_metadata" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/idp_metadata" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/pgp_keys" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/account_details" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/superadmin/1/activate/**"
			access="permitAll" />
		<intercept-url pattern="/superadmin/**" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/json-editor" access="hasRole('ROLE_SUPER_ADMIN')" />
		<intercept-url pattern="/sftp/**" access="hasRole('ROLE_SUPER_ADMIN')" />

		<intercept-url pattern="/**" access="permitAll" />

		<form-login login-page="/" authentication-failure-url="/login/failure"
			default-target-url="/login_success" />

		<logout delete-cookies="true" logout-url="/logout"
			logout-success-url="${marketing.url}" invalidate-session="true" />

		<access-denied-handler error-page="/denied" />
	<!--	<custom-filter position="FIRST" ref="customLogoutFilter" />
		<custom-filter after="LOGOUT_FILTER" ref="logoutFilter" />-->

	</http>

    <beans:bean id="tokenRepository"
        class="org.springframework.security.web.authentication.rememberme.JdbcTokenRepositoryImpl">
        <beans:property name="createTableOnStartup" value="false" />
        <beans:property name="dataSource" ref="dataSource" />
    </beans:bean>


	<!--<beans:bean id="saltSource"
		class="org.springframework.security.authentication.dao.ReflectionSaltSource">
		<beans:property name="userPropertyToUse" value="id" />
	</beans:bean>-->

	<authentication-manager alias="authenticationManager">
		<authentication-provider ref="simpleUserDetailsAuthenticationProvider" />
	</authentication-manager>

	<!--<beans:bean id="passwordEncoder"
		class="org.springframework.security.authentication.encoding.Md5PasswordEncoder" />
-->
	<beans:bean id="logoutFilter"
		class="org.springframework.security.web.authentication.logout.LogoutFilter">
		<beans:constructor-arg index="0"
			value="/common/authentication/logout" />
		<beans:constructor-arg index="1">
			<beans:list>
				<beans:ref bean="securityContextLogoutHandler" />
			</beans:list>
		</beans:constructor-arg>
	</beans:bean>

	<beans:bean id="securityContextLogoutHandler"
		class="org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler" />
<!--	<beans:bean id="customLogoutFilter" class="me.socure.filter.CustomLogoutFilter" />
	<beans:bean id="auditFilter" class="me.socure.filter.SuperAdminAuditFilter" />-->
	<beans:bean id="metricsFilter" class="me.socure.filter.SuperAdminMetricsFilter" />

	<beans:bean id="httpFirewall" class="org.springframework.security.web.firewall.DefaultHttpFirewall">
		<beans:property name="allowUrlEncodedSlash" value="true"/>
	</beans:bean>

	<http-firewall ref="httpFirewall"/>

</beans:beans>
