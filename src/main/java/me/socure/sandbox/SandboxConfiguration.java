package me.socure.sandbox;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SandboxConfiguration {

    private String configName;
    private String configVersion;
    private Long revision;
    private Boolean isLive;
    private String createdBy;
    private String markedLiveBy;
    private String markedLiveAt;

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getMarkedLiveAt() {
        return markedLiveAt;
    }

    public void setMarkedLiveAt(String markedLiveAt) {
        this.markedLiveAt = markedLiveAt;
    }

    public String getMarkedLiveBy() {
        return markedLiveBy;
    }

    public void setMarkedLiveBy(String markedLiveBy) {
        this.markedLiveBy = markedLiveBy;
    }



    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigVersion() {
        return configVersion;
    }

    public void setConfigVersion(String configVersion) {
        this.configVersion = configVersion;
    }

    public Long getRevision() {
        return revision;
    }

    public void setRevision(Long revision) {
        this.revision = revision;
    }

    public Boolean getLive() {
        return isLive;
    }

    public void setLive(Boolean live) {
        isLive = live;
    }
}
