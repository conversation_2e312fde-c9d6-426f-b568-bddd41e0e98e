<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
    <link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
    <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
    <script src="resources/scripts/jquery.min.js"></script>
    <script src="resources/scripts/bootstrap.min.js"></script>
    <script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
    <script type="text/javascript" src="resources/scripts/controlcenter.js"></script>
    <script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
         /* flag to manage page access type
           Full Access
           View Only
           No Access
        */
        canAccessControlCentreFlag = "#{canAccessControlCentre}";
        appenv = "#{bucket.appEnv}";
    </script>

    <style>
        /* Add styles to the form container */
        .form-container {
          padding: 10px;
        }

        /* Full-width input fields */
        .form-container input[type=text], .form-container select {
          width: 100%;
          padding: 5px;
          margin: 5px 0 22px 0;
          border: none;
          background: #f1f1f1;
        }

        /* When the inputs get focus, do something */
        .form-container input[type=text]:focus, .form-container select:focus {
          background-color: #ddd;
          outline: none;
        }

        /* Add a red background color to the cancel button */
        .form-container .cancel {
          background-color: #555;
        }

        /* Add some hover effects to buttons */
        .form-container .btn:hover, .open-button:hover {
          opacity: 1;
        }

        .btn.disabled, .btn.disabled:hover {
          opacity: .4!important;
        }

    </style>

    <meta charset="UTF-8"/>
</h:head>
<h:body>

    <iframe width="1" height="1" frameborder="0" src="" id="downloadHelper" style="display:none;"></iframe>

    <div align="center">
        <font color="blue"><h3>
            <h:outputText value="#{param['message']}" />
        </h3> </font>
    </div>
    <br/>
    <br/>

    <div align="center" rendered="#{canAccessControlCentreFlag == 'No Access'}">You do not have access to this page</div>
    <div align="right" rendered="#{canAccessControlCentreFlag != 'No Access'}">
    <div align="right">
        <h5>
            <h:outputLink value="industry">Manage Industry</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="active_users">Active Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="inactive_users">Inactive Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="logout">Logout</h:outputLink>
            &nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
    </div>
    <br/>
    <br/>
    <center><h4>Control Center for ID+ Features</h4></center>
    
    <hr/>
    <center><a id="downloadSettings" href="superadmin/control_center/settings/download">Download existing settings</a></center>
    <br/>
    <form id="settingsUploadForm"  align="center" enctype='multipart/form-data' class="form-container">
        <label for="file"><b>Settings File:</b></label>
        <input type="file" accept=".json" name="file"/>
        <br/><br/>
        <button type="button" id="settingsUploadBtn" class="btn btn-primary" onclick="uploadControlCenterSettings()" align="center">Upload all settings</button>
        <br/>
        <div id="message" style="display:none"></div>
    </form>
    <hr/>

    <center>
        <div>
            <form id="controlCenterForm" action="" class="form-container">
                <table id="settingsTable" align="center" cellpadding="0" cellspacing="0" border="0" class="table table-striped table-bordered dataTable" style="width: 40%;">
                    <thead>
                        <th><b>Feature</b></th>
                        <th><b>Description</b></th>
                        <th style="width:150px"><b>Value</b></th>
                    </thead>
                    <tbody id="settingsTableBody">
                    </tbody>
                </table>
                <br />
                <div id="message" style="display:none"></div>
            </form>
        </div>
    </center>
    </div>
</h:body>
</html>
