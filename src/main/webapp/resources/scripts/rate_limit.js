
var apiMap = {};
var type ;
var rate='rate';
var day='day';
var defaultRate = 10;
var defaultLimit = 'N/A';
var editType = 'edit';
var addType ='add';

function getWindowsInMills(selectedVal){
    if (selectedVal === 'rate'){
        return 1000
    } else if(selectedVal === 'day') {
        return 24 * 60 * 60 * 1000
    }
}

addNewRateLimit = function(myform) {
$("#AddDelegatedAdminModal").modal("hide");
	var url = 'superadmin/rate-limiter/v1/limits';
	var form = null;
	if (myform == null) {
		form = document.getElementById('delegated_admin1');
	} else {
		form = myform;
	}

    var lim =  $('#limit').val();
    if(type === 'rate')
        lim = $('#rate').val();

	$.ajax(
			url,
			{
				type : 'POST',
				contentType: "application/json",
				data :  JSON.stringify({
                    accountId : accountId,
                    environmentTypeId : $('#env_list option:selected').val(),
                    api : $('#publicApi option:selected').val(),
                    windowInMillis :getWindowsInMills(type),
                    limit : lim
				}) ,
				beforeSend: function() {
                    $('#Loading').show();
                },
                complete: function() {
                    $('#Loading').hide();
                },
				error: function(e) {
                    alert('Unable to save configurations!');
                    $("#AddDelegatedAdminModal").modal("hide");
                },
				success : function(data) {
				    loadRateLimitingPage(accountId);
					$("#AddDelegatedAdminModal").modal("hide");
				}
			});
};


loadRateLimitApis = function(){

    $.ajax({
        url: 'superadmin/rate-limiter/v1/apis',
        type: 'GET',
        error: function(e) {
            alert('Unable to fetch apis. Please refresh the Page!')
        },
        success: function(data) {
            var select = document.getElementById("publicApi");
                for (index in data) {
                    var key ="";
                    if(data[index].operation)
                        key = data[index].method + '  ' + data[index].name + '  ' + data[index].operation ;
                    else
                        key =  data[index].method + '  ' + data[index].name;

                    var option = new Option(key, data[index].publicId)
                    apiMap[data[index].publicId] = key
                    select.add(option);
                }
            }
        });
}

function getWindowTypeValue(winInMills){
    if(winInMills === "1000"){
        return 'sec';
    }
    else if(winInMills === "3600000"){
        return 'hour';
    }
    else if(winInMills === "********"){
        return 'day'
    }
}


function showEditRateLimitDialog(accountId, envTypeId , api , windowInMillis , limit , t , reqType){

    $("#publicApi").val(api);
    $("#env_list").val(envTypeId);
    type = t;

    if(t === rate){
        $("#rate").val(limit);
        $('#rate-div').show();
        $('#limit-div').hide();
    }
    else if(t === day){
        $("#limit").val(limit);
        $('#rate-div').hide();
        $('#limit-div').show();
    }

    $("#publicApi").prop("disabled",true);
    $('#env_list').prop("disabled",true);

    if(reqType == 'edit'){
        $("#edit").show();
        $("#add").hide();
    } else {
        $("#add").show();
        $("#edit").hide();
    }

    $('#AddDelegatedAdminModal').modal('show');
}

function editRateLimit(myform){
$("#AddDelegatedAdminModal").modal("hide");

var url = 'superadmin/rate-limiter/v1/limits';
	var form = null;
	if (myform == null) {
		form = document.getElementById('delegated_admin1');
	} else {
		form = myform;
	}

    var lim =  $('#limit').val();
    if(type === 'rate'){
        lim = $('#rate').val();
    }

    if(lim === defaultLimit){
        return;
    }

	$.ajax(
			url,
			{
				type : 'PUT',
				contentType: "application/json",
				data :  JSON.stringify({
                    accountId : accountId,
                    environmentTypeId : $('#env_list option:selected').val(),
                    api : $('#publicApi option:selected').val(),
                    windowInMillis : getWindowsInMills(type),
                    limit : lim,
				}) ,
				beforeSend: function() {
                    $('#Loading').show();
                },
                complete: function() {
                    $('#Loading').hide();
                },
				error: function(e) {
                    alert('Unable to save configurations!');
                    $("#AddDelegatedAdminModal").modal("hide");
                },
				success : function(data) {
				    loadRateLimitingPage(accountId);
					$("#AddDelegatedAdminModal").modal("hide");
				}
			});

}

function deleteLimit(accountId , envTypeId , publicApi , window){

        var url = 'superadmin/rate-limiter/v1/limits';
    	$.ajax(
    			url,
    			{
    				type : 'DELETE',
    				contentType: "application/json",
    				data :  JSON.stringify({
                        accountId : accountId,
                        environmentTypeId : envTypeId,
                        api : publicApi,
                        windowInMillis : window
    				}) ,
    				beforeSend: function() {
                        $('#Loading').show();
                    },
                    complete: function() {
                        $('#Loading').hide();
                    },
    				error: function(e) {
                        alert('Unable to delete configurations!');
                    },
    				success : function(data) {
    				    loadRateLimitingPage(accountId);
    				}
    			});

}

function showAddNewDialog(){
    type = 'rate';
     $('#AddDelegatedAdminModal').modal('show');
     $("#publicApi").prop("disabled",false);
     $("#publicApi").val($("#publicApi option:first").val());
     $('#rate').prop("disabled",false);
     $('#limit-div').hide();
     $('#rate-div').show();
     $('#env_list').prop("disabled",false);
     $("#env_list").val($("#env_list option:first").val());
     $('#rate').val('');
     $('#limit').val('');

     $("#edit").hide();
     $("#add").show();
}

function showAddNewLimitDialog(){
     type = 'day';
     $('#AddDelegatedAdminModal').modal('show');
     $("#publicApi").prop("disabled",false);
     $("#publicApi").val($("#publicApi option:first").val());
     $('#limit').prop("disabled",false);
     $('#rate').val('');
     $('#limit').val('');
     $('#rate-div').hide();
     $('#limit-div').show();
     $('#env_list').prop("disabled",false);
     $("#env_list").val($("#env_list option:first").val());
     $("#edit").hide();
     $("#add").show();
}

function getEnvironmentType(id){
    if(id === 1){
        return 'Production';
    }
    else if(id === 2) {
        return 'Certification';
    }
    else if(id === 3){
        return 'Sandbox';
    }
}

function loadRateLimitingPage(accountId){

    var dateFormat = 'yyyy-MM-dd HH:mm:ss';
	var url = 'superadmin/rate-limiter/v1/limits?accountId='+accountId;
	$.ajax(url,
			{
				type : 'GET',
				async : false,
				success : function(data) {
					var aaData = {};
					for (var i = 0; i < data.length; i++) {
						var rowData = data[i];
						var envType = rowData['environmentTypeId'];
                        var api = rowData['api'];
                        var window = rowData['windowInMillis'];
                        var limit = rowData['limit'];
                        var key = envType+"-"+api;

                        var arr = aaData[key];
                        if(!arr)
                            arr = [];

                       arr[0] = apiMap[api] ;
                       arr[1] = getEnvironmentType(envType);
                       if(!arr[2]){
                            arr[2] = defaultRate + '(default)  </br> <div style="display: inline-flex;"> <input type="button" class="btn btn-link color-orange" onClick="showEditRateLimitDialog(\'' + accountId + '\', \'' + envType + '\',  \'' + api + '\'  ,  \'' + window + '\'   ,  \'' + defaultRate+ '\'    ,  \'' + rate + '\' ,  \'' + addType + '\' )" value="Edit" /> </div> ';
                       }
                       if(!arr[3]){
                            arr[3] = defaultLimit + '</br> <div style="display: inline-flex;">  <input type="button" class="btn btn-link color-orange" onClick="showEditRateLimitDialog(\'' + accountId + '\', \'' + envType + '\',  \'' + api + '\'  ,  \'' + window + '\'   ,  \'' + defaultLimit + '\'   ,  \'' + day + '\'  ,  \'' + addType + '\' )" value="Edit" /> </div>';
                       }
                       if(window == 1000){
                            arr[2] =  limit + '</br><div style="display: inline-flex;">  <input  type="button" class="btn btn-link color-orange" onClick="showEditRateLimitDialog(\'' + accountId + '\', \'' + envType + '\',  \'' + api + '\'  ,  \'' + window + '\'   ,  \'' + limit + '\'    ,  \'' + rate + '\'  ,  \'' + editType + '\' )" value="Edit" /> '+
                                              						'  <input type="button" class="btn btn-link color-orange" onClick="deleteLimit(\'' + accountId + '\', \'' + envType + '\',  \'' + api + '\'  ,  \'' + window + '\'   )" value="Delete" /> </div>';
                       } else if (window === (24 * 60 * 60 * 1000)) {
                            arr[3] = limit + '</br><div style="display: inline-flex;">  <input type="button" class="btn btn-link color-orange" onClick="showEditRateLimitDialog(\'' + accountId + '\', \'' + envType + '\',  \'' + api + '\'  ,  \'' + window + '\'   ,  \'' + limit + '\'   ,  \'' + day + '\'  ,  \'' + editType + '\')" value="Edit" /> '+
                                             						'  <input  type="button" class="btn btn-link color-orange" onClick="deleteLimit(\'' + accountId + '\', \'' + envType + '\',  \'' + api + '\'  ,  \'' + window + '\'   )" value="Delete" /> </div>';
                       }

                        if(!arr[4] || arr[4] > rowData['createdAt'])
                            arr[4] = rowData['createdAt'];

                        if(!arr[5] || arr[5] < rowData['lastUpdatedAt'])
                            arr[5] = rowData['lastUpdatedAt'];

						aaData[key] = arr;
					}

					oTable = $('#tbl_rate_limiting')
						.dataTable(
							{
								"aaData" :  Object.values(aaData),
								"bProcessing" : true,
								"bDestroy" : true,
								"bAutoWidth" : true,
								"aoColumnDefs": [
                                    {
                                        "fnRender": function (oObj) {
                                            return jQuery.format.date(oObj.aData[4], dateFormat)
                                        },
                                        "aTargets": [4]
                                   },
                                   {
                                        "fnRender": function (oObj) {
                                            return jQuery.format.date(oObj.aData[5], dateFormat)
                                        },
                                        "aTargets": [5]
                                   }
                                ]
							});

				}
			});

}


$(document).ready(function(){
    loadRateLimitApis();
});

