<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>

    <script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
        appenv = "#{bucket.appEnv}";
    </script>

    <meta charset="UTF-8"/>

    <style>

        .page-margin {
          margin-top: 20px;
          margin-bottom: 20px;
          margin-left: 50px;
        }

        .prefix{
            margin-left: 300px;
        }

        .intent{
             margin-left: 100px;
        }

        .save-button{
            margin-left: 400px;
        }


    </style>
</h:head>
<h:body>
    <div id="addVendorConfig" class="page-margin">

        <h4>This Page is to define vendor prefix configuration values</h4>
        <h4>Note: The new selections overrides the existing one</h4>

        <div class="prefix">
            <div>
                <h4>Select Prefix : </h4>
                <span class="intent">
                    <select id="vendorPrefixSelect" onchange="selectVendorPrefix(this)">
                        <option>Select Prefix</option>
                    </select>
                </span>
            </div>

            <div id="vendorPrefixSelection">
                <h4> Current Configs : </h4>
                <div class="intent">
                    <span id="currentconfigs">

                    </span>
                </div>

                <h4> New Vendor Config : </h4>
                <div id="vendorConfigsTextBox" class="intent">


                </div>
                <br/>
                <button class="save-button" type="button" onclick="saveNewConfigs()">Save New Configs</button>
            </div>
        </div>

    </div>

</h:body>
</html>
