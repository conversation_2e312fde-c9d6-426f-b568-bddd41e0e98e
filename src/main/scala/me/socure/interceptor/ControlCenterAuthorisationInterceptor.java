package me.socure.interceptor;

import me.socure.common.servlet.HttpMethod;
import me.socure.control.center.constants.ControlCenterPermission;
import me.socure.superadmin.authentication.SimpleAuthenticatedToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class ControlCenterAuthorisationInterceptor implements HandlerInterceptor {
    private final Logger logger = LoggerFactory.getLogger(ControlCenterAuthorisationInterceptor.class);

   public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

       String currentMethod = request.getMethod();
       ControlCenterPermission userPermission = getPermissionInSession();

       if(!isReadMethod(currentMethod) && !isWriteMethod(currentMethod)) //If it is OPTIONS, HEAD etc.. not validating/blocking those.. just return true
           return true;

       if((isReadMethod(currentMethod)) && hasReadAccess(userPermission) || (isWriteMethod(currentMethod) && hasWriteAccess(userPermission))) {
            return true;
        }

        response.setStatus(403);
        return false;

    }

    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }

    ControlCenterPermission getPermissionInSession() {
        SimpleAuthenticatedToken token = (SimpleAuthenticatedToken)SecurityContextHolder.getContext().getAuthentication();
        ControlCenterPermission permissionInSession =  token.samlAttribute().controlCenterPermission();
        logger.info("Control center permission in session : {}", permissionInSession.name);
        return permissionInSession;
    }
    private boolean hasReadAccess(ControlCenterPermission userPermission) {
        return (userPermission == ControlCenterPermission.VIEW_ONLY || userPermission == ControlCenterPermission.FULL_ACCESS);
    }

    private boolean hasWriteAccess(ControlCenterPermission userPermission) {
        return (userPermission == ControlCenterPermission.FULL_ACCESS);
    }

    private boolean isWriteMethod(String method){
        return (method.equalsIgnoreCase(HttpMethod.POST.name()) || method.equalsIgnoreCase(HttpMethod.PUT.name())
                || method.equalsIgnoreCase(HttpMethod.PATCH.name()) ||method.equalsIgnoreCase(HttpMethod.DELETE.name())) ;
    }

    private boolean isReadMethod(String method){
        return (method.equalsIgnoreCase(HttpMethod.GET.name())) ;
    }

}
