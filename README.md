# Description

Super Admin service Microservice.

It contains management tools for Admin Dashboard. This is an internal service and should use VPN to access Super Admin service.

# Local Setup Instructions

[https://confluence.socure.com/display/ENG/Set+up+super+admin+locally](https://confluence.socure.com/display/ENG/Set+up+super+admin+locally)

### Login Locally

[http://localhost:8080/saml2/SSO?username=developer](http://localhost:8080/saml2/SSO?username=developer)

# Getting started

To get started, ensure you have cloned this repository and [idplus-service](https://gitlab-ee.us-east-vpc.socure.be/idp/idplus-service) locally. We will need import the `idplus-service/socure-commons-admin` module in IntelliJ to run the necessary backend.

1. If necessary, download JDK 8.261 from Socure Self Service or, if you do not currently have a Socure provisioned laptop, ask IT for the .pkg file.

2. Download maven with `brew install maven`

3. Create `~/.m2` if the folder does not exist. Get `settings.xml` from a teammate and place it in the folder.

4. Within `idplus-service/socure-commons-admin/src/main/scala/me/socure/action/audit/ActionAuditConfiguration.scala`, modify the following line from

```java
val queueUrl = sqsClient.createQueue(queueName).getQueueUrl
```

to

```java
val queueUrl = null
```

5. If the folder exists, delete `~/.m2/repository/me/socure/socure-commons-admin`, then run `mvn clean install -DskipTests -Dmaven.test.skip=true` to build `socure-commons-admin`.

If the above gives an unresolved host error or a `sun.security.provider.certpath.SunCertPathBuilderException`, refer to this [page](https://confluence.socure.com/pages/viewpage.action?spaceKey=IT&title=Palo+Alto+VPN+Instructions) and this [thread](https://socure-internal.slack.com/archives/C039Q3121JL/p1674254959022399?thread_ts=**********.697599&cid=C039Q3121JL) to add the necessary certificates into the JDK keystore. Also ensure that you are on VPN while running `mvn clean install`.

7. Set up IntelliJ. Open `socure-super-admin`. In `File > New > Module From Existing Sources...`, bring in `socure-commons-admin` from `idplus-service`.

In `File > Project Structure` ensure the following:

- Under `Project`, select `1.8` for SDK, and select `8` for Language Level.

- In `Modules`, make sure the language level is the same as the projects for both `socure-super-admin` and `socure-commons-admin`.

- Under `Platform Settings > SDKs`, select 1.8 and under `Platform Settings > Global Libraries`, add and/or select `scala-sdk-2.11.12`.

8. In `Preferences > Plugins`, download Smart Tomcat. Then go to `Run > Debug...` to open up `Run/Debug Configurations` to configure Tomcat using the following:

For tomcat server, download tomcat 9. Then select `Configure...` and add tomcat 9. Make sure it is selected in the config.

For deployment directory, it should look something like: `.../socure-super-admin/src/main/webapp`

For environmental variables, use the following:

```shell
CONFIGURATION_NAME=test;APP_ENVIRONMENT=test;FILE_STORAGE_HMAC_SECRET_KEY=test;SOCURE_HMAC_VPN_IP=***********; AWS_SECRET_ACCESS_KEY=[your secret access key here]
```

Note that you may not have to add AWS_SECRET_ACCESS_KEY if you already have a default profile configured in the AWS CLI. If you need to find the AWS variables, follow the "Ensure your AWS SSO credentials are up-to-date in your ~/.aws/credentials file" section on this [page](https://confluence.socure.com/pages/viewpage.action?pageId=234848416) to find the necessary values. If you come across a region missing issue, add 'us-east-1' as the region in your default config.

For the Extra JVM classpath, use `-Xbootclasspath/a:/Users/<USER>/.m2/repository/org/scala-lang/scala-library/2.11.12/scala-library-2.11.12.jar -Djava.net.preferIPv4Stack=true`

9. Save the config, then make the following changes in `socure-super-admin`:

Within `src/main/scala/me/socure/superadmin/authentication/saml/AuthenticationController.scala`, change the authenticate method:

```java
  @RequestMapping(value = Array("/SSO"), method = Array(RequestMethod.GET))
  def authenticate(request: HttpServletRequest, response: HttpServletResponse): String = {
    try {
      val username: String = ["YOUR_EMAIL_HERE"]
      val countryAttributeValueOpt: Option[String] = Some(Countries.UNITED_STATES.code2.toString)
      val countryValue: String = countryAttributeValueOpt match {
        case Some(countryCode2) => countryCode2
        case None =>
          logger.error(s"country attribute not found for $username")
          Countries.UNITED_STATES.code2
      }

      Countries.byCode2(countryValue) match {
        case Some(country) =>
          val samlAttribute: SamlAttribute = SamlAttribute(country, ControlCenterPermission.FULL_ACCESS, true, "true")

          val unauthenticatedToken: SimpleUnauthenticatedToken = SimpleUnauthenticatedToken(
            username = username,
            expirationTime = None,
            samlAttribute
          )
          val authentication: Authentication = authenticationManager.authenticate(unauthenticatedToken)
          SecurityContextHolder.getContext.setAuthentication(authentication)
          "redirect:/active_users"
        case None =>
          logger.error(s"Given country= $countryValue is Invalid")
          "redirect:/unauthorized"
      }

    } catch {
      case e: Throwable =>
        logger.error("Super-Admin SAML2 Authentication error", e)
        "redirect:/unauthorized"
    }
  }
```

Within `src/main/webapp/WEB-INF/web.xml`, ensure the following snippets of code are commented out:

```xml
<!-- <filter-mapping>-->
<!--     <filter-name>superAdminAuditFilter</filter-name>-->
<!--     <url-pattern>/*</url-pattern>-->
<!-- </filter-mapping>-->
```

```xml
<!-- <filter>-->
<!--     <filter-name>superAdminAuditFilter</filter-name>-->
<!--     <filter-class>me.socure.filter.SuperAdminAuditFilter</filter-class>-->
<!-- </filter>-->
```

Within `src/main/resources/spring/spring-security.xml`, make the following changes:

Comment out

```xml
<!--     <custom-filter position="FIRST" ref="customLogoutFilter" />-->
<!--     <custom-filter after="LOGOUT_FILTER" ref="logoutFilter" />-->

```

and

```xml
<!-- <beans:bean id="customLogoutFilter" class="me.socure.filter.CustomLogoutFilter" />-->
<!-- <beans:bean id="auditFilter" class="me.socure.filter.SuperAdminAuditFilter" />-->
```

9. Within IntelliJ, go to Preferences > Build, execution, deployment > Build Tools > Maven > Maven home path and use the brew installed version of Maven. This will help sync dependencies faster. Sync dependencies, then build the project.

10. Replace `.../socure-super-admin/src/main/resources/test.conf` with [this test.conf](https://gitlab-ee.us-east-vpc.socure.be/cmn/microservice-configurations/-/blob/main/superadmin-dashboard-v1/configurations/us-east-1/stage.conf) file from the `cmn/microservice-configurations` repo.

11. Make sure you have `~/.socure/.pbe/.password`. If not, request it from a teammate.

12. Run super-admin using the `Play` button next to the tomcat configuration dropdown.

13. Open super-admin locally at http://localhost:8080/socure-super-admin/saml2/SSO?username=developer.
