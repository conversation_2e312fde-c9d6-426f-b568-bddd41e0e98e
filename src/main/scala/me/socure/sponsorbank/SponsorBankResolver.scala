package me.socure.sponsorbank

import me.socure.account.sponsorbank.SponsorBankClient
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountIdName
import me.socure.model.sponsor.bank.{SponsorBankProgram, SponsorBankProgramLinkRequest}
import me.socure.util.EnvUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component

import java.util.concurrent.TimeUnit
import scala.concurrent.Await
import scala.concurrent.duration.Duration

@Component
class SponsorBankResolver {
  private val logger = LoggerFactory.getLogger(getClass)

  @Autowired
  @Qualifier("envUtil")
  var envUtil: EnvUtil = _

  @Autowired
  @Qualifier("sponsorBankClient")
  var sponsorBankClient: SponsorBankClient = _

  def getNonSponsorBankPrograms(): Either[String, Seq[AccountIdName]] = {
    Await.result(sponsorBankClient.getNonSponsorBankPrograms(), Duration.create(40, TimeUnit.SECONDS)) match {
      case Right(accounts) =>
        Right(accounts)
      case Left(errorResponse: ErrorResponse) =>
        logger.info(s"Error while fetching Non SponsorBank Programs", errorResponse)
        Left(errorResponse.message)
    }
  }

  def getSponsorBank(programId: Long): Either[String, AccountIdName] = {
    Await.result(sponsorBankClient.getSponsorBank(programId), Duration.create(40, TimeUnit.SECONDS)) match {
      case Right(response) =>
        Right(response)
      case Left(errorResponse: ErrorResponse) =>
        logger.info(s"Error while fetching Sponsor Bank for Program: $programId", errorResponse)
        Left(errorResponse.message)
    }
  }

  def getLinkedPrograms(sponsorBankId: Long): Either[String, Seq[SponsorBankProgram]] = {
    Await.result(sponsorBankClient.getLinkedPrograms(sponsorBankId), Duration.create(40, TimeUnit.SECONDS)) match {
      case Right(response) =>
        Right(response)
      case Left(errorResponse: ErrorResponse) =>
        logger.info(s"Error while fetching Linked Programs for the SponsorBank: $sponsorBankId", errorResponse)
        Left(errorResponse.message)
    }
  }

  def linkSponsorBankProgram(sponsorBankProgramLinkRequest: SponsorBankProgramLinkRequest): Either[String, Boolean] = {
    Await.result(sponsorBankClient.linkSponsorBankProgram(sponsorBankProgramLinkRequest), Duration.create(40, TimeUnit.SECONDS)) match {
      case Right(response) =>
        Right(response)
      case Left(errorResponse: ErrorResponse) =>
        logger.info(s"Error while adding a Program: ${sponsorBankProgramLinkRequest.programId} to the SponsorBank: ${sponsorBankProgramLinkRequest.sponsorBankId}", errorResponse)
        Left(errorResponse.message)
    }
  }

  def unlinkSponsorBankProgram(sponsorBankProgramLinkRequest: SponsorBankProgramLinkRequest): Either[String, Boolean] = {
    Await.result(sponsorBankClient.unlinkSponsorBankProgram(sponsorBankProgramLinkRequest), Duration.create(40, TimeUnit.SECONDS)) match {
      case Right(response) =>
        Right(response)
      case Left(errorResponse: ErrorResponse) =>
        logger.info(s"Error while removing a Program: ${sponsorBankProgramLinkRequest.programId} from the SponsorBank: ${sponsorBankProgramLinkRequest.sponsorBankId}", errorResponse)
        Left(errorResponse.message)
    }
  }
}