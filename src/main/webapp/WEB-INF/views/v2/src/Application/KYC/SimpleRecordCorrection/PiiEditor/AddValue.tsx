import React, { Fragment } from "react";
import { <PERSON>, Button, Grid, <PERSON><PERSON>, <PERSON><PERSON>ield, Typography } from "@mui/material";
import { Add as AddIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { toaster } from "../../../../Components/Toaster";

const modalStyle = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 800,
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
};

const regexCollection = {
    "BD": /^((19)|(20))[\d]{2}(((0[1-9])|(1[012]))((0[1-9])|(([1-2][0-9])|(3[0-1])))?)?$/,
    "SSN": /^(?!219099999|078051120)(?!666|000|9\d{2})\d{3}(?!00)\d{2}(?!0{4})\d{4}$/
}

class AddValue extends React.Component<any, any> {

    constructor(props: any) {
        super(props);
        this.state = {
            isOpen: false,
            one: "",
            two: "",
            three: "",
            four: "",
            metadata: []
        };
    }

    openModal = () => {
        this.setState({
            one: "",
            two: "",
            three: "",
            four: "",
            metadata: [],
            isOpen: true
        });
    }

    renderValue = () => {
        const { one } = this.state;
        const { field } = this.props;
        return <Grid sx={{ width: 500 }} item key="value">
            <TextField
                helperText={
                    field === "BD"?
                    "The date of birth must be entered in YYYY[MM[DD]] format without dashes. Square braces \"[]\" indicate optional part. Ignore optional part only if month and/or date is unknown. Valid formats include YYYYMMDD, YYYYMM, and YYYY."
                    :(
                        field === "SSN"?
                        "Enter a valid 9 digit SSN as specified by US Government."
                        :""
                    )
                }
                label={"Value"}
                value={one}
                size="small"
                onChange={e => this.setState({ one: e.target.value })}
                fullWidth
            />
        </Grid>
    }

    renderAddressValue = () => {
        const { one, two, three, four } = this.state;
        return <Grid item key="value">
            <Grid container direction="row" spacing={1}>
                <Grid item key="street" sm={5}>
                    <TextField
                        label={"Street"}
                        value={one}
                        size="small"
                        onChange={e => this.setState({ one: e.target.value })}
                        fullWidth
                    />
                </Grid>
                <Grid item key="city" sm={3}>
                    <TextField
                        label={"City"}
                        value={two}
                        size="small"
                        onChange={e => this.setState({ two: e.target.value })}
                        fullWidth
                    />
                </Grid>
                <Grid item key="zip" sm={2}>
                    <TextField
                        label={"Zip"}
                        value={three}
                        size="small"
                        onChange={e => this.setState({ three: e.target.value })}
                        fullWidth
                    />
                </Grid>
                <Grid item key="State" sm={2}>
                    <TextField
                        label={"State"}
                        value={four}
                        size="small"
                        onChange={e => this.setState({ four: e.target.value })}
                        fullWidth
                    />
                </Grid>
            </Grid>
        </Grid>;
    }

    addMetaData = () => {
        const { metadata } = this.state;
        metadata.push({ key: "", value: "" });
        this.setState({ metadata });
    }

    editMetaData = (idx: number, data: string, value: string) => {
        const { metadata } = this.state;
        metadata[idx][data] = value;
        this.setState({ metadata });
    }

    deleteMetaDataRow = (idx: number) => {
        const { metadata } = this.state;
        metadata.splice(idx, 1)
        this.setState({ metadata });
    }

    finish = () => {
        const { field, addData } = this.props;
        const { one, two, three, four, metadata } = this.state;
        const metaData: any = {};
        metadata.filter((_: any) => _.key.trim() !== "" && _.value.trim() !== "")
            .forEach((_: any) => metaData[_.key.trim()] = _.value.trim());
        if (field === "A" && one.trim() !== "" && two.trim() !== "" && three.trim() !== "" && four.trim() !== "") {
            addData({
                AX: one.trim().toLowerCase(),
                AY: two.trim().toLowerCase(),
                AZ: three.trim().toLowerCase(),
                AS: four.trim().toLowerCase(),
                metaData
            });
            this.setState({ isOpen: false });
        } else if((regexCollection as any)[field] !== undefined && (regexCollection as any)[field].exec(one) === null) {
            toaster.error("Invalid data. Please enter the data in valid format!");
        } else if (one.trim() !== "") {
            addData({
                metaData,
                cleanedValue: one.trim().toLowerCase()
            });
            this.setState({ isOpen: false });
        } else {
            toaster.error("Please fill the necessary values to complete!");
        }
    }

    renderModal = () => {
        const { isOpen, metadata } = this.state;
        const { field } = this.props;
        if (isOpen) {
            return <Modal
                open={true}
                onClose={() => this.setState({ isOpen: false })}
            >
                <Box sx={modalStyle}>
                    <Typography variant="h6" component="h2">
                        {"Add Value"}
                    </Typography>
                    <Grid container spacing={1} direction="column">
                        {
                            field === "A" ?
                                this.renderAddressValue() :
                                this.renderValue()
                        }
                        <Grid item key="metadata-title">
                            <Typography variant="h6" component="h2">
                                {"Metadata  "}
                                <Button onClick={this.addMetaData}>{"ADD"}</Button>
                            </Typography>
                        </Grid>
                        {metadata.map(
                            (_: any, i: number) => <Grid item key={`meta-${i}`}>
                                <Grid container direction="row" spacing={1}>
                                    <Grid item sm={4} key="key">
                                        <TextField
                                            label="Key"
                                            value={metadata[i]["key"]}
                                            onChange={e => this.editMetaData(i, "key", e.target.value)}
                                            fullWidth
                                            size="small"
                                        />
                                    </Grid>
                                    <Grid item sm={4} key="value">
                                        <TextField
                                            label="Value"
                                            value={metadata[i]["value"]}
                                            onChange={e => this.editMetaData(i, "value", e.target.value)}
                                            fullWidth
                                            size="small"
                                        />
                                    </Grid>
                                    <Grid item key="delete">
                                        <Button color="error" onClick={e => this.deleteMetaDataRow(i)}>
                                            <DeleteIcon />
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Grid>
                        )}
                        <Grid item key="buttons">
                            <Grid container direction="row" spacing={1}>
                                <Grid item>
                                    <Button
                                        variant="contained"
                                        color="primary"
                                        onClick={this.finish}
                                    >
                                        {"Done"}
                                    </Button>
                                </Grid>
                                <Grid item>
                                    <Button
                                        variant="contained"
                                        color="error"
                                        onClick={() => this.setState({ isOpen: false})}
                                    >
                                        {"Cancel"}
                                    </Button>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Box>
            </Modal >
        }
        return null;
    }

    render() {
        return <Fragment>
            <Button onClick={this.openModal} variant="contained" color="primary" startIcon={<AddIcon />}>
                {"Add Value"}
            </Button>
            {this.renderModal()}
        </Fragment>;
    }
}

export default AddValue;