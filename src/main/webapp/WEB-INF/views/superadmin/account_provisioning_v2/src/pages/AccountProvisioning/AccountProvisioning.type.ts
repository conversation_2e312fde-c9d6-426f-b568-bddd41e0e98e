import { SpecialRowProps } from "@socure/components/src/components/TreeGrid/TreeGrid.type"

export type Module = {
  id: number;
  name: string;
  businessUserRoleId: number;
  provisioningType: string;
  parentId?: number;
  order: number;
  defaultState: boolean;
  provisioned?: boolean;
  enabled: boolean;
  updatedBy: string;
  updatedAt: number;
}

export interface TreeFormattedModule extends Module {
  expanded?: boolean;
  indentLevel?: number;
  "enabled-disabled"?: boolean;
  "provisioned-disabled"?: boolean;
  specialRow?: SpecialRowProps;
  parent_businessUserRoleId: number;
}

export type DocVStrategiesList = {
  strategyId: string,
  name: string,
}

interface SaiPreferences {
  depositorName: string,
  memo: string,
  physicalAddress: string,
  city: string;
  state: string;
  zip: string;
  country: string;
}

export type SpecialProductsConfiguration = {
  ein?: string;
  saiPreferences?: SaiPreferences;
  retentionSchedule?: {
    cadence: number;
    routine: string;
  },
  lookupApiKey?: string,
  serviceId?: string,
  docVStrategy?: string,
}

export type AccountProvisioningDetails = {
  bundleReference: string;
  products: Module[];
  productConfiguration: SpecialProductsConfiguration
}

export type ModulesUpdatePayload = {
  id: number;
  enabled: boolean;
  provisioned: boolean;
}

export type AccountProvisioningUpdateDetails = {
  bundleReference: string;
  products: ModulesUpdatePayload[];
  productConfiguration: SpecialProductsConfiguration;
  unEnrollWatchListMonitors: boolean;
}

export type BundleDetails = {
  name: string,
  contents: string,
  resources: number[]
}

export type TreeGridPayload = {
  accordionPayload: TreeFormattedModule[];
  chosenModules: number;
  chosenFFs: number;
}

export type ModifiedModules = {
  update: 'ADD' | 'REMOVE',
  name: string
}

export type Metadata = {
  bundleMapping: BundleDetails[],
  docVStrategies: DocVStrategiesList[]
}