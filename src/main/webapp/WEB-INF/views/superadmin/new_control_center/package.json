{"name": "dynamic-control-centre", "version": "0.1.0", "private": true, "dependencies": {"axios": "^1.8.2", "react": "^17.0.2", "react-dom": "^17.0.2", "web-vitals": "^2.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && yarn run build-rename && yarn run build-mv", "test": "react-scripts test", "eject": "react-scripts eject", "build-mv": "rm -rf ../../../../resources/control_center && mv -f build '../../../../resources/control_center'", "build-rename": "yarn run build-rename-js && yarn run build-rename-css", "build-rename-js": "mv ./build/static/js/main*.js ./build/static/js/main.js && mv ./build/static/js/main*.js.map ./build/static/js/main.js.map ", "build-rename-css": "mv ./build/static/css/main*.css ./build/static/css/main.css && mv ./build/static/css/main*.css.map ./build/static/css/main.css.map"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "async": "2.6.4", "ejs": "3.1.10", "loader-utils": "1.4.2", "node-forge": "1.3.0", "nth-check": "2.0.1", "react-scripts": "5.0.1", "renamer": "^4.0.0", "webpack": "^5.94.0"}, "resolutions": {"nth-check": ">=2.0.1 ", "http-proxy-middleware": "^2.0.7", "follow-redirects": "^1.15.6", "postcss": "^8.4.38", "cross-spawn": "^7.0.5", "path-to-regexp": "^0.1.12", "axios": "^1.8.2", "nanoid": "^3.3.8", "cookie": "^0.7.0"}}