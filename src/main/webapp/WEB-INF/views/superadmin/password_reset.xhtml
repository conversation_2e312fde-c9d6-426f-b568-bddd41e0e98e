<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
    <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
    <link rel="stylesheet" href="resources/styles/socure-dashboard.css" ></link>
    <script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
    <script src="resources/scripts/jquery.min.js" ></script>
    <script src="resources/scripts/bootstrap.min.js"></script>
    <meta charset="UTF-8"/>
    <title>Reset Password</title>
    <link rel="stylesheet" href="#{bucket.resourceURL}/styles/chosen.min.css"/>
    <script>
    bucketurl = "#{bucket.resourceURL}";
    contexturl = "#{request.contextPath}";
    marketingurl = "#{bucket.marketingSite}";
    loggedinUsername = "#{loggedinUsername}";
</script>
    <script src="#{bucket.resourceURL}/scripts/chosen.jquery.min.js"></script>
    <script language="javascript">
	$(function(){
		$('#updatepassword').click(function() {
		    var emailId =$('#emailId').val();
            if(emailId == null || emailId.trim() == "" ){
                alert('Please enter Email ID');
            } else {
            if (confirm("Are you sure you want to initiate a password reset email for " + emailId + "?")) {
            	$('#resetpasswordresult').text("Processing...");
                $.ajax({
                    url : "superadmin/1/account_details/reset_password/" + emailId,
                    type : 'GET',
                    success : function(data) {
                        if(data['status'].toLowerCase() === 'ok' &amp;&amp; data['data'] >0){
                          $('#resetpasswordresult').css('white-space','pre');
                          $('#resetpasswordresult').css('color','blue');
                          $('#resetpasswordresult').text("Reset password email sent Successfully");
                          DD_RUM.setUser({ "id": loggedinUsername});
                          DD_RUM.track("Reset password for ", { userEmail: emailId, loggedinUsername: loggedinUsername});
                        } else {
                           $('#resetpasswordresult').css('color','red');
                           $('#resetpasswordresult').text("Unable to reset password...some thing went wrong");
                        }
                    }
                });
                }
            }
        });
	});

</script>

</h:head>
<h:body>

    <div align="center">
        <font color="blue"><h3>
            <h:outputText value="#{param['message']}" />
        </h3> </font>
    </div>
    <br/>
    <br/>
    <div align="right">
        <h5>
            <h:outputLink value="leaderboard">Leaderboard</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="industry">Manage Industry</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="active_users">Active Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="inactive_users">Inactive Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;

            <h:outputLink value="logout">Logout</h:outputLink>
            &nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
    </div>
    <br/>
    <br/>
    <center><h3>Reset Password</h3></center>
    <hr/>
    <center><b><div id="resetpasswordresult"></div></b></center>
    <div class="container">
        <div class="row">
            <div class="span12">

                <div class="accordion-inner">
                    <div>
                        Email Id &nbsp; <input id="emailId" type="text" style="margin: 15px 0"/>
                    </div>
                    <input class="btn btn-primary" type="button" value="Reset Password" id="updatepassword" />
                </div>

            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            $(".chosen").chosen({
                width:"25%",
                search_contains : true
            });
        });
    </script>
</h:body>
</html>