import React from "react";
import { InputType, TextInput } from '@socure/components';
import styled from 'styled-components';
import BusinessUserRoles from "./BusinessUserRoles";
import { useEffect, useState } from 'react';

const ApiKeyDetailsWrapper = styled.div`
    margin-bottom: 10px;
`;

const ApiKeyDetailsContainer = styled.div`
    display: flex;
    flex-wrap: wrap;

    .socure-c-textInput-OutterContainer {
        margin-right: 10px;
    }
`;

const Note = styled.div`
    font-size: 12px;
    color: #666;
    margin-top: 12px;
`;

type ApiKeyDetailsProps = {
    apiKeyDetails: {
        lookupApiKey: string;
        serviceId?: string;
    };
    moduleId: number;
    onChange: (
        field: string,
        value: string | number,
        errorMessageOnValidation: string
    ) => void;
};

const validateServiceId = (serviceId: string): string => {
    if (!serviceId || serviceId.trim() === '') return 'Invalid value';
    try {
        const decoded = atob(serviceId);
        return decoded && decoded.trim() === '' ? 'Not a valid encoded value' : '';
    } catch (e) {
        return "Not a valid encoded value";
    }
};

const validateLookupApiKey = (apiKey: string): string => {
    if (!apiKey) return "Api key cannot be empty";

    try {
        const decoded = atob(apiKey);
        return decoded.split(':').length === 2 ? '' : "Invalid api key value";
    } catch (e) {
        return "Not a valid encoded value";
    }
};

const ApiKeyDetails: React.FC<ApiKeyDetailsProps> = ({
    apiKeyDetails,
    moduleId,
    onChange,
}) => {
    const hideServiceId = moduleId === BusinessUserRoles.SIM_SWAP_LOOKUP;
    const LOOKUP_API_KEY = 'lookupApiKey';
    const SERVICE_ID = 'serviceId';

    const [apiKeyError, setApiKeyError] = useState('');
    const [serviceIdError, setServiceIdError] = useState('');

    useEffect(() => {
        const error = validateLookupApiKey(apiKeyDetails.lookupApiKey);
        setApiKeyError(error);
        onChange(LOOKUP_API_KEY, apiKeyDetails.lookupApiKey, error);
    }, [apiKeyDetails.lookupApiKey]);
    
    useEffect(() => {
        if (!hideServiceId) {
            const error = validateServiceId(apiKeyDetails.serviceId || '');
            setServiceIdError(error);
            onChange(SERVICE_ID, apiKeyDetails.serviceId || '', error);
        }
    }, [apiKeyDetails.serviceId, hideServiceId]);

    const changeHandler = (field: string, value: string) => {
        const isLookupField = field === LOOKUP_API_KEY;
        const otherValue = isLookupField
            ? apiKeyDetails.serviceId ?? ''
            : apiKeyDetails.lookupApiKey;
        const shouldValidateSecondaryField = !hideServiceId;

        const primaryError = isLookupField
            ? validateLookupApiKey(value)
            : validateServiceId(value);

        const secondaryError = shouldValidateSecondaryField?(isLookupField
            ? validateServiceId(otherValue)
            : validateLookupApiKey(otherValue)):'';

        const error = primaryError.trim() ? primaryError : secondaryError;

        onChange(field, value, error);
    };

    return (
        <ApiKeyDetailsWrapper>
            <ApiKeyDetailsContainer>
                <TextInput
                    type={InputType.text}
                    label="Lookup Api Key"
                    value={apiKeyDetails.lookupApiKey}
                    onChange={(value) => changeHandler(LOOKUP_API_KEY, value)}
                    doValidate
                    errorMsg={apiKeyError}
                />
                {!hideServiceId && (
                    <TextInput
                        type={InputType.text}
                        label="Service Id"
                        value={apiKeyDetails.serviceId}
                        onChange={(value) =>
                            changeHandler(SERVICE_ID, value)
                        }
                        doValidate
                        errorMsg={serviceIdError}
                    />
                )}
            </ApiKeyDetailsContainer>
            <Note>
                Note: Updating the credentials here will update them for MFA Orchestration, Silent Network Authentication and SIM Swap Lookup (if enabled).
            </Note>
        </ApiKeyDetailsWrapper>
    );
};

export default ApiKeyDetails;
