<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<h:html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="theme-color" content="#000000"/>
    <link rel="manifest" href="resources/control_center/manifest.json" />
    <title>Control Center</title>
    <script defer="defer" src="resources/control_center/static/js/main.js"></script>
    <link href="resources/control_center/static/css/main.css" rel="stylesheet" />
    <script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
        /* flag to manage page access type
           Full Access
           View Only
           No Access
        */
        canAccessControlCentreFlag = "#{canAccessControlCentre}";
        appenv = "#{bucket.appEnv}";
    </script>
</head>
<body>
<noscript>You need to enable JavaScript to run this app.</noscript>
<h:panelGroup rendered="#{canAccessControlCentre != 'Full Access'}" id="access-message">
    <h3 align="center">You do not have access to this page</h3>
</h:panelGroup>
<h:panelGroup rendered="#{canAccessControlCentre == 'Full Access'}" id="access-message">
    <div id="root"></div>
</h:panelGroup>
</body>
</h:html>