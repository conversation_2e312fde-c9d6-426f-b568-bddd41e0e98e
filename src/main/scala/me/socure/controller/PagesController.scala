package me.socure.controller

import me.socure.account.service.resolver.PartnerAndSubAccountInfoResolver
import me.socure.application.ApplicationResolver
import me.socure.json.controllerv1.bean.PartnerAndSubAccountInfoMapper
import me.socure.model.account.AccountInfoWithPermission
import me.socure.service.accountservice.AccountInfoResolver
import me.socure.superadmin.authentication.{SimpleAuthenticatedToken, UserDetailsWithoutCredentials}
import me.socure.superadmin.validator.USOnlyAccessValidator
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.{RequestMapping, RequestMethod, RequestParam}

@Controller
class PagesController {

  @Autowired
  private var accountInfoResolver: AccountInfoResolver = _

  @Autowired
  private var partnerAndSubAccountResolver: PartnerAndSubAccountInfoResolver = _

  @Autowired
  @Qualifier("applicationResolver")
  var applicationResolver: ApplicationResolver = _


  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/"))
  def welcomeFile(): String = {
    "index"
  }

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/alertlist_consortium_import"))
  def alertListConsortiumImport(model: Model): String = {
    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val userName = userDetails.getUsername;
    model.addAttribute("loggedinUsername", userName)
    "superadmin/alertlist_consortium_import"
  }

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/document_manager"))
  def documentManager(): String = {
    "superadmin/document_types"
  }

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/docv_strategies"))
  def docvStrategy(): String = {
    "superadmin/docv_strategy"
  }

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/control_center"))
  def controlCenter( model: Model): String = {
    val token: SimpleAuthenticatedToken = SecurityContextHolder.getContext.getAuthentication.asInstanceOf[SimpleAuthenticatedToken]
    model.addAttribute("canAccessControlCentre", token.samlAttribute.controlCenterPermission.name)
    "superadmin/dynamic_control_center"
  }

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/new_control_center"))
  def dynamicControlCenter(model: Model): String = {
    val token: SimpleAuthenticatedToken = SecurityContextHolder.getContext.getAuthentication.asInstanceOf[SimpleAuthenticatedToken]
    model.addAttribute("canAccessControlCentre", token.samlAttribute.controlCenterPermission.name)
    "superadmin/new_control_center"
  }

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/sandbox_config_mgmt"))
  def sandboxConfigMgmt(model: Model) = "superadmin/sandbox_config_mgmt"

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/sandbox_config"))
  def sandboxConfig(model: Model) = "superadmin/sandbox_config"

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/json-editor"))
  def jsonEditor(model: Model): String = "superadmin/json-editor"

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/v2", "/v2/", "/v2/**"))
  def v2(model: Model): String = "superadmin/v2"

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/identity_graph", "/identity_graph/", "/identity_graph/**"))
  def identityGraph(model: Model): String = "superadmin/identity_graph"


  @RequestMapping(value = Array("/account_details"), method = Array(RequestMethod.GET))
  def accountDetails(@RequestParam("accountid") accountid: String, model: Model): String = {
    val accountInfo: AccountInfoWithPermission = accountInfoResolver.getAccountInfoWithPermission(accountid.toLong)
    val accountInfoV2: PartnerAndSubAccountInfoMapper = partnerAndSubAccountResolver.getPartnerAndSubAccountInfo(accountid.toLong)
    val token: SimpleAuthenticatedToken = SecurityContextHolder.getContext.getAuthentication.asInstanceOf[SimpleAuthenticatedToken]
    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val userName = userDetails.getUsername;
    val oldProvisioningFeatureFlag = applicationResolver.getUXFeatureFlag("OldProvisioning", "UXFeatures")
    model.addAttribute("account_id", accountid)
    model.addAttribute("account_name", accountInfo.name)
    model.addAttribute("public_id", accountInfo.publicId)
    model.addAttribute("allowEdit", USOnlyAccessValidator.canEditAccountInfo(accountInfo))
    model.addAttribute("accountProvisioningAccess", token.samlAttribute.accountProvisioningAccess)
    model.addAttribute("account_type", accountInfoV2.getAccountType)
    model.addAttribute("loggedinUsername", userName)
    model.addAttribute("piiRetentionAccessPermission", token.samlAttribute.piiRetentionAccessPermission)
    model.addAttribute("isInternal", accountInfo.isInternal)
    model.addAttribute("showOldProvisioning", oldProvisioningFeatureFlag)
    "superadmin/account_details"
  }

  @RequestMapping(method = Array(RequestMethod.GET), value = Array("/fraud_mapping_v2"))
  def fraudMappingV2(model: Model): String = {
    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val userName = userDetails.getUsername
    model.addAttribute("loggedinUsername", userName)
    "superadmin/fraudmodel_mapping_v2"
  }
}
