import { FC, useCallback, useEffect, useState } from "react";
import { Divider } from "@mui/material";
import LandingInfo from "../LandingInfo";
import FraudMapping from "../FraudMapping";
import {
  AccountAutoComplete,
  FraudModelAutoComplete,
  MappingsForAccount,
  ModelInfo,
} from "../../types";
import { FraudModelMappingContext } from "../context/FraudModelMappingContext";
import FraudMappingTable from "../FraudMapping/FraudMappingTable";
import {
  getAccountsAndFraudModels,
  getMappingsForAccount,
} from "../../services";
import Loader from "../Loader";
import { useToast } from "@socure/components";

import "./Container.scss";

const Container: FC = () => {
  const { showToast } = useToast();
  const [activeAccount, setActiveAccount] = useState<AccountAutoComplete>(null);
  const [showModelFields, setShowModelFields] = useState(false);
  const [accounts, setAccounts] = useState<AccountAutoComplete[]>([]);
  const [models, setModels] = useState<FraudModelAutoComplete[]>([]);
  const [mapping, setMapping] = useState<MappingsForAccount>(null);
  const [modelsAndDescriptions, setModelsAndDescriptions] = useState<
    ModelInfo[]
  >([]);
  const [showLoader, setShowLoader] = useState(false);

  useEffect(() => {
    getAccountsAndFraudModels()
      .then((data) => {
        setAccounts(
          data.accounts.map((acc) => ({ label: acc.name, id: acc.id }))
        );
        setModels(
          data.fraudmodels.map((model) => ({
            label: model.name,
            publicId: model.publicId,
          }))
        );
      })
      .catch((errorMessage) => {
        showToast({
          title: "Error",
          description: errorMessage
            ? errorMessage
            : "Error while fetching accounts and fraud models",
          type: "error",
        });
      });
  }, [setAccounts, setModels, showToast]);

  const handleModelLinkClick = useCallback(() => {
    setShowModelFields((prevVal) => !prevVal);
  }, []);

  const getAndUpdateMappingsForAccount = useCallback(
    (accountId: number, setLoader = false) => {
      if (accountId) {
        setLoader && setShowLoader(true);
        getMappingsForAccount(accountId.toString())
          .then((mappings) => {
            if (mappings?.length > 0) {
              const newMapping = mappings[0];
              newMapping["models"] = newMapping.models.map((model, index) => ({
                ...model,
                rowId: `${model.model.publicId}_${model.model.environment}_${model.associationType}_${index}`,
              }));
              setMapping(newMapping);
            }
          })
          .catch((errorMessage) => {
            showToast({
              title: "Error",
              description: errorMessage
                ? errorMessage
                : "Error while fetching mappings for account",
              type: "error",
            });
          })
          .finally(() => setLoader && setShowLoader(false));
      }
    },
    [setMapping, showToast]
  );

  return (
    <FraudModelMappingContext.Provider
      value={{
        activeAccount,
        accounts,
        models,
        mapping,
        modelsAndDescriptions,
        setActiveAccount,
        setAccounts,
        setModels,
        setMapping,
        setModelsAndDescriptions,
        getAndUpdateMappingsForAccount,
        setShowLoader,
      }}
    >
      <h1 className="fraud_mapping_heading1">Fraud Model Mapping</h1>
      <Divider />
      <div className="fraud_mapping_model-container">
        <a className="fraud_mapping_link" onClick={handleModelLinkClick}>
          Fraud Model Service
        </a>
        <LandingInfo />
        {showModelFields && (
          <>
            <FraudMapping />
            {mapping && <FraudMappingTable />}
          </>
        )}
      </div>
      {showLoader && <Loader />}
    </FraudModelMappingContext.Provider>
  );
};

export default Container;
