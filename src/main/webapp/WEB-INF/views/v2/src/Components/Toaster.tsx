import React from "react";
import { <PERSON><PERSON>kbar, Alert, AlertColor, AlertPropsColorOverrides, Grid } from '@mui/material';
import { OverridableStringUnion } from '@mui/types';

interface ToastProps {
    type?: OverridableStringUnion<AlertColor, AlertPropsColorOverrides>,
    message: string;
    autoClose: number;
}

interface InternalToastState {
    type?: OverridableStringUnion<AlertColor, AlertPropsColorOverrides>,
    message: string;
    closeAfter: number;
    createdAt: number;
}

interface SocureToasterState {
    toasts: Map<string, InternalToastState>
}

export class SocureToaster extends React.Component<any, SocureToasterState> {
    
    constructor(props: any) {
        super(props);
        this.state = {
            toasts: new Map<string, InternalToastState>()
        };
        this.timer = setInterval(this.periodicRefresh, 1000)
    }

    timer?: NodeJS.Timer = undefined

    periodicRefresh = (): void => {
        const { toasts } = this.state;
        if (toasts.size > 0) {
            const now = (new Date()).getTime();
            Array
                .from(toasts.entries())
                .filter(_ => _[1].closeAfter <= now)
                .map(_ => _[0])
                .forEach(_ => toasts.delete(_));
            this.setState({ toasts })
        }
    }

    componentWillUnmount(): void {
        if (this.timer) {
            clearInterval(this.timer)
        }
    }

    closeToast = (key: string) => {
        const { toasts } = this.state;
        toasts.delete(key);
        this.setState({ toasts });
    }

    toast = (detalis: ToastProps) => {
        const { toasts } = this.state;
        const { autoClose, message, type } = detalis;
        const createdAt = (new Date()).getTime();
        const key = crypto.randomUUID();
        const toast: InternalToastState = {
            message,
            closeAfter: autoClose + createdAt,
            type,
            createdAt
        }
        toasts.set(key, toast);
        this.setState({ toasts });
    };

    render(): React.ReactNode {
        const { toasts } = this.state;

        if (toasts.size === 0) {
            return null;
        }

        return <Snackbar anchorOrigin={{ vertical: "bottom", horizontal: "right"}} open>
            <div style={{ maxWidth: "30vw"}}>
                <Grid container spacing={1} direction="column">
                    {Array
                        .from(toasts.entries())
                        .sort((a, b) => a[1].createdAt - b[1].createdAt)
                        .map(([key, details]) => {
                            const closer = this.closeToast.bind(this, key)
                            return <Grid item key={key}>
                                <Alert
                                    onClose={closer}
                                    severity={details.type}
                                    variant="filled"
                                >
                                    {details.message}
                                </Alert>
                            </Grid>;
                        })}
                </Grid>
            </div>
        </Snackbar>;
    }
}

let socureToasterReference: React.RefObject<SocureToaster> | null = null;

export const updateToasterReference = function (ref: React.RefObject<SocureToaster>) {
    socureToasterReference = ref;
}

const toasterUtil = async function(
    type: OverridableStringUnion<AlertColor, AlertPropsColorOverrides> | undefined,
    message: string,
    autoClose: number = 8000) {
        if (socureToasterReference !== null && socureToasterReference.current !== null) {
            socureToasterReference.current.toast({
                type,
                message,
                autoClose
            });
        } else {
            alert(message);
        }
}

export const toaster = Object.seal(Object.freeze({
    error: toasterUtil.bind(null, "error"),
    info: toasterUtil.bind(null, "info"),
    warning: toasterUtil.bind(null, "warning"),
    success: toasterUtil.bind(null, "success")
}));