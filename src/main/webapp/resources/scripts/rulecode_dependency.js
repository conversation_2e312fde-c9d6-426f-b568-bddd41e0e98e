function populateSupportedDependencyDropDown(tables) {
   var tableDropDown = $("#supportedDependencies");
   for (index in tables) {
       var option = new Option(tables[index].name);
       option.setAttribute("title", tables[index].name);
       option.setAttribute("columns", tables[index].columns);
       option.setAttribute("json", tables[index].jsonColumns);
       tableDropDown.append(option);
   }
}

function populateSupportedDependencyDropDownV2(tables) {
    var tableDropDown = $("#supportedDependenciesV2");
    for (index in tables) {
        var option = new Option(tables[index].name);
        option.setAttribute("title", tables[index].name);
        option.setAttribute("columns", tables[index].columns);
        option.setAttribute("json", tables[index].jsonColumns);
        tableDropDown.append(option);
    }
 }

function getListOfConfiguredTables() {
    return $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/table/configured",
            type: 'GET',
            contentType: "application/json"
    });
}

function populateDependencyTableDropDown(tables) {
   var $tableDropDown = $("#dependencies");
   $tableDropDown.html("<option value='default' selected='selected'>Choose dependency</option>");
   $.each(tables, function() {
       $tableDropDown.append($("<option />").text(this.name));
   });
}

function populateDependencyTableDropDownV2(tables) {
    var $tableDropDown = $("#dependenciesV2");
    $tableDropDown.html("<option value='default' selected='selected'>Choose dependency</option>");
    $.each(tables, function() {
        $tableDropDown.append($("<option />").text(this.name));
    });
 }

function openDependencyCreationForm() {
    $("#dependencyCreationForm").show();
}

function openDependencyCreationFormV2() {
    $("#dependencyCreationFormV2").show();
}

function closeDependencyCreationForm() {
    $("#dependencyCreationForm").hide();
}

function closeDependencyCreationFormV2() {
    $("#dependencyCreationFormV2").hide();
}

function resetDependencyCreationForm() {
    $("#dependencyCreationFormFields").trigger("reset");
    $("#dependencyKeys").html("<label>Name</label><input type='text' /><label>Value</label><input type='text' />");
}

function resetDependencyCreationFormV2() {
    $("#dependencyCreationFormFieldsV2").trigger("reset");
    $("#dependencyKeysV2").html("<label>Name</label><input type='text' /><label>Value</label><input type='text' />");
}

function getListOfSupportedTables() {
    return $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/table/supported",
            type: 'GET',
            contentType: "application/json"
    });
}

function addKey() {
    var $tableKeysDiv = $("#dependencyKeys");
    $tableKeysDiv.append($("<label>Name</label><input type='text' />"));
    $tableKeysDiv.append($("<label>Value</label><input type='text' />"));
}


function configureNewTable(payload) {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/table/add",
        type: 'POST',
        data: JSON.stringify(payload),
        contentType: "application/json"
    });
}

function createDependencyTable() {
    if (document.getElementById("supportedDependencies").value === 'default') {
        alert('Cannot create a new dependency without selecting a table first!');
        return
    }

    var tableKeyInputs = $("#dependencyKeys > :input");
    var tableKeys = [];
    for (i = 0; i < tableKeyInputs.length; i = i + 2) {
        if(tableKeyInputs[i].value.trim() === '' || tableKeyInputs[i + 1].value.trim() === '') {
            alert('Either the key name or key value field is empty. Please check the fields and try again.');
            return
        }

        if(tableKeyInputs[i + 1].value.trim().lastIndexOf('input.', 0) === -1) {
            alert('All key values need to start with "input.". Please check the fields and try again.');
            return
        }

        const key = {
            name: tableKeyInputs[i].value.trim(),
            value: tableKeyInputs[i + 1].value.trim()
        }
        tableKeys.push(key);
    }

    const payload = {
        name: document.getElementById("supportedDependencies").value,
        keys: tableKeys,
        jsonField: "data"
    };

    configureNewTable(payload).then((data) => {
        getListOfConfiguredTables().then((data) => populateDependencyTableDropDown(data), () => alert('Unable to fetch dependencies. Please refresh the Page!'));
        resetDependencyCreationForm();
        loadDependencyDefinition();
    }, () => alert('Unable to create new dependency'));

    closeDependencyCreationForm();
}

function createDependencyTableV2() {
    if (document.getElementById("supportedDependenciesV2").value === 'default') {
        alert('Cannot create a new dependency without selecting a table first!');
        return
    }

    var tableKeyInputs = $("#dependencyKeysV2 > :input");
    var tableKeys = [];
    for (i = 0; i < tableKeyInputs.length; i = i + 2) {
        if(tableKeyInputs[i].value.trim() === '' || tableKeyInputs[i + 1].value.trim() === '') {
            alert('Either the key name or key value field is empty. Please check the fields and try again.');
            return
        }

        if(tableKeyInputs[i + 1].value.trim().lastIndexOf('input.', 0) === -1) {
            alert('All key values need to start with "input.". Please check the fields and try again.');
            return
        }

        const key = {
            name: tableKeyInputs[i].value.trim(),
            value: tableKeyInputs[i + 1].value.trim()
        }
        tableKeys.push(key);
    }

    const payload = {
        name: document.getElementById("supportedDependenciesV2").value,
        keys: tableKeys,
        jsonField: "data"
    };

    configureNewTable(payload).then((data) => {
        getListOfConfiguredTables().then((data) => populateDependencyTableDropDownV2(data), () => alert('Unable to fetch dependencies. Please refresh the Page!'));
        resetDependencyCreationFormV2();
        loadDependencyDefinitionV2();
    }, () => alert('Unable to create new dependency'));

    closeDependencyCreationFormV2();
}

function fetchDependencyDefinitions() {
    return $.ajax({
        url: `${contexturl}/superadmin/rulecode-service/v1/dependencydefinitions`,
        type : 'GET'
    });
}

function loadDependencyDefinition(){
    fetchDependencyDefinitions().then((data) => {
        let aaData = {};
        for (var i = 0; i < data.length; i++) {
            const rowData = data[i];
            let arr = [];
            arr.push(rowData.name);
            arr.push(JSON.stringify(rowData.keys));
            arr.push(rowData.json_field);
            aaData[i] = arr;
        }
        oTable = $('#rulecodeDependency').dataTable({
            "aaData" :  Object.values(aaData),
            "bProcessing" : true,
            "bDestroy" : true,
            "bAutoWidth" : true
        });
    });
}

function loadDependencyDefinitionV2(){
    fetchDependencyDefinitions().then((data) => {
        let aaData = {};
        for (var i = 0; i < data.length; i++) {
            const rowData = data[i];
            let arr = [];
            arr.push(rowData.name);
            arr.push(JSON.stringify(rowData.keys));
            arr.push(rowData.json_field);
            aaData[i] = arr;
        }
        oTable = $('#rulecodeDependencyV2').dataTable({
            "aaData" :  Object.values(aaData),
            "bProcessing" : true,
            "bDestroy" : true,
            "bAutoWidth" : true
        });
    });
}