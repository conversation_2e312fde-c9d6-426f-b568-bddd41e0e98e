import IdentityGraph from "../KYC/IdentityGraph";
import RecordCorrection from "../KYC/RecordCorrection";
import SimpleRecordCorrection from "../KYC/SimpleRecordCorrection";
import Dashboard from "../Dashboard";
import ModelListing from "../FraudModelManager/ModelListing";
import { PageGroups } from "./types";

export const GlobalPageList: Array<PageGroups> = [
    {
        display: "Dashboard",
        key: "dashboard",
        pages: [
            {
                type: "v2",
                display: "Dashboard Overview",
                key: "dashboard-overview",
                route: "/dashboard",
                component: Dashboard
            }
        ]
    },
    {
        display: "Fraud Model Manager",
        key: "fraud-model-manager",
        pages: [
            {
                type: "v2",
                display: "Model Listing",
                key: "model-listing",
                route: "/fraud-model-manager/model-listing",
                component: ModelListing
            }
        ]
    },
    {
        display: "KYC",
        key: "kyc",
        pages: [
            {
                type: "v2",
                display: "Record Correction",
                key: "record-correction",
                route: "/record_correction",
                component: RecordCorrection
            },
            {
                type: "v2",
                display: "Record Correction (Simple)",
                key: "record-correction-simple",
                route: "/kyc/simple-record-correction",
                component: SimpleRecordCorrection
            },
            {
                type: "v2",
                display: "Identity Graph",
                key: "identity-graph",
                route: "/kyc/identity-graph*",
                linkRoute: "/kyc/identity-graph",
                component: IdentityGraph
            }
        ]
    },
    {
        display: "Legacy",
        key: "legacy",
        pages: [
            {
                type: "legacy",
                display: "Active Users",
                key: "active-users",
                route: "/active_users"
            },
            {
                type: "legacy",
                display: "Troubleshooting",
                key: "troubleshooting",
                route: "/troubleshooting"
            }
        ]
    }
];