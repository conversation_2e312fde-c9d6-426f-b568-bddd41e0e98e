<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" 
       	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       	xmlns:p="http://www.springframework.org/schema/p" 
       	xmlns:aop="http://www.springframework.org/schema/aop"
       	xsi:schemaLocation="
			http://www.springframework.org/schema/beans 
			http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
			http://www.springframework.org/schema/aop 
			http://www.springframework.org/schema/aop/spring-aop-3.1.xsd">

	<!-- For parsing classes with @Aspect annotation -->
	<aop:aspectj-autoproxy/>

	<bean id="customizableTraceInterceptor" class="me.socure.aop.TraceInterceptor"
		p:enterMessage="Entering $[targetClassShortName].$[methodName]($[arguments])"
		p:exitMessage="Leaving $[targetClassShortName].$[methodName](): $[returnValue]"/>

	<aop:config>
	  <aop:advisor advice-ref="customizableTraceInterceptor" pointcut="execution(public * me.socure.service..*(..))"/>
	  <aop:advisor advice-ref="customizableTraceInterceptor" pointcut="execution(public * me.socure.controller..*(..))"/>
	</aop:config>

</beans>