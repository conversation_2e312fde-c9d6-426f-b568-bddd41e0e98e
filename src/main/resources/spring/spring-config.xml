<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:mvc="http://www.springframework.org/schema/mvc"
	   xmlns:task="http://www.springframework.org/schema/task"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	   		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
	   		http://www.springframework.org/schema/context
	   		http://www.springframework.org/schema/context/spring-context-3.1.xsd
			http://www.springframework.org/schema/mvc
			http://www.springframework.org/schema/mvc/spring-mvc-3.1.xsd
			http://www.springframework.org/schema/task
			http://www.springframework.org/schema/task/spring-task-3.0.xsd">


	<!-- Activates various annotations to be detected in bean classes -->
	<context:annotation-config />


	<!-- Scans the classpath for annotated components that will be auto-registered
		as Spring beans. For example @Controller and @Service. Make sure to set the
		correct base-package -->
		<context:component-scan base-package="me.socure" >
			<context:exclude-filter type="regex" expression="me\.socure\.service\.yb\..*"/>
			<context:exclude-filter type="regex" expression="me\.socure\.service\.ping\..*"/>
			<context:exclude-filter type="regex" expression="me\.socure\.service\.hc\..*"/>
			<context:exclude-filter type="regex" expression="me\.socure\.util\.web\.WebConfig.*"/>
			<context:exclude-filter type="regex" expression="me\.socure\.acxiom\.identify\.service\..*"/>
			<context:exclude-filter type="regex" expression="me\.socure\.service\.acxiom\.impl\..*"/>
			<context:exclude-filter type="regex" expression="me\.socure\.service\.util\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.api\.controllerv1\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.geocoding\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.audit\.util\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.util\.api\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.util\.(?!.*(SpringApplicationContext|EnvUtil|MailUtil|IPDomainUtil|BusinessUserValidator|IndustryValidator|BatchJobValidator|ValidatorUtil|AccountJsonUtil|ApplicationJsonUtil|ScoringComponentUtil|EntityImageUtil|FutureUtil|PublicAccessTokenUtil|PublicAccessTokenUtilAsync|RESTUtil|UserScoreStatusUtil|UserScoreUtil|AccountUtil)).*" />
			<context:exclude-filter type="regex" expression="me\.socure\.api\.blacklist\.controllerv1\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.audit\.impl\.TPAuditServiceImpl.*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.audit\.impl\.TPAuditServiceImpl.*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.factory\.watchlistmonitoring\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.coord\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.fb\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.fc\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.gp\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.hc\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.in\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.pipl\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.ssn\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.tw\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.wl\.bean\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.yb\.impl\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.impl\.(?!.*(UserDetailsServiceImpl|ThirdPartyAuditServiceImpl|SocureCacheServiceImpl|JobDefinitionConfigServiceImpl|AccountServiceImpl|RuleCodeIngestionServiceImpl|RegistrationServiceImpl|RulecodeCommonServiceImpl|RateLimitingService|IndustryServiceImpl|ScoringComponentServiceImpl|CacheScoreComponentServiceImpl|AccountCacheServiceImpl|CacheScoreServiceImpl|AccountProfileServiceImpl|CacheUsageTrackerServiceImpl|ProfileServiceImpl|BatchJobServiceImpl)).*" />
			<context:exclude-filter type="regex" expression="me\.socure\.acxiom\.identify\.service\..*" />
			<context:exclude-filter type="regex" expression="me\.socure\.json\.controllerv1\.DashboardControllerV1.*" />
			<context:exclude-filter type="regex" expression="me\.socure\.json\.controllerv1\.FeedbackControllerV1.*" />
			<context:exclude-filter type="regex" expression="me\.socure\.json\.controllerv1\.ContactControllerV1.*" />
			<context:exclude-filter type="regex" expression="me\.socure\.json\.controllerv1\.FeedbackControllerV1.*" />
			<context:exclude-filter type="regex" expression="me\.socure\.service\.factory\.refactorScala\..*" />
		</context:component-scan>


	<!-- Configures the annotation-driven Spring MVC Controller programming
		model. Note that, with Spring 3.0, this tag works in Servlet MVC only! -->
	<mvc:annotation-driven />

	<mvc:resources mapping="/resources/**" location="/resources/" />

	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="/superadmin/account/edit/1/*"/>
			<bean class="me.socure.interceptor.USOnlyAccessValidateIntercept" />
		</mvc:interceptor>
		<mvc:interceptor>
			<mvc:mapping path="/api/v2/**"/>
			<bean class="me.socure.superadmin.api.v2.common.ApiInterceptorV2" />
		</mvc:interceptor>
		<mvc:interceptor>
			<mvc:mapping path="/superadmin/*control_center/*"/>
			<bean class="me.socure.interceptor.ControlCenterAuthorisationInterceptor" />
		</mvc:interceptor>
		<mvc:interceptor>
			<mvc:mapping path="/rulecode/dataload/*"/>
			<bean class="me.socure.rulecode.RulecodeHmacInterceptor" />
		</mvc:interceptor>
	</mvc:interceptors>

	<!-- i18n Configuration -->
	<bean id="messageSource"
		  class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
		<property name="basename" value="classpath:properties/messages" />
		<property name="defaultEncoding" value="UTF-8" />
	</bean>

	<bean id="localeChangeInterceptor"
		  class="org.springframework.web.servlet.i18n.LocaleChangeInterceptor">
		<property name="paramName" value="lang" />
	</bean>

	<bean id="localeResolver"
		  class="org.springframework.web.servlet.i18n.CookieLocaleResolver">
		<property name="defaultLocale" value="en" />
	</bean>

	<!-- Imports logging configuration -->

	<import resource="classpath:spring/spring-properties.xml" />
	<import resource="classpath:spring/spring-data.xml" />
	<import resource="classpath:spring/spring-memcache.xml" />

	<!-- Import AWS beans -->
	<import resource="classpath:spring/socure-aws.xml" />
	<import resource="classpath:spring/socure-account-service-clients.xml"/>
	<import resource="classpath:spring/socure-thirdparty-service-client.xml"/>

	<bean class="me.socure.service.factory.ExecutionContextFactory"/>
	<bean id="modelParametersResolver" class="me.socure.account.service.factory.ModelParametersResolverFactory"/>
	<import resource="classpath*:/spring/socure-model-beans-config.xml"/>

	<!-- <import resource="classpath:spring/socure-aspects.xml" /> -->



	<!-- For @Async thread pool size. Default is set to 50-2000 with 100 queue size. Do STAGE testing than reconfigure -->
	<task:annotation-driven executor="taskExecutor" mode="aspectj"/>
	<task:executor id="ontologyEntityTaskExecutor"  pool-size="10-50" queue-capacity="5" rejection-policy="DISCARD_OLDEST"/>

	<!--<task:executor id="taskExecutor" -->
	<!--pool-size="${thread.pool.executor.pool.size}"-->
	<!--queue-capacity="${thread.pool.executor.queue.capacity}"-->
	<!--keep-alive="${thread.pool.executor.keep.alive.seconds}"-->
	<!--rejection-policy="CALLER_RUNS"/>-->

	<bean id="generalCallerRunsPolicy" class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy" />

	<bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
		<property name="corePoolSize" value="${thread.pool.executor.pool.size}" />
		<property name="maxPoolSize" value="${thread.pool.executor.max.pool.size}" />
		<property name="rejectedExecutionHandler">
			<bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy" />
		</property>
	</bean>

	<bean id="schedulerExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler"/>

	<bean id="monitoredTaskExecutor" class="me.socure.async.MetricsThreadPoolExecutorFactory" />


	<bean id="bucket" class="me.socure.util.StaticResourceURL">
		<property name="resourceURL" value="${staticurl.super.admin}"></property>
		<property name="marketingSite" value="${marketing.url}"></property>
		<property name="authScoreBase" value="${application.authScore.baseUrl}"></property>
		<property name="appEnv" value="${appEnv}"></property>
	</bean>

	<bean id="configUtil" class="me.socure.util.ConfigUtil">
		<property name="activatinUrl" value="${application.activation.url}"></property>
		<property name="resetUrl" value="${application.forget.pass.url}"></property>
		<property name="setPasswordUrl" value="${application.set.pass.url}"></property>
	</bean>

	<bean class="me.socure.json.config.JsonpCallbackFilter" id="jsonpCallbackFilter" />
	<bean id="restTemplate" class="org.springframework.web.client.RestTemplate">
	</bean>

	<bean id="multipartResolver"
		  class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="maxUploadSize" value="${file.upload.max.size:**********}"></property>
	</bean>

	<import resource="spring-controllers.xml" />
	<import resource="classpath:spring/spring-memcache.xml" />

	<!-- Cache services -->
	<import resource="classpath:spring/socure-cache.xml" />

	<!--<bean class="me.socure.mdc.MDCAwareAsyncAnnotationBeanPostProcessor">-->
	<!--<property name="executor" ref="taskExecutor"></property>-->
	<!--</bean>-->

	<bean id="accountInfoClient" class="me.socure.service.account.AccountInfoClientFactory" />
	<bean id="accountInfoClientV2" class="me.socure.service.accountservice.AccountInfoClientV2Factory" />
	<bean id="reasonCodeServiceClient" class="me.socure.service.reasoncode.ReasonCodeServiceFactory" />
	<bean class="me.socure.util.FileDownloadClientFactory"/>
	<bean class="me.socure.saml2.metadata.Saml2MetadataProviderFactory"/>
	<bean class="me.socure.account.service.factory.Saml2MetadataClientFactory"/>
	<bean class="me.socure.account.service.factory.AuthenticationClientFactory"/>
	<bean class="me.socure.service.factory.auditing.rest.client.TransactionRestClientServiceFactory"/>
	<bean class="me.socure.alertlist.consortium.ConsortiumImporterClientFactory"/>
	<bean class="me.socure.decision.DecisionServiceFactory"/>
	<bean class="me.socure.decision.DecisionServiceFactoryV2"/>
	<bean class="me.socure.sandbox.SandboxServiceClientFactory"/>
	<bean id="documentManagerClient" class="me.socure.document.manager.client.DocumentManagerFactory"/>
	<bean id="eventAuditingClient" class="me.socure.account.service.factory.EventAuditingClientFactory" />
	<bean id="batchJobClient" class="me.socure.account.service.factory.BatchJobClientFactory" />
	<bean id="ruleCodeClient" class="me.socure.account.service.factory.RuleCodeClientFactory" />
	<bean id="jobDefinitionClient" class="me.socure.account.service.factory.JobDefinitionClientFactory"/>
	<bean id="dashboardUserPermissionClient" class="me.socure.account.service.factory.DashboardUserPermissionClientFactory"/>
	<bean id="eventRuleClient" class="me.socure.account.service.factory.EventRuleClientFactory"/>
	<bean id="s3Client" class="me.socure.account.service.factory.S3ClientFactory" />
	<bean id="objectMapper" class="com.fasterxml.jackson.databind.ObjectMapper" />
	<bean id="h2oPredictorClient" class="me.socure.account.service.factory.H2OPredictorClientFactory"/>
	<bean id="h2oMLPredictorClient" class="me.socure.account.service.factory.H2OMLPredictorClientFactory"/>
	<bean id="modelManagementClient" class="me.socure.account.service.factory.ModelManagementClientFactory"/>
	<bean id="s3AuditBucketSyncClientFactory" class="me.socure.rulecode.factory.S3AuditBucketSyncClientFactory" />
    <bean id="s3ConfigBucketSyncClientFactory" class="me.socure.rulecode.factory.S3ConfigBucketSyncClientFactory" />
    <bean id="s3DataBucketClientFactory" class="me.socure.rulecode.factory.S3DataBucketClientFactory" />
    <bean id="rulecodeConfigProvider" class="me.socure.service.rulecode.RulecodeConfigProvider" />
	<bean id="stepUpProcessDetailsClient" class="me.socure.stepUp.factory.StepUpProcessDetailsClientFactory"/>
	<bean id="newRateLimiterClient" class="me.socure.account.service.factory.NewRateLimitConfigClientFactory" />
	<bean id="docvOrchestraClient" class="me.socure.docv.DocvOrchestraClientFactory"/>
	<bean id="controlCenterServiceClient" class="me.socure.control.center.factory.ControlCenterServiceFactory" />
	<!-- Temporarily disabled due to network connectivity issues -->
	<!-- <bean id="dynamicControlCenterServiceClient" class="me.socure.dynamic.control.center.factory.DynamicControlCenterServiceFactory" /> -->
	<bean id="dynamicControlCenterV2Upload" class="me.socure.dynamic.control.center.factory.DynamicControlCenterServiceFactoryV2" />
	<bean id="s3BatchJob" class="me.socure.batchjob.factory.S3BatchJobClientFactory" />
	<bean id="transferClient" class="me.socure.sftp.TransferClientFactory" />
	<bean id="accountSftpUserClient" class="me.socure.sftp.AccountSftpUserClientFactory" />
	<bean id="accountAutomationClient" class="me.socure.account.provisioning.AccountAutomationClientFactory" />
	<bean id="sponsorBankClient" class="me.socure.sponsorbank.SponsorBankClientFactory" />
	<bean id="pgpKeysClient" class="me.socure.account.service.factory.PGPKeyManagmentClientFactory" />
	<bean id="caseMgmtClient" class="me.socure.case_mgmt.factory.CaseMgmtServiceResolverFactory" />
	<bean id="wlSettingsClient" class="me.socure.case_mgmt.factory.WatchlistSettingsClientFactory" />
	<bean id="entityFeedbackClient" class="me.socure.case_mgmt.factory.EntityFeedbackClientFactory" />
	<bean id="berbixClient" class="me.socure.berbix.factory.BerbixClientFactory" />
	<bean id="modelManagementClientV2" class="me.socure.service.factory.dsmodel.ModelManagementClientV2Factory"/>

	<!-- Aspect Log -->
	<bean id="loggingAspect" class="me.socure.util.log.LoggingAspect" />

	<bean id="httpClient" class="org.apache.http.impl.client.DefaultHttpClient">
		<constructor-arg>
			<bean class="org.apache.http.impl.conn.PoolingClientConnectionManager" />
		</constructor-arg>
	</bean>

	<bean id="reCaptcha" class="net.tanesha.recaptcha.ReCaptchaImpl">
		<property name="privateKey" value="6LfGEwgTAAAAAPithJiufWOSfCifzwKN28Rgb_Yq"/>
	</bean>
</beans>
