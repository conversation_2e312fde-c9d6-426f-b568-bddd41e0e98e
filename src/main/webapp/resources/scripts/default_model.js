//Java script for fraud service model

const CORRELATION_SCORE = "correlation scores";
const RISK_SCORE = "risk score";

window.onload = function() {
  if(DD_RUM) {
    DD_RUM.startView({ name: "/default_models" });
    DD_RUM.setUser({ id: loggedinUsername });
  }
}

update_model_name = function (nTr) {
    this.get_fraudmodel();
    var dataBase = oTable.fnGetData(nTr);
    var name = get_original_data_for_public_id(dataBase[2]).name;
    document.getElementById("fraudname").value = name;
    DD_RUM.track("Update Fraud Model Name", { newName: name });
};

get_fraudmodel = function () {
    var url = 'api/1/fraudmodel/models?category=active';
    $.ajax(url, {
        type: 'GET',
        success: function (data) {
            if (data !== null) {
                data = data['data'];
                render_model_name_dropdown(data);

            }
        }
    });
};

get_defaultmodel = function () {
    var url = "";
    url = 'api/1/fraudmodel/models?category=default';
    var formData = new FormData();
    formData.append('category', 'default');
    $.ajax(url, {
        type: "GET",
        cache: false,
        contentType: false,
        processData: false,
        data: formData,
        success: function (data) {
            if (data !== null) {
                data = data['data'];

                show_defaultmodel(data);

            }
        }
    });
    DD_RUM.track("Load default fraud models");
};

get_original_data_for_public_id = function (publicId) {
    if(window.original_model_data) {
        for (var i in window.original_model_data) {
            var d = window.original_model_data[i];
            if(d.publicId === publicId) {
                return d;
            }
        }
    }
    return null;
};

show_defaultmodel = function (data) {
    window.delegated_admin_email = [];
    if (data !== null) {
        window.original_model_data = data;
        var aaData = [];
        var imgPath = bucketurl + '/assets/images/edit.png';
        var infoImgPath = bucketurl + '/assets/images/info.png';
        for (var j = 0; j < data.length; j++) {
            var rowData = data[j];
            var a = [];
            a.push(imgPath); //0
            a.push(rowData['featureLabel']); //1
            a.push(rowData['publicId']); //2
            a.push(rowData['name']); //3
            a.push(rowData['identifier']); //4
            a.push(rowData['scoreName']); //5
            a.push(rowData['version']); //6
            a.push(rowData['quantileMapping']); //7
            a.push(rowData['params']); //8
            a.push(rowData['config']); //9
            a.push(rowData['description']); //10
            a.push(infoImgPath); //11

            aaData.push(a);
        }

        oTable = $('#industry_table')
            .dataTable(
                {
                    "aaData": aaData,
                    "bProcessing": true,
                    "bDestroy": true,
                    "bAutoWidth": false,
                    "iDisplayLength": 25,
                    "aoColumnDefs": [{
                        "bSortable": false,
                        "aTargets": [0, -1]
                        },
                        {
                            "aTargets": [8, 9],
                            "bVisible": false
                        }],
                    "aaSorting": [[2, 'asc']],
                    "aoColumns": [
                        {
                            "fnRender": function (oObj) {
                                return "<img id='editIcon' src='"
                                    + oObj.aData[0]
                                    + "' width='20px' height='20px' style='margin-left:5px;'/>"
                            },
                            "aTargets": [0]
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[1];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[2];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                var result = oObj.aData[3];
                                if (oObj.aData[10]) {
                                    result += '<a href="#" data-toggle="tooltip" title="'
                                              + oObj.aData[10]
                                              +'">'
                                              + "<img style='margin-left:5px;margin-bottom:2px' src='"
                                              + oObj.aData[11]
                                              + "' width='15px' height=15px' />"
                                              +'</a>';
                                }
                                return result;
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[4];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[5];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[6];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                if(oObj.aData[7] && oObj.aData[7].length > 0) {
                                    var lines = oObj.aData[7].split(/(?:\r\n|\r|\n)/g);
                                    var tmp = lines.join("</br>");

                                    var sOut = "<a href='javascript:viewQuantileMapping(\"" + tmp + "\")' class='button submit'>View</a>"
                                        + " | "
                                        +" <a href='data:text/plain;charset=UTF-8,"
                                        + oObj.aData[7]
                                        +"' download=\"quantileMapping.csv\">Download</a>";
                                    return sOut;
                                }
                                else {
                                    return "--";
                                }
                            }
                        }
                    ]
                });

        $(document).off('click', '#industry_table tbody td #editIcon').on('click', '#industry_table tbody td #editIcon', function () {
            var nTr = $(this).parents('tr')[0];
            if (!oTable.fnIsOpen(nTr)) {
                // Open this row
                show_defaultmodel_update_form(nTr)
                update_model_name(nTr);
            }
        });
    }
};

viewQuantileMapping = function(qm) {
    if(qm && qm.length>0) {
        $('#quantile_mappings').html(qm);
    }
    else{
        $('#quantile_mappings').html("Quantile Mapping not set!!!")
    }

    $('#ViewQuantileMappingModal').modal('show');
};

show_defaultmodel_update_form = function (nTr) {
    var dataBase = oTable.fnGetData(nTr);
    var data;
    data = get_original_data_for_public_id(dataBase[2]);
    if (data == null) {
        $('#msg_font').prop('color', 'red');
        $('#super_admin_msg').html('Please select the default model to update.');
    } else {
        $('#msg_font').prop('color', 'blue');
        $('#super_admin_msg').html('');
        show_defaultmodel_update_dialog(data);
    }
};

var current_row_data = null;

show_defaultmodel_update_dialog = function (data) {
    sOut = "<fieldset>"
                + "<div>"
                + "     <h6>Feature: </h6>"
                + "     <div style='font-size: 16px;'> "+ data.featureLabel + " </div>"
                + "</div><div style='margin-top:20px;'>"
                + "<div style='margin-top:20px;'>"
                + "     <h6>Model Name: </h6>"
                + '     <div><input id="fraudname" class="form-control identifier-width" list="fraudnamelist" />'
                + '        <datalist id="fraudnamelist"></datalist><div class="error-msg" id="nameerrormsg"></div>'
                + "</div>"
    if (data != null) {
        $('#update_form').html(sOut);
        $('#MessageModal').modal('show');
        current_row_data = data;
    } else {
        $('#admin_message').html('Please select the default model to update.');
    }
};

update_defaultmodel = function () {
    $("#UpdateConfirmationModal").modal("hide");

    var data = current_row_data;
    var url = contexturl + '/api/1/fraudmodel/default';

    var formData = new FormData();

    formData.append('publicId', data.publicId);

    var nameErrorMsg = document.getElementById("nameerrormsg");
    var namefield = document.getElementById("fraudname");
    if (namefield.value) {
        formData.append('name', namefield.value);
    } else {
        nameErrorMsg.innerHTML = "Please select a valid model.";
        return;
    }
    formData.append('feature', data.featureName);
    formData.append('config', data.config);
    $.ajax(
            url,
            {
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,

                success: function (data) {
                    if (data['status'].toLowerCase() === 'ok') {
                        $('#message').html('');
                        $("#MessageModal").modal("hide");
                        $('#super_admin_msg').html(data['msg']);
                        get_defaultmodel();
                    } else {
                        $("#MessageModal").modal("show");
                        $('#message').html(data['msg']);
                    }
                }
    });
    DD_RUM.track("Update default fraud model", { name: namefield.value, publicId: data.publicId, featureName: data.featureName, config: data.config });
}

var POJO_MODELS = [];
var MOJO_MODELS = [];

render_model_name_dropdown = function (data) {
    var dropdown = document.getElementById('fraudnamelist');
    dropdown.innerHTML = "";
    for (var i = 0; i < data.length; i++) {
        var option = document.createElement('option');
        option.value = data[i].name;
        dropdown.appendChild(option);
    }
}

showConfirmUpdateDialog = function() {
    var msg = "Are you sure you want to update the default model?";
    $('#update_msg').html(msg);
    $('#UpdateConfirmationModal').modal({backdrop: 'static', keyboard: false});
};