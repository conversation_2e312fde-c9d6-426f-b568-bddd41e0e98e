package me.socure.interceptor;

import me.socure.model.account.AccountInfoWithPermission;
import me.socure.service.accountservice.AccountInfoResolver;
import me.socure.superadmin.validator.USOnlyAccessValidator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class USOnlyAccessValidateIntercept implements HandlerInterceptor {


  private final AccountInfoResolver accountInfoResolver;

  private final Logger logger = LoggerFactory.getLogger(USOnlyAccessValidateIntercept.class);

  @Autowired
  public USOnlyAccessValidateIntercept(AccountInfoResolver accountInfoResolver) {
    this.accountInfoResolver = accountInfoResolver;
  }

  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

    String accountId = request.getParameter("accountid");

    if (StringUtils.isNotBlank(accountId)) {
        AccountInfoWithPermission accountInfoWithPermission = accountInfoResolver.getAccountInfoWithPermission(Long.parseLong(accountId));

        boolean canEditAccountInfo = USOnlyAccessValidator.canEditAccountInfo(accountInfoWithPermission);

        if (!canEditAccountInfo) {
          response.setStatus(403);
        }
        return canEditAccountInfo;
    } else {
        logger.error("Account id not found in request");
        return Boolean.FALSE;
    }
  }

  public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

  }

  public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

  }

}
