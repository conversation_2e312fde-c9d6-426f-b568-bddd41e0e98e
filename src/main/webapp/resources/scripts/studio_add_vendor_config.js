function getVendorPrefixSuccessCallback(data) {
    var select = document.getElementById("vendorPrefixSelect");
    for (index in data) {
        var option = new Option(data[index])
        select.add(option);
    }
}

function getVendorPrefixSuccessCallbackV2(data) {
    var select = document.getElementById("vendorPrefixSelectV2");
    for (index in data) {
        var option = new Option(data[index])
        select.add(option);
    }
}

function getVendorPrefix(selectedVendor) {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+selectedVendor+"/prefix",
        type: 'GET'
    });
}

function getConfigured(selectedVendor, selectedVendorPrefix, version) {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+selectedVendor+"/prefix/"+selectedVendorPrefix+"/configs",
        type: 'GET',
        data: {version},
        dataType: "json"
    });
}

function getSupportedVendorConfigs() {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/configs",
        type: 'GET'
    });
}

var selectedVendorPrefix;
function selectVendorPrefix(selectObject) {
    selectedVendorPrefix = selectObject.value;
    if(selectedVendorPrefix=='')
      return;
    $('#vendorPrefixSelection').show();
    // getConfigured
    getConfigured(selectedVendor, selectedVendorPrefix).then((data) => {
        let $currentConfigs = $("#currentconfigs");
        $currentConfigs.empty();
        for (index in data) {
            $currentConfigs.append($("<label>" + index +" : "+data[index] +"</label>"))
        }
    }, () => alert('Unable to fetch existing vendor configs. Please refresh the Page!'));

    // getSupportedVendorConfigs
    getSupportedVendorConfigs().then((data) => {
        var $vendorConfigsTextBoxes = $("#vendorConfigsTextBox");
        $vendorConfigsTextBoxes.empty();
        for (index in data) {
            $vendorConfigsTextBoxes.append($( "<label>" + data[index].name + "</label> <input id='"+data[index].name+"' type='text' />  </br>"))
        }
    }, () => alert('Unable to fetch supported vendor configs. Please refresh the Page!'));

}

function selectVendorPrefixV2(selectObject) {
    selectedVendorPrefix = selectObject.value;
    if(selectedVendorPrefix=='')
      return;
    $('#vendorPrefixSelectionV2').show();
    // getConfigured
    getConfigured(selectedVendor, selectedVendorPrefix, "v2").then((data) => {
        let $currentConfigs = $("#currentconfigsV2");
        $currentConfigs.empty();
        for (index in data) {
            $currentConfigs.append($("<label>" + index +" : "+data[index] +"</label>"))
        }
    }, () => alert('Unable to fetch existing vendor configs. Please refresh the Page!'));

    // getSupportedVendorConfigs
    getSupportedVendorConfigs().then((data) => {
        var $vendorConfigsTextBoxes = $("#vendorConfigsTextBoxV2");
        $vendorConfigsTextBoxes.empty();
        for (index in data) {
            $vendorConfigsTextBoxes.append($( "<label>" + data[index].name + "</label> <input id='"+data[index].name+"' type='text' />  </br>"))
        }
    }, () => alert('Unable to fetch supported vendor configs. Please refresh the Page!'));
}

function makeSaveConfigsRequest(selectedVendor, prefix, configs, version) {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+selectedVendor+"/prefix/"+prefix+`/configs?${new URLSearchParams({version})}`,
        type: 'POST',
        data: JSON.stringify(configs),
        contentType: "application/json"
    });
}

function saveNewConfigs() {

    const prefix  = $('#vendorPrefixSelect').val();
    const inputBoxes = $("#vendorConfigsTextBox > :text");
    let configs = {};
    for(var i=0;i<inputBoxes.length;i++){
        if(inputBoxes[i].value){
            configs[inputBoxes[i].id] = inputBoxes[i].value;
        }
    }
    if(!configs){
        alert('Invalid Configs!');
        return;
    }
    makeSaveConfigsRequest(selectedVendor, prefix, configs).then((data) => {
        selectVendorPrefix({value: prefix});
        alert('Saved Vendor configs Successfully!');
    }, () => alert('Unable to Save New Vendor configs!'));
}

function saveNewConfigsV2() {
    const prefix  = $('#vendorPrefixSelectV2').val();
    const inputBoxes = $("#vendorConfigsTextBoxV2 > :text");
    let configs = {};
    for(var i=0;i<inputBoxes.length;i++){
        if(inputBoxes[i].value){
            configs[inputBoxes[i].id] = inputBoxes[i].value;
        }
    }
    if(!configs){
        alert('Invalid Configs!');
        return;
    }
    makeSaveConfigsRequest(selectedVendor, prefix, configs, "v2").then((data) => {
        selectVendorPrefixV2({value: prefix});
        alert('Saved Vendor configs Successfully!');
    }, () => alert('Unable to Save New Vendor configs!'));
}