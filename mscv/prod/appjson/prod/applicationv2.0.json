{"version": "1.0-SNAPSHOT-TEST-2", "weights": {"weight": 0, "maxScore": 100, "minScore": -100, "scoreComponents": {"ACVAL": {"100010": {"description": "Invalid SSN", "identity": "INVALID_SSN", "positive": false, "reasoncode": "R303", "weight": 0}, "100020": {"description": "GLBA record validation failed on Name", "identity": "NAME_RESOLV_FAIL", "positive": false, "reasoncode": "R304", "weight": 0}, "100030": {"description": "GLBA record validation failed on Date of Birth", "identity": "DOB_RESOLV_FAIL", "positive": false, "reasoncode": "R305", "weight": 0}, "100040": {"description": "GLBA record validation failed on Address", "identity": "ADDRESS_RESOLV_FAIL", "positive": false, "reasoncode": "R306", "weight": 0}, "100050": {"description": "GLBA record validation failed on Mobile Number", "identity": "MOBILE_NUMBER_RESOLV_FAIL", "positive": false, "reasoncode": "R307", "weight": 0}, "100060": {"description": "GLBA record validation failed on Driver's License", "identity": "DRIVERS_LICENSE_RESOLV_FAIL", "positive": false, "reasoncode": "R308", "weight": 0}, "200020": {"description": "Given state matches SSN state code", "identity": "AREA_MATCH", "positive": true, "reasoncode": "I305", "weight": 0}, "400401": {"description": "R_SSN_SUSPICIOUS", "identity": "R_SSN_SUSPICIOUS", "reasoncode": "R401", "weight": 0}, "400402": {"description": "R_SSN_TRUE_NAME_FRAUD", "identity": "R_SSN_TRUE_NAME_FRAUD", "reasoncode": "R402", "weight": 0}, "400403": {"description": "R_SSN_DECEASED_OR_DEATHBENEFITS", "identity": "R_SSN_DECEASED_OR_DEATHBENEFITS", "reasoncode": "R403", "weight": 0}, "400413": {"description": "R_TEL_SUSPICIOUS", "identity": "R_TEL_SUSPICIOUS", "reasoncode": "R413", "weight": 0}, "400414": {"description": "R_TEL_TRUE_NAME_FRAUD", "identity": "R_TEL_TRUE_NAME_FRAUD", "reasoncode": "R414", "weight": 0}, "400425": {"description": "R_ADD_SUSPICIOUS", "identity": "R_ADD_SUSPICIOUS", "reasoncode": "R425", "weight": 0}, "400426": {"description": "R_ADD_TRUE_NAME_FRAUD", "identity": "R_ADD_TRUE_NAME_FRAUD", "reasoncode": "R426", "weight": 0}, "400435": {"description": "R_PRS_ISDECEASED", "identity": "R_PRS_ISDECEASED", "reasoncode": "R435", "weight": 0}, "400501": {"description": "Input SSN reported as suspicious", "identity": "HRA_SSN_SUSPICIOUS", "reasoncode": "I501", "weight": 0}, "400502": {"description": "Input SSN reported misused and requires further investigation", "identity": "HRA_SSN_MISUSED", "reasoncode": "I502", "weight": 0}, "400503": {"description": "Input SSN reported used in true name fraud", "identity": "HRA_SSN_FRAUD", "reasoncode": "I503", "weight": 0}, "400504": {"description": "Input SSN is reported deceased", "identity": "HRA_SSN_DECEASED", "reasoncode": "I504", "weight": 0}, "400505": {"description": "Input SSN not issued by Social Security Administration", "identity": "HRA_SSN_UNKNOWN_ISSUER", "reasoncode": "I505", "weight": 0}, "400506": {"description": "Input SSN requires further investigation", "identity": "HRA_SSN_REQUIRES_INVESTIGATION", "reasoncode": "I506", "weight": 0}, "400507": {"description": "Input SSN used in death benefits claim for John Doe DOB (date) DOC: (date) ZIP Code where benefits were paid is (ZIP), most likely (City, state) ZIP Code Last residence is (ZIP), Most likely (City state,Address, SSN or Telelphone Number reported by more than one source)", "identity": "HRA_SSN_MULTIPLE_SOURCES", "reasoncode": "I507", "weight": 0}, "400508": {"description": "Input telephone number is an answering service", "identity": "HRA_TEL_ANSWERING_SERVICE", "reasoncode": "I508", "weight": 0}, "400509": {"description": "Input telephone number is a cellular phone", "identity": "HRA_TEL_CELL", "reasoncode": "I509", "weight": 0}, "400510": {"description": "Input telephone is a public/pay phone", "identity": "HRA_TEL_PUBLIC", "reasoncode": "I510", "weight": 0}, "400511": {"description": "Input telephone number is commercial", "identity": "HRA_TEL_COMMERCIAL", "reasoncode": "I511", "weight": 0}, "400512": {"description": "Input telephone number is institutional", "identity": "HRA_TEL_INSTITUTIONAL", "reasoncode": "I512", "weight": 0}, "400513": {"description": "Input telephone number is governmental", "identity": "HRA_TEL_GOVERNMENTAL", "reasoncode": "I513", "weight": 0}, "400514": {"description": "Input telephone number reported as suspicious", "identity": "HRA_TEL_SUSPICIOUS", "reasoncode": "I514", "weight": 0}, "400515": {"description": "Input telephone number reported misused and requires further investigation", "identity": "HRA_TEL_MISUSED", "reasoncode": "I515", "weight": 0}, "400516": {"description": "Input telephone number reported used in true name fraud", "identity": "HRA_TEL_FRAUD", "reasoncode": "I516", "weight": 0}, "400517": {"description": "Input telephone number requires further investigation", "identity": "HRA_TEL_REQUIRES_INVESTIGATION", "reasoncode": "I517", "weight": 0}, "400518": {"description": "Address, SSN or telephone number reported by more than one source", "identity": "HRA_MULTIPLE_SOURCES", "reasoncode": "I518", "weight": 0}, "400519": {"description": "Address is a mail receiving/forwarding service", "identity": "HRA_ADD_FORWARDING_SERVICE", "reasoncode": "I519", "weight": 0}, "400520": {"description": "Address is a hotel/motel or temporary residence", "identity": "HRA_ADD_TEMPORARY", "reasoncode": "I520", "weight": 0}, "400521": {"description": "Address is a credit correction service", "identity": "HRA_ADD_CORRECTION_SERVICE", "reasoncode": "I521", "weight": 0}, "400522": {"description": "Address is a camp site", "identity": "HRA_ADD_CAMP_SITE", "reasoncode": "I522", "weight": 0}, "400523": {"description": "Address is a secretarial service", "identity": "HRA_ADD_SECRETARIAL_SERVICE", "reasoncode": "I523", "weight": 0}, "400524": {"description": "Address is a check cashing service", "identity": "HRA_ADD_CASHING_SERVICE", "reasoncode": "I524", "weight": 0}, "400525": {"description": "address is a restaurant/bar/nightclub", "identity": "HRA_ADD_BAR", "reasoncode": "I525", "weight": 0}, "400526": {"description": "Address is a storage facility", "identity": "HRA_ADD_STORAGE_FACILITY", "reasoncode": "I526", "weight": 0}, "400527": {"description": "Address is an airport/airfield", "identity": "HRA_ADD_AIRPORT", "reasoncode": "I527", "weight": 0}, "400528": {"description": "Address is a truck stop", "identity": "HRA_ADD_TRUCK_STOP", "reasoncode": "I528", "weight": 0}, "400529": {"description": "Address is commercial", "identity": "HRA_ADD_COMMERCIAL", "reasoncode": "I529", "weight": 0}, "400530": {"description": "Address is a correctional institution", "identity": "HRA_ADD_CORRECTIONAL_INSTITUTION", "reasoncode": "I530", "weight": 0}, "400531": {"description": "Address is a hospital or clinic", "identity": "HRA_ADD_HOSPITAL", "reasoncode": "I531", "weight": 0}, "400532": {"description": "Address is a nursing home", "identity": "HRA_ADD_NURSING_HOME", "reasoncode": "I532", "weight": 0}, "400533": {"description": "Address is institutional", "identity": "HRA_ADD_INSTITUTIONAL", "reasoncode": "I533", "weight": 0}, "400534": {"description": "Address is a U.S. post office", "identity": "HRA_ADD_POST_OFFICE", "reasoncode": "I534", "weight": 0}, "400535": {"description": "Address is governmental", "identity": "HRA_ADD_GOVERNMENTAL", "reasoncode": "I535", "weight": 0}, "400536": {"description": "Address reported as suspicious", "identity": "HRA_ADD_SUSPICIOUS", "reasoncode": "I536", "weight": 0}, "400537": {"description": "Address is in a multi-unit building reported suspicious", "identity": "HRA_ADD_MU_SUSPICIOUS", "reasoncode": "I537", "weight": 0}, "400538": {"description": "Address reported misused and requires further investigation", "identity": "HRA_ADD_SUS_REQUIRES_INVESTIGATION", "reasoncode": "I538", "weight": 0}, "400539": {"description": "Address is a multi-unit building reported as misused and requires further investigation", "identity": "HRA_ADD_MU_REQUIRES_INVESTIGATION", "reasoncode": "I539", "weight": 0}, "400540": {"description": "Address reported used in true name fraud or credit fraud", "identity": "HRA_ADD_NAME_CREDIT_FRAUD", "reasoncode": "I540", "weight": 0}, "400541": {"description": "Address has been used xxx times in the last xx days on different inquiries", "identity": "HRA_ADD_SUSPICIOUS_ACTIVITY", "reasoncode": "I541", "weight": 0}, "400542": {"description": "Address has been reported more than once", "identity": "HRA_ADD_MULTIPLE_REPORTING", "reasoncode": "I542", "weight": 0}, "400543": {"description": "Address is a multi-unit building", "identity": "HRA_ADD_MU_BUILDING", "reasoncode": "I543", "weight": 0}, "400544": {"description": "Address requires further investigation", "identity": "HRA_ADD_REQUIRES_FURTHER_INVESTIGATION", "reasoncode": "I544", "weight": 0}, "400545": {"description": "Individual has been reported as a victim of true name or credit fraud", "identity": "HRA_VICTIM_OF_FRAUD", "reasoncode": "I545", "weight": 0}, "410411": {"description": "I_TEL_ISANSWERING_SERVICE", "identity": "I_TEL_ISANSWERING_SERVICE", "reasoncode": "I411", "weight": 0}, "410412": {"description": "I_TEL_ISCELLURLAR", "identity": "I_TEL_ISCELLURLAR", "reasoncode": "I412", "weight": 0}, "410413": {"description": "I_TEL_ISPUBLIC", "identity": "I_TEL_ISPUBLIC", "reasoncode": "I413", "weight": 0}, "410414": {"description": "I_TEL_ISCOMM_INST_GOV", "identity": "I_TEL_ISCOMM_INST_GOV", "reasoncode": "I414", "weight": 0}, "410421": {"description": "I_ADD_ISRECFRDSERVICE", "identity": "I_ADD_ISRECFRDSERVICE", "reasoncode": "I421", "weight": 0}, "410423": {"description": "I_ADD_ISCOMM_INST_GOV", "identity": "I_ADD_ISCOMM_INST_GOV", "reasoncode": "I423", "weight": 0}, "410424": {"description": "I_ADD_HIGHUSAGE", "identity": "I_ADD_HIGHUSAGE", "reasoncode": "I424", "weight": 0}}, "BOVAL": {"100131": {"description": "Timeout error", "identity": "BOSS_TIMEOUT", "weight": 0}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "weight": 0}, "600001": {"description": "Match", "identity": "MATCH", "weight": 0}, "600002": {"description": "Boss match with FC or PIPL match. if it exists and it's 0, means there are no match between FC+PIPL and LINKEDIN", "identity": "BOSS__FC_PI_IN_MATCH", "weight": 0}, "600003": {"description": "Boss match on no match situation for FC and PIPL", "identity": "BOSS_FC_PI_NO_MATCH", "weight": 0}, "600006": {"description": "Linkedin id missmatch", "identity": "LI_MISSMATCH", "weight": 0}, "confidence": 0}, "EMVAL": {"100113": {"description": "Domain is invalid or email format is bad", "identity": "DOMAIN_INVALID", "positive": false, "reasoncode": "I113", "weight": -2.01}, "100114": {"description": "Email can't be validated (permanent)", "identity": "EMAIL_CANT_VALIDATE", "weight": 0}, "100118": {"description": "Email validation failed", "identity": "EMAIL_VALIDATION_FAILED", "positive": false, "reasoncode": "I113"}, "100126": {"description": "Name part of email doesn't match first name last name", "identity": "FIRST_LAST_NAME_NO_MATCH", "weight": 0}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200125": {"description": "Email is valid", "identity": "EMAIL_VALID", "positive": true, "reasoncode": "I125", "weight": 2}, "confidence": 0.1}, "FBANL": {"100001": {"description": "InSufficientAccountAge", "identity": "FB_INSUFFICIENT_ACC", "positive": false, "reasoncode": "I131", "weight": 0}, "100003": {"description": "EducatedAtSameAsNegative", "identity": "FB_EDUCATE_SAME_NEGATIVE", "weight": 0}, "100004": {"description": "WorkedAtSameAsNegative", "identity": "FB_WORK_SAME_NEGATIVE", "weight": 0}, "100005": {"description": "LivesAtSameAsNegative", "identity": "FB_LIVES_SAME_NEGATIVE", "weight": 0}, "100006": {"description": "HometownAtSameAsNegative", "identity": "FB_HOME_TOWN_SAME_NEGATIVE", "weight": 0}, "100007": {"description": "NotEnoughPostsOnBirthday", "identity": "FB_NOT_ENOUGH_POST_BDAY", "reasoncode": "I133", "weight": 0}, "100008": {"description": "InsufficientTagsForFacebookProfile", "identity": "FB_INSUFFICIENT_TAG_PROFILE", "reasoncode": "I133", "weight": 0}, "100010": {"description": "TooManyTagsPerPhoto or NoTagsPerPhoto", "identity": "FB_TO_MANY_TAG_PHOTO", "reasoncode": "I133", "weight": 0}, "100012": {"description": "((PostFrequencyMean / No of Posts) * 100) greater than or equal to 8", "identity": "FB_POST_FRQ_GREATER_8", "reasoncode": "I133", "weight": 0}, "100013": {"description": "Too many or too few gender specific friends for their gender", "identity": "FB_MANY_OR_FEW_SPECIFIC_GENDER_FRIENDS", "reasoncode": "I132", "weight": 0}, "100014": {"description": "Users friends share less than or equal 8 friends on average", "identity": "FB_FRIEND_SHARE_LESS", "reasoncode": "I132", "weight": 0}, "100015": {"description": "AccountAge greater than or equal 5 months AND no of Friends less than or equal 50", "identity": "FB_AGE_GREATER", "reasoncode": "I134", "weight": 0}, "100016": {"description": "Name does not match email", "identity": "FB_NAME_DOES_NOT_MATCH", "weight": 0}, "100017": {"description": "Account appears abandoned", "identity": "FB_ACC_ABANDONED", "weight": 0}, "100018": {"description": "More than 10 timeline posts, but no personal comments or no personal likes", "identity": "FB_TIMELINE_POST", "reasoncode": "I133", "weight": 0}, "100019": {"description": "Account no longer exists", "identity": "FB_ACC_NO_EXIST", "positive": false, "reasoncode": "I130", "weight": 0}, "100020": {"description": "COUNT(100003, 100004, 100005, 100006) greater than or equal 3", "identity": "FB_NEGATIVE_COUNT_LESSER", "reasoncode": "I132", "weight": 0}, "100029": {"description": "Account suspended", "identity": "FB_ACC_SUSPENED", "positive": false, "reasoncode": "R131", "weight": 0}, "200002": {"description": "VerifiedPOSITIVE", "identity": "FB_VERIFIED_POSITIVE", "weight": 0}, "200003": {"description": "EducatedAtSameAsPOSITIVE", "identity": "FB_EDUCATE_SAME_POSITIVE", "weight": 0}, "200004": {"description": "WorkedAtSameAsPOSITIVE", "identity": "FB_WORK_SAME_POSITIVE", "weight": 0}, "200005": {"description": "LivesAtSameAsPOSITIVE", "identity": "FB_LIVES_SAME_POSITIVE", "weight": 0}, "200006": {"description": "HometownAtSameAsPOSITIVE", "identity": "FB_HOME_TOWN_SAME_POSITIVE", "weight": 0}, "200007": {"description": "EnoughPostOnBirthday", "identity": "FB_ENOUGH_POST_BDAY", "reasoncode": "I233", "weight": 0}, "200008": {"description": "SufficiantTagsForFacebookProfile", "identity": "FB_SUFFICIANT_TAG_PROFILE", "reasoncode": "I233", "weight": 0}, "200009": {"description": "AverageTagsPerPhoto", "identity": "FB_AVE_TAG_PHOTO", "weight": 0}, "200010": {"description": "((PostFrequencyMean / No of Posts) * 100) less than or equal 8", "identity": "FB_POST_FRQ_LESS_8", "reasoncode": "I233", "weight": 0}, "200011": {"description": "HasCoverPhoto true", "identity": "FB_HAS_COVER_PHOTO_TRUE", "weight": 0}, "200013": {"description": "Name matches email", "identity": "FB_NAME_MATCH", "weight": 0}, "200014": {"description": "Users friends share greater than or equal 18 friends on average", "identity": "FB_FRIEND_SHARE_GREATER", "reasoncode": "I232", "weight": 0}, "200019": {"description": "Has at least one close friend", "identity": "FB_HAS_ONE_CLOSE_FRIEND", "weight": 0}, "200020": {"description": "COUNT(200003, 200004, 200005, 200006) greter than or equal 3", "identity": "FB_POSSIVE_COUNT_GREATER", "reasoncode": "I232", "weight": 0}, "300001": {"description": "Account age in months", "identity": "FB_ACC_AGE_MONTHS", "weight": 0}, "300002": {"description": "Number of friends", "identity": "FB_NO_FRIENDS", "weight": 0}, "300003": {"description": "Number of posts", "identity": "FB_NO_POST", "weight": 0}, "300004": {"description": "Number of close friends", "identity": "FB_NO_CLOSE_FRIENDS", "weight": 0}, "300005": {"description": "Number of personal posts", "identity": "FB_NO_PESONAL_POST", "weight": 0}, "300006": {"description": "Number of personal comments", "identity": "FB_NO_PERSONAL_COMMENTS", "weight": 0}, "300007": {"description": "Average of number of posts per day of posting (non-posting days are ignored)", "identity": "FB_AVE_POST_PER_DAY", "weight": 0}, "300008": {"description": "Standard deviation of number of posts on days of posting (non-posting days are ignored)", "identity": "FB_STD_DEVIATION_NO_POST", "weight": 0}, "300009": {"description": "Number of things the user likes", "identity": "FB_NO_USER_LIKE", "weight": 0}, "300010": {"description": "Average number of days between the user liking something new", "identity": "FB_AVG_DAYS_USER_LIKE", "weight": 0}, "300011": {"description": "Standard deviation (in millis) of the above mean", "identity": "FB_STD_DEVIATION", "weight": 0}, "300012": {"description": "Mutual friends average", "identity": "FB_MUTAL_FRIEND", "weight": 0}, "300013": {"description": "isVerified (if present, it's true)", "identity": "FB_IS_VERIFIED", "weight": 0}, "300014": {"description": "educatedSameAs", "identity": "FB_EDUCATE_SAME", "weight": 0}, "300015": {"description": "workedSameAs", "identity": "FB_WORK_SAME", "weight": 0}, "300016": {"description": "livesSameAs", "identity": "FB_LIVES_SAME", "weight": 0}, "300017": {"description": "hometownSameAs", "identity": "FB_HOME_TOWN_SAME", "weight": 0}, "300018": {"description": "Number of posts on bday", "identity": "FB_NO_POST_BDAY", "weight": 0}, "300019": {"description": "Number of Profile tags", "identity": "FB_NO_PROFILE_TAG", "weight": 0}, "300020": {"description": "Number of photos", "identity": "FB_NO_PHOTOS", "weight": 0}, "300021": {"description": "Number of photos with tags", "identity": "FB_PHOTO_TAG", "weight": 0}, "300022": {"description": "has cover photo", "identity": "FB_COVER_PHOTOS", "weight": 0}, "300023": {"description": "gender from FB (0 for male. 1 for female)", "identity": "FB_GENDER", "weight": 0}, "300024": {"description": "male friend count", "identity": "FB_MALE_FRI_COUNT", "weight": 0}, "confidence": 0.075}, "FBVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0.25, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0}, "FBPERM": {"description": "Enough FB Permission available.", "identity": "FB_PERMISSION_AVAILABLE", "weight": 0}, "PERFECT_ID": {"weight": 0}}, "FCPIVAL": {"100132": {"description": "1st PIPL run sucked and 2nd PIPL run didn't add any perfect matches. (wasting our time on 2nd run ?)", "identity": "FCPI_PIPL_SUCKED"}, "100133": {"description": "FC and PIPL disagree with each other on FB", "identity": "FCPI_FC_PIPL_DISAGREE_FB"}, "100134": {"description": "Two PIPL runs disagree with each other on FB", "identity": "FCPI_TWO_PIPL_DISAGREE_FB", "positive": false, "reasoncode": "R140"}, "100135": {"description": "Two PIPL runs disagree with each other on LinkedIn", "identity": "FCPI_TWO_PIPL_DISAGREE_LI"}, "100136": {"description": "FC and PIPL disagree with each other on TW", "identity": "FCPI_FC_PIPL_DISAGREE_TW", "positive": false, "reasoncode": "R130"}, "confidence": 0.05, "weight": 0}, "FCVAL": {"100117": {"confidence": 0.1, "description": "FC no match", "identity": "FC_NO_MATCH", "weight": 0}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "100199": {"confidence": 0.15, "description": "FC address is not good", "identity": "FC_ADDRESS_NOT_GOOD", "weight": 0}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0}, "200128": {"confidence": 0.1, "description": "Number of non-social perfect matches", "identity": "NO_SOCIAL_PERFECT_MATCH", "positive": true, "reasoncode": "I127", "weight": 0.33}, "700011": {"confidence": 0, "description": "Fullcontact bad result. Should this fire 100117 instead ?", "identity": "FC_BAD_RESULT", "weight": 0}}, "FMVAL": {"100122": {"description": "Address fake", "identity": "ADDRESS_FAKE", "positive": false, "reasoncode": "I122", "weight": 0}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "100140": {"confidence": 0.1, "description": "If physical address is fake (address search doesn't find anything)", "identity": "FAKE_PHYSICAL_ADDRESS", "weight": -1}, "100142": {"description": "Phone number fake", "identity": "PHONE_NO_FAKE", "weight": 0}, "100143": {"description": "Phone number different country", "identity": "PHONE_NO_DIFF_COUNTRY", "weight": -1}, "100144": {"description": "Phone number does not match address by 500km", "identity": "PHONE_NO_UNMATCH", "weight": 0}, "100150": {"description": "Gender unknown", "identity": "GENDER_UNKWON", "weight": 0.2}, "200150": {"description": "Genderize probability that the user is female", "identity": "USER_FEMALE", "weight": 0}, "300666": {"description": "Age in years at time of Email API request ?", "identity": "AGE_YEAR", "weight": 0}, "500101": {"description": "State must be two characters", "identity": "FM_STATE_TWO_CHAR", "positive": false, "reasoncode": "V101", "weight": 0}, "500102": {"description": "Country must meet ISO two character standard", "identity": "FM_COUNTRY_ISO_STD", "positive": false, "reasoncode": "V102", "weight": 0}, "500103": {"description": "State is only permitted when country is US", "identity": "FM_STATE_ONLY_IN_US", "positive": false, "reasoncode": "V103", "weight": 0}, "500104": {"description": "Physical address requires at least zip/country or city/country", "identity": "FM_ADDRESS_NEED_ATLEAST_ZIP", "positive": false, "reasoncode": "V104", "weight": 0}, "500105": {"description": "Physical address and city in US requires at least zip or state", "identity": "FM_ADDRESS_NEED_ZIP", "positive": false, "reasoncode": "V105", "weight": 0}, "500106": {"description": "Country code is required unless all are specified (physical address, city, state, zip)", "identity": "FM_COUNTRY_CODE_NEED", "positive": false, "reasoncode": "V106", "weight": 0}, "500107": {"description": "Supported date or birth formats are yyyyMMdd, yyyy-MM-dd, yyyy/MM/dd", "identity": "FM_", "positive": false, "reasoncode": "V107", "weight": 0}, "500108": {"description": "IPAddress must meet IPv4 or IPv6 standard", "identity": "FM_IP_MEET_IP_STD", "positive": false, "reasoncode": "V108", "weight": 0}, "500109": {"description": "Geocode format is invalid", "identity": "FM_GEOCODE_INVALID", "positive": false, "reasoncode": "V109", "weight": 0}, "500110": {"description": "Supported phone number formats are +*********** or + **************", "identity": "FM_SUPPORT_PHONE_FORMAT", "positive": false, "reasoncode": "V110", "weight": 0}, "500111": {"description": "Missing Required fields firstname/surname", "identity": "FM_MISSING_FIRST_NAME", "positive": false, "reasoncode": "V111", "weight": 0}, "500112": {"description": "Missing Required fields provider", "identity": "FM_MISSING_PROVIDER", "positive": false, "reasoncode": "V112", "weight": 0}, "500116": {"description": "National ID is permitted,only when country is US", "identity": "FM_COUNTRY_NEEDED", "positive": false, "reasoncode": "V116", "weight": 0}, "500117": {"description": "Invalid driver license format", "identity": "FM_INVALID_DRIVING_LICENSE", "positive": false, "reasoncode": "V117", "weight": 0}, "500118": {"description": "Invalid national id format", "identity": "FM_INVALID_NATIONAL_ID", "positive": false, "reasoncode": "V118", "weight": 0}, "500119": {"description": "User is blacklisted", "identity": "FM_BLACKLISTED_USER", "positive": false, "weight": 0}}, "FSVAL": {"100131": {"description": "Timeout error", "identity": "FS_TIMEOUT", "weight": 0}, "200127": {"confidence": 0.1, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "reasoncode": "I127", "weight": 0}, "600001": {"description": "Match", "identity": "MATCH", "weight": 0}, "600002": {"description": "No Foursquare match", "identity": "NO_FS_MATCH", "weight": 0}, "600003": {"description": "API error", "identity": "FS_API_ERROR", "weight": 0}, "600004": {"description": "Error on Foursquare search", "identity": "ERROR_FS_SEARCH", "weight": 0}, "610001": {"description": "Number of Foursquare matches", "identity": "NO_FS_MATCH", "weight": -1}, "confidence": 0.1}, "GLOBAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "100137": {"description": "RISK  Search by email and by address on perfect match doesn't have any url in common", "identity": "GLOBAL_SEARCH_BY_EMAIL_PERFECT_MATCH_DOESNT"}, "100138": {"description": "RISK Search by email and by address on perfect and partial doesn't have any url in common", "identity": "GLOBAL_SEARCH_BY_EMAIL_PARTIAL_MATCH_DOESNT", "positive": false, "reasoncode": "R101", "weight": 0}, "100139": {"description": "Access token and email refers to different emails", "identity": "GLOBAL_SEARCH_BY_EMAIL", "positive": false, "reasoncode": "R114"}, "100140": {"description": "The two emails are linked to two different facebook accounts", "identity": "GLOBAL_EMAIL_DIFF_FB_ACC", "positive": false, "reasoncode": "R130", "weight": 0}, "100141": {"description": "The two emails are linked to two different twitter accounts", "identity": "GLOBAL_EMAIL_DIFF_TW_ACC", "positive": false, "reasoncode": "R140", "weight": 0}, "100142": {"description": "The two emails are linked to two different linkedin accounts", "identity": "GLOBAL_EMAIL_DIFF_IN_ACC", "positive": false, "reasoncode": "R150", "weight": 0}, "100143": {"description": "RISK Search by email doesn't provide any urls", "identity": "GLOBAL_ EMAIL_DOESNT_PROVIDE_URL", "positive": false, "reasoncode": "R115", "weight": -2}, "100144": {"description": "The two emails are linked to two different googleplus accounts", "identity": "GLOBAL_EMAIL_DIFF_GP_ACC", "positive": false, "reasoncode": "R160", "weight": 0}, "200127": {"description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "weight": 0}, "600001": {"description": "Match", "identity": "MATCH", "weight": 0}, "confidence": 0.05, "weight": 0}, "GPANL": {"100001": {"description": "Account age less than or equal 5 months", "identity": "GP_ACC_AGE_LESS_5_MONTHS", "positive": false, "reasoncode": "I161", "weight": 0}, "100002": {"description": "no activity count", "identity": "GP_NO_ACTIVITY_COUNT", "reasoncode": "I163", "weight": 0}, "100003": {"description": "no resharers", "identity": "GP_NO_RESHARER", "reasoncode": "I162", "weight": 0}, "100004": {"description": "no +1s at all", "identity": "GP_NO_LS_AT_ALL", "reasoncode": "I163", "weight": 0}, "100005": {"description": "NoReplyAtAllAnybodyListening", "identity": "GP_NO_REPLY", "reasoncode": "I162", "weight": 0}, "100006": {"description": "No companies listed", "identity": "GP_NO_COMPANY_LIST", "weight": 0}, "100007": {"description": "Has no profile picture", "identity": "HAS_NO_PROFILE_PIC", "positive": false, "reasoncode": "I164", "weight": 0}, "100008": {"description": "Has No birthday listed", "identity": "HAS_NO_BDAY_LIST", "weight": 0}, "100009": {"description": "Missing one of the above", "identity": "MISSING_ONE_ABOVE", "weight": 0}, "100019": {"description": "Account no longer exists", "identity": "ACC_NO_LONGER_EXIST", "positive": false, "reasoncode": "I160", "weight": 0}, "200001": {"description": "Account age more than 5 months", "identity": "GP_ACC_MORE_5_MONTHS", "weight": 0}, "200002": {"description": "at least one Activity Count", "identity": "GP_ONE_ACTIVITY_COUNT", "reasoncode": "I263", "weight": 0}, "200003": {"description": "at least one resharer", "identity": "GP_ONE_RESHARER", "weight": 0}, "200004": {"description": "at least one +1", "identity": "GP_LEAST_ONE", "reasoncode": "I263", "weight": 0}, "200005": {"description": "GooglePlus connections are good", "identity": "GP_ONE_REPLY", "reasoncode": "I262", "weight": 0}, "200006": {"description": "One or more companies listed", "identity": "GP_ONE_MORE_COMPANY_LIST", "weight": 0}, "200007": {"description": "Has profile pic", "identity": "HAS_PROFILE_PIC", "weight": 0}, "200008": {"description": "Has birthday listed", "identity": "HAS_BDAY_LIST", "weight": 0}, "200009": {"description": "Has fName , lName, email and gender", "identity": "HAS_NAME_GENDER", "weight": 0}, "300001": {"description": "Account age in months", "identity": "GP_ACC_AGE_MONTHS", "weight": 0}, "300002": {"description": "Activity Count", "identity": "GP_ACTIVITY_COUNT", "weight": 0}, "300003": {"description": "resharers", "identity": "GP_RESHARERS", "weight": 0}, "300004": {"description": "plusOners", "identity": "GP_PLUSONERS", "weight": 0}, "300005": {"description": "replies to activites", "identity": "GP_REPLIES", "weight": 0}, "300006": {"description": "Company count", "identity": "GP_COMPANY_COUNT", "weight": 0}, "confidence": 0.075}, "GPVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0.1, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0}, "400001": {"description": "Google plus match", "identity": "GP_MATCH", "weight": 0}, "400002": {"description": "No google plus match", "identity": "NO_GP_MATCH", "weight": 0}, "400003": {"description": "API error", "identity": "GP_API_ERROR", "weight": 0}, "400004": {"description": "Error on google plus search", "identity": "ERROR_GP_SEARCH", "weight": 0}, "410001": {"description": "Number of gplus matches", "identity": "NO_GP_MATCH", "weight": 0}, "confidence": 0.1}, "IMVAL": {"100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "800001": {"description": "Perfect profile image match found", "identity": "FACE_PERFECT_MATCH", "reasoncode": "I436", "weight": 0}, "800002": {"description": "Partial profile image match found", "identity": "FACE_PARTIAL_MATCH", "reasoncode": "I437", "weight": 0}, "800003": {"description": "No profile image match found", "identity": "FACE_NO_MATCH", "reasoncode": "I438", "weight": 0}, "800004": {"description": "Face match score", "identity": "FACE_MATCH_SCORE", "weight": 0}}, "LIANL": {"100001": {"description": "No education listed", "identity": "NO_EDU_LIST", "positive": false, "reasoncode": "I154", "weight": 0}, "100002": {"description": "Has no legit companies connected to them", "identity": "NO_LEGIT_COMPANY", "positive": false, "reasoncode": "I154", "weight": 0}, "100003": {"description": "Works for a company , but has no education listed", "identity": "WORKS_COMPANY_NOT_EDU_LIST", "positive": false, "reasoncode": "I153", "weight": 0}, "100004": {"description": "No birthday listed", "identity": "LI_NO_BDAY_LIST", "weight": 0}, "100005": {"description": "Less than 75 connections", "identity": "LESS_75_CONNECTION", "positive": false, "reasoncode": "I154", "weight": 0}, "100006": {"description": "Is an open connector", "identity": "IS_OPEN_CONNECTOR", "positive": false, "reasoncode": "I152", "weight": 0}, "100008": {"description": "worksameas less than or equal 4", "identity": "WORK_LESS_4", "positive": false, "reasoncode": "I152", "weight": 0}, "100009": {"description": "locationsameas less than or equal 4", "identity": "LOCATIONSAME_LESS_4", "positive": false, "reasoncode": "I152", "weight": 0}, "200002": {"description": "Has at least one legit company", "identity": "ONE_LEGIT_COMPANY", "weight": 0}, "200005": {"description": "More than 400 connections", "identity": "MORE_400_CONNECTION", "weight": 0}, "200006": {"description": "Is not an open connector", "identity": "NOT_OPEN_CONNECTOR", "positive": true, "reasoncode": "I252", "weight": 0}, "200007": {"description": "Has at least one recommendation", "identity": "HAS_ONE_RECOMMED", "positive": true, "reasoncode": "I252", "weight": 0}, "200008": {"description": "worksameas greater than 50", "identity": "WORK_GREATER_50", "positive": true, "reasoncode": "I252", "weight": 0}, "200009": {"description": "locationsameas greater than 50", "identity": "LOCATIONSAME_GREATER_50", "positive": true, "reasoncode": "I252", "weight": 0}, "200010": {"description": "Has at least one group", "identity": "HAS_ONE_GROUP", "positive": true, "reasoncode": "I253", "weight": 0}, "300002": {"description": "Number of Connections", "identity": "LI_NO_CONNECTION", "weight": 0}, "300003": {"description": "workSameAs", "identity": "LI_WORK_SAME", "weight": 0}, "300004": {"description": "livesSameAs", "identity": "LI_LIVES_SAME", "weight": 0}, "300006": {"description": "Number of Recommendations", "identity": "LI_NO_RECOMMEND", "weight": 0}, "300007": {"description": "Number of Groups", "identity": "LI_NO_GROUP", "weight": 0}, "987647": {"description": "number of viewed profiles", "identity": "LI_NUMBER_OF_VIEWED_PROFILES", "weight": 0}, "987648": {"description": "number of connections", "identity": "LI_NUMBER_OF_CONNECTIONS", "weight": 0}, "987649": {"description": "number of educations", "identity": "LI_NUMBER_OF_EDUCATIONS", "weight": 0}, "987650": {"description": "number of skills", "identity": "LI_NUMBER_OF_SKILLS", "weight": 0}, "987651": {"description": "number of courses", "identity": "LI_NUMBER_OF_COURSES", "weight": 0}, "987652": {"description": "duration of most recent experience", "identity": "LI_DURATION_OF_MOST_RECENT_EXPERIENCE", "weight": 0}, "987653": {"description": "average experience duration", "identity": "LI_AVERAGE_EXPERIENCE_DURATION", "weight": 0}, "987654": {"description": "has valid given/first name", "identity": "LI_HAS_FIRST_NAME", "weight": 0}, "987655": {"description": "has valid family name", "identity": "LI_HAS_FAMILY_NAME", "weight": 0}, "987656": {"description": "has valid full name", "identity": "LI_HAS_FULL_NAME", "weight": 1.1}, "987657": {"description": "currently employed", "identity": "LI_CURRENTLY_EMPLOYED", "weight": 0}, "987658": {"description": "number of days since last visit", "identity": "LI_NUMBER_OF_DAYS_SINCE_LAST_VISIT", "weight": 0}, "987660": {"description": "number of days since last update", "identity": "LI_NUMBER_OF_DAYS_SINCE_LAST_UPDATE", "weight": 0}, "987661": {"description": "Boolean as to whether headline is empty", "identity": "LI_HEADLINE_IS_EMPTY", "weight": -1.3}, "987662": {"description": "has profile image", "identity": "LI_HAS_PROFILE_IMAGE", "weight": 1.1}, "987663": {"description": "has industry", "identity": "LI_HAS_INDUSTRY", "weight": 1.2}, "987664": {"description": "number of certifications", "identity": "LI_NUMBER_OF_CERTIFICATIONS", "weight": 0}, "987665": {"description": "number of experiences", "identity": "LI_NUMBER_OF_EXPERIENCES", "weight": 0}, "confidence": 0.075}, "LIVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0.1, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0}}, "META": {"ACCOUNTID": {"weight": 0}, "INDUSTRYID": {"weight": 0}}, "PAVAL": {"100117": {"confidence": 0.1, "description": "Pipl no match", "identity": "PI_NO_MATCH", "weight": 0}, "100120": {"confidence": 0.05, "description": "Partial match only (no perfect)", "identity": "PA_ONLY_PARTIAL_MATCH", "weight": 0}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0}, "200128": {"confidence": 0.1, "description": "Number of non-social sources", "identity": "PA_NON_SOCIAL_SOURCE", "positive": true, "reasoncode": "I127", "weight": 0}, "300101": {"confidence": 0, "description": "Total number of sources if two partials become perfect", "identity": "PP_PARTIAL_MATCH", "weight": 0}}, "PBVAL": {"100117": {"confidence": 0, "description": "Pipl no match", "identity": "PB_NO_MATCH", "weight": -3}, "100120": {"confidence": 0, "description": "Partial match only (no perfect)", "identity": "PB_ONLY_PARTIAL_MATCH", "weight": 1.2}, "100130": {"description": "Internal Error", "identity": "PB_INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "PB_TIMEOUT", "weight": 0}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PB_PERFECT_MATCH_COUNT", "positive": true, "weight": 0}, "200128": {"confidence": 0, "description": "Number of non-social sources", "identity": "PB_NO_NON_SOCIAL_SOURCE", "positive": true, "reasoncode": "I127", "weight": 0}, "200201": {"confidence": 0, "description": "Number of names", "identity": "PB_NUMBER_OF_NAMES", "weight": 0}, "200203": {"confidence": 0, "description": "Number of usernames", "identity": "PB_NUMBER_OF_USERNAMES", "weight": 0}, "200204": {"confidence": 0, "description": "Number of phones", "identity": "PB_NUMBER_OF_PHONES", "weight": 0}, "200205": {"confidence": 0, "description": "Nominal feature with 2 values: male, female", "identity": "PB_GENDER", "weight": 0}, "200206": {"confidence": 0, "description": "Age (current date - DOB) in years", "identity": "PB_AGE", "weight": 0}, "200208": {"confidence": 0, "description": "Number of languages", "identity": "PB_NUMBER_OF_LANGUAGES", "weight": 0}, "200209": {"confidence": 0, "description": "Numebr of origin countries", "identity": "PB_NUMBER_OF_ORIGIN_COUNTRIES", "weight": 0}, "200210": {"confidence": 0, "description": "Number of addresses", "identity": "PB_NUMBER_OF_ADDRESSES", "weight": 0}, "200211": {"confidence": 0, "description": "Number of jobs", "identity": "PB_NUMBER_OF_JOBS", "weight": 0}, "200212": {"confidence": 0, "description": "Number of educations", "identity": "PB_NUMBER_OF_EDUCATIONS", "weight": 0}, "200213": {"confidence": 0, "description": "Number of relationships", "identity": "PB_NUMBER_OF_RELATIONSHIPS", "weight": 0}, "200214": {"confidence": 0, "description": "Number of user ids", "identity": "PB_NUMBER_OF_USER_IDS", "weight": 0}, "200215": {"confidence": 0, "description": "Number of images", "identity": "PB_NUMBER_OF_IMAGES", "weight": 0}, "200216": {"confidence": 0, "description": "Number of urls", "identity": "PB_NUMBER_OF_URLS", "weight": 0}, "200235": {"confidence": 0, "description": "One of names has person title", "identity": "PB_NAME_PREFIX", "weight": 1.2}, "200243": {"confidence": 0, "description": "Number of friends (friend relationships)", "identity": "PB_NUMBER_OF_FRIEND_RELATIONSHIPS", "weight": 0}, "200244": {"confidence": 0, "description": "Number of family relationships; number of relatives", "identity": "PB_NUMBER_OF_FAMILY_RELATIONSHIPS", "weight": 0}, "200245": {"confidence": 0, "description": "Number of work relationships; number of work contacts", "identity": "PB_NUMBER_OF_WORK_RELATIONSHIPS", "weight": 0}, "200246": {"confidence": 0, "description": "Number of other relationships", "identity": "PB_NUMBER_OF_OTHER_RELATIONSHIPS", "weight": 0}, "200247": {"confidence": 0, "description": "Background report urls", "identity": "PB_NUMBER_OF_BACKGROUND_REPORT_URLS", "weight": 0}, "200248": {"confidence": 0, "description": "number of contact details url", "identity": "PB_NUMBER_OF_CONTACT_DETAILS_URLS", "weight": 0}, "200249": {"confidence": 0, "description": "Number of email urls", "identity": "PB_NUMBER_OF_EMAIL_URLS", "weight": 0}, "200250": {"confidence": 0, "description": "Number of media urls", "identity": "PB_NUMBER_OF_MEDIA_URLS", "weight": 0}, "200251": {"confidence": 0, "description": "Number of personal profile urls", "identity": "PB_NUMBER_OF_PERSONAL_PROFILE_URLS", "weight": 0}, "200252": {"confidence": 0, "description": "Number of business urls", "identity": "PB_NUMBER_OF_BUSINESS_URLS", "weight": 0}, "200253": {"confidence": 0, "description": "Number of public record urls", "identity": "PB_NUMBER_OF_PUBLIC_RECORD_URLS", "weight": 0}, "200254": {"confidence": 0, "description": "Number of publication urls", "identity": "PB_NUMBER_OF_PUBLICATION_URLS", "weight": 0}, "200255": {"confidence": 0, "description": "Number of classmate urls", "identity": "PB_NUMBER_OF_CLASSMATE_URLS", "weight": 0}, "200256": {"confidence": 0, "description": "Number of webpage urls", "identity": "PB_NUMBER_OF_WEBPAGE_URLS", "weight": 0}, "200259": {"confidence": 0, "description": "form address country is different from returned country", "identity": "PB_LEAST_ONE_ADDRESS_DIFF_REGION", "weight": -1.1}, "200260": {"confidence": 0, "description": "Origin Country is different from primary/input address country", "identity": "PB_INPUT_COUNTRY_DIFF_ORIGIN_COUNTRY", "reasoncode": "I334", "weight": -1.8}, "200261": {"confidence": 0, "description": "Number of filtered persons passed into Entity resolution", "identity": "PB_NUMBER_OF_PERSONS_TO_ER", "weight": 0}, "300101": {"confidence": 0, "description": "Total number of sources if two partials become perfect", "identity": "PB_PARTIAL_PERFECT", "weight": 0}}, "PIVAL": {"100117": {"confidence": 0.1, "description": "Pipl no match", "identity": "PI_NO_MATCH", "weight": 0}, "100120": {"confidence": 0.05, "description": "Partial match only (no perfect)", "identity": "ONLY_PARTIAL_MATCH", "weight": 0}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0}, "200128": {"confidence": 0.1, "description": "Number of non-social sources", "identity": "NO_NON_SOCIAL_SOURCE", "positive": true, "reasoncode": "I127", "weight": 0}, "300101": {"confidence": 0, "description": "Total number of sources if two partials become perfect", "identity": "PI_PARTIAL_PERFECT", "weight": 0}}, "PPVAL": {"100117": {"confidence": 0.1, "description": "Pipl no match", "identity": "PP_NO_MATCH", "weight": 0}, "100120": {"confidence": 0.05, "description": "Partial match only (no perfect)", "identity": "PP_ONLY_PARTIAL_MATCH", "weight": 0}, "100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0}, "200128": {"confidence": 0.1, "description": "Number of non-social sources", "identity": "PP_NON_SOCIAL_SOURCE", "positive": true, "reasoncode": "I127", "weight": 0}, "200202": {"confidence": 0, "description": "Number of emails", "identity": "PP_NUMBER_OF_EMAILS", "weight": 0}, "200217": {"confidence": 0, "description": "Age of oldest mail", "identity": "PP_AGE_OF_OLDEST_EMAIL", "weight": 0}, "200218": {"confidence": 0, "description": "Age of newest emmail", "identity": "PP_AGE_OF_NEWEST_EMAIL", "weight": 0}, "200219": {"confidence": 0, "description": "Form email matches and age returned if available", "identity": "PP_INPUT_EMAIL_AGE", "weight": 0}, "200220": {"confidence": 0, "description": "Form email matches with disposable", "identity": "PP_INPUT_EMAIL_DISPOSABLE", "weight": 0}, "200221": {"confidence": 0, "description": "At least one received emails is a public domain email address (by PUBLIC provider)", "identity": "PP_LEAST_ONE_EMAIL_PUBLIC_DOMAIN", "reasoncode": "I315", "weight": 0}, "200222": {"confidence": 0, "description": "At least one received emails is disposable", "identity": "PP_LEAST_ONE_EMAIL_DISPOSABLE", "reasoncode": "I316", "weight": 0}, "200223": {"confidence": 0, "description": "Age of oldest username", "identity": "PP_AGE_OF_OLDEST_USERNAME", "weight": 0}, "200224": {"confidence": 0, "description": "Age of newest username", "identity": "PP_AGE_OF_NEWEST_USERNAME", "weight": 0}, "200225": {"confidence": 0, "description": "Age of oldest phone", "identity": "PP_AGE_OF_OLDEST_PHONE", "weight": 0}, "200226": {"confidence": 0, "description": "Age of newest phone", "identity": "PP_AGE_OF_NEWEST_PHONE", "weight": 0}, "200227": {"confidence": 0, "description": "Form phone matched and the age returned", "identity": "PP_INPUT_PHONE_AGE", "weight": 0}, "200228": {"confidence": 0, "description": "At least one phone from different region", "identity": "PP_LEAST_ONE_PHONE_FROM_DIFF_REGION", "reasoncode": "I321", "weight": 0}, "200229": {"confidence": 0, "description": "Age of oldest address", "identity": "PP_AGE_OF_OLDEST_ADDRESS", "weight": 0}, "200232": {"confidence": 0, "description": "Newest address age in years", "identity": "PP_AGE_OF_NEWEST_ADDRESS", "weight": 0}, "200233": {"confidence": 0, "description": "Age of oldest job ", "identity": "PP_AGE_OF_OLDEST_JOB", "weight": 0}, "200234": {"confidence": 0, "description": "Age of newest job", "identity": "PP_AGE_OF_NEWEST_JOB", "weight": 0}, "200236": {"confidence": 0, "description": "Atleast one name has first and last names", "identity": "PP_LEAST_ONE_NAME_HAS_FIRST_AND_LAST_NAMES", "weight": 0}, "200237": {"confidence": 0, "description": "Number of present names", "identity": "PP_NUMBER_OF_PRESENT_NAMES", "weight": 0}, "200238": {"confidence": 0, "description": "Number of maiden name - count for Female, NA for male", "identity": "PP_NUMBER_OF_MAIDEN_NAME", "weight": 0}, "200239": {"confidence": 0, "description": "Has at least one 'former' name", "identity": "PP_HAS_LEAST_ONE_FORMER_NAME", "weight": 0}, "200240": {"confidence": 0, "description": "Number of alias names", "identity": "PP_NUMBER_OF_ALIAS_NAMES", "weight": 0}, "200241": {"confidence": 0, "description": "Has current job title", "identity": "PP_HAS_RECENT_JOB_TITLE", "weight": 0}, "200242": {"confidence": 0, "description": "Has current industry; the industry of the current job", "identity": "PP_HAS_RECENT_INDUSTRY", "weight": 0}, "200257": {"confidence": 0, "description": "1 is perfect match; >1 is partial match", "identity": "PP_NUMBER_OF_PERSONS_IN_RESPONSE", "weight": 0}, "200258": {"confidence": 0, "description": "matched form address age from pipl", "identity": "PP_INPUT_ADDRESS_AGE", "weight": 0}, "300101": {"confidence": 0, "description": "Total number of sources if two partials become perfect", "identity": "PP_PARTIAL_MATCH", "weight": 0}}, "TWANL": {"100001": {"description": "Number of friends (user follows them, they follow the user) is less than or equal 49", "identity": "TW_NO_FRIENDS_LESSER", "reasoncode": "I142", "weight": -0.05}, "100002": {"description": "No profile picture", "identity": "TW_NO_PROFILE_PIC", "reasoncode": "I144", "weight": 0}, "100003": {"description": "Profile complete percent less 50%", "identity": "TW_PROFILE_COMLETE_LESSER_PERCENTAGE", "weight": 0}, "100004": {"description": "rule 100001 and tweet count greater than 50", "identity": "TW_TWEET_COUNT_LESSER", "reasoncode": "I143", "weight": -0.25}, "100005": {"description": "numTweets / numDayOfPosting greater than or equal 100", "identity": "TW_POSTING_LESSER", "weight": 0}, "100006": {"description": "following 1000+ users", "identity": "TW_FOLLOWING_GREATER", "reasoncode": "I142", "weight": -2.51}, "100007": {"description": "rule 100006 and does NOT have an AuthorityWebsite", "identity": "TW_HAS_NO_AUTH_WEBSITE", "reasoncode": "I142", "weight": 0}, "100008": {"description": "account age 4 months or less", "identity": "TW_ACC_AGE_LESSER", "positive": false, "reasoncode": "I141", "weight": 0}, "100009": {"description": "account not verified", "identity": "TW_ACC_NOT_VERIFIED", "weight": -2.01}, "100010": {"description": "no re-tweets", "identity": "TW_NO_RETWEETED", "reasoncode": "I143", "weight": -2.01}, "100011": {"description": "numRetweets less than 1", "identity": "TW_NO_TWEETS_LESSER", "weight": -1.51}, "100012": {"description": "rule 200008 and following less than 10 people", "identity": "TW_FOLLOWING_PEOPLE_LESSER", "reasoncode": "I144", "weight": -0.25}, "100015": {"description": "Has no value for any of the following profile image, name, firstName, lastName, gender, locale, and email", "identity": "TW_HAS_NO_DETAILS", "weight": 0}, "100016": {"description": "The word BOT appears in at least one tweet.(Highly likely for large number of tweets ?)", "identity": "TW_BOT_WORD", "weight": 0}, "100017": {"description": "freqMean / numTweets * 100 greater 8", "identity": "TW_FRQ_MEAN_GREATER", "weight": -0.25}, "100019": {"description": "Account no longer exists", "identity": "TW_ACC_NOT_EXISTS", "positive": false, "reasoncode": "I140", "weight": 0}, "100029": {"description": "Account suspended", "identity": "TW_ACC_SUSPENDED", "positive": false, "reasoncode": "R141", "weight": 0}, "200001": {"description": "Number of friends (user follows them, they follow the user) is greater than or equal 50", "identity": "TW_NO_FRIENDS_GREATER", "reasoncode": "I242", "weight": 1.1}, "200002": {"description": "Has a profile picture", "identity": "TW_HAS_PROFILE_PIC", "reasoncode": "I144", "weight": 1.1}, "200003": {"description": "Profile complete percent greater than or equal 50%", "identity": "TW_PROFILE_COMLETE_GREATER_PERCENTAGE", "weight": 0}, "200004": {"description": "rule 200001 and tweet count greater than or equal 50", "identity": "TW_TWEET_COUNT_GREATER", "reasoncode": "I243", "weight": 1.1}, "200005": {"description": "numTweets / numDayOfPosting less than 100", "identity": "TW_POSTING_GREATER", "weight": 1.1}, "200006": {"description": "following between 10 and 1000 users", "identity": "TW_FOLLOWING_LESSER", "reasoncode": "I242", "weight": 1.1}, "200007": {"description": "rule 100006 and has an AuthorityWebsite (so we gave you a -0.5, now you get +1)", "identity": "TW_HAS_AUTH_WEBSITE", "reasoncode": "I242", "weight": 0}, "200008": {"description": "account age 5 months or more", "identity": "TW_ACC_AGE_GREATER", "weight": 1.1}, "200009": {"description": "verified account", "identity": "TW_VERIFIED_ACC", "weight": 0}, "200010": {"description": "has been re-tweeted", "identity": "TW_HAS_RETWEETED", "reasoncode": "I243", "weight": 0}, "200011": {"description": "numRetweets greater than or equal 1", "identity": "TW_NO_TWEETS_GREATER", "weight": 1.1}, "200012": {"description": "Account age &lt;= 1 month and following less than 100 people", "identity": "TW_FOLLOWING_PEOPLE_GREATER", "reasoncode": "I144", "weight": 0}, "200015": {"description": "Has profile image, name, firstName, lastName, gender, locale, and email", "identity": "TW_HAS_DETAILS", "weight": 0}, "200016": {"description": "The word BOT never appears in a tweet", "identity": "TW_NO_BOT_WORD", "weight": 1.1}, "200017": {"description": "freqMean / numTweets * 100 less than or equal 8", "identity": "TW_FRQ_MEAN_LESSER", "weight": 1.1}, "300001": {"description": "Account age in months", "identity": "TW_ACC_AGE_MONTHS", "weight": 0}, "300002": {"description": "Number of tweets", "identity": "TW_NO_TWEETS", "weight": 0}, "300003": {"description": "Number of tweets retweeted at least once", "identity": "TW_NO_TWEETS_RETWEETS", "weight": 0}, "300004": {"description": "Number of tweets per day on a day of tweeting", "identity": "TW_NO_TWEET_PER_DAY", "weight": 0}, "300005": {"description": "Number of followers", "identity": "TW_NO_FOLLOWERS", "weight": 0}, "300006": {"description": "Number of accounts user follows", "identity": "TW_NO_ACC_USER_FOLLOW", "weight": 0}, "300007": {"description": "Number of mutual followings (user follows them, they follow user)", "identity": "TW_NO_MUTUAL_FOLLOWING", "weight": 0}, "300008": {"description": "Number of favorites", "identity": "TW_NO_FAV", "weight": 0}, "300009": {"description": "Active days", "identity": "TW_ACTIVE_DAYS", "weight": 0}, "300010": {"description": "Burst days", "identity": "TW_BURST_DAYS", "weight": 0}, "300011": {"description": "Frequency mean", "identity": "TW_FRQ_MEAN", "weight": 0}, "300012": {"description": "Standard deviation (in millis) of the above mean", "identity": "TW_STD_DEVIATION", "weight": 0}, "500001": {"description": "Twitter Profile Match", "identity": "TW_PROFILE_MATCH", "weight": 0}, "500002": {"description": "Account age calculated", "identity": "ACCOUNT_AGE_FOUND", "weight": 0}, "confidence": 0.075}, "TWVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200127": {"confidence": 0.25, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "positive": true, "weight": 0.5}, "500003": {"confidence": 0.1, "description": "if is verified", "identity": "TW_VERFIED", "weight": 0}}, "WLVAL": {"100130": {"description": "Internal Error", "identity": "INTERNAL_ERROR", "weight": 0}, "100131": {"description": "Timeout", "identity": "TIMEOUT", "weight": 0}, "200001": {"description": "No watch list match", "identity": "WATCH_LIST_NO_MATCH", "positive": true, "reasoncode": "I196", "weight": 0}}, "YOVAL": {"100131": {"description": "Timeout error", "identity": "YO_TIMEOUT", "weight": 0}, "200127": {"confidence": 0.1, "description": "Number of perfect matches", "identity": "PERFECT_MATCH_COUNT", "weight": 0}, "600001": {"description": "Match", "identity": "MATCH", "weight": 0}, "600002": {"description": "Doesn exist anymore", "identity": "YO_DOES_EXIST", "weight": 0}, "600003": {"description": "API error", "identity": "YO_API_ERROR", "weight": 0}, "600004": {"description": "Error on Yahoo search", "identity": "ERROR_YOHOO_SEARCH", "weight": 0}, "610001": {"description": "Number of Foursquare matches", "identity": "NO_YO_MATCH", "weight": 0}, "confidence": 0.1}}, "entityResolution": {"FCvFB": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FCvFM": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 1.1}, "gender": {"weight": 1.51}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 1.1}, "username": {"weight": 0}}, "FCvPA": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FCvPI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FMvFB": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FMvGP": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FMvPI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FMvTW": {"email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "name": {"weight": 1.01}}, "FSvFB": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FSvFC": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FSvFM": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FSvPA": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "FSvPI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "GPvFB": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "GPvFC": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "GPvFS": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "GPvLI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "GPvPA": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "GPvPI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "GPvYO": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "LIvFB": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "LIvFC": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "LIvFM": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "LIvFS": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "LIvPA": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "LIvPI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "LIvYO": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "PAvFB": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "PAvFC": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "PAvFM": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "geocode": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "PAvPI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "PIvFB": {"email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "name": {"weight": 0}}, "TWvFB": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "TWvFC": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 1.1}, "username": {"weight": 0}}, "TWvFS": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "TWvGP": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "TWvLI": {"email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "name": {"weight": 0}}, "TWvPA": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "TWvPI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "TWvYO": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "YOvFB": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "YOvFC": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "YOvFM": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "YOvPA": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "YOvPI": {"address": {"weight": 0}, "companyname": {"weight": 0}, "dob": {"weight": 0}, "email": {"weight": 0}, "gender": {"weight": 0}, "image": {"weight": 0}, "mobilenumber": {"weight": 0}, "name": {"weight": 0}, "username": {"weight": 0}}, "confidence": 0.075, "weight": 0}, "confidence": 0}, "reasoncode_descriptions": {"email": {"I109": "Provided email does not correlate", "I113": "Email validation failed", "I114": "Email cannot be validated", "I118": "Email does not exist", "I125": "Email validation succeeded", "R114": "Email address mismatch"}, "address": {"I110": "Provided address does not correlate", "I111": "Provided geocode does not correlate", "I122": "Provided address could not be found", "I123": "Provided address has low entity resolution", "I323": "Language associated with profile is different from primary address country", "I328": "No address found associated with profile", "I421": "Address is a mail receiving/forwarding service", "I422": "Address is non-residential", "I423": "Address is commercial/institutional/governmental", "I424": "Address has been used [##] times in the last [##] days on different inquiries", "I518": "Address, SSN or telephone number reported by more than one source", "I519": "Address is a mail receiving/forwarding service", "I520": "Address is a hotel/motel or temporary residence", "I521": "Address is a credit correction service", "I522": "Address is a camp site", "I523": "Address is a secretarial service", "I524": "Address is a check cashing service", "I525": "Address is a restaurant/bar/nightclub", "I526": "Address is a storage facility", "I527": "Address is an airport/airfield", "I528": "Address is a truck stop", "I529": "Address is commercial", "I530": "Address is a correctional institution", "I531": "Address is a hospital or clinic", "I532": "Address is a nursing home", "I533": "Address is institutional", "I534": "Address is a U.S. post office", "I535": "Address is governmental", "I536": "Address reported as suspicious", "I537": "Address is in a multi-unit building reported suspicious", "I538": "Address reported misused and requires further investigation", "I539": "Address is a multi-unit building reported as misused and requires further investigation", "I540": "Address reported used in true name fraud or credit fraud", "I541": "Address has been used xxx times in the last xx days on different inquiries", "I542": "Address has been reported more than once", "I543": "Address is a multi-unit building", "I544": "Address requires further investigation", "R425": "Address reported misused", "R426": "Address reported used in true name fraud or credit fraud"}, "phone": {"I321": "At least one secondary phone associated with profile is International", "I336": "Primary/Input phone associated with profile is International", "I411": "Input telephone number is an answering service", "I413": "Input telephone number is a public/pay phone", "I414": "Input telephone number is commercial/institutional/governmental", "I508": "Input telephone number is an answering service", "I509": "Input telephone number is a cellular phone", "I510": "Input telephone is a public/pay phone", "I511": "Input telephone number is commercial", "I512": "Input telephone number is institutional", "I513": "Input telephone number is governmental", "I514": "Input telephone number reported as suspicious", "I515": "Input telephone number reported misused and requires further investigation", "I516": "Input telephone number reported used in true name fraud", "I517": "Input telephone number requires further investigation", "R102": "Phone number provided is invalid", "R103": "Phone number location does not match address provided", "R104": "Phone number country does not match country provided", "R413": "Telephone number reported as suspicious", "R414": "Telephone number reported used in true name fraud"}, "ip": {"I112": "Provided ipaddress does not correlate"}, "image": {"I436": "Perfect profile image match found", "I437": "Partial profile image match found", "I438": "No profile image match found"}, "kyc": {"I301": "Pre-randomized SSN", "I302": "Randomized SSN", "I305": "Given state matches SSN state code", "I412": "Input telephone number is a cellular phone", "I501": "Input SSN reported as suspicious", "I502": "Input SSN reported misused and requires further investigation", "I503": "Input SSN reported used in true name fraud", "I504": "Input SSN is reported deceased", "I505": "Input SSN not issued by Social Security Administration", "I506": "Input SSN requires further investigation", "I507": "Input SSN used in death benefits claim for John Doe DOB (date) DOC: (date) ZIP Code where benefits were paid is (ZIP), most likely (City, state) ZIP Code Last residence is (ZIP), Most likely (City state,Address, SSN or Telelphone Number reported by more than one source)", "I545": "Individual has been reported as a victim of true name or credit fraud", "R170": "Given state does not match SSN state code", "R303": "Invalid SSN", "R304": "Credit check failure on Name", "R305": "Credit check failure on Date of Birth", "R306": "Credit check failure on Address", "R307": "Credit check failure on Mobile Number", "R308": "Credit check failure on Driver License", "R401": "SSN reported as suspicious and/or misused", "R402": "SSN reported used in true name fraud", "R403": "SSN reported deceased and/or used in death benefits claim", "R404": "Fraudulent SSN", "R435": "Person is deceased"}, "watchlist": {"I196": "No watchlist match", "R186": "Watchlist match"}, "others": {}, "social_network_profile": {"I100": "Insufficient account history", "I101": "Account information unverifiable", "I102": "Connections are unnatural", "I103": "Activity is unnatural", "I104": "Account unmaintained", "I105": "Identity in Alert List", "I106": "Provided firstname does not correlate", "I107": "Provided username does not correlate", "I108": "Provided companyname does not correlate", "I115": "No social network matches", "I117": "No external sources match", "I119": "Entity resolution failed", "I121": "Social network(s) match", "I126": "Name part of email", "I127": "External source(s) match", "I129": "Entity resolution succeeded", "I130": "Facebook account abandoned", "I131": "Facebook insufficient account history", "I132": "Facebook connections are unnatural", "I133": "Facebook activity is unnatural", "I134": "Facebook account unmaintained", "I140": "Twitter account abandoned", "I141": "Twitter insufficient account history", "I142": "Twitter connections are unnatural", "I143": "Twitter activity is unnatural", "I144": "Twitter account unmaintained", "I150": "LinkedIn account abandoned", "I151": "LinkedIn insufficient account history", "I152": "LinkedIn connections are unnatural", "I153": "LinkedIn activity is unnatural", "I154": "LinkedIn account unmaintained", "I155": "LinkedIn headline is empty", "I156": "LinkedIn number of skills > 10", "I157": "LinkedIn currently employed", "I158": "LinkedIn duration of most recent experience > 30 days", "I159": "LinkedIn has industry", "I160": "GooglePlus account abandoned", "I161": "GooglePlus insufficient account history", "I162": "GooglePlus connections are unnatural", "I163": "GooglePlus activity is unnatural", "I164": "GooglePlus account unmaintained", "I170": "Pinterest Days since last visit > 90 days", "I171": "Pinterest Days since last update > 180 days", "I172": "Pinterest Number of followers > 5", "I173": "Pinterest Number of following > 5", "I174": "Pinterest Number of social networks > 1", "I175": "Pinterest Number of Likes > 1", "I176": "Pinterest Number of Pins > 1", "I177": "Pinterest Number of Boards > 1", "I178": "Pinterest Profile has image", "I230": "Facebook account match", "I232": "Facebook connections are good", "I233": "Facebook activity is good", "I240": "Twitter account match", "I241": "Twitter good account history", "I242": "Twitter connections are good", "I243": "Twitter activity is good", "I244": "Twitter account is well maintained", "I250": "LinkedIn account match", "I251": "LinkedIn good account history", "I252": "LinkedIn connections are good", "I253": "LinkedIn activity is good", "I254": "LinkedIn account is well maintained", "I255": "LinkedIn days since last update > 180 days", "I256": "LinkedIn at least one education degree", "I257": "LinkedIn number of connections > 5", "I258": "LinkedIn days since last visit > 90 days", "I260": "GooglePlus account match", "I261": "GooglePlus good account history", "I262": "GooglePlus connections are good", "I263": "GooglePlus activity is good", "I264": "GooglePlus account is well maintained", "I317": "Age could not be determined", "I318": "Age of person < 21 years old", "I319": "Age of person > 80 years old", "I322": "At least one secondary phone associated with profile is less than 30 days old", "I325": "More than 1 Educational Degree found associated with profile", "I326": "More than 1 job found associated with profile", "I327": "More than 1 Phone found associated with profile", "I329": "No Educational Degree found associated with profile", "I330": "No employing organization found", "I331": "No job title found", "I332": "No profile image found", "I333": "One or more relationships found associated with profile", "I334": "Origin Country is different from primary address country", "R101": "Identity data provided linked to different identities", "R110": "Entity resolution failed for provided name", "R111": "Entity resolution failed for provided geocode", "R112": "Entity resolution failed for provided address", "R113": "Entity resolution failed for provided gender", "R115": "Poor email resolution", "R130": "Different Facebook IDs found", "R131": "Facebook account suspended", "R140": "Different Twitter IDs found", "R141": "Twitter account suspended", "R150": "Different LinkedIn IDs found", "R151": "LinkedIn account suspended", "R160": "Different GooglePlus IDs found", "R161": "GooglePlus account suspended"}, "validation": {"E101": "Data source lookup timed out", "V101": "State must be two characters", "V102": "Country must meet ISO two character standard", "V103": "State permitted only when country=US", "V104": "Physical address requires at least zip/country or city/country", "V105": "Physical address and city in US requires at least zip or state", "V106": "Country code required unless physical address, city, state, and zip ", "V107": "Supported date of birth formats are yyyyMMdd, yyyy-MM-dd, and yyyy/MM/dd", "V108": "IP address must meet IPV4 or IPV6 standard", "V109": "Invalid geocode format", "V110": "Supported phone number formats are +*********** and +**************", "V111": "Missing Required fields: firstname/surname", "V112": "Missing Required field: email", "V113": "Missing Required field: provider", "V114": "Missing Required field: <PERSON><PERSON><PERSON>", "V115": "Missing Required field: accesstokensecret", "V116": "National ID permitted only when country=US", "V117": "Invalid driver license format", "V118": "Invalid National ID format", "V119": "Invalid Image file"}}, "error_codes": {"701": "Missing Required field: email", "702": "Invalid geocode format", "703": "Supported date of birth formats are yyyyMMdd, yyyy-MM-dd, and yyyy/MM/dd", "704": "Country code required unless physical address, city, state, and zip are specified", "705": "IP address must meet IPV or IPV standard", "706": "State is only permitted when country=US", "707": "State must be two characters", "708": "Supported phone number formats are + and +   ", "801": "Possible blacklist reasons are <PERSON><PERSON>, Violated ToS, Suspected of fraud, Committed fraud, Chargeback <PERSON><PERSON>, First Payment Default <PERSON>, Identity Fraud and Fake ID.", "802": "Possible blacklist reasons are <PERSON><PERSON>, <PERSON>ted ToS, Suspected of fraud, ..", "803": "The user identity associated to this access token is not in any blacklist as a potential or known fraudulent identity. If you believe this is in error, <NAME_EMAIL> for resolution.", "804": "No user authorization found", "805": "Unable to find user.", "806": "The user identity associated to this access token is not in any blacklist as a potential or known fraudolent identity. If you believe this is in error, <NAME_EMAIL> for resolution.", "807": "Already added to blacklist", "808": "The user identity is blacklisted as <PERSON><PERSON>. If you believe this is in error, <NAME_EMAIL> for resolution.", "809": "Required Reason Code input missing", "810": "Your account has been created successfully. A Socure sales representative will be in touch with you shortly.", "811": "An account with this email address already exists", "901": "InternalServerException", "902": "MissingRequiredParametersException", "903": "MissingParameterOptionsException", "904": "InsufficientPermissionException", "905": "DomainNotAuthorizedException", "906": "Fraudscore internal Error", "907": "StillProcessingException", "908": "InvalidRequestParametersException", "909": "AccountNotActivatedException", "910": "NoAccountAssociatedException", "911": "InvalidTokenException", "912": "Missing Required fields: firstname/surname", "913": "Missing Required field: <PERSON><PERSON><PERSON>", "928": "Accounts with PII Masking cannot use blacklist functions"}}