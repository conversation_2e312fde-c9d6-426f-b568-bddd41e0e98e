package me.socure.docv

import me.socure.docv.client.DocvOrchestraClient
import me.socure.docv.common.dto.{AccountStrategyDto, AuthenticIdStrategyDto}
import org.mockito.Mockito.when
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.Future

class AuthenticIdResolverTest extends FunSuite with MockitoSugar with Matchers {
  val docvClient = mock[DocvOrchestraClient]

  val service = new AuthenticIdResolver
  service.docvOrchestraClient = docvClient

  val authStrategyDto = AuthenticIdStrategyDto(
    id= Some(6),
    strategyId= Some("dvs-Vo7Ge62usoUnoVIJ7HzD"),
    name= "Test_Strategy",
    keyType= "US",
    lenientLocationCode= "LLC01",
    strictLocationCode= "LLC02"
  )

  val updateAuthStrategyDto = AuthenticIdStrategyDto(
    id = Some(6),
    strategyId= Some("dvs-Vo7Ge62usoUnoVIJ7HzD"),
    name= "Test_Strategy",
    keyType= "International",
    lenientLocationCode= "LLC01",
    strictLocationCode= "LLC02"
  )

  val accStrategyDto = AccountStrategyDto(
    publicAccountId= "acc-c6WorgegGr",
    strategyId= "dvs-Vo7Ge62usoUnoVIJ7HzD"
  )

  val allStrategies = Seq(authStrategyDto, updateAuthStrategyDto)

  test("list all strategies"){
    when(docvClient.fetchAllAuthenticIdStrategies()) thenReturn(Future.successful(Right(allStrategies)))
    val res = service.fetchAllAuthenticIdStrategies()
    res shouldBe Right(allStrategies)
  }

  test("fetch associated strategy for an account"){
    val accountPublicId = "public-12345"
    when(docvClient.fetchAssociatedAuthenticIdStrategy(accountPublicId)) thenReturn(Future.successful(Right(authStrategyDto)))
    val res = service.fetchAssociatedAuthenticIdStrategy(accountPublicId)
    res shouldBe Right(authStrategyDto)
  }

  test("create a new strategy"){
    when(docvClient.addAuthenticIdStrategy(authStrategyDto)) thenReturn(Future.successful(Right(authStrategyDto)))
    val res = service.addAuthenticIdStrategy(authStrategyDto)
    res shouldBe Right(authStrategyDto)
  }

  test("update existing strategy"){
    when(docvClient.updateAuthenticIdStrategy(updateAuthStrategyDto)) thenReturn(Future.successful(Right(updateAuthStrategyDto)))
    val res = service.updateAuthenticIdStrategy(updateAuthStrategyDto)
    res shouldBe Right(updateAuthStrategyDto)
  }

  test("fetch the strategy"){
    when(docvClient.fetchAuthenticIdStrategy("DVS-12345")) thenReturn(Future.successful(Right(authStrategyDto)))
    val res = service.fetchAuthenticIdStrategy("DVS-12345")
    res shouldBe Right(authStrategyDto)
  }

  test("associate strategy to an account"){
    when(docvClient.associateAuthenticIdStrategy(accStrategyDto)) thenReturn(Future.successful(Right(true)))
    val res = service.associateAuthenticIdStrategy(accStrategyDto)
    res shouldBe Right(true)
  }


}
