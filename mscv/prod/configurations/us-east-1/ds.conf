#Spring social configuration
application.secureUrl="http://datasci.socure.com"  #FQDN to datasci service url to hit the api against from Admin.
facebook.longlived.accesstoken.url="https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token"
facebook.appid.url="https://graph.facebook.com/app?access_token="

#Mysql db configuration

db.driver="com.mysql.cj.jdbc.Driver"


db.url="*********************************************************************************************,account-service-datasci-cluster.cluster-cazlhli6ii9k.us-east-1.rds.amazonaws.com/socure?useUnicode=true&characterEncoding=UTF-8&loadBalanceBlacklistTimeout=5000&loadBalanceConnectionGroup=socuregroup&loadBalanceEnableJMX=true&autoReconnect=true&autoReconnectForPools=true"
db.username="socure-2018"
db.password="""ENC(pablSRgk65nvzYpnQCCbutYlwjXGJqNQmZsIvlNY9o3CRM0XmkDCyJ/dMCBafcpDUQn1hQ==)"""
db.defaultAutoCommit="true"

# Model Management DB config
mmdb.driver="com.amazonaws.secretsmanager.sql.AWSSecretsManagerMySQLDriver"
mmdb.jdbcUrl="jdbc-secretsmanager:mysql://model-management-ds.cazlhli6ii9k.us-east-1.rds.amazonaws.com:3306/model_management"
mmdb.dataSourceName=model-management-db
mmdb.user="rds/model-management/ds"
mmdb.maxIdleTime=900
mmdb.maxConnectionAge=3600
mmdb.forceUseNamedDriverClass="true"

mmdb.maxPoolSize=50
mmdb.minPoolSize=3
mmdb.initialPoolSize=3
mmdb.testConnectionOnCheckIn="true"
mmdb.testConnectionOnCheckOut="false"
mmdb.idleConnectionTestPeriod=20

mmdb.useNewDB="true"
mmdb.modelIdSize=10

#Hibernate pool
hibernate.c3p0.initialPoolSize=10
hibernate.c3p0.min_size=10
hibernate.c3p0.max_size=25

hibernate.connection.provider_class="org.hibernate.connection.C3P0ConnectionProvider"
hibernate.c3p0.timeout=100
#
hibernate.c3p0.max_statements=500
hibernate.c3p0.idle_test_period=90

#Newly added attribute
hibernate.c3p0.unreturnedConnectionTimeout=10
hibernate.c3p0.debugUnreturnedConnectionStackTraces="true"
hibernate.c3p0.acquireincrement=8
hibernate.c3p0.maxconnectionage=3600
hibernate.c3p0.validationQuery="SELECT 1"
hibernate.c3p0.testOnBorrow="true"
hibernate.c3p0.numHelperThreads=200
hibernate.c3p0.automaticTestTable="tbl_hibernate_c3p0TestTable"
hibernate.c3p0.testOnCheckin=true

#Hibernate JConsole
hibernate.generate_statistics="true"

#Memcache Servers
memcache.servers="vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com:11211"
opt.timeout=500

#Static resources like js, css, images url from s3 bucket
static.resource.url="/resources"

#Debug Settings
debugMode="true"

#Scoring Cache Properties
#
authscore.memcached.timout=300
#
authscore.low_confidence.memcached.timeout=300
#
authscore.high_confidence.memcached.timeout=300
#
authscore.last_scanned_days.cache.timeout=2


#Facebook throttling mail ID
fb.throttling.mail.to="Engineering <<EMAIL>>"


#Factual Credentials
factual.oauth.key="g1tCPVR41NlU96STdd5kw1LnYzHkCd5aAGoBfY3w"
factual.oauth.secret="GMHuY7NpNcNjnioQCepzhOSqKjynMaSZhLAXrot3"

#Scoring Component Memcache
component.email.memcached.timeout=600
component.form.memcached.timeout=600
component.fb.memcached.timeout=600
component.pipl.email.memcached.timeout=600
component.pipl.address.memcached.timeout=600
component.gplus.memcached.timeout=600
component.twitter.memcached.timeout=600
component.fullcontact.memcached.timeout=600
component.yahoo.memcached.timeout=600

#Scoring Component Database
db.component.email.timeout=600
db.component.form.timeout=600
db.component.fb.timeout=600
db.component.pipl.email.timeout=600
db.component.pipl.address.timeout=600
db.component.gplus.timeout=600
db.component.twitter.timeout=600
db.component.fullcontact.timeout=600
db.yahoo.memcached.timeout=600

#===============Mapquest Credentials===============
mapquest.oauth.key="""ENC(21J7DO3PFX/kF3wicfY3T+CKZsEpxS5jb5qEiuo4hfbqpnL4uqfVadIsRTNcRsRiTZAJ/T07FQ8augYHYpi6y6M8pA==)"""

#=============TowerData Integration=================
towerdata.apikey="""ENC(2p55hWU1xISr3ET3dkLkjax0TCS7xK27zGSNUmyvwwx265d0CZzsC8a0)"""

#==============FullContact Integration===============
fullcontact.apikey="""ENC(yirPC8WfgkN6uFKzgfDMDwN1WRkjs+ghSSRV4uxMtIpIouxqMYZGvj4EfFnw5OMX)"""
fullcontact.webhook.base.url="https://DO_I_NEED_THIS?datasci.socure.com/"

#AWS Profile Image Bucket

#============== Admin Specific Properties =================

application.authScore.baseUrl="https://datasci.socure.com"

marketing.url="http://datasci-www.socure.be/"

application.logoutUrl="http://datasci-admin.s3-website-us-east-1.amazonaws.com"

application.activation.url="https://datasci-dashboardv2.socure.com/#//#/activate"
application.forget.pass.url="https://datasci-dashboardv2.socure.com/#//reset_password/"

application.set.pass.url="https://datasci-dashboardv2.socure.com/#!/reset_password_token"

#static resources



#============== Admin Specific Properties =================

#============== Empire properties ================
#annotation.index = annotation.config

#0.name = activity
#0.factory = sesame

# You can obtain this file at http://inkdroid.org/journal/2009/12/22/hacking-oreilly-rdfa/
#0.files = catalog.rdf

#0.name = Empire-JPA-Repo
#0.factory = sesame
#0.repo=activity
#0.url=http://ec2-107-22-72-245.compute-1.amazonaws.com:8080/openrdf-sesame/
#============== Empire properties ================

cache.image.timeout=86400
hibernate.search.fuzzy.threshold=18
#==== Fraud Score Service API =======

#============= Hibernate Search ==============
#
#============= Hibernate Search ==============


#===Component Timeout Config=================
#
#
#
#
#
#
#
#
#
#
#
#
#

#
#
#


#=============== Thread pool configuration ==================
thread.pool.executor.pool.size=400
thread.pool.executor.max.pool.size=700
thread.pool.executor.queue.capacity=0
thread.pool.executor.keep.alive.seconds=1

#=============== Thread pool configuration ==================

#============= SLA ROLE =====================

component.default.timeout=4

default.sla.fmval.timeout=4
default.sla.fcval.timeout=4
default.sla.yoval.timeout=4
default.sla.pival.timeout=4
default.sla.paval.timeout=4
default.sla.boval.tiemout=4
default.sla.lival.timeout=4
default.sla.gpval.timeout=4
default.sla.fbval.timeout=4
default.sla.twval.timeout=4
default.sla.kycofacval.timeout=4

default.sla.api.v2.scheduler.timeout ="5000"
default.sla.api.v2.overall.timeout ="5400"
default.sla.api.system.overall.timeout=6000

#=======================================

highperformace.sla.fmval.timeout=2
highperformace.sla.fcval.timeout=2
highperformace.sla.yoval.timeout=2
highperformace.sla.pival.timeout=2
highperformace.sla.paval.timeout=2
highperformace.sla.boval.tiemout=2
highperformace.sla.lival.timeout=2
highperformace.sla.gpval.timeout=2
highperformace.sla.fbval.timeout=2
highperformace.sla.twval.timeout=2
highperformace.sla.kycofacval.timeout=2

highperformace.sla.api.v2.scheduler.timeout ="2400"
highperformace.sla.api.v2.overall.timeout ="2800"
highperformace.sla.api.system.overall.timeout=2900

#====================================================

#=============== PIPL Credentials ============
pipl.key="5gfch344n92dcrxvbhb4ys5j"
pipl.premium.key="""ENC(3KWT6kolPT/TTiZjKUE2cD/zOlKjAFf8r2dFJzz0ruThoQ4KrXlA7CB2cOgTUFenrnys3YfgbQI=)"""
pipl.match.criteria="matching"
pipl.partial.threshold=5
#=============== PIPL Credentials ============


#============= Entity Resolution Filter Config =============#
#
#
#
#
#
#
#
#
#
#
#

#============= Entity Resolution Filter Config =============#

#======App Environment==========#
appEnv="ds"
#======App Environment==========#

#======TPAuditStats==========================#
aws.tp.stats.bucket="mlpipe"
aws.tp.stats.prefix="thirdparty/datascience"
#=======TPAudit Stats=======================#

#============= Super Admin Specific Config =============
#
staticurl.super.admin="/resources"
isexternal.app="false"
#=====================================================

#================= Mail Config =================#
socure.support.email="<EMAIL>"
socure.sales.email="<EMAIL>"
mailgun.apikey="""ENC(NtZqIitOi89xHjGHKxLWifHSRZgs+3QjH5VUAvsqJ6luR+6YMWvr1MpzXuKxADXM/LjZ+6GJeWFlgOdrZRdJjaJ4/Xw=)"""
mailgun.domain="socure.com"
mailgun.from="Socure Support <<EMAIL>>"
#================= Mail Config =================#

## account service
account.service.url="http://account-service/"

#============= Mailgun config =============#
mailgun.endpoint="https://api.mailgun.net/v2/socure.com/messages"
mailgun.key="""ENC(nwdMFkMjymA9O+vkWrLq4KZAeptmP3iQ9DU9GOCith+ZLOx8bvmHVSOYgem/LpLzSCfpXN8n3DJAdG3d4ZuBoSXNYuM=)"""
mailgun.domain_name="socure.com"
#============= Datadog config =============#
#============= Model monitoring error reporting =============#
error.reporter.model.monitoring.subject="Model Monitoring Metrics Reporting Failure - DataScience"
error.reporter.model.monitoring.from="<EMAIL>"
error.reporter.model.monitoring.to="<EMAIL>"
error.reporter.model.monitoring.cc=""
error.reporter.model.monitoring.bcc=""
#============= Model monitoring error reporting =============#

#============= Model Mapping =============#
h2opredictor.service.url="http://h2o-predictor-service"
h2opredictor.service.endpoint2="http://h2o-predictor-service"
h2omlpredictor.service.url="http://h2o-ml-predictor"
h2omlpredictor.service.endpoint2 = "http://h2o-ml-predictor"
modelmanagement {
  endpoint="http://model-management"
  hmac {
    realm = "Socure"
    version = "1.0"
    strength = 512
    secret.refresh.interval = 5000
    aws.secrets.manager.id="model-management/ds/hmac"
  }
  enableModelManagement = "true"
}

#============= Audit Actioning =============#
sqsEndpoint="https://sqs.us-east-1.amazonaws.com/"
actionAuditS3BucketName=action-auditing-ds
actionAuditQueueName=action-auditing-ds
region="us-east-1"
# batchrun-ds
kmsKey="ENC(+bDbLWhw/2yGM3qYQOf+P0nXIvNDdW3yVtgHxxEXApuvAwEkfQ+l8faIvxX9uYwmhzmJCDCxIHHqMehfFV1h+f4J2qrX+UcU2AzZZmDX/+ck7WcapdcFVlBB9UHVRAUVAgQZKGlChPqI0B8=)"

#============= Audit Actioning =============#

s3.transaction_error_bucket="datasci-audit-errors"

#============= Client specific encryption =============#
# ds-kms-client
client.specific.encryption.aws.access.key="""ENC(SFGvjwLFUyzTWZkX+ET6pu/iFgcHYEU8p7WHj5oyagppVz7bsbQ/PcyQeuLglm5yvK1NNw==)"""
client.specific.encryption.aws.secret.key="""ENC(dqJexf+2UX+rzeBDC55PK9yPNc169dSRsu8h+amH972f7YO+wNOAI/FUo/ZvvyTjzl+ekVssnFjReWDTLeWAbrFjGK7/SYtk)"""
client.specific.encryption.encryption.context.account_id.key="socure_account_id"
client.specific.encryption.operation.timeout.ms=20000
client.specific.encryption.kms.ids.us-east-1="arn:aws:kms:us-east-1:************:alias/client-specific-encryption-ds"
client.specific.encryption.kms.ids.us-west-1="arn:aws:kms:us-west-1:************:alias/client-specific-encryption-ds"
#============= Client specific encryption =============#

#============= Image auditing bucket configuration =============#
idplus.audit.image.bucket="idplus-audit-ds"
idplus.audit.image.encryption.mode="aws:kms"
idplus.audit.image.kms.id="arn:aws:kms:us-east-1:************:alias/idplus-audit-ds"
#============= Image auditing bucket configuration =============#

#========== DATASCI SPECIFIC CONFIG BELOW HERE
account.lockout.max.bad.tries=3

account.lockout.reset.automatically="false"

account.lockout.reset.duration=1

account.lockout.reset.time.unit="DAYS"

account.lockout.reset.at.zero="true"

#Recaptcha Configuration
recaptcha.privatekey = "6LfGEwgTAAAAAFNaWC9Wwumw-HBqioJceicBR5s_"
recaptcha.publickey = "6LfGEwgTAAAAAPithJiufWOSfCifzwKN28Rgb_Yq"

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {
    maxRetries = 10

    primary {
      sqs {
        region=us-west-1
        transaction {
          queueName=transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-ds"
      }
      third-party {
        region=us-east-1
        bucket="mlpipe/thirdparty/datascience"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

#================ SP metadata endpoint ================#
saml2.sp.metadata {
  endpoint = "http://authentication-service"
  path = """ENC(Bys4gXjyWugLJVNz92YoJT4jlyMeVF4rMa3WcduWVKUnVWM1CqHYFYQnxxXI8z517jUOCUuwU672JrQ1VPE1rLqR4od5a+qKvhzfRl1g/HlO0Q==)"""
  token = """ENC(5FqxDwGlAXGKHiUxubK0aOcrnxmzdq/WjE3wwC1FbMxIj0uG0RsrT/sL0Pg6I7w5K/VjJzw5/zW1+eMiDrkqcnykYUlKoOpRwBYEoFKoFQhVIjnCg2GZa3z6OtKlVvNLIXWAtEf22uWIPt4/MLoBUJ+nZ7wYidlFdy3zcT8mMyXdfM/8)"""
}
#================ SP metadata endpoint ================#

transaction.auditing {
  endpoint = "http://transaction-auditing-service"
  hmac {
    secret.key="""ENC(qDnq5Gj7xT0gYSoLa/tiCY5pfIDhKEk0dmIU1+ISmvlFDCEoKaZVpM8xdvyFPB66IeoPZCWLARcFHC0=)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}

#================ File storage download config ================#
file.storage.download {
  endpoint = "http://file-storage-download"

  dynamic.control.center {
    s3 {
      bucketName="globalconfig-ds"
    }
    memcached {
      host = "vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    secret.key = """ENC(QXdofEAGpy6/8P+5ZjVbzmvieQQ1k1iA6HrEjHk5vYxHWoNYZx0f4FftDhKZaexpXHXwLCMtAdDuo7a0yUCZiQ==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ File storage download config ================#

#================ AlertList Maintainer =================#
alertlist.maintainer {
  endpoint = "http://alertlist-maintainer/"
  hmac {
    secret.key = """ENC(+kR79jMNfr1gG1/938IiJJyfeNx40/Ve7c6gkEzHIWr7+qzvmODga0/2sV2OnX6YCveQ8UbTGrrjFA==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ AlertList Maintainer =================#

#================ SAML ================#
saml {
  assertionConsumerServiceUrl = "https://superadmin.webapps.us-east-1.ds.socure.link/saml2/SSO"
  maxAuthenticationAge = 43200 # 12 hours
}

#================ SAML ================#

authentication.service {
    endpoint = "http://authentication-service"
    hmac {
      secret.key="""ENC(kh27SL0ZHNIwlPRbEjfbYrHQPHziMFbkvae9LkpCYmh3vk0+H/1Bc/EwJpD8DqM/9G2qt3tuLD0K3WRsWmm5Dw==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
}

#================ Account Service config ================#
account.service {
  endpoint="http://account-service"
  hmac {
    secret.key="""ENC(StTNOmkYqJQQPNk3lM2cDQSvc3IbgOTFTp+o9QPXqbWn7gL+7UU70hhW1jRRRnyYerJfFf6AiFHn2Of5/fD4BQ==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ Account Service config ================#

#================ Reasoncode Service config ================#
reasoncode.service {
  endpoint="http://reasoncode-service"
  hmac {
    secret.key="""ENC(0KGt50p61/OcfPPkbXPHKadFjcZWIXFOwhKDQFMjcm/8SJiSfZZ9q7wNR9J+prpInVij6FdH9AANaTLBlD/ekQ==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ Reasoncode Service config ================#

#================ Event Auditing Service =================#
event.auditing{
  security{
    endpoint = "http://event-auditing-service/api"
    hmac {
      realm="Socure"
      version=1.0
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="event-auditing/stage/hmac"
      secret.refresh.interval=5000
    }
  }
}

#================ Event Auditing Service =================#

#================ Batch Job Service ================#

batch.job {
  endpoint="http://batch-job-service"
  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "batchjob-service/ds/hmac"
    secret.refresh.interval = 5000
    realm="Socure"
    version = "1.0"
  }
}

#================ Batch Job Service ================#

#================ Rulecode Service config ================#
rulecode.service {
  endpoint = "http://rulecode-service"

  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="rulecode-service/ds/hmac"
    secret.refresh.interval=5000
  }
}
#================ Rulecode Service config ================#

#=================Rulecode Ingestion config =============#
rulecode.ingestion{
    aws{
        s3{
            bucketname="rulecode-audit-ds"
            kmsKey="arn:aws:kms:us-east-1:************:key/7c148caf-8dba-4277-81e2-51c7ae149a1b"
        }
    }
}
#=================Rulecode Ingestion config =============#

#=================Rulecode Studio config =============#

rulecode.studio {
    git{
        projectId = "235"
        access_token = """ENC(RvMBCm2I0RuTiDgS75ebJUyp4SncblrR9jhSiRYAfsl3an7yFROD9AvJEynGQhGS7zHSVg==)"""
        rulecodePath = "rulecode_definitions/"
        rulecodePathV2 = "rulecode_definitions_v2/"
        dependencyPath = "table_definitions/"
        branchName = "master"
        baseUrl = "https://gitlab-ee.us-east-vpc.socure.be/api/v4/projects/"
    }
   s3{
        audit{
            bucketname="rulecode-audit-ds"
            kmsKey="arn:aws:kms:us-east-1:************:key/7c148caf-8dba-4277-81e2-51c7ae149a1b"
            rulecodePath = "studio/rulecode_definitions/"
            rulecodePathV2 = "studio/rulecode_definitions_v2/"
            dependencyPath = "studio/table_definitions/"
        }
        config{
            bucketname="rulecode-config-ds"
            rulecodePath = "rulecode_definitions/"
            rulecodePathV2 = "rulecode_definitions_v2/"
            dependencyPath = "table_definitions/"
        }
        data{
            bucketname="rulecode-data-ds"
            bulkFilePath = "rulecode_create_data/"
        }
   }
   test{
        envUrl = "https://datasci.socure.com"
        inputBucketname="rulecode-data-ds"
        outputBucketname="batch-job-storage-ds"
        prefix = "studio-test/"
   }
}
#=================Rulecode Studio config =============#

#=================Document Manager config =============#
document.manager {
  document.manager {
    endpoint="http://document-manager"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager-service/ds/hmac"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host=vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com
      port=11211
    }
  }

  dynamic.control.center {
    s3 {
      bucketName="globalconfig-ds"
    }
    memcached {
      host = "vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }
}
#=================Document Manager config =============#
#=================Decision Service Config===============#
decision.service {
  endpoint = "http://decision-service"
}
#=================Decision Service Config===============#

#=================StepUp Service=====================#
stepUp.service {
  endpoint="https://stepup-datasci.socure.com"
  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "stepup/ds/hmac"
    secret.refresh.interval = 5000
    realm = "Socure"
    version = "1.0"
  }
}
#=================StepUp Service=====================#

#=================Sandbox Service Config===============#
sandbox.service {
  endpoint = "http://idplus-sandbox"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="idplus-sandbox/ds/hmac"
    secret.refresh.interval=5000
  }
}
#=================Sandbox Service Config===============#
#=================Docv Orchestra Service=====================#
 docvOrchestra {
     endpoint = "http://docv-orchestra-service/"
     hmac {
        realm="Socure"
        version = "1.0"
        strength=512
        aws.secrets.manager.id="dv-orchestra/ds/hmac"
      }
  }
#=================Docv Orchestra Service=====================#

#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "idplus-global-settings-ds"
  }
  memcached {
    host=vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com
    port=11211
    ttl="24 hours"
  }
}

#===================Control Center==========================#

#===================batchjob==========================#
batch.job {
  aws {
    s3 {
      bucket.name = "batch-job-storage-ds"
      kms.key = """ENC(gdC01mCgrWsiZ7Y5G4LkBJitzy15YTDPGdZ0BGMSo5BQFMAegxdg1uGxYmMNT+08mgf5WOnbeo5kwSylQWJ8NFLItdVz3a28tw+NAh0Jm9vxZnqNURGZC05QppcnNAg+havUXzjO+WKRMp0=)"""
    }
  }
}

#===================batchjob==========================#

#===================Dynamic Control Center==========================#

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-ds"
  }
  memcached {
    host=vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

#================ ThirdParty Reader Service config ================#
thirdparty.reader.service {
  endpoint = "http://thirdparty-reader-service"

  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="thirdparty-reader-service/ds/hmac"
    secret.refresh.interval=5000
  }
}
#================ ThirdParty Reader Service config ================#

#===================sftp==========================#
sftp {
 role="arn:aws:iam::************:role/socure-transfer-deliveries-users"
 homeDirectoryType="LOGICAL"
 region="us-east-1"
 serverId="s-1edb1c1a1fc442da8"
 target="client-datafile-ds/"
}
#===================sftp==========================#

txn.case.workflow.service {
  endpoint = "http://txn-case-workflow-service"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "txn-case-workflow-service/stage/hmac"
    secret.refresh.interval = 5000
  }
}

globalwatchlist.service {
  endpoint = "http://watchlist-private-service/api"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="watchlist-private-service/ds/hmac"
    secret.refresh.interval=5000
  }
}

entity.feedback.reader {
  endpoint = "http://entity-feedback-service"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "entity-feedback-service/stage/hmac"
    secret.refresh.interval = 5000
  }
}

superadmin.hmac="superadmin-backend/ds/hmac"

#===================docv-berbix==========================#
berbix.v1 {
   	endpoint=""
    hmac {
        secret.key="""ENC(gs9kZO7YjKbqbm3r6/Q5iTWy6MqjfjSRlN7uzvaKN6ddJFK3ya241N4cgUhOb+Ms/8vB1m661zpLCT7luSteLKYVnypk+n/qSLcqsA==)"""
        strength=512
        realm="Socure"
        version="1.0"
    }
}

cors{
    alloweddomains = ["https://superadmin.webapps.us-east-1.ds.socure.link/"]
}
launchdarkly {
  sdk.key = "ENC(km17vrqxsaSUojxhAXXl79HvyBy7f0PmzxscvOS3HQIsO5q2pOyME76ZHYl1QtwoPH6U6qHCg3YlVEPj58Q9aiaek04tjCwh)"
  use.fedramp.version = false
}

kyc {
  endpoint = "http://kyc-search-service"
  recordcorrection.endpoint = "http://transaction-resolved-entity-worker/es/record-correction"
}
