package me.socure.sftp

import com.typesafe.config.{Config, ConfigFactory}
import me.socure.account.client.sftp.AccountSftpUserClient
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountSftpUser
import me.socure.util.EnvUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component
import software.amazon.awssdk.services.transfer.TransferClient
import software.amazon.awssdk.services.transfer.model.{CreateUserRequest, HomeDirectoryMapEntry}

import java.util.concurrent.TimeUnit
import javax.annotation.PostConstruct
import scala.concurrent.Await
import scala.concurrent.duration.Duration
import scala.util.matching.Regex

@Component
class SFTPResolver {
  private val logger = LoggerFactory.getLogger(getClass)

  @Autowired
  @Qualifier("envUtil")
  var envUtil: EnvUtil = _

  @Autowired
  @Qualifier("transferClient")
  var transferClient: TransferClient = _

  @Autowired
  @Qualifier("accountSftpUserClient")
  var accountSftpUserClient: AccountSftpUserClient = _

  private var sftpConfig: Config = _

  private val sftpUserNamePattern = new Regex("^[\\w][\\w@.-]{2,99}$")

  def createSFTPUser(accountId: Long, publicAccountId: String, userName: String, sshKey: String): Either[ErrorResponse, Boolean] = {
    try{
      if(!validateSftpUserName(userName)){
        throw new Exception(s"Invalid SFTP Username, for Account:$accountId, SFTPUser:$userName")
      }

      val role = sftpConfig.getString("role")
      val homeDirectoryType = sftpConfig.getString("homeDirectoryType")
      val serverId = sftpConfig.getString("serverId")
      val target = sftpConfig.getString("target")
      val entry = sftpConfig.getString("entry")

      val homeDirectoryMapEntry = HomeDirectoryMapEntry.builder()
        .entry(entry)
        .target(s"$target$publicAccountId").build()
      val createUserRequest = CreateUserRequest.builder()
        .role(role)
        .userName(userName)
        .homeDirectoryType(homeDirectoryType)
        .homeDirectoryMappings(homeDirectoryMapEntry)
        .serverId(serverId)
        .sshPublicKeyBody(sshKey)
        .build()
      val createUserResponse = transferClient.createUser(createUserRequest)
      if(createUserResponse != null && createUserResponse.userName().equals(userName)) {
        Await.result(accountSftpUserClient.saveAccountSftpUser(accountId, userName), Duration.Inf)
      }else{
        throw new Exception(s"Create SFTP User failed for Account:$accountId, SFTPUser:$userName")
      }
    }catch{
      case t:Throwable =>
        logger.info("Error While Creating SFTP User, using AWS transfer ", t)
        Left(ErrorResponse(900, t.getMessage))
    }
  }

  def listSFTPUsers: Either[String, Seq[AccountSftpUser]] = {
    Await.result(accountSftpUserClient.listAccountSftpUsers(), Duration.create(40, TimeUnit.SECONDS)) match {
      case Right(sftpUsers) =>
        Right(sftpUsers)
      case Left(errorResponse: ErrorResponse) =>
        logger.info("Error while fetching SFTP users ", errorResponse)
        Left(errorResponse.message)
    }
  }

  def validateSftpUserName(userName: String): Boolean = {
    userName.matches(sftpUserNamePattern.regex)
  }

  @PostConstruct
  def initializeSftpConfig(): Unit = {
    sftpConfig  = ConfigFactory.parseProperties(envUtil.getProperties).getConfig("sftp")
  }
}
