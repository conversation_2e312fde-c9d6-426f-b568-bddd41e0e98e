import React from 'react';
import './App.css';
import Graph from './Graph';

const validURLRegex = /^v2\/kyc\/identity-graph\/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?$/

const validateGetTransactionID = (referrer:string): string|undefined => {
  const validated = validURLRegex.exec(referrer.split("/").slice(3).join("/"));
  if(validated) {
    return validated[1];
  } else {
    return undefined;
  }
}

function App() {
  const transactionID = validateGetTransactionID(document.referrer);
  if(transactionID) {
    return <Graph transactionID={transactionID} />
  } else {
    return <div>{"Invalid URL format"}</div>;
  }
}

export default App;
