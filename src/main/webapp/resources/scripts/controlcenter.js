var settingsFromS3 = "";
$(document).ready(function () {
    getControlCenterSettings();
});

showControlCenterViewOnly = function () {
    $('#settingsUploadForm :input').prop('disabled', true);
    $('#settingsUploadForm :button')[0].classList.add("disabled")

    var radioButtons = document.getElementById('controlCenterForm').elements;
    $.each(radioButtons, function(index, radio) {
            radio.setAttribute("disabled", true);
            radio.classList.add('disabled');
    });
}
getControlCenterSettings = function () {
    var url = 'superadmin/control_center/settings';
    var form = document.getElementById('control-center-frm');
    $.ajax(
        {
            url: url,
            type: 'GET',
            cache: false,
            contentType: "application/json",
            success: function (response) {
                var jsonResponse = JSON.parse(response).data;
                settingsFromS3 = jsonResponse;
                createTableRows(jsonResponse.toggleableSettings);
            },
            error: function (error) {
                console.log(error);
                alert("Error while fetching the settings");
            }
        });
}

createTableRows = function (toggleableSettings) {
    $('#settingsTableBody').children().remove();
    $.each(toggleableSettings, function (i, item) {
        var trHTML = '';
        trHTML += '<tr><td>'
        trHTML += '<label for="' + item.name + '">';
        trHTML += '<p>' + item.name + '</p>';
        trHTML += '</label>'
        trHTML += '</td>'
        trHTML += '<td>'
        trHTML += '<p>' + item.description + '</p>';
        trHTML += '</td>'
        trHTML += '<td>'
        trHTML += '<input class="radio" type="radio" value="true" name="' + item.name + '" onchange="toggleSetting(\'' + item.name + '\',\'' + item.description + '\',true)" />&nbsp;&nbsp;On&nbsp;&nbsp;';
        trHTML += '<input class="radio" type="radio" value="false" name="' + item.name + '" onchange="toggleSetting(\'' + item.name + '\',\'' + item.description +  '\', false)" />&nbsp;&nbsp;Off&nbsp;&nbsp;';
        trHTML += '</td>';
        trHTML += '</tr>';
        $('#settingsTableBody').append(trHTML);
    });

    $.each(toggleableSettings, function (i, item) {
        var radioButtonGroup = document.getElementsByName(item.name);
        if(item.value === true){
            radioButtonGroup[0].checked = true;
        }else{
            radioButtonGroup[1].checked = true;
        }

        if(canAccessControlCentreFlag === "View Only") {
            showControlCenterViewOnly();
        }
    });
}


toggleSetting = function(settingName, description, newValue) {
    var turnOnMessage = 'Are you sure you want to turn ON ' + settingName + '?'
    var turnOffMessage = 'Are you sure you want to turn OFF ' + settingName + '?' 
    var message = newValue === true ? turnOffMessage : turnOffMessage;
    var answer = window.confirm(message);
    if (answer) {
        var updatedObject = {
            name: settingName,
            value: newValue,
            description: description
        }
        updateControlCenterSettings(updatedObject);    
    }
}

uploadControlCenterSettings = function () {
    var url = 'superadmin/control_center/settings/upload';
    var form = document.getElementById('settingsUploadForm');
    if ($.trim($(form.file).val()).length == 0) {
        alert("Please provdie a valid JSON file to import");
        return;
    }
    $('#message').attr("style", "display:block");
    $('#message').html("<b>Uploading .....Please wait. This might take a few seconds.</b>");
    var formData = new FormData();
    formData.append('file', $(form.file).get(0).files[0]);

    $.ajax(
        {
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                alert("Settings Uploaded successfully");
                $('#message').attr("style", "display:none");
                form.reset();
                getControlCenterSettings();
            },
            error: function (response) {
                form.reset();
                if(response.status == 400){
                    try{
                        alert(response.responseJSON.msg);
                        $('#message').attr("style", "display:none");
                    } catch{
                        alert("Unable to upload settings.");
                        $('#message').attr("style", "display:none");
                    }
                }else{
                    alert("Unable to upload settings.");
                        $('#message').attr("style", "display:none");
                }
            }
        });

}

updateControlCenterSettings = function (settingToBeUpdated) {
    var url = contexturl + "/superadmin/control_center/settings/toggle";
    var formData = settingToBeUpdated;
    console.log(settingToBeUpdated);
    $.ajax({
            url: url,
            data: JSON.stringify(formData),
            type: 'POST',
            contentType: 'application/json',
            async: false,
            processData: false,
            success: function (data) {
                alert("Updated the setting successfully");
            },
            error: function (error) {
                alert("Some error occured while updating the setting");
                console.log(error);
            }
        });
};
