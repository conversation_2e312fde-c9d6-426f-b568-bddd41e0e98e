package me.socure.account.provisioning

import me.socure.model.AccountProducts
import me.socure.model.account.ProductProvisioningTypes
import me.socure.model.account.automation.{AccountProvisioningDetails, ProductConfiguration => PProductConfiguration}
import me.socure.superadmin.authentication.UserDetailsWithoutCredentials
import org.mockito.Mockito
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}
import org.scalatest.mockito.MockitoSugar.mock
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.{SecurityContext, SecurityContextHolder}

class AccountAutomationControllerTest extends FreeSpec with Matchers with BeforeAndAfter{

  private val accountAutomationResolver: AccountAutomationResolver = mock[AccountAutomationResolver]

  private val accountAutomationController: AccountAutomationController = new AccountAutomationController
  accountAutomationController.accountAutomationResolver = accountAutomationResolver

  before {
    Mockito.reset(accountAutomationResolver)
    val authentication = Mockito.mock(classOf[Authentication])
    val securityContext = Mockito.mock(classOf[SecurityContext])
    val userDetails: UserDetailsWithoutCredentials =  Mockito.mock(classOf[UserDetailsWithoutCredentials])
    Mockito.when(securityContext.getAuthentication).thenReturn(authentication)
    Mockito.stub(authentication.getPrincipal).toReturn(userDetails)
    Mockito.stub(userDetails.getUsername).toReturn("<EMAIL>")
    SecurityContextHolder.setContext(securityContext)
  }

  "Account Provisioning Details for account" - {
    "should get provisioning details for account" in {
      val expected = AccountProvisioningDetails(Some("Bundle One"),
        List(AccountProducts(1,"Watchlist-3.0",65, ProductProvisioningTypes.CASCADE,None,1,false,false,false,None,None,None,None)),
        PProductConfiguration(Some("*********"), None))
      Mockito.when(accountAutomationResolver.getAccountProvisioningDetails(1)).thenReturn(Right(expected))
      accountAutomationController.getAccountProvisioningInformation(1)
      Mockito.verify(accountAutomationResolver).getAccountProvisioningDetails(1)
    }

    "should fail to get provisioning for account" in {
      Mockito.when(accountAutomationResolver.getAccountProvisioningDetails(100)).thenReturn(Left("Failed to fetch provisioning details for Account"))
      accountAutomationController.getAccountProvisioningInformation(100)
      Mockito.verify(accountAutomationResolver).getAccountProvisioningDetails(100)
    }
  }
}
