<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
    <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css"
          href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
    <link rel="stylesheet" type="text/css"
          href="#{bucket.resourceURL}/styles/datepicker.css" />
    <link rel="stylesheet" type="text/css"
          href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/bootstrap.min.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/ZeroClipboard.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/TableTools.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/bootstrap-datepicker.js"></script>
    <script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>

    <script>
        bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		appenv = "#{bucket.appEnv}";
    </script>
    <meta charset="UTF-8"/>

    <style>
        .trns-table td{
            max-width:200px;
        }
        .input-append.date .add-on {
            height: 30px;
        }
    </style>

    <title>Audit Statistics</title>

    <script language="javascript">

        $(document).ready(function(){
            firstLoadingDone = false;
            $('#auditStatistics').hide();
            $('#byMonth').hide();
            $('#byDate').hide();

            formatDate = function(dateObj) {
                return dateObj.getFullYear()
                        + "-"
                        + (dateObj.getMonth() + 1 > 9 ? (dateObj
                                .getMonth() + 1) : "0"
                        + (dateObj.getMonth() + 1))
                        + "-"
                        + (dateObj.getDate() > 9 ? dateObj.getDate()
                                : "0" + dateObj.getDate());
            };

            formatMonth = function(dateObj) {
                return dateObj.getFullYear()
                        + "-"
                        + (dateObj.getMonth() + 1 > 9 ? (dateObj
                                .getMonth() + 1) : "0"
                        + (dateObj.getMonth() + 1))
                        + "-"
                        + ("01");
            };

            var lastMonthToday = new Date();
            lastMonthToday.setMonth(lastMonthToday.getMonth() - 1);

            var $sd = $('#lbStartDate');
            var $ed = $('#lbEndDate');

            $sd.attr('data-date', formatDate(lastMonthToday));
            $ed.attr('data-date', formatDate(new Date()));

            $sd.find('input').val(formatDate(lastMonthToday));
            $ed.find('input').val(formatDate(new Date()));

            $sd.datepicker();
            $ed.datepicker();

            var $sm = $('#lbStartMonth');
            var $em = $('#lbEndMonth');

            $sm.attr('data-date', formatMonth(lastMonthToday));
            $em.attr('data-date', formatMonth(new Date()));

            $sm.find('input').val(formatMonth(lastMonthToday));
            $em.find('input').val(formatMonth(new Date()));

            $sm.datepicker();
            $em.datepicker()

        })
        $(function() {
            $("#dateValue").change(function(){
                var dateValue = $('#dateValue').val();

                if(dateValue == "byDate" ){
                    $('#byDate').show();
                    $('#byMonth').hide();
                } else if(dateValue == "byMonth" ){
                    $('#byMonth').show();
                    $('#byDate').hide();
                } else {
                    $('#byMonth').hide();
                    $('#byDate').hide();
                }
            });

            $('#txnsubmit').click(function() {

                var dateValue = $('#dateValue').val();
                auditStatistics(dateValue);

            });

            $('#mthsubmit').click(function() {

                var dateValue = $('#dateValue').val();
                auditStatistics(dateValue);

            });

            function auditStatistics(dateValue) {
                var startDate;
                var endDate;


                if(dateValue == 'none' ){
                    alert('Please choose any one.');
                    $('#dateValue').focus();
                } else {

                    if(dateValue == 'byDate'){
                        startDate = new Date($('#lbStartDate').find('input').val());
                        endDate = new Date($('#lbEndDate').find('input').val());
                    } else {
                        startDate = new Date($('#lbStartMonth').find('input').val());
                        endDate = new Date($('#lbEndMonth').find('input').val());
                    }

                    var dat = {
                        startDate : startDate,
                        endDate : endDate,
                        dateValue : dateValue
                    };

                    var hasVal = function(a) {
                        return (typeof(a) !== 'undefined' &amp;&amp; a !== null &amp;&amp; (!hasVal(a.length) || a.length > 0));
                    };

                    var createRow = function(services, date, stats) {
                      var row = [date];
                        for(var i in services) {
                            var service = services[i];
                            if(hasVal(stats[service])) {
                                row.push(stats[service]);
                            } else {
                                row.push(0);
                            }
                        }
                        return row;
                    };

                    $.ajax({
                        url : contexturl + '/superadmin/1/audit_statistics',
                        //dataType : 'jsonp',
                        data : dat,
                        type : 'GET',
                        success : function(data) {

                            if(hasVal(data) &amp;&amp; hasVal(data.services) &amp;&amp; hasVal(data.stats)) {

                                var $tblAuditStats = $('#tblAuditStatistics');
                                var $sTblHead = $tblAuditStats.find('thead tr');
                                $sTblHead.empty();
                                var isFirstCol = true;
                                $sTblHead.append('<th class="sorting_asc" role="columnheader" tabindex="0" aria-controls="example" rowspan="1" colspan="1">Date/Month</th>');
                                $.each(data.services, function (key, col) {
                                    $sTblHead.append($(' <th class="sorting_asc" role="columnheader" tabindex="0" aria-controls="example" rowspan="1" colspan="1">' + col + '</th>'));
                                });

                                var rows = [];
                                for (var di in data.stats) {
                                    rows.push(createRow(data.services, di, data.stats[di]));
                                }

                                var renderDT = function (destroy) {
                                    if (destroy) {
                                        $tblAuditStats.find('tbody').empty();
                                        $tblAuditStats.dataTable().fnDestroy();
                                    }
                                    $tblAuditStats
                                            .dataTable(
                                                    {
                                                        'aaData': rows,
                                                        'bProcessing': true,
                                                        'bDestroy': true,
//                                                    'bAutoWidth': true,
//                                                    'sScrollX': '100%',
                                                        'aaSorting': [
                                                            [0, 'asc']
                                                        ]
                                                    });
                                };

                                renderDT(true);
                                $tblAuditStats.wrap('<div style="width: 100%; overflow: auto;"></div>');
                                $sTblHead.find('th:first').width('163px');
                                $tblAuditStats.find('tbody tr td:first').width('163px');
                                $('#auditStatistics').show();
                            } else {
                                alert('No data found');
                            }
                        }

                    });
                }

            }
        });

    </script>
</h:head>
<h:body>

    <iframe width="1" height="1" frameborder="0" src="" id="downloadHelper" style="display:none;"></iframe>

    <div align="center">
        <font color="blue"><h3>
            <h:outputText value="#{param['message']}" />
        </h3> </font>
    </div>
    <br/>
    <br/>
    <div align="right">
        <h5>
            <h:outputLink value="industry">Manage Industry</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="active_users">Active Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="inactive_users">Inactive Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;


            <h:outputLink value="logout">Logout</h:outputLink>
            &nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
    </div>
    <br/>
    <br/>
    <center><h3>Audit statistics</h3></center>
    <hr/>

    <div class="container-fluid">
        <div class="row-fluid">
            <div class="col-lg-12">
                <div class="form-group row">
                    <label for="dateValue" class="col-sm-2 col-form-label">Search Type</label>
                    <div class="col-sm-10">
                        <select id="dateValue">
                            <option value="none">-- Select --</option>
                            <option value="byDate">Date</option>
                            <option value="byMonth">Month</option>
                        </select>
                    </div>
                </div>

                <div class="form-group row" id="byDate">
                    <label for="dateValue" class="col-sm-2 col-form-label">Range</label>
                    <div class="col-sm-10">
                        Start Date: <div class="input-append date" id="lbStartDate" data-date-format="yyyy-mm-dd">
                        <input class="span8" size="16" type="text" value=""/>
                        <span class="add-on"><i class="icon-th"></i></span>
                    </div>

                        End Date: <div class="input-append date" id="lbEndDate"
                                       data-date-format="yyyy-mm-dd">
                        <input class="span8" size="16" type="text" value=""/>
                        <span class="add-on"><i class="icon-th"></i></span>
                    </div>
                    </div>
                </div>

                <div class="form-group row" id="byMonth">
                    <label for="dateValue" class="col-sm-2 col-form-label">Range</label>
                    <div class="col-sm-10">
                            Start Date: <div class="input-append date" id="lbStartMonth" data-date-format="yyyy-mm-dd">
                            <input class="monthPicker span8" size="16" type="text" value=""/>
                            <span class="add-on"><i class="icon-th"></i></span>
                        </div>

                            End Date: <div class="input-append date" id="lbEndMonth"
                                           data-date-format="yyyy-mm-dd">
                            <input class="span8" size="16" type="text" value=""/>
                            <span class="add-on"><i class="icon-th"></i></span>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-sm-10">
                        <button id="txnsubmit" type="submit" class="btn btn-primary">Search</button>
                    </div>
                </div>
                <div id="auditStatistics" align="center" >
                    <table id="tblAuditStatistics" border="1"  class="table table-striped table-bordered dataTable" width="100%">
                        <thead>
                        <tr role="row">

                        </tr>
                        </thead>
                    </table>
                </div>

            </div>
        </div>
    </div>
</h:body>
</html>