package me.socure.account.provisioning

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class BundleManagerResolver {

  private val logger = LoggerFactory.getLogger(classOf[BundleManagerResolver])

  val bundles = Set(
    Bundle("Bundle 1",
      "Fraud Score, Alert List, Email RiskScore, Phone RiskScore, Device Risk, Synthetic, Enforce Segmentation, Enable Customer Facing Analytics",
      Set(33, 52, 29, 30, 71, 37, 188, 99, 244, 207)),
    Bundle("Bundle 2",
      "Fraud Score, Alert List, Email RiskScore, Phone RiskScore, Address RiskScore, KYC, Device Risk, Synthetic, Enforce Segmentation, Enable Customer Facing Analytics",
      Set(33, 52, 29, 30, 28, 13, 71, 37, 188, 99, 244, 207)),
    Bundle("Bundle 3",
      "Fraud Score, Alert List, Email RiskScore, Phone RiskScore, Address RiskScore, KYC, Device Risk, Watchlist, Synthetic, Enforce Segmentation, Enable Customer Facing Analytics",
      Set(33, 52, 29, 30, 28, 13, 71, 65, 37, 188, 99, 244, 207)),
    Bundle("Bundle 4",
      "Fraud Score, Alert List, Email RiskScore, Phone RiskScore, Address RiskScore, KYC, Device Risk, Watchlist, Synthetic, Enforce Segmentation, Enable Customer Facing Analytics",
      Set(33, 52, 29, 30, 28, 13, 71, 65, 37, 188, 99, 244, 207)),
    Bundle("Bundle 5",
      "Fraud Score, Alert List, Email RiskScore, Phone RiskScore, Address RiskScore, KYC, Device Risk, Watchlist, Synthetic, Enforce Segmentation, Enable Customer Facing Analytics",
      Set(33, 52, 29, 30, 28, 13, 71, 65, 37, 188, 99, 244, 207)),
    Bundle("International", "ID+ 3.0, Admin Dashboard, Device Risk, KYC, Watchlist, Document Verification, Decision Module, International Expansion Enabled,  Watchlist Monitoring, Watchlist Case Management, Enforce Segmentation, Enable Customer Facing Analytics",
      Set(37, 188, 71, 13, 65, 35, 56, 174, 183, 169, 244, 207)),
    Bundle("Prospects",
      "Fraud Score, Alert List, Email RiskScore, Phone RiskScore, Address RiskScore, KYC, Device Risk, " +
        "Watchlist, Synthetic, Document Verification, Decision Module, Activate BB backend, BBCertification, BBProduction, " +
        "Activate Capture App V3, Certification - Capture App V3, Production - Capture App V3, Enable NIST liveness, KYC Plus, Digital Intelligence, Account Intelligence, Name vs Address Correlation Score, Name vs Email Correlation Score, Name vs Phone Correlation Score",
      Set(33, 52, 29, 30, 28, 13, 71, 65, 37, 188, 99, 35, 56, 195, 196, 197, 201, 202, 203, 128, 176, 235, 171, 41, 42, 43)),
      Bundle("Public Sector",
      "ID+ 3.0, Dashboard, Sub Accounts, Can Administer SubAccounts, Sub Accounts can create users, Switch from Dashboard v2 to v3, Allow Customer File Upload, " +
      "Batch Job, Allow Export Transaction Search Results, Allow Export Event Audit, Bulk Processing, Enable Bulk User Creation, Device Risk, Device Fingerprint, " +
      "Device Risk v2, Behavioral Profiling, Fraud Score, Phone Risk Score, Allow Name and Phone Risk Correlation Association, Email Risk Score, " +
      "Allow legacy email risk and correlation associations, Alert List, Address Risk Score, KYC, Allow Age-Related Features (I350,I351,I352 and R354), " +
      "Synthetic, Decision Module, Use Decision Service, Decision Module Details, Decision Self Service UI, Document Verification, Activate DocV V2 backend, " +
      "Certification, Production, Show Submitted Documents, Expose Document OCR data, Document Verification for Call Center Support, New Capture App Interface, " +
      "Production, Certification, Enable DocV Orchestra, Document Verification Subscription, Enable NIST liveness, Activate BB Backend, Production, Certification, " +
      "Activate Capture App v3, Certification, Production",
      Set(37, 188, 25, 95, 96, 127, 88, 185, 75, 151, 200, 199, 71, 62, 131, 177, 33, 30, 60, 29, 55, 52, 28, 13, 120, 99, 56, 103, 111, 168, 35, 179, 181, 182, 59,
        58, 69, 112, 114, 113, 129, 184, 128, 195, 196, 197, 201, 202, 203))
    )

  def getBundleInformation: Set[Bundle] = {
    bundles
  }

}
