<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:c="http://java.sun.com/jsp/jstl/core">
<h:head>
	<link rel="stylesheet" type="text/css"
		  href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		  href="#{bucket.resourceURL}/styles/datepicker.css" />
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

	<script src="resources/scripts/bootstrap.min.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/ZeroClipboard.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/TableTools.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/bootstrap-datepicker.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
			src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.js"></script>
	
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		media="print" />
	<script>
		bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		loggedinUsername = "#{loggedinUsername}";
		appenv = "#{bucket.appEnv}";
	</script>

	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
	<script src="https://js-stage.dvnfo.com/devicer.min.js"></script>
	<script language="javascript">

	    window.onload = function() {
			if(DD_RUM) {
			  DD_RUM.startView({name: "/inactive_users"});
			  DD_RUM.setUser({ "id": loggedinUsername});
			}
        }

		$(function() {

			// add multiple select / deselect functionality
			$("#selectall").click(function() {
				//if(this.checked){
				//	$('.case').prop('checked', true);
				//}else{
					$('.case').prop('checked', this.checked);
				//}
				
			});

			// if all checkbox are selected, check the selectall checkbox
			// and viceversa
			$(".case").click(function() {

				if ($(".case").length == $(".case:checked").length) {
					$("#selectall").attr("checked", "checked");
				} else {
					$("#selectall").removeAttr("checked");
				}

			});
		});
	</script>
	<script type="text/javascript">
		$(document).ready(function() {
			$('#tbl_inactive_users').dataTable( {
				"aaSorting": [[ 1, "asc" ]]
			} );
			$('.dataTables_filter input').addClass('form-control');
			var deviceOptions = {
                publicKey: '5fbc1ca0-2f99-4fdb-ba54-0b06db002416',
                endpoint: 'https://stage.dvnfo.com/api/ingestion',
				isConsenting: true
			};
            devicer.run(deviceOptions, function(response) {
                $("#fingerprint-notice").css({"color": "green"});
			});

			$('#tbl_inactive_users_filter input').unbind();


  		    const processChange = debounce((val) => search(val));
		    $('#tbl_inactive_users_filter input').bind('keyup', function(e) {
			  processChange($(this).val())
			  DD_RUM.track("Search Inactive Account", {searchVal: $(this).val(), });
		    });
		  });

          function debounce(func, timeout = 300){
            let timer;
            return (...args) => {
              clearTimeout(timer);
              timer = setTimeout(() => { func.apply(this, args); }, timeout);
            };
          }

          function search(val) {
	         var inactiveUserTable = $('#tbl_inactive_users').dataTable()
	         inactiveUserTable.fnFilter(val);
		     if(val == '') {
		       $("#tbl_inactive_users_info").show()
		     } else {
		       $("#tbl_inactive_users_info").hide()
		     }
          }

		function validate() {
			var selected = new Array();
			var email = '';
			var noUserAccounts = new Array();
			$("input:checkbox[name=case]:checked").each(function() {
			    var hasUsers = $(this).val().split(':')[2];
			    if(hasUsers === 'false')
			        noUserAccounts.push($(this).val().split(':')[1]);
				else
					selected.push($(this).val().split(':')[0]);
			});
			email = selected.toString();
			if(noUserAccounts.length > 0){
                alert('Sending Activation failed: At least one of the accounts selected do not have associated email id: ' + noUserAccounts.toString());
			}
			else if (email !== '') {
				$
						.ajax({
							url : "send_activation",
							data : {
								email : email,
								async : false
							},
							type : 'GET',
							success : function(data) {
								$('#notification').text(data);
								$('input:checkbox[name=case]').attr('checked',
										false);
								window.location.href = 'inactive_users?message='
										+ data;
							},
							error : function(data){
								window.location.href = 'logout';
							}

						});
						DD_RUM("Send Activation Link", { userEmail: email });
			} else {
				alert('Please select at least one user to activate');
			}
		}

		function deleteUser() {
			var selected = new Array();
			var accountId = '';
			$("input:checkbox[name=case]:checked").each(function() {
				selected.push($(this).val().split(':')[1]);
			});
			accountId = selected.toString();
			if (accountId != '') {

				if (confirm("The account may have associated user profiles. Are you sure to delete?")) {

					window.location.href = 'delete?account_id=' + accountId;
				}
				DD_RUM.track("Delete User(s)", { accountIds: accountId });
			} else {
				alert('Please select at least one user to delete');
			}
		}

        function activateAccounts() {
            var selected = new Array();
            var accountids = '';
            $("input:checkbox[name=case]:checked").each(function() {
                selected.push($(this).val().split(':')[1]);
            });
            accountids = selected.toString();
            if (accountids != '') {
                $.ajax({
                    url : "activate_accounts",
                    data : {
                        accountids : accountids
                    },
                    type : 'POST',
                    success : function(data) {
                        $('#notification').text(data);
                        $('input:checkbox[name=case]').attr('checked',
                            false);
                        window.location.href = 'inactive_users?message='
                            + data;
                    },

                });
                DD_RUM.track("Activate User(s)", { accountIds: accountids });
            } else {
                alert('Please select at least an account to activate');
            }
        }
	</script>

</h:head>

<h:body>
	<div align="center">
		<font color="blue"><h3>
				<h:outputText value="#{param['message']}" />
			</h3> </font>
	</div>
	<div align="right">
        <h:outputLink value="#{request.contextPath}/account/lockout/list">Locked Users</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="fraudmanager">Fraud Model Manager</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div id="user_content">
	<fieldset>
		<div class="legend-alt">Inactive Users</div>
		<div id="fingerprint-notice" class="legend-alt-border">Visitors to this site are subject to fingerprinting. Browser and device information that reflects your setup is recorded for research and testing purposes</div>

		<div class="tab-content">
			<div class="tab-pane active" id="tab_inactive_users">
				<div id="example_wrapper" class="dataTables_wrapper form-inline"
					 role="grid">

					<c:choose>
						<c:when test="#{request.getAttribute('errorMsg') != null}">
							<div><h3 style="color: red">
								<h:outputText value="#{request.getAttribute('errorMsg')}" />
							</h3></div>
						</c:when>
						<c:otherwise>
							<table cellpadding="0" cellspacing="0" border="0"
								   class="table table-striped table-bordered dataTable"
								   id="tbl_inactive_users"
								   aria-describedby="example_info">
								<thead>
								<tr role="row">
									<th
											style="width: 50px;"><input type="checkbox" id="selectall" /></th>
									<th class="sorting_asc" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-sort="ascending"
										aria-label="FirstName: activate to sort column descending"
										style="width: 163px;">Account ID</th>
									<th class="sorting_asc" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-sort="ascending"
										aria-label="FirstName: activate to sort column descending"
										style="width: 163px;">First Name</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="LastName: activate to sort column ascending"
										style="width: 237px;">LastName</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="Company: activate to sort column ascending"
										style="width: 217px;">Company</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="ContactNumber: activate to sort column ascending"
										style="width: 139px;">Contact Number</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="Email: activate to sort column ascending"
										style="width: 98px;">Email</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="ApiKey: activate to sort column ascending"
										style="width: 98px;">ApiKey</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="Role: activate to sort column ascending"
										style="width: 98px;">Role</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="RegisteredOn: activate to sort column ascending"
										style="width: 98px;">Registered On</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="EncryptionEnabled: activate to sort column ascending"
										style="width: 98px;">Encryption Enabled</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="EncryptionEnabled: activate to sort column ascending"
										style="width: 98px;">PII Mask Enabled</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="HasUsers: activate to sort column ascending"
										style="width: 98px;">Has Users</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="IsParent: activate to sort column ascending"
										style="width: 98px;">Is Parent</th>
									<th class="sorting" role="columnheader" tabindex="0"
										aria-controls="example" rowspan="1" colspan="1"
										aria-label="ExternalAccountId: activate to sort column ascending"
										style="width: 98px;">External Account ID</th>
								</tr>
								</thead>
								<tbody>

								<ui:repeat var="user" value="#{registred_user}" varStatus="status">
									<tr>
										<td align="center"><input type="checkbox" class="case"
																  name="case" value="${user.email}:${user.accountId}:${user.userId > 0}" /></td>
										<td>#{user.accountId}</td>
										<td>#{user.firstName}</td>
										<td>#{user.lastName}</td>
										<td>#{user.companyName}</td>
										<td>#{user.contactNumber}</td>
										<td>#{user.email}</td>
										<td>#{user.apiKey}</td>
										<td><ui:repeat value="#{user.roles}" var="role"
													   varStatus="roleStatus">
											#{role} <h:outputText value=", " rendered="#{!roleStatus.last}" />
										</ui:repeat></td>
										<!-- <td>#{user.roles}</td> -->
										<td><h:outputText value="#{user.registredon.getTime()}">
											<f:convertDateTime pattern="dd-MMM-yyyy" />
										</h:outputText></td>
										<td>#{user.encryptionEnabled}</td>
										<td>#{user.piiMaskEnabled}</td>
										<td>#{user.userId > 0}</td>
										<td>#{user.parentAccountId == 0}</td>
										<td>${user.externalAccountId}</td>
									</tr>
								</ui:repeat>

								<input type="button" value="Send Activation Link"
									   onclick="validate();" />
								<input type="button" value="Activate" onclick="activateAccounts();" />
								<input type="button" value="Delete" onclick="deleteUser();" />
								</tbody>
							</table>
						</c:otherwise>
					</c:choose>
				</div>
			</div>
		</div>
	</fieldset>
	</div>

	<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
	<script src="#{bucket.resourceURL}/scripts/lib.js"></script>

	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
	<!-- <script type="text/javascript">
    if (typeof(Zenbox) !== "undefined") {
            Zenbox.init({
                dropboxID: "********",
                url: "https://socure.zendesk.com",
                tabTooltip: "Support",
                tabImageURL: "https://assets.zendesk.com/external/zenbox/images/tab_support.png",
                tabColor: "#f59024",
                tabPosition: "Left"
            });
        }
    </script> -->
</h:body>
</html>