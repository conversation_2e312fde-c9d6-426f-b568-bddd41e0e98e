<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
>
<h:head>
    <style>
        /* The popup form - hidden by default */
        .form-popup-config {
          display: none;
          position: fixed;
          border: 3px solid #f1f1f1;
          left: 400px;
          top: 150px;
          width:500px;
          height:200px;
          z-index: 9;
        }

        /* Add styles to the form container */
        .form-container {
          padding: 10px;
          background-color: white;
        }

        /* Full-width input fields */
        .form-container input[type=text], .form-container select {
          width: 100%;
          padding: 5px;
          margin: 5px 0 22px 0;
          border: none;
          background: #f1f1f1;
        }

        /* When the inputs get focus, do something */
        .form-container input[type=text]:focus, .form-container select:focus {
          background-color: #ddd;
          outline: none;
        }

        /* Set a style for the submit/login button */
        .form-container .btn {
          background-color: #555;
          color: white;
          border: none;
          cursor: pointer;
          margin-bottom:10px;
          opacity: 0.8;
        }

        /* Add a red background color to the cancel button */
        .form-container .cancel {
          background-color: #555;
        }

        /* Add some hover effects to buttons */
        .form-container .btn:hover, .open-button:hover {
          opacity: 1;
        }

        .config-table {
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 1.0em;
            font-family: sans-serif;
            min-width: 400px;
            box-shadow: 20px 20px 20px rgba(0, 0, 0, 0.15);
        }

        .config-table thead tr {
            background-color: #ee8e3c;
            color: #ffffff;
            text-align: left;
        }

        .config-table th,
        .config-table td {
            padding: 12px 15px;
        }

        .config-table tbody tr {
            border-bottom: 1px solid #dddddd;
        }

        .config-table tbody tr:nth-of-type(even) {
            background-color: #f3f3f3;
        }

        .config-table tbody tr:last-of-type {
            border-bottom: 2px solid #ee8e3c;
        }

        .btn-primary {
            color: #ee8e3c;
            background: #fff;
            border-color: #ee8e3c;
        }

        .btn {
            display: inline-block;
            font-weight: 400;
            color: #212529;
            text-align: center;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-color: transparent;
            border: 1px solid transparent;
            padding: .375rem .75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: .25rem;
            transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        }

        .header-text {
          text-align: center;
          font-size: 20px;
        }
    </style>
    <script type="text/javascript" src="resources/scripts/sandbox_config.js"></script>
</h:head>
<h:body>

    <button id="create_config_popup" class="btn btn-primary" onclick="addConfig()" > Create New Configuration</button>

    <table class="config-table" style="margin-top:20px">
        <thead>
        <tr>
            <th>Revision</th>
            <th>Name</th>
            <th>Version</th>
            <th>IsLive</th>
            <th>Created By</th>
            <th>Marked Live By</th>
            <th>Marked Live At</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody id="configTable"></tbody>
    </table>

    <div class="form-popup-config" id="create_config_container">
        <form action="#" method="post" id="sandbox_configuration" class="form-container">
            <label class="header-text"><b>Add Configuration</b></label><br/>
            <label><b>Name</b></label>
            <input type="text" id="configName"/>

            <label><b>Version</b></label>
            <input type="text" id="configVersion"/>

            <label for="isConfigLive"><b>IsLive</b>&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" id="isConfigLive"/></label><br/>
            <label><b>Configuration File</b></label>
            <input type="file" id="configFile"/><br/><br/>

            <button type="button" class="btn" id="create_config_submit_btn" value="" onclick="createConfig(this)" >Submit</button>
            <button type="button" class="btn cancel" onclick="closeCreateConfigForm()">Close</button>
        </form>
    </div>

</h:body>
</html>
