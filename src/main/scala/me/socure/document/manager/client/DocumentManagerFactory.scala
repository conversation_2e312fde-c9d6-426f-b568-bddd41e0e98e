package me.socure.document.manager.client

import com.typesafe.config.Config
import me.socure.common.config.MsConfigProvider
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}

import scala.concurrent.ExecutionContext

class DocumentManagerFactory extends FactoryBean[DocumentManagerClient]{
  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  override def getObject: DocumentManagerClient = {
    val config: Config  = MsConfigProvider.provide().value.getConfig("document.manager")
    val client = DocumentManagerClientFactory.create(config)(executionContext)
    client
  }

  override def getObjectType: Class[DocumentManagerClient] = classOf[DocumentManagerClient]

  override def isSingleton: Boolean = true
}