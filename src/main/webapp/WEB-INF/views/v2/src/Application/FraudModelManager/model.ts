export interface Model {
  modelId: string;
  name: string;
  commonName: string;
  description: string;
  defaultResponseName: string;
  defaultResponseVersion: string;
  h2oIdentifier: string;
  engine: string;
  featureId: number;
  params: string;
  config: string;
  default: boolean;
  active: boolean;
  legacy: boolean;
  implicit: boolean;
  createdAt: number;
  updatedAt: number;
  productId: number;
}
