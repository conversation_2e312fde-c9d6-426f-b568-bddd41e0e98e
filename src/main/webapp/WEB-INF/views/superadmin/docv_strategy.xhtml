<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core" xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Docv Strategies</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css" media="print" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

	<h:outputStylesheet>
		.truncate {
		width: 150px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		}
	</h:outputStylesheet>

	<script>
		var bucketurl = "#{bucket.resourceURL}";
		var contexturl = "#{request.contextPath}";
		var marketingurl = "#{bucket.marketingSite}";
		var appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px;">
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>
	<div class="accordion" id="accordion2">
		<div class='container' style='margin-top: 10px'>
			<table cellpadding='0' cellspacing='0' border='0' class='table table-striped table-bordered' id='docv_strategy_table'>
				<thead>
					<tr>
						<th>Id</th>
						<th>StrategyId</th>
						<th>Name</th>
						<th>KeyType</th>
						<th>Lenient code</th>
						<th>Strict code</th>
						<th></th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<div style="padding-left: 215px;">
		<a href="javascript:show_strategy_add_form();">Add New Strategy</a>&nbsp;&nbsp; | &nbsp;
	</div>

	<div class="modal fade" id="AddStrategy" tabindex="-1" role="dialog" aria-labelledby="AddStrategyLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="AddStrategyLabel">Docv Strategy Changes</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="message" class="alert alert-error"></div>
					<form id="add_strategy" action="#" method="post">
						<div class="form-group row">
							<label for="docvId" class="col-sm-3 col-form-label">Id</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="docvId" readonly="true"/>
							</div>
						</div>

						<div class="form-group row">
							<label for="strategyId" class="col-sm-3 col-form-label">StrategyId</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="strategyId" placeholder="System generated" readonly="true"/>
							</div>
						</div>

						<div class="form-group row">
							<label for="strategyName" class="col-sm-3 col-form-label">Name*</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="strategyName" placeholder="Name of the strategy" />
							</div>
						</div>

						<div class="form-group row">
							<label for="keyType" class="col-sm-3 col-form-label">Key Type*</label>
							<div class="col-sm-9 model-format-dropdown format-selector">
								<select id="keyType">
									<option value="International">International</option>
									<option value="US">US</option>
								</select>
								<div class="error-msg" id="identityerrormsg"></div>
							</div>
						</div>

						<div class="form-group row">
							<label for="llc" class="col-sm-3 col-form-label">Lenient code*</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="llc" placeholder="Lenient Location Code" />
							</div>
						</div>

						<div class="form-group row">
							<label for="slc" class="col-sm-3 col-form-label">Strict code*</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="slc" placeholder="Strict Location Code" />
							</div>
						</div>

					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					<button type="button" class="btn btn-primary" onclick="saveStrategy()">Save</button>
				</div>
			</div>
		</div>
	</div>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="resources/scripts/bootstrap.min.js"></script>
	<script type="text/javascript" src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/docv_strategy.js"></script>
	<script>
		$(document).ready(function() {
			getAllStrategies();
		});
	</script>
</h:body>

</html>
