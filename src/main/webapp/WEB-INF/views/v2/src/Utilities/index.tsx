import axios, { AxiosResponse, AxiosRequestConfig } from "axios";
export const deepClone = function <T = any>(obj: T): T {
    return JSON.parse(JSON.stringify(obj)) as T;
}

export const apiCall = async function (path: string, data: any = {}): Promise<AxiosResponse<any, any>> {
    if (path.startsWith("http")) {
        return await axios.post(path, data);
    } else {
        return await axios.post(`${process.env.REACT_APP_API_URL}${path}`, data);
    }
}

export const apiCallV2 = async function <T = any>(
  path: string,
  options: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    data?: any;
    params?: any;
    headers?: any;
  } = {}
): Promise<AxiosResponse<T>> {
  const { method = 'GET', data, params, headers = {} } = options;

  const url = path.startsWith('http')
    ? path
    : `${process.env.REACT_APP_API_URL}${path}`;

  const config: AxiosRequestConfig = {
    url,
    method,
    headers,
    params
  };

  // Only include data for methods that support a request body
  if (method !== 'GET' && method !== 'DELETE') {
    config.data = data;
  }

  return axios(config);
};

// Smart API call that routes to v1 or v2 based on path
export const apiCallSmart = async function <T = any>(
  path: string,
  options: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    data?: any;
    params?: any;
    headers?: any;
  } = {}
): Promise<AxiosResponse<T>> {
  const { method = 'GET', data, params, headers = {} } = options;

  let url: string;

  if (path.startsWith('http')) {
    url = path;
  } else if (path.startsWith('/api/1/') || path.startsWith('/api/v1/')) {
    // For v1 APIs, use base URL without v2 prefix
    const baseUrl = process.env.REACT_APP_API_URL?.replace('/api/v2', '') || '';
    url = `${baseUrl}${path}`;
  } else {
    // For v2 APIs, use the configured API URL (which includes /api/v2)
    url = `${process.env.REACT_APP_API_URL}${path}`;
  }

  const config: AxiosRequestConfig = {
    url,
    method,
    headers,
    params
  };

  // Only include data for methods that support a request body
  if (method !== 'GET' && method !== 'DELETE') {
    config.data = data;
  }

  return axios(config);
};

export const goToLink = (path: string, external: boolean = false) => {
    if (external) {
        window.location.href = path;
    } else {
        window.location.href = path;
    }
}