package me.socure.filter

import javax.servlet.http.{HttpServletRequest, HttpServletResponse}
import javax.servlet.{Filter<PERSON>hain, ServletRequest, ServletResponse}
import me.socure.action.audit.ActionAuditConfiguration
import me.socure.actionaudit.filter.{AuditExtractor, HttpServletResponseCopier, MultiReadHttpServletRequest}
import me.socure.actionaudit.model.Action
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.util.SecurityUtil
import org.slf4j.LoggerFactory
import org.springframework.security.core.context.SecurityContextImpl

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

/**
 * Created by an<PERSON><PERSON><PERSON><PERSON> on 3/6/17.
 */
object ProcessFilter {
  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)
  private val source = "SuperAdmin"
  private val EMPTY_DETAILS =""
  private[filter] val documentsDownloadUri = "/superadmin/1/download_file"

  def getUsername(path: String, username: Try[String]): String = {
    if (path.contains("logout") && username.isSuccess) username.getOrElse("Anonymous") else Option(SecurityUtil.getUsername).getOrElse("Anonymous")
  }

  def doFilter(servletRequest: ServletRequest,
               servletResponse: ServletResponse,
               filterChain: FilterChain,
               actionAuditConfiguration: ActionAuditConfiguration): Unit = {
    implicit val ec: ExecutionContext = actionAuditConfiguration.getExecutionContext

    try {
      val path = servletRequest.asInstanceOf[HttpServletRequest].getRequestURI
      val suppressReqResp = if (path.contains("/decrypt_input_file")) true else false
      val startTime = actionAuditConfiguration.getClock.now().getMillis

      if (suppressReqResp) {
        val multiReadRequest = servletRequest.asInstanceOf[HttpServletRequest]
        val responseWrapper = servletResponse.asInstanceOf[HttpServletResponse]
        val username = Try(multiReadRequest.getSession.getAttribute("SPRING_SECURITY_CONTEXT").asInstanceOf[SecurityContextImpl].getAuthentication.getName)

        filterChain.doFilter(multiReadRequest, responseWrapper)
        responseWrapper.flushBuffer

        val actionOpt = AuditExtractor.extractAction(clock = actionAuditConfiguration.getClock,
          source = source,
          startTime = startTime,
          request = multiReadRequest,
          responseStatus = "",
          () => getUsername(path, username), accountId = "", publicAccountId = "", EMPTY_DETAILS
        )

        actionOpt.map(action => {
          val accountId: Future[Option[String]] = actionAuditConfiguration.getBusinessUserManagementClient.getPublicAccountIdByUserName(getUsername(path, username)) map {
            case Right(accountId) => Some(accountId)
            case _ => None
          }
          accountId map (id => {
            actionAuditConfiguration.getSQSMessageProducer.pushActionAudit(fixDocDownloadResponseBody(requestUri = path,action.copy(accountId = id)))
          })
        })
      } else {
        val multiReadRequest = new MultiReadHttpServletRequest(servletRequest.asInstanceOf[HttpServletRequest])
        val responseWrapper = new HttpServletResponseCopier(servletResponse.asInstanceOf[HttpServletResponse])
        val username = Try(multiReadRequest.getSession.getAttribute("SPRING_SECURITY_CONTEXT").asInstanceOf[SecurityContextImpl].getAuthentication.getName)

        filterChain.doFilter(multiReadRequest, responseWrapper)
        responseWrapper.flushBuffer

        val actionOpt = AuditExtractor.extractAction(clock = actionAuditConfiguration.getClock,
          source = source,
          startTime = startTime,
          request = multiReadRequest,
          responseCopier = responseWrapper,
          () => getUsername(path, username),
          shouldAuditRequest = !suppressReqResp,
          shouldAuditResponse = !suppressReqResp, accountId = "", publicAccountId = "", EMPTY_DETAILS) // There is no account info associated with super-admin SAML account.

          actionOpt match {
            case Some(action) => actionAuditConfiguration.getSQSMessageProducer.pushActionAudit(fixDocDownloadResponseBody(requestUri = path,action))
            case None => logger.info("Unable to extract action ")
          }
      }
    } catch {
      case e: Throwable =>
        logger.error("Exception while invoking the actionAudit message producer ", e)
    }
  }

  def fixDocDownloadResponseBody(requestUri: String, action: Action): Action = {
    if (requestUri == documentsDownloadUri)
      action.copy(responseData = "")
    else action
  }
}
