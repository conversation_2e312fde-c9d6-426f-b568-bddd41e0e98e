<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:ui="http://java.sun.com/jsf/facelets">

<h:head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<title>Socure - AccountDetails</title>

	<link rel="icon" href="#{bucket.resourceURL}/assets/images/favicon.png"
		type="image/x-icon" />
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/datepicker.css" />

	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/common.css" />
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/account_details.css" />
	<!-- Latest compiled and minified CSS -->
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/chosen.min.css"/>
	<style>
		.chosen-search input{
			margin-left:0 !important;
		}
		.modal-body{
			overflow-y: inherit;
		}
	</style>
	<script src="resources/scripts/bootstrap.min.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="resources/scripts/d3.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/vkbeautify.0.99.00.beta.js"></script>
	<script src="#{bucket.resourceURL}/scripts/chosen.jquery.min.js"></script>

	<script>
		bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		servicehost = '#{bucket.authScoreBase}';
		accountId = '#{account_id}';
		accountName = "#{account_name}";
		publicId = "#{public_id}";
		loggedinUsername = "#{loggedinUsername}";
		piiRetentionAccessPermission = "#{piiRetentionAccessPermission}";
		showOldProvisioning = "#{showOldProvisioning}";
		appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/ZeroClipboard.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/TableTools.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/bootstrap-datepicker.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/account_details.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
			src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/toastr.min.js"></script>
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/toastr.min.css" />
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/toastr-config.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/toastr.min.js"></script>
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/toastr.min.css" />
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/toastr-config.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery-dateFormat.min.js"></script>

	<script defer="defer" type="text/javascript" src="resources/account_provisioning/main.js"></script>
	<link rel="stylesheet" type="text/css" href="resources/account_provisioning/main.css" />
	<style>
		.error-table td,th,tr{
			max-width : 500px;
		}
		.color-orange{
			color: #ee8e3c;
		}
	</style>
</h:head>
<h:body class="">
	<br></br>
	<div align="right" style="margin-right:20px;">
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="account_details_msg"></label>
		</font>
	</div>

	<div class="modal" tabindex="-1" role="dialog" id="migrationModal">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Account Migration</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeMigrationModal()">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body" style="overflow-x: scroll;">
					<div>
						<div class="form-group">Account Id   : #{account_id}</div>
						<div class="form-group">Account Name   : #{account_name}</div>
						<input type="radio" name="migration-type" value="migration" checked="true" onclick="switchMerge(false)" />
						<label>Migration</label>
						<input type="radio" name="migration-type" value="merge" onclick="switchMerge(true)" />
						<label>Merge</label>
						<div id="account-type" class="form-group">
							<label>Account Type:</label>
							<select onchange="updateAccountType()" id="migration_account_type">
								<option value='1'>Direct Customer</option>
								<option value='2'>Partner-Reseller</option>
								<option value='3'>Partner-Aggregator</option>
								<option value='4'>Sub-Account</option>
							</select>
						 </div>
						 <div class="form-group">
							<label>Associate parent users to sub accounts:</label>
							<input id='associate_all_users' type='checkbox' onclick="associateAllUserClick()"/>
						 </div>
						 <div class="form-group" id="migrated_account_form" style="display: none;">
							<label id="migrated_account_form_label"></label>
							<select onchange="updateMigratedAccount()" id="migrated_account_dropdown"></select>
							<div id="account_administer_form" style="display: none;">
								<label for="parent_account_administer_check"> Administer</label>
								<input id='parent_account_administer_check' type='checkbox'/>
							</div>
						 </div>
					</div>
					<table cellpadding="0" cellspacing="0" border="0"
									class="table table-striped table-bordered dataTable"
									id="tbl_migration_user_details" aria-describedby="example_info">
									<thead>
										<tr role="row">
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-sort="ascending"
												aria-label="User ID"
												style="width: 163px;">User ID</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="Name"
												style="width: 237px;">Name</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="Email"
												style="width: 217px;">Email</th>
												<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="Assign all permissons"
												style="width: 217px;">Assign all permissons</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="Phone"
												style="width: 217px;">Phone</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="4"
												aria-label="Roles"
												style="width: 217px; align:centre;">Roles</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="IsPrimary"
												style="width: 217px;">IsPrimary</th>
										</tr>
										<tr>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="2" colspan="1"
												aria-label="Global"
												style="width: 217px;">Global</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="3"
												aria-label="Environment"
												style="width: 217px;align:centre;">Environment</th>
										</tr>
										<tr>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Production"
												style="width: 217px;">Production</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Development"
												style="width: 217px;">Development</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Sandbox"
												style="width: 217px;">Sandbox</th>
										</tr>

									</thead>
								</table>

					<table cellpadding="0" cellspacing="0" border="0"
						class="table table-striped table-bordered dataTable"
						id="tbl_migration_subaccount_details" aria-describedby="example_info">
		
						<thead>
							<tr role="row">
								<th role="columnheader" tabindex="0"
								aria-controls="example" rowspan="1" colspan="1"
								style="width: 50px;"><input onclick="toggleMigrationCheckBox()" type="checkbox" id="selectall" /></th>
								<th class="sorting_asc" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-sort="ascending"
									aria-label="Sub-Account Id"
									style="width: 163px;">Sub-Account Id</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-sort="ascending"
									aria-label="Sub-Account Name"
									style="width: 163px;">Sub-Account Name</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="Primary User"
									style="width: 163px;">Primary User</th>
								<th class="sorting" role="columnheader" tabindex="1"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="Administer"
									style="width: 163px;">Administer</th>
							</tr>
						</thead>
					</table>
				</div>
				<div class="modal-footer">
					<button onclick="migrate()" id="migrate-btn" type="button" class="btn btn-primary">Migrate</button>
					<button onclick="closeMigrationModal()" type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal" tabindex="-1" role="dialog" id="SubAccountMigrationModal">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">SubAccount Migration</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="handleSubAccountMigrationModal(false)">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body" style="overflow-x: scroll;">
					<div class="form-group">
						<label>Associate parent users to sub accounts:</label>
						<input id='associate_all_users_subaccount' type='checkbox' onclick="associateAllUserSubAccountClick()"/>
					 </div>
					<table cellpadding="0" cellspacing="0" border="0"
						class="table table-striped table-bordered dataTable"
						id="tbl_migration_subaccount" aria-describedby="example_info">
		
						<thead>
							<tr role="row">
								<th class="sorting_asc" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-sort="ascending"
									aria-label="Sub-Account Id"
									style="width: 163px;">Sub-Account Id</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-sort="ascending"
									aria-label="Sub-Account Name"
									style="width: 163px;">Sub-Account Name</th>
								<th class="sorting" role="columnheader" tabindex="0"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="Primary User"
									style="width: 163px;">Primary User</th>
								<th class="sorting" role="columnheader" tabindex="1"
									aria-controls="example" rowspan="1" colspan="1"
									aria-label="Administer"
									style="width: 163px;">Administer</th>
							</tr>
						</thead>
					</table>
				</div>
				<div class="modal-footer">
					<button onclick="migrateSubAccount()" type="button" class="btn btn-primary">Migrate</button>
					<button onclick="handleSubAccountMigrationModal(false)" type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal" tabindex="-1" role="dialog" id="swapRoleModal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Role Swap</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeRoleSwapPopup()">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div>
						<label for="swapping-user-id" style="width: 60px;">From</label>
						<span id="swapping-user-id"></span>
					</div>
					<div>
						<label for="active-users" style="width: 60px;">To</label>
						<select id="active-users"></select>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="swapRoleHandler()">Swap</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeRoleSwapPopup()">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal" tabindex="-1" role="dialog" id="accountAssociationModal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">User Account Assoociation</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeAccountAssocationPopup()">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div>
						<label for="user-association" style="width: 60px;">User:</label>
						<span id="user-association"></span>
					</div>
					<div>
						<label for="account-association" style="width: 60px;">Account</label>
						<select id="account-association"></select>
					</div>
					<div style="margin-top: 10px">
						<label for="role-association" style="width: 60px;">Role</label>
						<select id="role-association"></select>
					</div>
					
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="assocaiteUser()">Associate</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeAccountAssocationPopup()">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal" tabindex="-1" role="dialog" id="ProvisionMessageModal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Provision - roles/permissons/preferences</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="revertSelection(true)">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="provision-msg"></label>
					<table id="mlainput">
						<tr><td><label for="mNumber">MemberNumber</label></td><td><input type="text" id="memberNumber" name="memberNumber" required="required"></input></td></tr>
						<tr><td><label for="sCode">SecurityCode</label></td><td><input type="text" id="securityCode" name="securityCode" required="required"></input></td></tr>
					</table>
					<table id="ecbsvinput">
						<tr><td style="padding-right: 10px"><label for="ein">Employer Identification Number</label></td><td><input type="number" id="ein" name="ein" required="required"></input></td></tr>
					</table>
				</div>
				<div class="modal-footer">
					<button onclick="provision()" type="button" class="btn btn-primary" data-dd-action-name="Confirm Provision">Yes</button>
					<button onclick="revertSelection(true)" type="button" class="btn btn-secondary" data-dismiss="modal" data-dd-action-name="Revert Provision">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div id="DeprovisionMessageModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Deprovision - roles/permissons/preferences</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="revertSelection(false)">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="deprovision-msg"></label>
				</div>
				<div class="modal-footer">
					<button onclick="deprovision()" type="button" class="btn btn-primary" data-dd-action-name="Deprovision Confirm">Yes</button>
					<button onclick="revertSelection(false)" type="button" class="btn btn-secondary" data-dismiss="modal" data-dd-action-name="Revert Deprovision">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal" tabindex="-1" role="dialog" id="saveConfirmModal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Confirmation</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="message">Are you sure?</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary btn-save">Yes</button>
					<button type="button" class="btn btn-secondary btn-cancel" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal -->
	<div class="container-fluid" style="margin-top: 10px">
		<div class="row-fluid">
			<div class="span12">
				<h3 class="text-center">Account Details</h3>
				<input type="hidden" name="accountName" id="adAccountName" value="#{account_name}" />
				<input type="hidden" name="publicId" id="publicId" value="#{public_id}" />
				<br></br>
				<div id="accountNameEditContainer">
					<label for="accountNameInput">Account Name:</label>
					<div class="email-row d-inline-block">
						<div class="email-label">
							<span class="label">#{account_name}</span>
							<span class="edit-icon icon clickable #{allowEdit ? '' : 'hide'}"></span>
						</div>
						<div class="email-editable">
							<input id="accountNameInput" type="text" value="#{account_name}" />
							<span class="save-icon icon clickable"></span>
							<span class="cancel-icon icon clickable"></span>
						</div>
					</div>
				</div>
				Account Type : #{account_type}
				<br></br>
				Account Id   : #{account_id}
				<div id="sponsorBankDetailsContainer" hidden="true">
				</div>
				<div id="migrationBtnWrapper" style="float: right;margin-right: 25%;display: none;">
					<button type="button" class="btn btn-primary" onclick="showMigrationModal()">Migrate/Merge, do not use it</button>
				</div>
				<input type="hidden" name="accountId" id="adAccountId" value="#{account_id}" />
				<input type="hidden" name="allowEdit" id="allowEdit" value="#{allowEdit}" />
				<br></br>

				<br></br>
				<div class="tabbable" id="accountdetails-tabs" style="width: 100%;">
					<div class="nav nav-tabs" id="nav-tab"  role="tablist">
						<a class="nav-item nav-link account-tab active"
						   id="nav-partner-subaccount-tab" data-toggle="tab"
						   href="#tab_partner_and_subaccount" role="tab" aria-controls="nav-partner-subaccount"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="PARTNER_AND_SUBACCOUNT"
						   aria-selected="true" >Hierarchy and Users</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-old-provisioning-tab" data-toggle="tab"
						   href="#tab_roles_permissions_old" role="tab" aria-controls="nav-provisioning"
						   title="roles-permissions_old"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="ROLES_PERMISSIONS"
						   aria-selected="true">Old Provisioning</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-provisioning-tab" data-toggle="tab"
						   href="#tab_roles_permissions" role="tab" aria-controls="nav-new-provisioning"
						   title="roles-permissions"
						   onclick="reloadAccountProvisioning(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   aria-selected="true">Provisioning</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-preferences-tab" data-toggle="tab"
						   href="#tab_preferences" role="tab" aria-controls="nav-preferences"
						   title="preferences"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="PREFERENCES"
						   aria-selected="true">Preferences</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-users-tab" data-toggle="tab"
						   href="#tab_users" role="tab" aria-controls="nav-users"
						   title="roles-permissions"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="USERS"
						   aria-selected="true" hidden="true">Users</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-ip-whitelisting-tab" data-toggle="tab"
						   href="#tab_ip_whitelisting" role="tab" aria-controls="nav-ip-whitelisting"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="IP_WHITELISTING"
						   aria-selected="true">IP Whitelisting</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-tab_api_keys" data-toggle="tab"
						   href="#tab_api_keys" role="tab" aria-controls="nav-tab_api_keys"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="API_KEYS"
						   aria-selected="true">Api Keys</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-tab_docTypes" data-toggle="tab"
						   href="#tab_docTypes" role="tab" aria-controls="nav-tab_docTypes"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="DOCUMENT_TYPES"
						   aria-selected="true" hidden="true">Document Types</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-tab_decision_logic" data-toggle="tab"
						   href="#tab_decision_logic" role="tab" aria-controls="nav-tab_decision_logic"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="DECISION_LOGIC"
						   aria-selected="true" hidden="true">Decision Logic</a>

						<a class="nav-item nav-link account-tab"
						   id="nav-settings-tab" data-toggle="tab"
						   href="#tab_settings" role="tab" aria-controls="nav-settings"
						   onclick="handleTabClick(this)"
						   data-ad-accountid="#{account_id}"
						   data-ad-publicid="#{public_id}"
						   data-ad-target="SETTINGS"
						   aria-selected="true">Settings</a>
					</div>
					<div class="tab-content">
						<div class="tab-pane" id="tab_roles_permissions" data-writeAccess="#{accountProvisioningAccess &amp;&amp; allowEdit}" data-accountType="#{account_type}"
							  data-loggedinUsername="#{loggedinUsername}" data-piiRetentionAccessPermission="#{piiRetentionAccessPermission}" data-isInternal="#{isInternal}"></div>
						<div class="tab-pane" id="tab_roles_permissions_old" data-loggedinUsername="#{loggedinUsername}">
							<div id="roles_permissions" class="dataTables_wrapper form-inline" role="grid" style="width:100%">
								<table cellpadding="0" cellspacing="0" border="0"
									class="table table-striped table-bordered dataTable"
									id="tbl_account_details_roles_permissions"
									aria-describedby="example_info">
									<thead>
										<tr role="row">
											<th role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Id"
												style="width: 163px;"></th>
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="Roles and Permissions"
												style="width: 163px;">Roles and Permissions</th>
											<th role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Provisioned"
												style="width: 237px;">Provisioned</th>
										</tr>
									</thead>
								</table>
							</div>
							<div id="subscription_types" class="dataTables_wrapper form-inline" role="grid" style="width:100%">
								<table cellpadding="0" cellspacing="0" border="0" class="table table-striped table-bordered dataTable"
									id="tbl_account_details_subscription_types" aria-describedby="example_info">
									<thead>
										<tr role="row">
											<th role="columnheader" tabindex="0" aria-controls="example" rowspan="1" colspan="1" aria-label="Id" style="width: 10px;"></th>
											<th role="columnheader" tabindex="0" aria-controls="example" rowspan="1" colspan="1" aria-label="Subscription Types"
												style="width: 200px;">Subscription Types</th>
											<th class="sorting_asc" role="columnheader" tabindex="0" aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending" aria-label="Description" style="width: 500px;">Description
											</th>
											<th role="columnheader" tabindex="0" aria-controls="example" rowspan="1" colspan="1"
												aria-label="Provisioned" style="width: 75px;">Provisioned</th>
										</tr>
									</thead>
								</table>
							</div>
						</div>
						<div class="tab-pane" id="tab_users">
							<div id="example_wrapper" class="dataTables_wrapper form-inline" role="grid">
								<table cellpadding="0" cellspacing="0" border="0"
									class="table table-striped table-bordered dataTable"
									id="tbl_account_details_users" aria-describedby="example_info">
									<thead>
										<tr role="row">
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-sort="ascending"
												aria-label="User ID"
												style="width: 163px;">User ID</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="Name"
												style="width: 237px;">Name</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="Email"
												style="width: 217px;">Email</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="Phone"
												style="width: 217px;">Phone</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="3"
												aria-label="Roles"
												style="width: 217px; align:centre;">Roles</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="3" colspan="1"
												aria-label="IsPrimary"
												style="width: 217px;">IsPrimary</th>
										</tr>
										<tr>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="2" colspan="1"
												aria-label="Global"
												style="width: 217px;">Global</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="2"
												aria-label="Environment"
												style="width: 217px;align:centre;">Environment</th>
										</tr>
										<tr>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Production"
												style="width: 217px;">Production</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Certification"
												style="width: 217px;">Certification</th>
										</tr>

									</thead>
								</table>
							</div>
						</div>
						<div class="tab-pane" id="tab_ip_whitelisting">
							<div id="example_wrapper" class="dataTables_wrapper form-inline" role="grid">
								<table cellpadding="0" cellspacing="0" border="0"
									class="table table-striped table-bordered dataTable"
									id="tbl_account_details_ip_whitelisting" aria-describedby="example_info">
									<thead>
										<tr role="row">
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="Environment"
												style="width: 163px;">Environment</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Domains"
												style="width: 237px;">Domains</th>
										</tr>
									</thead>
								</table>
							</div>
						</div>
						<div class="tab-pane" id="tab_preferences">
							<div id="example_wrapper" class="dataTables_wrapper form-inline" role="grid">
								<table cellpadding="0" cellspacing="0" border="0"
									   class="table table-striped table-bordered dataTable"
									   id="tbl_account_details_preferences" aria-describedby="example_info">
									<thead>
									<tr role="row">
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Label"
											style="width: 163px;">Label</th>
										<th class="sorting_asc" role="columnheader" tabindex="1"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Actions"
											style="width: 163px;">Actions</th>
									</tr>
									</thead>
								</table>
							</div>
						</div>
						<div class="tab-pane" id="tab_docTypes">
							<div id="example_wrapper" class="dataTables_wrapper form-inline" role="grid">
								<table cellpadding="0" cellspacing="0" border="0"
									   class="table table-striped table-bordered dataTable"
									   id="tbl_account_details_docTypes" aria-describedby="example_info">
									<thead>
									<tr role="row">
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Document PublicId"
											style="width: 163px;">Document PublicId</th>
										<th class="sorting_asc" role="columnheader" tabindex="1"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Name"
											style="width: 163px;">Name</th>
										<th class="sorting_asc" role="columnheader" tabindex="1"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Name"
											style="width: 163px;">Actions</th>
									</tr>
									</thead>
								</table>
							</div>
							<div class="form-group row">
								<label for="otherDocTypes" class="col-sm-3 col-form-label"> Provision new document types</label>
								<div class="col-sm-9 model-format-dropdown format-selector">
									<select id="otherDocTypes">

									</select>
									<input type="button" value="Provision" id="provisionDocType"/>
								</div>
							</div>
						</div>

						<div class="tab-pane active" id="tab_partner_and_subaccount">
							<div id="partner_and_subaccount" class="dataTables_wrapper form-inline" role="grid" style="width: 100%;">
								<table cellpadding="0" cellspacing="0" border="0"
									class="table table-striped table-bordered dataTable"
									id="tbl_account_details_partner_and_subaccount" aria-describedby="example_info">

									<thead>
										<tr role="row">
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="Account Id"
												style="width: 163px;">Account Id</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Name"
												style="width: 163px;">Name</th>
											<th class="sorting" role="columnheader" tabindex="1"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Public Id"
												style="width: 163px;">Public Id</th>
											<th class="sorting" role="columnheader" tabindex="1"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Account Type"
												style="width: 163px;">Account Type</th>
											<th class="sorting" role="columnheader" tabindex="1"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Sponsor Bank"
												style="width: 163px;">Sponsor Bank</th>
											<th class="sorting" role="columnheader" tabindex="1"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Hierarchy Path"
												style="width: 163px;">Hierarchy Path</th>
											<th class="sorting" role="columnheader" tabindex="1"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Administer"
												style="width: 163px;">Administer</th>
										</tr>
									</thead>
								</table>
							</div>
							<div class="tabbable" id="accountAccessManager-tabs" style="width: 100%;">
								<div class="nav nav-tabs" id="nav-tab"  role="tablist">
									<a class="nav-item nav-link active"
										id="nav-hierarchy-tab" data-toggle="tab"
										href="#tab_hierarchy" role="tab" aria-controls="nav-hierarchy"
									    onclick="handleTabClick(this)"
										aria-selected="true">Account Hierarchy
									</a>
									<a class="nav-item nav-link"
										id="nav-user-tab" data-toggle="tab"
										href="#tab_user" role="tab" aria-controls="nav-user"
									    onclick="handleTabClick(this)"
										aria-selected="true">Migrated Users
									</a>
									<a class="nav-item nav-link"
										id="nav-role-tab" data-toggle="tab"
										href="#tab_role" role="tab" aria-controls="nav-role"
									    onclick="handleTabClick(this)"
										aria-selected="true">Roles
									</a>
									<a class="nav-item nav-link"
										id="nav-permissions-tab" data-toggle="tab"
										href="#tab_permissions" role="tab" aria-controls="nav-permissions"
									    onclick="handleTabClick(this)"
										aria-selected="true">Permissions
									</a>
									<a class="nav-item nav-link"
										id="nav-subaccounts-not-migrated-tab" data-toggle="tab"
										href="#tab_subaccounts_not_migrated" role="tab" aria-controls="nav-subaccounts-not-migrated"
									    onclick="handleTabClick(this)"
										aria-selected="true">Sub Accounts Not Migrated
									</a>
									<a class="nav-item nav-link"
									   id="nav-linked-programs-tab" data-toggle="tab"
									   href="#tab_linked_programs" role="tab" aria-controls="nav-linked-programs"
									   onclick="handleTabClick(this)"
									   aria-selected="true">Linked Programs
									</a>
								</div>
							</div>

							<div class="tab-content">
								<div class="tab-pane active" id="tab_hierarchy">
									<div id="sub-accounts-tree"></div>
								</div>

								<div class="tab-pane" id="tab_user">
									<table cellpadding="0" cellspacing="0" border="0"
										class="table table-striped table-bordered dataTable"
										id="tbl_account_details_partner_and_subaccount_users" aria-describedby="example_info">

										<thead>
											<tr role="row">
												<th class="sorting_asc" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-sort="ascending"
													aria-label="Id"
													style="width: 163px;">Id</th>
												<th class="sorting_asc" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-sort="ascending"
													aria-label="Name"
													style="width: 163px;">Name</th>
												<th class="sorting_asc" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-sort="ascending"
													aria-label="Email"
													style="width: 163px;">Email</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-label="status"
													style="width: 163px;">Status</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-label="Roles"
													style="width: 163px;">Roles</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-label="Action"
													style="width: 163px;">Action</th>
											</tr>
										</thead>
									</table>
								</div>

								<div class="tab-pane" id="tab_role">
									<table cellpadding="0" cellspacing="0" border="0"
										class="table table-striped table-bordered dataTable"
										id="tbl_account_details_roles" aria-describedby="example_info">

										<thead>
											<tr role="row">
												<th class="sorting_asc" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-sort="ascending"
													aria-label="Id"
													style="width: 163px;">Id</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-sort="ascending"
													aria-label="Name"
													style="width: 163px;">Name</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-label="Description"
													style="width: 163px;">Description</th>
											</tr>
										</thead>
									</table>
								</div>

								<div class="tab-pane" id="tab_permissions">
									<table cellpadding="0" cellspacing="0" border="0"
										class="table table-striped table-bordered dataTable"
										id="tbl_account_details_permission_templates" aria-describedby="example_info">

										<thead>
											<tr role="row">
												<th class="sorting_asc" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-sort="ascending"
													aria-label="Permissions Name"
													style="width: 163px;">Permissions Name</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-sort="ascending"
													aria-label="Role Name"
													style="width: 163px;">Role Name</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-label="Environment"
													style="width: 163px;">Environment</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-label="Permissions"
													style="width: 163px;">Permissions</th>
												<th class="sorting" role="columnheader" tabindex="0"
													aria-controls="example" rowspan="1" colspan="1"
													aria-label="Global Scope"
													style="width: 163px;">Global Scope</th>
											</tr>
										</thead>
									</table>
								</div>

								<div class="tab-pane" id="tab_subaccounts_not_migrated">
									<div>
										<button onclick="migrateSelectedSubAccountHandler()" type="button" class="btn btn-primary">Migrate Selected</button>
										<button onclick="migratedAllSubAccounts()" type="button" class="btn btn-primary">Migrate All Sub Accounts</button>
									</div>
									<br/>
									<table cellpadding="0" cellspacing="0" border="0"
									class="table table-striped table-bordered dataTable"
									id="tbl_account_details_subaccounts_not_migrated" aria-describedby="example_info">
					
									<thead>
										<tr role="row">
											<th role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												style="width: 50px;"><input onclick="toggleSubAccountCheckBox()" type="checkbox" id="selectallSubAccount" /></th>
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="Id"
												style="width: 163px;">Id</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="Name"
												style="width: 163px;">Name</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Industry"
												style="width: 163px;">Industry</th>
										</tr>
									</thead>
								</table>
								</div>

								<div class="tab-pane" id="tab_linked_programs">
									<div class="container">
										<div class="form-group row">
											<label for="non-linked-programs" class="col-sm-3 col-form-label">Select Program to Link </label>
											<select name="programid" id="non-linked-programs" class="chosen">
												<option value="none">--Select--</option>
											</select>
										</div>
										<div class="form-group row align-items-center">
											<input type="button" class="btn btn-primary" value="Link" onclick="confirmLinkProgram()" />
										</div>
									</div>
									<br/>
									<table cellpadding="0" cellspacing="0" border="0"
										   class="table table-striped table-bordered dataTable"
										   id="tbl_account_details_linked_programs" aria-describedby="example_info">

										<thead>
										<tr role="row">
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="ProgramId"
												style="width: 163px;">Program Id</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="ProgramName"
												style="width: 163px;">Program Name</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="CreatedBy"
												style="width: 163px;">Created By</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="CreatedAt"
												style="width: 163px;">Created At</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Action"
												style="width: 163px;">Action</th>
										</tr>
										</thead>
									</table>
								</div>

							</div>
						</div>

						<div class="tab-pane" id="tab_decision_logic">
							<div style="display:flex; justify-content: space-between;">
								<div class="form-group row">
									<div style="padding-left: 25px;">
										<a href="javascript:showNewRevisionForm();">Add New Revision</a>
										<br></br>Mark-as-live is disabled when Multiple Live Logics is enabled. This is because customer needs to map live logic to use cases (i.e. traffic segmentation parameters eventType &amp; eventSubType)
									</div>
									<div style="margin-left: 15%;" id="revisionMessage" class="alert alert-error"></div>
								</div>
								<div class="form-group">
									<label>Environment:</label>
									<select onchange="updateDecisionLogicEnvironment()" id="decision_logic_environment">
										<option value='1'>Production</option>
										<option value='2'>Certification</option>
										<option value='3'>Sandbox</option>
									</select>
								</div>
							</div>
							<div id="decision_logic_wrapper" class="dataTables_wrapper form-inline" role="grid">
								<table cellpadding="0" cellspacing="0" border="0"
									   class="table table-striped table-bordered dataTable"
									   id="tbl_account_details_decision_logic" aria-describedby="example_info">
									<thead>
									<tr role="row">
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Logic Id"
											style="width: 163px;">Logic Id</th>
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Model Name"
											style="width: 163px;">Model Name</th>
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Model Version"
											style="width: 163px;">Model Version</th>
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Version"
											style="width: 163px;">Version</th>	
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Revision"
											style="width: 163px;">Revision</th>
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Tag"
											style="width: 163px;">Tag</th>
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Is Live"
											style="width: 163px;">Is Live</th>
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Created By"
											style="width: 163px;">Created By</th>
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Created At"
											style="width: 163px;">Created At</th>
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Event Mappings"
											style="width: 163px;">Event Mappings</th>	
										<th class="sorting_asc" role="columnheader" tabindex="0"
											aria-controls="example" rowspan="1" colspan="1"
											aria-label="Name"
											style="width: 163px;">Actions</th>
									</tr>
									</thead>
								</table>
							</div>
						</div>
						<div class="tab-pane" id="tab_api_keys">
							<div id="example_wrapper" class="dataTables_wrapper form-inline"
								role="grid">
								<table cellpadding="0" cellspacing="0" border="0"
									class="table table-striped table-bordered dataTable"
									id="tbl_account_details_api_keys" aria-describedby="example_info">
									<thead>
										<tr role="row">
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="Account"
												style="width: 163px;">Account ID</th>
											<th class="sorting_asc" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-sort="ascending"
												aria-label="Account"
												style="width: 163px;">Account Name</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="Environment"
												style="width: 237px;">Environment</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="ApiKey"
												style="width: 217px;">Active ApiKey</th>
											<th class="sorting" role="columnheader" tabindex="0"
												aria-controls="example" rowspan="1" colspan="1"
												aria-label="ApiKey"
												style="width: 217px;">New ApiKey</th>
										</tr>
									</thead>
								</table>
							</div>
						</div>
						<div class="tab-pane" id="tab_settings">
							<div class="tabbable" id="settings-tabs" style="width: 100%;">
								<div class="nav nav-tabs" id="nav-tab"  role="tablist">
									<a class="nav-item nav-link active"
									   id="nav-kyc" data-toggle="tab"
									   href="#tab_kyc" role="tab" aria-controls="nav-kyc"
									   aria-selected="true">KYC
									</a>
									<a class="nav-item nav-link"
									   id="nav-wl" data-toggle="tab"
									   href="#tab_wl" role="tab" aria-controls="nav-wl"
									   aria-selected="true">Watchlist
									</a>
									<a class="nav-item nav-link"
									   id="nav-webhook" data-toggle="tab"
									   href="#tab_webhook" role="tab" aria-controls="nav-webhook"
									   aria-selected="true">Webhooks
									</a>
									<a class="nav-item nav-link"
									   id="nav-dv" data-toggle="tab"
									   href="#tab_dv" role="tab" aria-controls="nav-dv"
									   aria-selected="true">DocV
									</a>
									<a class="nav-item nav-link"
									   id="nav-cm" data-toggle="tab"
									   href="#tab_cm" role="tab" aria-controls="nav-cm"
									   aria-selected="true">Case Manager
									</a>
									<a class="nav-item nav-link"
									   id="nav-watchlist" data-toggle="tab"
									   href="#tab_wlSettings" role="tab" aria-controls="nav-watchlist"
									   aria-selected="true">New Watchlist
									</a>
								</div>
							</div>
							<div class="tab-content">
								<div class="tab-pane active" id="tab_kyc">
								</div>
								<div class="tab-pane" id="tab_wl">
								</div>
								<div class="tab-pane" id="tab_webhook">
								</div>
								<div class="tab-pane" id="tab_dv">
								</div>
								<div class="tab-pane" id="tab_cm">
								</div>
								<div class="tab-pane" id="tab_wlSettings">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<section id="Loading" class="overlay show">
        <p>Loading ...</p>
        <div class="loader">
            <img src="#{bucket.resourceURL}/assets/images/socure-loader.svg" /> <span
                class="dot"></span>
        </div>
	</section>

	<div class="modal fade" id="AddNewRevision" tabindex="-1" role="dialog" aria-labelledby="AddNewRevision" aria-hidden="true">
		<div class="modal-dialog" role="decision">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="AddRevisionLabel">Add Revision</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="errorMsg" class="alert alert-error"></div>
					<form id="add_new_revision" action="#" method="post">
						<div class="form-group row">
							<label for="modelName" class="col-sm-3 col-form-label">Model Name</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="modelName" required="true" placeholder="Model Name" />
							</div>
						</div>
						<div class="form-group row">
							<label for="modelVersion" class="col-sm-3 col-form-label">Model Version</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="modelVersion" required="true" placeholder="Model Version" />
							</div>
						</div>
						<div class="form-group row">
							<label for="tag" class="col-sm-3 col-form-label">Tag</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="tag" required="true" placeholder="Tag" />
							</div>
						</div>
						<div class="form-group row">
							<label for="decision_logic_create_environment" class="col-sm-3 col-form-label">Environment</label>
							<div class="col-sm-9">
								<select id="decision_logic_create_environment">
									<option value='0'>All</option>
									<option value='1'>Production</option>
									<option value='2'>Certification</option>
									<option value='3'>Sandbox</option>
								</select>
							</div>
						</div>
						<div class="form-group row">
							<label for="isLive" class="col-sm-3 col-form-label">Is Live</label>
							<div class="col-sm-9">
								<input class="form-control" id="isLive" required="true" placeholder="Is Live" type="checkbox"/>
							</div>
						</div>
						<div class="form-group row">
							<label for="file" class="col-sm-3 col-form-label">Logic</label>
							<div class="col-sm-9">
								<input type="file" id="file" name="file" required="true" placeholder="Decision Logic"/>
								<label class="file-caption">(Please select .conf file)</label>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					<button type="button" class="btn btn-primary" onclick="saveNewRevision(this)">Save</button>
				</div>
			</div>
		</div>
	</div>
	<div class="modal fade" id="SimulateExecution" tabindex="-1" role="dialog" aria-labelledby="SimulateExecution" aria-hidden="true">
		<div class="modal-dialog" role="decision">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="SimulateExecutionLabel">Simulate Decision Logic Execution</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="sfErrorMsg" class="alert alert-error"></div>
					<form id="simulate_execution" action="#" method="post" enctype='multipart/form-data'>
						<div class="form-group row">
							<label for="modelName" class="col-sm-3 col-form-label">Revision</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="revision" required="true" placeholder="Revision" />
							</div>
						</div>
						<div class="form-group row">
							<label for="inputDataFormat" class="col-sm-3 col-form-label">Input</label>
							<div class="col-sm-9" id="inputDataFormat">
								<input type="radio" name="isCSV" value="true" checked="checked" onclick="javascript:csvClick();" >
									<span class="label">CSV</span></input>
								<input type="radio" name="isCSV" value="false" class="pad" onclick="javascript:transactionClick();">
									<span class="label">Past Transactions</span></input>
							</div>
						</div>
						<div class="form-group row" id="simulate_csv_file">
							<label for="file" class="col-sm-3 col-form-label">CSV File</label>
							<div class="col-sm-9">
								<input type="file" id="file_selection" name="file" required="true" placeholder="Test file"/>
								<label class="file-caption">(max. limit 4000 records)</label>
							</div>
						</div>
						<div class="form-group row" id="simulate_past_txn_start_dt">
							<label for="pastTransactionStartDate" class="col-sm-3 col-form-label">Start Date</label>
							<div class="col-sm-9">
								<input type="date" class="form-control" id="pastTransactionStartDate" />
							</div>
						</div>
						<div class="form-group row" id="simulate_past_txn_end_dt">
							<label for="pastTransactionEndDate" class="col-sm-3 col-form-label">End Date</label>
							<div class="col-sm-9">
								<input type="date" class="form-control" id="pastTransactionEndDate" />
							</div>
						</div>
						<div class="form-group row" id="simulate_past_txn_count">
							<label for="pastTransactionsCount" class="col-sm-3 col-form-label">Count</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="pastTransactionsCount" required="true" placeholder="Count" />
							</div>
						</div>
						<div class="form-group row" id="simulate_decision_filter">
							<label for="pastTransactionDecisionFilter" class="col-sm-3 col-form-label">Decision</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="pastTransactionDecisionFilter" required="true" placeholder="Decision" />
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					<button type="button" class="btn btn-primary" onclick="simulateExecution(this)">Simulate</button>
				</div>
			</div>
		</div>
	</div>
</h:body>
</html>
