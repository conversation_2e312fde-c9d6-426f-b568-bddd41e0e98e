<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jpa="http://www.springframework.org/schema/data/jpa"
       xmlns="http://www.springframework.org/schema/beans"


       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd

        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.1.xsd
         http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-3.1.xsd              




        http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa-1.2.xsd">
	<!-- Start Spring Social -->
<bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
	<property name="ignoreResourceNotFound"><value>true</value></property>
	<property name="locations">
		<list>
			<value>classpath:properties/dev_joon.environment.properties</value>
		</list>
	</property>
</bean>

	<bean
		class='org.springframework.beans.factory.config.PropertyPlaceholderConfigurer'>
		<property name='location'>
			<value>classpath:properties/${PARAM1}.environment.properties</value>
		</property>
	</bean>

	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
		<property name="driverClass" value="${db.driver}"></property>
		<property name="jdbcUrl" value="${db.url}"></property>
		<property name="user" value="${db.username}"></property>
		<property name="password" value="${db.password}"></property>
		<property name="initialPoolSize" value="${hibernate.c3p0.initialPoolSize}"></property>
		<property name="maxPoolSize" value="${hibernate.c3p0.max_size}"></property>
		<property name="minPoolSize" value="${hibernate.c3p0.min_size}"></property>
		<property name="idleConnectionTestPeriod" value="${hibernate.c3p0.idle_test_period}"></property>
		<property name="maxStatements" value="${hibernate.c3p0.max_statements}"></property>
		<property name="maxIdleTime" value="${hibernate.c3p0.idle_test_period}"></property>
		<property name="preferredTestQuery" value="${hibernate.c3p0.validationQuery}"></property>
		<property name="testConnectionOnCheckout" value="${hibernate.c3p0.testOnBorrow}"></property>
		<property name="acquireIncrement" value="${hibernate.c3p0.acquireincrement}"></property>
		<property name="unreturnedConnectionTimeout"
			value="${hibernate.c3p0.unreturnedConnectionTimeout}"></property>
		<property name="maxConnectionAge" value="${hibernate.c3p0.maxconnectionage}"></property>
	</bean>

	<bean id="entityManagerFactory"
		class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
		<property name="dataSource" ref="dataSource" />
		<property name="persistenceUnitName" value="persistenceUnit" />
		<!-- <property name="packagesToScan"> <value>me.socure.service.bean org.springframework.social.connect.jpa.hibernate</value> 
			</property> -->
		<property name="jpaVendorAdapter">
			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
				<property name="showSql" value="false" />
				<property name="generateDdl" value="true" />
				<property name="database" value="MYSQL" />
			</bean>
		</property>
	</bean>

	<bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
		<property name="entityManagerFactory" ref="entityManagerFactory" />
	</bean>



	<tx:annotation-driven transaction-manager="transactionManager"
		proxy-target-class="false" />


		<jpa:repositories base-package="me.socure.service.ipaddress.dao"
		entity-manager-factory-ref="entityManagerFactory"
		transaction-manager-ref="transactionManager" />
		
	<jpa:repositories base-package="me.socure.service.dao"
		entity-manager-factory-ref="entityManagerFactory"
		transaction-manager-ref="transactionManager" />
		
		<!-- The following block of code added to test FRAUD MODELS transaction in dao -->
		<jpa:repositories base-package="me.socure.service.acxiom.dao"
		entity-manager-factory-ref="entityManagerFactory"
		transaction-manager-ref="transactionManager" />

	<!-- Remove when spring social security beans are replaced -->
	<context:component-scan
		base-package="org.springframework.social.connect.jpa.hibernate"></context:component-scan>



	<!-- Audit db config -->

	<bean id="auditdataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
		<property name="driverClass" value="${auditdb.driver}"></property>
		<property name="jdbcUrl" value="${auditdb.url}"></property>
		<property name="user" value="${auditdb.username}"></property>
		<property name="password" value="${auditdb.password}"></property>
		<property name="initialPoolSize" value="${hibernate.c3p0.initialPoolSize}"></property>
		<property name="maxPoolSize" value="${hibernate.c3p0.max_size}"></property>
		<property name="minPoolSize" value="${hibernate.c3p0.min_size}"></property>
		<property name="idleConnectionTestPeriod" value="${hibernate.c3p0.idle_test_period}"></property>
		<property name="maxStatements" value="${hibernate.c3p0.max_statements}"></property>
		<property name="maxIdleTime" value="${hibernate.c3p0.idle_test_period}"></property>
		<property name="preferredTestQuery" value="${hibernate.c3p0.validationQuery}"></property> 
		<!-- <property name="preferredTestQuery" value="0"></property> -->
		<property name="testConnectionOnCheckout" value="${hibernate.c3p0.testOnBorrow}"></property>
		<property name="acquireIncrement" value="${hibernate.c3p0.acquireincrement}"></property>
		<property name="unreturnedConnectionTimeout"
			value="${hibernate.c3p0.unreturnedConnectionTimeout}"></property>
		<property name="maxConnectionAge" value="${hibernate.c3p0.maxconnectionage}"></property>
	</bean>

	<bean id="auditentityManagerFactory"
		class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
		<property name="dataSource" ref="auditdataSource" />
		<property name="packagesToScan">
			<value>me.socure.service.audit.bean</value>
		</property>
		<property name="jpaVendorAdapter">
			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
				<property name="showSql" value="false" />
				<property name="generateDdl" value="true" />
				<property name="database" value="MYSQL" />
			</bean>
		</property>
	</bean>

	<bean id="audittransactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
		<property name="entityManagerFactory" ref="auditentityManagerFactory" />
	</bean>

	<jpa:repositories base-package="me.socure.service.audit.dao"
		entity-manager-factory-ref="auditentityManagerFactory"
		transaction-manager-ref="audittransactionManager" />

	<bean class="org.springframework.orm.jpa.support.PersistenceAnnotationBeanPostProcessor" />

</beans>