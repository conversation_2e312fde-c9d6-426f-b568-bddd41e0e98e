package me.socure.account.provisioning

import me.socure.account.automation.AccountAutomationClient
import me.socure.model.ErrorResponse
import me.socure.model.account.automation.{AccountProvisioningDetails, UpdateAccountProvisioningDetails}
import me.socure.util.EnvUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component

import java.util.concurrent.TimeUnit
import scala.concurrent.Await
import scala.concurrent.duration.Duration

@Component
class AccountAutomationResolver {
  private val logger = LoggerFactory.getLogger(getClass)

  @Autowired
  @Qualifier("envUtil")
  var envUtil: EnvUtil = _

  @Autowired
  @Qualifier("accountAutomationClient")
  var accountAutomationClient: AccountAutomationClient = _

  def getAccountProvisioningDetails(accountId: Long): Either[String, AccountProvisioningDetails] = {
    Await.result(accountAutomationClient.getAccountProvisioningDetails(accountId), Duration.create(40, TimeUnit.SECONDS)) match {
      case Right(accountProvisioningDetails) =>
        Right(accountProvisioningDetails)
      case Left(errorResponse: ErrorResponse) =>
        logger.info(s"Error while fetching Account Provisioning Details for Account: $accountId", errorResponse)
        Left(errorResponse.message)
    }
  }

  def saveAccountProvisioningDetails(accountId: Long, updateAccountProvisioningDetails: UpdateAccountProvisioningDetails): Either[String, Boolean] = {
    Await.result(accountAutomationClient.updateAccountProvisioningDetails(accountId, updateAccountProvisioningDetails), Duration.create(40, TimeUnit.SECONDS)) match {
      case Right(response) =>
        Right(response)
      case Left(errorResponse: ErrorResponse) =>
        logger.info(s"Error while saving Account Provisioning Details for Account: $accountId", errorResponse)
        Left(errorResponse.message)
    }
  }

}
