//
// Thumbnails
// --------------------------------------------------


// Mixin and adjust the regular image class
.thumbnail {
  .img-thumbnail();
  display: block; // Override the inline-block from `.img-thumbnail`
  margin-bottom: @line-height-computed;

  > img {
    .img-responsive();
    margin-left: auto;
    margin-right: auto;
  }
}


// Add a hover state for linked versions only
a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
  border-color: @link-color;
}

// Image captions
.thumbnail .caption {
  padding: @thumbnail-caption-padding;
  color: @thumbnail-caption-color;
}
