<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
<link rel="stylesheet" href="#{bucket.resourceURL}/styles/awesomplete.base.css"/>
<link rel="stylesheet" href="#{bucket.resourceURL}/styles/awesomplete.theme.css"/>
<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
<script src="#{bucket.resourceURL}/scripts/awesomplete.min.js"></script>
<script src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
<script src="#{bucket.resourceURL}/scripts/fraudmodel_mapping.js"></script>
<script src="#{bucket.resourceURL}/scripts/bootstrap.min.js" type="text/javascript"></script>
<meta charset="UTF-8"/>
<title>Fraud Model Mapping</title>

<script>
		bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		loggedinUsername = "#{loggedinUsername}";
		appenv = "#{bucket.appEnv}";
</script>
<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>


<script language="javascript">


$(function(){
    var comboplete = new Awesomplete('input.accounts-dropdown-input', {
        minChars: 0,
        maxItems: 1000
    });
    Awesomplete.$('.dropdown-btn').addEventListener("click", function() {
        if (comboplete.ul.childNodes.length === 0) {
            comboplete.minChars = 0;
            comboplete.evaluate();
        }
        else if (comboplete.ul.hasAttribute('hidden')) {
            comboplete.open();
        }
        else {
            comboplete.close();
        }
    });

	$('#mapfraudmodel').click(function() {

	  function validateJson(input) {
          if (input.trim() === "") {
             return "";
          }
          try {
             const json = JSON.parse(input);
             return input;
          } catch (error) {
             return "invalid json";
          }
      }
        var selectedAccount =$('.accounts-dropdown-input').val().trim().split(" - ")[0];
        var selectedmodel = $('#fraudmodel').val().trim();
        var filterCriteria = $('#filterCriteriaInput').val().trim();
        var dat = {
                userId : selectedAccount,
                fraudModelId : selectedmodel,
                associationType : $('#associationType').val().trim(),
                environment : $('#environment').val().trim(),
                filterCriteria : filterCriteria
            };
        if(selectedAccount == null ||
			selectedAccount === undefined ||
			selectedAccount.trim().length === 0 ||
			isNaN(selectedAccount)){
            alert("Please select Account from dropdown");
        } else if(selectedmodel == 'default') {
        	alert("Please select Fraud Model");
        } else if (validateJson(filterCriteria) === "invalid json") {
            alert("Please enter valid json for filter criteria");
        } else {
        	var url = "";
            if (app.modelManagement) {
        		url = contexturl + '/api/1/fraudmodel/map';
   			} else {
        		url = contexturl + "/api/1/fraudmodel/fraudassociate.jsonp";
    		}
        	$('#fraudModelResult').text("Processing...");
            $.ajax({
                url : url,
                data : dat,
                type : 'GET',
                success : function(data) {
                    $('#fraudModelResult').css('white-space','pre');
                    $('#fraudModelResult').text(data['msg']);
                    $('.accounts-dropdown-input').val('');
                    comboplete.destroy();
                    comboplete = new Awesomplete('input.accounts-dropdown-input', {
                        minChars: 0,
                        maxItems: 1000
                    });
                    $('#fraudmodel').val('default');
                    $('#filterCriteriaInput').val('');
                    get_fraudmodel_account_list();
                }

            });
        }
         DD_RUM.track("Map Fraud Model", { accountId: selectedAccount, fraudModel: selectedmodel, associationType: $('#associationType').val().trim(), environment : $('#environment').val().trim(), filterCriteria: filterCriteria });
    });
});

	</script>
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/fraud_model.js"></script>
</h:head>
<h:body>

<iframe width="1" height="1" frameborder="0" src="" id="downloadHelper" style="display:none;"></iframe>
	<section id="Loading" class="overlay show">
		<p>Loading ...</p>
		<div class="loader">
			<img src="#{bucket.resourceURL}/assets/images/socure-loader.svg" /> <span
				class="dot"></span>
		</div>
	</section>
<div align="center">
		<font color="blue"><h3>
				<h:outputText value="#{param['message']}" />
			</h3> </font>
	</div>
	<br/>
	<br/>
	<div align="right">
	   <h5>
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="fraudmanager">Fraud Model Manager</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;


		<h:outputLink value="logout">Logout</h:outputLink>
		&nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
	</div>
	<br/>
	<br/>
      <center><h3>Fraud Model Mapping</h3></center>
     <hr/>
           <center><b><div id="fraudModelResult"></div></b></center>
            <div class="container" style="width: 100%; min-width:1600px; margin-left: 120px;" >
                <div class="row" style="width: 100%">
                    <div class="span12" style="width: 100%" >

                        <div class="accordion" id="myaccordion" style="width: 97%" >

                            <!-- Delete Dialog box -->
							<div class="modal fade" id="MessageModal" tabindex="-1" role="dialog" aria-labelledby="MessageModalLabel"
								 aria-hidden="true">
								<div class="modal-dialog" role="document">
									<div class="modal-content">
										<div class="modal-header">
											<h5 class="modal-title" id="exampleModalLabel">Fraud Model Mapping</h5>
											<button type="button" class="close" data-dismiss="modal" aria-label="Close">
												<span aria-hidden="true">&times;</span>
											</button>
										</div>
										<div class="modal-body">
											<div id="information" style="padding-top: 15px;">
												<label id="delete_msg"></label>
											</div>
										</div>
										<div class="modal-footer">
											<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
											<button type="button" class="btn btn-primary" onclick="delete_fraud_mapping();">Delete</button>
										</div>
									</div>
								</div>
							</div>
                           <!-- Delete Dialog box -->

							<p>
								<a id="showMapping" data-toggle="collapse" href="#collapseExample" role="button" aria-expanded="false" aria-controls="collapseExample">
									Fraud Model Service
								</a>
								<ul style="width:inherit">
									<li>While mapping a model to an account, you can select between 'Primary', 'Sigma', 'Custom' &amp; 'Shadow'.</li>
									<li>Only one primary and one sigma models can be mapped to an account</li>
									<li>Sigma model is returned only on 3.0 calls</li>
									<li>Sigma models always have the name 'sigma' in the response, like generic models have the name 'generic'(not in debug node)</li>
									<li>3.0 require minimum one model mapped in the account. It can be any of the 3 model types mentioned above.</li>
									<li>3.0 calls with 'Legacy Associations 3.0' calls use 'generic' model from config if not explicitly mapped through super-admin as primary model (not a new change, but worth mentioning)</li>
									<li>'Sigma' models can only be mapped through super-admin. They cannot be configured in microservice-configurations.</li>
									<li>Shadow model scores will only appeat in the debug node and not in the response.</li>
									<li>Filter Criteria can be filled with a configuration following the syntax in <a href="https://www.notion.so/Model-Filter-Criteria-1431e4e9397380609dacd297f339428c">here</a> to use different models under different input conditions. This only works for Sigma Identity models and Sigma Synthetic models.</li>
								</ul>
						</p>
							<div class="collapse" id="collapseExample">
								<div class="card card-body" style="margin-left:-57px; min-width:1223px;">

									<form name ="input" action="" method="get">
										<div class="form-group row">
											<label for="accountSelect" class="col-sm-2 col-form-label">Account</label>
											<div class="col-sm-10">
												<input class="accounts-dropdown-input form-control-plaintext"
													   id="accountSelect" data-list="#socure_accounts"
													   style="border: 1px solid #ccc;border-radius: 5px;"
												/>
												<ul id="socure_accounts" style="display:none">
													<ui:repeat var="account" value="#{accounts}" varStatus="status">
														<li>#{account.id} - #{account.name}</li>
													</ui:repeat>
												</ul>
												<button class="dropdown-btn" type="button" style="height: 28px;"><span class="caret"></span></button>
											</div>
										</div>
										<div class="form-group row">
											<label for="fraudmodel" class="col-sm-2 col-form-label">Model</label>
											<div class="col-sm-10">
												<select id="fraudmodel" onchange="render_description_for_mapping();">
													<option value ="default" selected="selected">-- Select Fraud Model --</option>
													<ui:repeat var ="fm" value="#{fraudmodels}" varStatus = "status">
														<option value = "#{fm.publicId}">#{fm.name}</option>
													</ui:repeat>
												</select>
											</div>
											<div class="col-sm-8" id="description">
											</div>
										</div>

										<div class="form-group row">
											<label for="fraudmodel" class="col-sm-2 col-form-label">Description</label>
											<div class="col-sm-10" id="descriptionfield">
											</div>
										</div>

										<div class="form-group row">
											<label for="fraudmodel" class="col-sm-2 col-form-label">Association Type</label>
											<div class="col-sm-10">
												<select id="associationType">
													<option value="1">Primary</option>
													<option value="2">Sigma</option>
													<option value="3" selected="selected">Custom</option>
													<option value="4">Shadow</option>
												</select>
											</div>
										</div>
										<div class="form-group row">
											<label for="environment" class="col-sm-2 col-form-label">Environment</label>
											<div class="col-sm-10">
												<select id="environment">
													<option value="Production" selected="selected">Production</option>
													<option value="Sandbox">Sandbox</option>
													<option value="Development">Development</option>
												</select>
											</div>
										</div>
										<div class="form-group row">
											<label for="fraudmodel" class="col-sm-2 col-form-label">Filter Criteria</label>
											<div class="col-sm-10" id="filterCriteria">
												<textarea id="filterCriteriaInput" name="filterCriteriaInput" rows="5" cols="25" ></textarea>
											</div>
										</div>
										<input type="button" Value="Map" id="mapfraudmodel"/>
									</form>

									<ui:repeat var="mapping" value="#{mappings}" varStatus="status">
										<br />
										Account Id: #{mapping.accountId} <br />
										<p>Account Name:<i>#{mapping.accountName}</i></p>

										<table cellpadding='0' cellspacing='0' border='0'
											   class='table table-striped table-bordered' id='fraudaccount_table'>
											<thead>
											<tr id="headers">
												<th>Edit</th>
												<th>Name</th>
												<th>Identifier</th>
												<th>Version</th>
												<th>Score Name</th>
												<th>Association Type</th>
												<th>Normalized</th>
												<th>Environment</th>
												<th>Filter Criteria</th>
												<th>Delete</th>
											</tr>
											</thead>

											<ui:repeat var="model" value="#{mapping.models}" varStatus="status">
												<tr>
													<td><a class="mapping-edit-button" onclick="show_mapping_update_dialog('#{model.model.name}','#{mapping.accountId}', '#{model.associationType}')">Edit</a></td>
													<td>#{model.model.name}</td>
													<td>#{model.model.identifier}</td>
													<td>#{model.model.version}</td>
													<td>#{model.model.scoreName}</td>
													<td>
														<h:panelGroup rendered="#{model.associationType == 1}">
															Primary
														</h:panelGroup>
														<h:panelGroup rendered="#{model.associationType == 2}">
															Sigma
														</h:panelGroup>
														<h:panelGroup rendered="#{model.associationType == 3}">
															Custom
														</h:panelGroup>
														<h:panelGroup rendered="#{model.associationType == 4}">
															Shadow
														</h:panelGroup>
													</td>
													<td>#{model.model.normalized}</td>
													<td>#{model.model.environment}</td>
													<td>#{model.filterCriteria}</td>
													<td><a onclick="unmapModel(#{mapping.accountId}, '#{model.model.publicId}', '#{model.model.environment}', #{model.associationType})" href="#">Delete</a></td>
												</tr>
											</ui:repeat>
										</table>

									</ui:repeat>

								</div>
							</div>
                        </div>
                    </div>
                </div>
            </div>

	<div class="modal fade" id="UpdateConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="UpdateConfirmationLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="UpdateConfirmationLabel">Update Model Mapping</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="update_form"></label>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
					<button type="button" class="btn btn-primary" id="update_mapping">Update</button>
				</div>
			</div>
		</div>
	</div>
	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
</h:body>

</html>
