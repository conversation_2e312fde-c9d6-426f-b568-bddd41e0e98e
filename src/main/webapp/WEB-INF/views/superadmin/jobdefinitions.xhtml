<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html
        PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
    <style>
    /* The popup form - hidden by default */
    .form-popup-jobdefinition {
      display: none;
      position: fixed;
      border: 3px solid #f1f1f1;
      left: 400px;
      top: 15px;
      width: 700px;
      height: 200px;
      z-index: 9;
    }

    /* Add styles to the form container */
    .form-container {
      padding: 10px;
      background-color: white;
      overflow-y: scroll;
      max-height: 749px;
    }

    /* Full-width input fields */
    .form-container input[type=text],
    .form-container select,
    .form-container textarea {
      width: 100%;
      padding: 5px;
      margin: 5px 0 22px 0;
      border: none;
      background: #f1f1f1;
    }

    /* When the inputs get focus, do something */
    .form-container input[type=text]:focus,
    .form-container select:focus {
      background-color: #ddd;
      outline: none;
    }

    /* Set a style for the submit/login button */
    .form-container .btn {
      background-color: #555;
      color: white;
      padding: 16px 20px;
      border: none;
      cursor: pointer;
      width: 50%;
      margin-bottom: 10px;
      opacity: 0.8;
    }

    /* Add a red background color to the cancel button */
    .form-container .cancel {
      background-color: #555;
    }

    /* Add some hover effects to buttons */
    .form-container .btn:hover,
    .open-button:hover {
      opacity: 1;
    }
  </style>
    <script type="text/javascript" src="resources/scripts/jobdefinitions.js"></script>
</h:head>
<h:body>

    <button id="create_jobdefinition_popup" class="button-pad" onclick="addEditJobDefinitions()"> Create New Job
        Definition </button>

    <table border="2" style="margin-top:20px">
        <thead>
        <tr>
            <th>Type</th>
            <th>Name</th>
            <th>Image</th>
            <th>MSCV Version</th>
            <th>Description</th>
            <th>Service Account Name</th>
            <th>Event Rule Id</th>
            <th></th>
        </tr>
        </thead>
        <tbody id="jobdefinitionsTable"></tbody>
    </table>

    <div class="form-popup-jobdefinition" id="create_jobdefinition_container">
        <form action="" class="form-container">
            <label><b>Job Definition Name</b></label>
            <input type="text" id="jobdefinitionName" />

            <label><b>Job Definition Type</b></label>
            <input type="number" id="jobdefinitionType" />

            <label><b>Description</b></label>
            <input type="text" id="jobdefinitionDescription" />

            <label for="jobdefinitionImage"><b>Image</b></label>
            <input type="text" id="jobdefinitionImage" />

            <label for="jobdefinitionMSCVVersion"><b>mscv version</b></label>
            <input type="text" id="jobdefinitionMSCVVersion" />

            <label><b>BatchJob Path Specifier</b></label>
            <input type="text" id="jobdefinitionSpecifier" placeholder="ex: me.socure.batch.job.exporter.Main"/>

            <label><b>Service Account Name</b></label>
            <input type="text" id="jobdefinitionServiceAccount" />

            <label for="jobdefinitionIamRole"><b>IAMRole</b></label>
            <input type="text" id="jobdefinitionIamRole" />

            <label for="jobdefinitionParameterTemplate"><b>Parameter Template</b></label>
            <textarea id="jobdefinitionParameterTemplate"></textarea>

            <label><b>Event Rule</b></label>
            <input type="text" id="jobDefinitionEventRule" />

            <button type="button" class="btn" id="create_jobdefinition_submit_btn" value=""
                    onclick="createJobDefinition()">Submit</button>
            <button type="button" class="btn cancel" onclick="closeCreateJobDefinitionForm()">Close</button>
        </form>
    </div>

</h:body>

</html>
