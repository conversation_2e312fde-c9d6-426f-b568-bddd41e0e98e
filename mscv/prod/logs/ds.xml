<?xml version='1.0' encoding='UTF-8'?>
<configuration>
  <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <pattern>[%d] [%5p] [%X{TRANSACTION_ID}] [%X{INSTANCE_ID}] [%X{akkaSource}] [%X{akkaTimestamp}] [%t] \(%F:%M:%L\) %m%n</pattern>
    </encoder>
  </appender>
  <logger name="org.perf4j.TimingLogger" level="ERROR" additivity="false">
    <appender-ref ref="console"/>
  </logger>
  <root level="INFO">
    <appender-ref ref="console"/>
  </root>
</configuration>