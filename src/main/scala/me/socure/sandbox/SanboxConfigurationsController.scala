package me.socure.sandbox

import java.io.File
import java.util

import me.socure.common.json.bean.APIOutput
import me.socure.resolver.SandboxServiceResolver
import me.socure.sandbox.common.model.{AddConfigurationRequest, ListConfigurationResponse, MarkAsLiveRequest}
import me.socure.service.constants.IConstants
import me.socure.superadmin.authentication.UserDetailsWithoutCredentials
import me.socure.superadmin.factory.ObjectMapperFactory
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.FileSystemResource
import org.springframework.http.{HttpHeaders, HttpStatus, ResponseEntity}
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{PathVariable, RequestMapping, RequestMethod, RequestParam}
import org.springframework.web.multipart.MultipartFile


@Controller
@RequestMapping(Array("/superadmin/sandbox/*"))
class SanboxConfigurationsController {

  val logger = LoggerFactory.getLogger(getClass)

  val objectMapperFactory = ObjectMapperFactory.get()

  @Autowired
  var sandboxServiceResolver : SandboxServiceResolver = _

  @RequestMapping(value = Array("configuration"), method = Array(RequestMethod.GET))
  def getAllConfigurations(): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = sandboxServiceResolver.getAllConfigurations()
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    result match {
      case Right(response) => {
        var javaObject = new util.ArrayList[SandboxConfiguration]()
        response.foreach(x => javaObject.add(getConfigurationAsJava(x)));
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(javaObject);
        new ResponseEntity(apiOutput, headers, HttpStatus.OK)
      }
      case Left(error) => {
        logger.error("Error fetching all the revisions", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity(apiOutput, headers, HttpStatus.INTERNAL_SERVER_ERROR)
      }
    }
  }

  def getConfigurationAsJava(eachData : ListConfigurationResponse) : SandboxConfiguration = {
    val sandboxConfig = new SandboxConfiguration()
    sandboxConfig.setConfigName(eachData.configName)
    sandboxConfig.setConfigVersion(eachData.configVersion)
    sandboxConfig.setLive(eachData.isLive)
    sandboxConfig.setRevision(eachData.revision)
    sandboxConfig.setCreatedBy(eachData.createdBy)
    eachData.markedLiveAt.map(sandboxConfig.setMarkedLiveAt(_))
    eachData.markedLiveBy.map(sandboxConfig.setMarkedLiveBy(_))
    sandboxConfig
  }

  @RequestMapping(value = Array("configuration"), method = Array(RequestMethod.POST))
  def createNewRevision(@RequestParam("configFile") multipartFile: MultipartFile,
                        @RequestParam("configName") configName: String,
                        @RequestParam("configVersion") configVersion: String,
                        @RequestParam("isLive") isLive: Boolean): ResponseEntity[Object] = {

    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val addConfigurationRequest = AddConfigurationRequest(
      configName = configName,
      configVersion = configVersion,
      isLive = isLive,
      configuration = multipartFile.getInputStream,
      createdBy = userDetails.getUsername,
      markedLiveBy = if (isLive) Some(userDetails.getUsername) else None
    )
    val apiOutput = new APIOutput()
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    sandboxServiceResolver.addConfiguration(addConfigurationRequest) match {
      case Right(response) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response)
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(apiOutput), headers, HttpStatus.OK)
      }
      case Left(error) => {
        logger.error("Error creating revision", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(apiOutput), headers, HttpStatus.INTERNAL_SERVER_ERROR)
      }
    }
  }

  @RequestMapping(value = Array("configuration/{revision}/mark-as-live"), method = Array(RequestMethod.GET))
  def markAsLive(@PathVariable("revision") revision: Long): ResponseEntity[Object] = {
    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val markAsLiveRequest = MarkAsLiveRequest(revision = revision, markedLiveBy = userDetails.getUsername)
    val apiOutput = new APIOutput()
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    sandboxServiceResolver.markAsLive(markAsLiveRequest) match {
      case Right(response) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response);
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(apiOutput), headers, HttpStatus.OK)
      }
      case Left(error) => {
        logger.error("Error marking the revision live", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(apiOutput), headers, HttpStatus.INTERNAL_SERVER_ERROR)
      }
    }
  }

  @RequestMapping(value = Array("configuration/{revision}"), method = Array(RequestMethod.GET))
  def getConfiguration(@PathVariable("revision") revision: Long): ResponseEntity[Object] = {
    sandboxServiceResolver.getConfiguration(revision) match {
      case Left(error) => {
        logger.error("Error fetching configuration revision file", error)
        val headers = new HttpHeaders()
        headers.add("Content-Type", "application/json")
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(error.message), headers, HttpStatus.valueOf(error.code))
      }
      case Right(res) => {
        val targetFile = File.createTempFile("sandbox_configuration", "tmp")
        FileUtils.copyInputStreamToFile(res, targetFile)
        val headers = new HttpHeaders()
        headers.add("Content-Type", "application/octet-stream")
        headers.add("Content-Disposition", "attachment; filename=sandbox_configuration.csv")
        new ResponseEntity(new FileSystemResource(targetFile), headers, HttpStatus.OK)
      }
    }

  }
}
