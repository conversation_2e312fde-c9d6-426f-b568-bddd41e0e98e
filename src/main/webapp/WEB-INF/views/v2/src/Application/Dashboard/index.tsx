import React, { useState, useEffect } from 'react';
import { Grid, Typography, Paper, Box } from '@mui/material';
import { toaster } from '../../Components/Toaster';
import { apiCall } from '../../Utilities';

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<any>(null);

  useEffect(() => {
    // Example of loading data when component mounts
    const fetchData = async () => {
      setLoading(true);
      try {
        // Replace with your actual API endpoint
        const response = await apiCall('/api/dashboard/data');
        setData(response.data);
        toaster.success('Dashboard data loaded successfully');
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toaster.error('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    // Uncomment to enable data fetching
    // fetchData();
    
    // For now, just set some dummy data
    setData({ title: 'Dashboard Overview', metrics: [
      { name: 'Total Users', value: 1250 },
      { name: 'Active Sessions', value: 78 },
      { name: 'Alerts', value: 12 }
    ]});
  }, []);

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h5" gutterBottom>
          {data?.title || 'Dashboard'}
        </Typography>
      </Grid>
      
      {data?.metrics && data.metrics.map((metric: any, index: number) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Paper elevation={3} sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6" color="primary">
              {metric.name}
            </Typography>
            <Box mt={2}>
              <Typography variant="h4">
                {metric.value}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      ))}
      
      {loading && (
        <Grid item xs={12}>
          <Typography>Loading dashboard data...</Typography>
        </Grid>
      )}
    </Grid>
  );
};

export default Dashboard;
