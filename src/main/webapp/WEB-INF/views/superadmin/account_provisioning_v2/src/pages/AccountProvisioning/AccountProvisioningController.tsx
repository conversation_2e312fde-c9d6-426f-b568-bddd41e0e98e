import React from 'react';
import { groupBy, map, sortBy, cloneDeep, remove, orderBy } from 'lodash-es';
import { NumericDictionary } from 'lodash';
import { CellActionProps, HeaderProps } from '@socure/components/src/components/TreeGrid/TreeGrid.type';
import { InputType } from '@socure/components';
import { getAccountModules, getMetadata, updateAccountProvisioning, getAccountState } from './service';
import AccountProvisioningView from './AccountProvisioningView';
import './accountProvisioning.scss';
import { AccountProvisioningUpdateDetails, BundleDetails, Module, ModulesUpdatePayload, TreeFormattedModule, ModifiedModules, SpecialProductsConfiguration, DocVStrategiesList } from './AccountProvisioning.type';
import RetentionPolicyDetails from './RetentionPolicyDetails';
import BusinessUserRoles from './BusinessUserRoles';
import SaiPennyDropEnablement,{SaiPennyDropValues} from "./SpecialComponents/SaiPennyDropEnablement"
import { deepClone } from '../../Utilities';
import ApiKeyDetails from './ApiKeyDetails';

let provisionClicksCount = 0;
const piiRetentionEnum = {
  READ_ONLY: "Read Only",
  FULL_ACCESS: "Full Access"
}

const IP = "Improved Provisioning";

const EVENTS = {
  UPDATE_CONFIRMATION: "Update Confirmation",
  PROVISIONED: "Provisioned",
  DEPROVISIONED: "Deprovisioned",
  ACTIVATED: "Activated",
  DEACTIVATED: "Deactivated",
  BUNDLE_UPDATE: "Bundle Update",
  SHOW_UPDATE_CONFIRMATION: "Show Update Confirmation",
  RESET_PROVISIONING: "Reset Provisioning"
}


type AccountProvisioningProps = {
  accountId: string;
  isReadOnly: boolean;
  isSubaccount: boolean;
  loggedinUsername: string;
  piiRetentionAccessPermission: string;
  isInternal: boolean;
}

type AccountProvisioningState = {
  selectedBundle: string;
  treeFormattedModules: TreeFormattedModule[],
  existingModules: Module[],
  bundleMetadata: BundleDetails[],
  docVStrategiesMetadata: DocVStrategiesList[],
  showConfirmDialog: boolean,
  showWatchListMonitorDialog:boolean,
  existingBundle: string,
  ein: {
    value?: string,
    errorMessageOnValidation: string,
  },
  saiPennyDropValues: SaiPennyDropValues,
  retentionDetails: {
    cadence?: number,
    routine?: string,
    errorMessageOnValidation: string
  },
  apiKeyDetails: {
    lookupApiKey?: string,
    serviceId?: string,
    simSwapErrorMessageOnValidation: string,
    lookupApiKeyServiceIdErrorMessageOnValidation: string
  },
  docVStrategy?: string,
  performanceSla: string,
  wlmCadence: string,
  existingProductConfiguration: SpecialProductsConfiguration,
  accountState: boolean,
  cellActionProperty: CellActionProps,
  showToggleFlagDialog: boolean,
  decisionWaningText: string;
}

const getRootModuleIndentLevel = (module: TreeFormattedModule, treeFormattedModules: TreeFormattedModule[]) => {
  let rootModule = module;
  let count = 0;
  while (rootModule.parentId) {
    rootModule = treeFormattedModules.find(x => x.id === rootModule.parentId) as TreeFormattedModule;
    count++;
  }
  return count;
}

const getTreeFormattedModules = (existingModules: Module[], isSubaccount: boolean) => {

  const treeFormattedModules: TreeFormattedModule[] = cloneDeep(orderBy(existingModules, 'enabled', 'desc'));              // enabled modules to be iterated first

  treeFormattedModules.forEach(flag => {
    const parentModule = treeFormattedModules.find(x => flag.parentId && x.id === flag.parentId);
    if (parentModule) {
      if (parentModule.expanded === undefined) {
        parentModule.expanded = false;
      }
      flag.indentLevel = parentModule.indentLevel !== undefined ? parentModule.indentLevel + 1 : getRootModuleIndentLevel(flag, treeFormattedModules);       // DP approach to calculate indentLevel from parent's indentLevel if available
      flag.parent_businessUserRoleId = parentModule.businessUserRoleId
    } else {
      flag.indentLevel = 0;
    }
    if (flag.businessUserRoleId === BusinessUserRoles.MULTI_MODEL_DECISIONING) {
        flag["enabled-disabled"] = true;
    } else if (parentModule) {
      flag["provisioned-disabled"] = isSubaccount && flag.provisioningType.toUpperCase() !== 'ADHOC';
      flag["enabled-disabled"] = (isSubaccount && flag.provisioningType.toUpperCase() === 'FORCED INHERIT') || !(parentModule && parentModule?.enabled);
    } else {
      flag["provisioned-disabled"] = isSubaccount && flag.provisioningType.toUpperCase() !== 'ADHOC' && !flag.allowEditing;
      flag["enabled-disabled"] = (isSubaccount && flag.provisioningType.toUpperCase() === 'FORCED INHERIT');
    }

  })
  return treeFormattedModules;
}

const headerConfig: HeaderProps[] = [{ "key": "name", "label": "modules", "type": "text", "width": 500, "hierarchyColumn": true, fontWeight: 'bold' },
{ "key": "provisioned", "label": "provision", "type": "checkBox", "width": 75 },
{ "key": "enabled", "label": "active", "type": "checkBox", "width": 50 },
//{ "key": "provisioningType", "label": "inheritance", "type": "text", "width": 100 },
{ "key": "updatedBy", "label": "updated by", "type": "text", "width": 150 },
{ "key": "updatedAt", "label": "last updated", "type": "dateTime", "width": 200, "dateFormat": "DD/MM/YYYY", "timeFormat": "hh:mm a" }];
const specialCaseFlags = [BusinessUserRoles.PERFORMANCE_SLA, BusinessUserRoles.WLM_CADENCE];
const invalidEINMessage = "EIN number should be 9 digits"
const valueNotEmptyMessage = "Value should not be empty";
const defaultDocVStrategy = "DocV V2 Standard";

class AccountProvisioning extends React.PureComponent<AccountProvisioningProps, AccountProvisioningState> {
  modifiedProducts: ModifiedModules[];
  modifiedFeatureFlags: ModifiedModules[];
  modifiedProvisioning: ModifiedModules[];

  constructor(props: AccountProvisioningProps) {
    super(props);
    this.state = {
      treeFormattedModules: [],
      existingModules: [],
      selectedBundle: '',
      bundleMetadata: [],
      docVStrategiesMetadata: [],
      showConfirmDialog: false,
      showToggleFlagDialog: false,
      showWatchListMonitorDialog:false,
      decisionWaningText: 'string',
      existingBundle: '',
      ein: {
        value: '',
        errorMessageOnValidation: '',
      },
      saiPennyDropValues:  {
        depositorName: {
            value: '',
            errorMessageOnValidation: '',
        },
        memo: {
            value: '',
            errorMessageOnValidation: '',
        },
        physicalAddress: {
            value: '',
            errorMessageOnValidation: '',
        },
        city: {
            value: '',
            errorMessageOnValidation: '',
        },
        state: {
            value: '',
            errorMessageOnValidation: '',
        },
        zip: {
            value: '',
            errorMessageOnValidation: '',
        },
        country: {
            value: '',
            errorMessageOnValidation: '',
        }
    },
      retentionDetails: {
        cadence: 0,
        routine: '',
        errorMessageOnValidation: ''
      },
      apiKeyDetails:{
        lookupApiKey: '',
        serviceId: '',
        simSwapErrorMessageOnValidation: '',
        lookupApiKeyServiceIdErrorMessageOnValidation: ''
      },
      docVStrategy: '',
      performanceSla: '',
      wlmCadence: '',
      existingProductConfiguration: {},
      accountState: false,
      cellActionProperty: {
        id: '',
        column: '',
        data: false
      }
    }
    this.modifiedProducts = [];
    this.modifiedFeatureFlags = [];
    this.modifiedProvisioning = [];
    this.getTreeGridPayload = this.getTreeGridPayload.bind(this);
    this.handleCellAction = this.handleCellAction.bind(this);
    this.updateHandler = this.updateHandler.bind(this);
    this.handleBundleUpdate = this.handleBundleUpdate.bind(this);
    this.generatePayload = this.generatePayload.bind(this);
    this.getAccountData = this.getAccountData.bind(this);
    this.getRootModule = this.getRootModule.bind(this);
    this.resetProvisioning = this.resetProvisioning.bind(this);
    this.noErrorInProductConfig = this.noErrorInProductConfig.bind(this);
    this.canEnableSubmission = this.canEnableSubmission.bind(this);
    this.updateSaiPennyDropValuesHandler = this.updateSaiPennyDropValuesHandler.bind(this);
    this.updateSaiPennyDropValuesFromApi = this.updateSaiPennyDropValuesFromApi.bind(this);
  }

  componentDidMount() {
    getMetadata()
      .then(metadata => {
        this.setState({
          bundleMetadata: sortBy(metadata.bundleMapping, ['name']),
          docVStrategiesMetadata: metadata.docVStrategies
        })
        this.getAccountData();
        this.getAccountState();
      })
  }

  updateSaiPennyDropValuesFromApi(apiValue: SpecialProductsConfiguration): void {
    const saiPennyDropValues:SaiPennyDropValues = {
      depositorName: {
        value: '',
        errorMessageOnValidation: ''
      },
      memo: {
        value: '',
        errorMessageOnValidation: ''
      },
      physicalAddress: {
        value: '',
        errorMessageOnValidation: ''
      },
      city: {
        value: '',
        errorMessageOnValidation: ''
      },
      state: {
        value: '',
        errorMessageOnValidation: ''
      },
      zip: {
        value: '',
        errorMessageOnValidation: ''
      },
      country: {
        value: '',
        errorMessageOnValidation: ''
      }
    }
    if(apiValue.saiPreferences) {
      const apiValues = apiValue.saiPreferences;
      saiPennyDropValues.depositorName.value = apiValues.depositorName;
      saiPennyDropValues.memo.value = apiValues.memo;
      saiPennyDropValues.physicalAddress.value = apiValues.physicalAddress;
      saiPennyDropValues.city.value = apiValues.city;
      saiPennyDropValues.state.value = apiValues.state;
      saiPennyDropValues.zip.value = apiValues.zip;
      saiPennyDropValues.country.value = apiValues.country;
    }
    this.setState({ saiPennyDropValues });
  }

  getAccountState() {
    const { accountId } = this.props;
    getAccountState(accountId)
      .then(data => this.setState({ accountState: data }))
  }

  updateSaiPennyDropValuesHandler(field: string, value: string) {
    const { saiPennyDropValues } = this.state;
    (saiPennyDropValues as any)[field].value = value;
    if (value) {
      (saiPennyDropValues as any)[field].errorMessageOnValidation = '';
    } else {
      (saiPennyDropValues as any)[field].errorMessageOnValidation = `${field} is required.`;
    }
    this.setState({
      saiPennyDropValues: deepClone(saiPennyDropValues)
    })
  }


  getAccountData() {
    const { accountId, isSubaccount } = this.props;
    const { saiPennyDropValues } = this.state;

    getAccountModules(accountId)
      .then(data => {
        this.updateSaiPennyDropValuesFromApi(data.productConfiguration)
        this.setState({
        existingBundle: data.bundleReference,
        selectedBundle: data.bundleReference,
        treeFormattedModules: getTreeFormattedModules(data.products, isSubaccount),
        existingModules: data.products,
        existingProductConfiguration: data.productConfiguration,
        ein: { value: data.productConfiguration.ein || '', errorMessageOnValidation: '' },
        retentionDetails: {
          cadence: data.productConfiguration?.retentionSchedule?.cadence,
          routine: data.productConfiguration?.retentionSchedule?.routine,
          errorMessageOnValidation: ''
        },
        apiKeyDetails: {
            lookupApiKey: data.productConfiguration?.lookupApiKey ?? '',
            serviceId: data.productConfiguration?.serviceId ?? '',
            simSwapErrorMessageOnValidation: '',
            lookupApiKeyServiceIdErrorMessageOnValidation: ''
          },
        docVStrategy: data.productConfiguration?.docVStrategy,
        performanceSla: '',
        wlmCadence: '',
      })
    })
  }
  confirmWatchListMonitorUnEnrollment = () => {
    this.setState({showWatchListMonitorDialog: false});
    this.updateHandler(true);
  }
  updateHandler = (confirmedWatchListMonitorUnEnrollment?:boolean) => {
    const { accountId, loggedinUsername } = this.props;
    this.setState({ showConfirmDialog: false });
    const payload = this.generatePayload();
    if(payload.unEnrollWatchListMonitors && !confirmedWatchListMonitorUnEnrollment){
      this.setState({showWatchListMonitorDialog:true}) //get confirmation from user to un-enroll all watchlist monitors
    } else {
    this.setState({ showWatchListMonitorDialog: false })
    updateAccountProvisioning(accountId, payload)
      .then(() => {
        this.getAccountData();
        this.modifiedProducts = [];
        this.modifiedFeatureFlags = [];
        this.modifiedProvisioning = [];
        DD_RUM.track(EVENTS.UPDATE_CONFIRMATION, { user: loggedinUsername, accountId: accountId, clickCount: ++provisionClicksCount, provType: IP });
      });
    }
  }

  proceedToggleHandler = () => {
    this.setState({ showConfirmDialog: false });
    this.handleCellAction(this.state.cellActionProperty)
  }

  getRootModule = (module: TreeFormattedModule) => {
    const { treeFormattedModules } = this.state;
    let rootModule = module;
    while (rootModule.parentId) {
      rootModule = treeFormattedModules.find(x => x.id === rootModule.parentId) as TreeFormattedModule;
    }
    return rootModule;
  }

  getTreeGridPayload = (forBundle: Boolean) => {
    const { treeFormattedModules, bundleMetadata, selectedBundle, retentionDetails, ein, performanceSla, wlmCadence, docVStrategy, docVStrategiesMetadata, apiKeyDetails} = this.state;
    const { piiRetentionAccessPermission, isInternal } = this.props;
    const treeFormattedModulesClone = sortBy(cloneDeep(treeFormattedModules), ['order']);
    const featureFlags = treeFormattedModulesClone.filter(x => x.parentId !== undefined);
    featureFlags.forEach(flag => delete flag.provisioned);
    const groupedFeatureFlags: NumericDictionary<TreeFormattedModule[]> = groupBy(featureFlags, 'parentId');
    const sortedFeatureFlagGroup: NumericDictionary<TreeFormattedModule[]> = {}
    map(groupedFeatureFlags, (value, key) => sortedFeatureFlagGroup[key] = orderBy(value, ['defaultState', 'order'], ['desc', 'asc']))
    const treeGridPayload: TreeFormattedModule[] = [];
    let chosenProducts: number = 0;
    let chosenFFs: number = 0;
    let INSTANTLY_REMOVE_PII_enabled = false;

    treeFormattedModulesClone.forEach(module => {
      if (module.businessUserRoleId === BusinessUserRoles.ECBSV) {       // eCBSV is a special flag to have an additional text input of EIN number
        module.specialRow = {
          componentName: "TextInput", props: {
            label: "EIN Number",
            value: ein.value,
            onChange: (value: string) => this.setState({ ein: { value, errorMessageOnValidation: value.length !== 9 ? invalidEINMessage : '' } }),
            inputType: InputType.number,
            doValidate: true,
            errorMsg: ein.value ? ein.errorMessageOnValidation : valueNotEmptyMessage
          }
        }
      }

      if (module.businessUserRoleId === BusinessUserRoles.SIM_SWAP_LOOKUP) {      
        module.specialRow = {
          component: <ApiKeyDetails 
          apiKeyDetails = {apiKeyDetails}
          moduleId =  {module.businessUserRoleId}
          onChange={(key: string, value: string | number, errorMessageOnValidation: string) => this.setState({
            apiKeyDetails:{
              ...apiKeyDetails, [key]: value,
              simSwapErrorMessageOnValidation: errorMessageOnValidation
            }
          })} />
        }
      }

      if (module.businessUserRoleId === BusinessUserRoles.SAI_PENNY_DROP_ENABLEMENT) {
        module.specialRow = {
          component: <SaiPennyDropEnablement
            value={this.state.saiPennyDropValues}
            handleChange={this.updateSaiPennyDropValuesHandler}
          />
        }
      }

      if (module.businessUserRoleId === BusinessUserRoles.MULTI_MODEL_DECISIONING) {
        const toggledFlag = treeFormattedModulesClone.find(flag => flag.businessUserRoleId === BusinessUserRoles.MULTI_MODEL_DECISIONING)
        toggledFlag && (toggledFlag["enabled-disabled"] = true);
      }

      if (module.businessUserRoleId === BusinessUserRoles.DECISION_DOCV_UI && module.enabled) {       // disable he decision self service flag if docv decision enable
        const toggledFlag = treeFormattedModulesClone.find(flag => flag.businessUserRoleId === BusinessUserRoles.DECISION_SELF_SERVICE_UI)
        if (toggledFlag && toggledFlag.enabled) {
          toggledFlag["enabled-disabled"] = true;
        }
      }

      if (module.businessUserRoleId === BusinessUserRoles.PII_RETENTION_SCHEDULE) {        // PII Retention Schedule
        module.specialRow = {
          component: <RetentionPolicyDetails retentionDetails={retentionDetails}
            onChange={(key: string, value: string | number, errorMessageOnValidation: string) => this.setState({
              retentionDetails: {
                ...retentionDetails, [key]: value,
                errorMessageOnValidation
              }
            })} />
        }
      }

      if (module.businessUserRoleId === BusinessUserRoles.MFA_ORCHESTRATION || module.businessUserRoleId === BusinessUserRoles.SILENT_NETWORK_AUTHENTICATION) {  
        module.specialRow = {
          component: <ApiKeyDetails 
          apiKeyDetails = {apiKeyDetails}
          moduleId =  {module.businessUserRoleId}
          onChange={(key: string, value: string | number, errorMessageOnValidation: string) => this.setState({
            apiKeyDetails:{
              ...apiKeyDetails, [key]: value,
              lookupApiKeyServiceIdErrorMessageOnValidation: errorMessageOnValidation
            }
          })} />
        }
      }

      if (module.businessUserRoleId === BusinessUserRoles.ENABLE_DOCV_ORCHESTRA) {
        const docVStrategyNames = docVStrategiesMetadata?.map(x => x.name);
        module.specialRow = {
          componentName: "Dropdown", props: {
            label: "DocV use case config",
            selected: docVStrategy ? docVStrategiesMetadata.find(x => x.strategyId === docVStrategy)?.name as string : defaultDocVStrategy,
            values: docVStrategyNames,
            onChange: (value: string) => this.setState({ docVStrategy: docVStrategiesMetadata.find(x => x.name === value)?.strategyId as string }),
            doValidate: true,
            errorMsg: docVStrategy === '' && valueNotEmptyMessage
          }
        }
      }

      if (module.businessUserRoleId === BusinessUserRoles.PERFORMANCE_SLA) {        // Performance SLA to have sub feature flags as dropdown
        const specialFlags = treeFormattedModulesClone.filter(x => x.parentId === module.id);
        const chosenFlag = performanceSla || specialFlags.find(x => x.enabled)?.name || specialFlags.find(x => x.defaultState)?.name
        const specialValues = specialFlags.map(x => x.name);
        module.specialRow = {
          componentName: "Dropdown", props: {
            selected: chosenFlag,
            values: specialValues,
            onChange: (value: string) => this.setState({ performanceSla: value })
          }
        }
        delete sortedFeatureFlagGroup[module.id];
        module.expanded = undefined;
      }

      if (module.businessUserRoleId === BusinessUserRoles.WLM_CADENCE) {        // WLM Cadence to have sub feature flags as dropdown
        const specialFlags = treeFormattedModulesClone.filter(x => x.parentId === module.id);
        const chosenFlag = wlmCadence || specialFlags.find(x => x.enabled)?.name || specialFlags.find(x => x.defaultState)?.name
        const specialValues = specialFlags.map(x => x.name);
        module.specialRow = {
          componentName: "Dropdown", props: {
            selected: chosenFlag,
            values: specialValues,
            onChange: (value: string) => this.setState({ wlmCadence: value })
          }
        }
        delete sortedFeatureFlagGroup[module.id];
        module.expanded = undefined;
      }

    })

    treeFormattedModulesClone.forEach(module => {
      const isProduct = !module.parentId;
      const selectedBundleDetails = selectedBundle ? bundleMetadata.find(x => x.name === selectedBundle) as BundleDetails : '';
      let listingModule;
      if (selectedBundleDetails === '') { // for A la carte flow
        listingModule = true
      } else {
        listingModule = forBundle ? selectedBundleDetails.resources.includes(this.getRootModule(module).businessUserRoleId) : !selectedBundleDetails.resources.includes(this.getRootModule(module).businessUserRoleId);
      }

      module.enabled && listingModule && (isProduct ? chosenProducts++ : chosenFFs++);
      if (listingModule && isProduct) {
        !module.provisioned && (module["enabled-disabled"] = true);
        treeGridPayload.push(module);
        if (module.expanded) {
          if (sortedFeatureFlagGroup[module.id]) {
            treeGridPayload.push(...sortedFeatureFlagGroup[module.id]);
            sortedFeatureFlagGroup[module.id].forEach(featureFlag => {
              if (featureFlag.expanded) {
                const index = treeGridPayload.findIndex(a => a.id === featureFlag.id);
                treeGridPayload.splice(index + 1, 0, ...sortedFeatureFlagGroup[featureFlag.id])
              }
            })
          }
        }
      }
      // disable PII Policy only when okta flag is not "Full Access" & is not internal account
      if (this.getRootModule(module).businessUserRoleId === BusinessUserRoles.PII_RETENTION_POLICY) {
        if (piiRetentionAccessPermission !== piiRetentionEnum.FULL_ACCESS && !isInternal) {
          module['enabled-disabled'] = true;
          module['provisioned-disabled'] = true;
        }

        // get all child flags
        const allChildFF = treeFormattedModulesClone.filter(x => this.getRootModule(x).id === module.id);
        allChildFF.forEach(child => {
          // disable drop PII when account is activated
          if (child.businessUserRoleId === BusinessUserRoles.INSTANTLY_REMOVE_PII) {
            INSTANTLY_REMOVE_PII_enabled = child.enabled;
            if (this.state.accountState) {
              child['enabled-disabled'] = true;
              child['provisioned-disabled'] = true;
            }
          }

          // disable PII Retentionschedule & Mask PII on view when Drop PII is enabled.
          if ((child.businessUserRoleId === BusinessUserRoles.PII_RETENTION_SCHEDULE || child.businessUserRoleId === BusinessUserRoles.MASK_PII_ON_VIEW) && INSTANTLY_REMOVE_PII_enabled) {
            child['enabled-disabled'] = true;
            child['provisioned-disabled'] = true;
          }
          
          if (child.businessUserRoleId === BusinessUserRoles.MULTI_MODEL_DECISIONING) {
            child["enabled-disabled"] = true;
          }
        })
      }
    })
    return { accordionPayload: treeGridPayload, chosenModules: chosenProducts, chosenFFs };
  }


  getModifiedProductsList = (updatedRow: TreeFormattedModule, data: boolean) => {
    const alreadyModifedProducts = this.modifiedProducts.find(x => x.name === updatedRow.name);
    if (alreadyModifedProducts) {
      remove(this.modifiedProducts, alreadyModifedProducts);
    }
    else {
      this.modifiedProducts.push({ name: updatedRow.name, update: data ? 'ADD' : 'REMOVE' })
    }
  }

  getModifiedFeatureFlags = (updatedRow: TreeFormattedModule, data: boolean) => {
    const alreadyModifedFeatureflag = this.modifiedFeatureFlags.find(x => x.name === updatedRow.name);
    if (alreadyModifedFeatureflag) {
      remove(this.modifiedFeatureFlags, alreadyModifedFeatureflag);
    }
    else {
      this.modifiedFeatureFlags.push({ name: updatedRow.name, update: data ? 'ADD' : 'REMOVE' })
    }
  }

  handleCellAction = (cellActionProps: CellActionProps) => {
    const { treeFormattedModules } = this.state;
    const { isReadOnly, isSubaccount, accountId, loggedinUsername } = this.props;

    const updatedFormattedModules = cloneDeep(treeFormattedModules);
    const { id, column, data } = cellActionProps;
    const updatedRow = updatedFormattedModules.find(x => x.id === id) as TreeFormattedModule;

    if (column === 'expanded') {
      updatedRow.expanded = data;
    } else if (!isReadOnly) {
      updatedRow[column] = data
      if (column === 'provisioned') {
        updatedRow["enabled-disabled"] = isSubaccount && updatedRow.provisioningType.toUpperCase() === 'FORCED INHERIT' ? false : !data;   // enable enabled checkbox only when product is provisioned for parent accounts and for sub accounts, disable for forced inherit
        updatedRow["enabled"] = data;
        updatedRow.expanded = updatedRow.expanded !== undefined ? data : undefined;

        if (!data) {           // set enabled of the module's all feature flags to false when the product is deprovisioned  
          DD_RUM.track(EVENTS.DEPROVISIONED, { user: loggedinUsername, accountId: accountId, clickCount: ++provisionClicksCount, provType: IP });
          const allChildFlags = updatedFormattedModules.filter(x => this.getRootModule(x).id === updatedRow.id);
          allChildFlags.forEach(childFlag => {
            if (childFlag.id !== updatedRow.id && !specialCaseFlags.includes(updatedRow.businessUserRoleId)) { // only for child flags
              childFlag.enabled = false;
              childFlag["enabled-disabled"] = true;
            }
          });
        } else {        // Set enabled of the module's feature flags to it's default state when the product is provisioned
          DD_RUM.track(EVENTS.PROVISIONED, { user: loggedinUsername, accountId: accountId, clickCount: ++provisionClicksCount, provType: IP });
          const featureFlags = updatedFormattedModules.filter(x => x.parentId === updatedRow.id && !specialCaseFlags.includes(updatedRow.businessUserRoleId));
          featureFlags.forEach(x => {
            x.enabled = x.defaultState;
            x["enabled-disabled"] = false;
            if (x.expanded !== undefined && x.defaultState) {       // Set the sub feature flags also to default state when the parent feature flag's default state is true
              const subFeatureFlags = updatedFormattedModules.filter(y => y.parentId === x.id);
              subFeatureFlags.forEach(y => {
                y.enabled = y.defaultState;
                y["enabled-disabled"] = false;
              });
            }
          });
        }

        const alreadyModifed = this.modifiedProvisioning.find(x => x.name === updatedRow.name);
        if (alreadyModifed) {
          remove(this.modifiedProvisioning, alreadyModifed);
        }
        else {
          this.modifiedProvisioning.push({ name: updatedRow.name, update: data ? 'ADD' : 'REMOVE' })
        }

        if (updatedRow.parentId === undefined) {
          this.getModifiedProductsList(updatedRow, data);
        }
        if (updatedRow.parentId !== undefined) {
          this.getModifiedFeatureFlags(updatedRow, data);
        }
      }

      if (column === 'enabled' && updatedRow.parentId === undefined) {       // information captured for update summary component
        this.getModifiedProductsList(updatedRow, data);
      } else if (column === 'enabled' && updatedRow.parentId !== undefined) { // track feature flag
        if (updatedRow.businessUserRoleId === BusinessUserRoles.DECISION_DOCV_UI) { // toggle role between  "Decision Self Service UI" and "Enable Decision UI for customers only live with DocV"
          if (this.state.showToggleFlagDialog === false){
            data ? this.setState({ decisionWaningText: 'Warning! By changing to "Enable Decision UI for customers only live with DocV" flag, there will be significant changes to the decision logic view, along with a new live logic. All of the existing logics in this account, if any, will be removed and replaced with a new DocV Baseline file as live by default. Please proceed cautiously and only if you acknowledge and agree to these changes.' })
              : this.setState({ decisionWaningText: 'Warning! By changing to "Decision Self-service UI" flag, there will be significant changes to the decision logic view, along with a new live logic. All of the existing logics in this account, if any, will be removed and replaced with a new baseline logic v6 file as live by default. Please proceed cautiously and only if you acknowledge and agree to these changes.' })
            this.setState({ cellActionProperty: cellActionProps, showToggleFlagDialog: true })
            return
          } else {
            this.setState({ showToggleFlagDialog: false });
            const toggledFlag = updatedFormattedModules.find(flag => flag.businessUserRoleId === BusinessUserRoles.DECISION_SELF_SERVICE_UI)
            if (toggledFlag) {
              if (data) {
                if (!toggledFlag.enabled) {
                  toggledFlag.enabled = true
                  this.getModifiedFeatureFlags(toggledFlag, true);
                }
                toggledFlag["enabled-disabled"] = true; 
              } else {
                toggledFlag["enabled-disabled"] = false; 
              }
            }
            this.getModifiedFeatureFlags(updatedRow, data);
          }
        } else {
          this.getModifiedFeatureFlags(updatedRow, data);
        }
      }

      if (column === 'enabled' && !specialCaseFlags.includes(updatedRow.businessUserRoleId)) {
        if (data) {          // expand the module when enabled and set all it's submodules to it's default state
          DD_RUM.track(EVENTS.ACTIVATED, { user: loggedinUsername, accountId: accountId, clickCount: ++provisionClicksCount, provType: IP });
          const subModules = updatedFormattedModules.filter(x => x.parentId === id);
          updatedRow.expanded !== undefined ? data : undefined;
          if (subModules) {
            subModules.forEach(x => {
              x.enabled = x.defaultState;
              x["enabled-disabled"] = false;
              if (x.enabled) {
                this.getModifiedFeatureFlags(x, x.enabled);
              }
              if (x.expanded !== undefined && x.defaultState) {
                const subFeatureFlags = updatedFormattedModules.filter(y => y.parentId === x.id);
                subFeatureFlags.forEach(y => {
                  y.enabled = y.defaultState;
                  y["enabled-disabled"] = false;
                  if (y.enabled) {
                    this.getModifiedFeatureFlags(y, y.enabled);
                  }
                });
              }
            });
          }
        } else {
          DD_RUM.track(EVENTS.DEACTIVATED, { user: loggedinUsername, accountId: accountId, clickCount: ++provisionClicksCount, provType: IP });
          const isProduct = updatedRow.parentId === undefined;  // set enabled of all it's feature flag as false when the product is disabled
          const allChildFlags = updatedFormattedModules.filter(flag => isProduct ? this.getRootModule(flag).id === updatedRow.id && flag.id !== updatedRow.id : flag.parentId === updatedRow.id);
          allChildFlags.forEach(childFlag => {
            childFlag.enabled = false;
            childFlag["enabled-disabled"] = true
          });
        }
      }
    }

    this.setState({ treeFormattedModules: updatedFormattedModules })
  };

  handleBundleUpdate = (selectedBundle: string) => {
    const { bundleMetadata, treeFormattedModules } = this.state;
    const { accountId, loggedinUsername } = this.props;
    const updatedFormattedModules = cloneDeep(treeFormattedModules);
    const selectedBundleDetails = bundleMetadata.find(x => x.name === selectedBundle) as BundleDetails;
    selectedBundleDetails.resources.forEach(productId => {
      const bundleProduct = updatedFormattedModules.find(x => x.businessUserRoleId === productId);
      if (bundleProduct) {
        bundleProduct.provisioned = true;
        bundleProduct.enabled = true;
        bundleProduct["enabled-disabled"] = false;
        const featureFlags = updatedFormattedModules.filter(x => x.parentId === bundleProduct.id);
        if (featureFlags) {
          featureFlags.forEach(x => {
            // if a feature flag is a bundle resource, enable it by default
            x.enabled = (selectedBundleDetails.resources.includes(x.businessUserRoleId)) ? true : x.defaultState;
            x["enabled-disabled"] = false;

            if (x.expanded !== undefined && x.defaultState) {
              const subFeatureFlags = updatedFormattedModules.filter(y => y.parentId === x.id);
              subFeatureFlags.forEach(y => {
                // if a feature flag is a bundle resource, enable it by default
                y.enabled = (selectedBundleDetails.resources.includes(y.businessUserRoleId)) ? true : y.defaultState;
                y["enabled-disabled"] = false;
              })
            }
          });
        }

        const alreadyModifedProducts = this.modifiedProducts.find(x => x.name === bundleProduct.name);
        if (alreadyModifedProducts) {
          remove(this.modifiedProducts, alreadyModifedProducts);
        }

      }
    })
    DD_RUM.track(EVENTS.BUNDLE_UPDATE, { user: loggedinUsername, accountId: accountId, selectedBundleName: selectedBundle, existingBundle: this.state.existingBundle, clickCount: ++provisionClicksCount, provType: IP });
    this.setState({ selectedBundle, treeFormattedModules: updatedFormattedModules })
  };

  generatePayload = (): AccountProvisioningUpdateDetails => {
    const { treeFormattedModules, existingModules, selectedBundle, ein, retentionDetails, performanceSla, wlmCadence, existingProductConfiguration, docVStrategy, docVStrategiesMetadata, saiPennyDropValues, apiKeyDetails } = this.state;
    const products: ModulesUpdatePayload[] = [];
    const requestPayload: AccountProvisioningUpdateDetails = { bundleReference: selectedBundle, products, productConfiguration: {}, unEnrollWatchListMonitors: false }
    existingModules.forEach(module => {
      const updatedModule = treeFormattedModules.find(x => x.id === module.id) as TreeFormattedModule;
      if (updatedModule.parentId) {          // set the provisioned of feature flag as enabled flag if it's set true, or retain it's old value to retain the audit log
        updatedModule.provisioned = updatedModule.enabled || module.provisioned;
      }
      if (!specialCaseFlags.includes(updatedModule.parent_businessUserRoleId as number) && (module.provisioned !== updatedModule.provisioned || module.enabled !== updatedModule.enabled)) {
        const entry = { id: module.id, provisioned: updatedModule.provisioned as boolean, enabled: updatedModule.enabled }
        products.push(entry);
      }

      if (updatedModule.businessUserRoleId === BusinessUserRoles.ECBSV && updatedModule.enabled && existingProductConfiguration.ein !== ein.value) {
        requestPayload.productConfiguration.ein = ein.value;
      }

      if (updatedModule.businessUserRoleId === BusinessUserRoles.MFA_ORCHESTRATION && updatedModule.enabled && (existingProductConfiguration.lookupApiKey !== apiKeyDetails.lookupApiKey || existingProductConfiguration.serviceId !== apiKeyDetails.serviceId)) {
        requestPayload.productConfiguration.lookupApiKey = apiKeyDetails.lookupApiKey;
        requestPayload.productConfiguration.serviceId = apiKeyDetails.serviceId;
      }

      if (updatedModule.businessUserRoleId === BusinessUserRoles.SILENT_NETWORK_AUTHENTICATION && updatedModule.enabled && (existingProductConfiguration.lookupApiKey !== apiKeyDetails.lookupApiKey || existingProductConfiguration.serviceId !== apiKeyDetails.serviceId)) {
        requestPayload.productConfiguration.lookupApiKey = apiKeyDetails.lookupApiKey;
        requestPayload.productConfiguration.serviceId = apiKeyDetails.serviceId;
      }

      if (updatedModule.businessUserRoleId === BusinessUserRoles.SIM_SWAP_LOOKUP && updatedModule.enabled && (existingProductConfiguration.lookupApiKey !== apiKeyDetails.lookupApiKey)) {
        requestPayload.productConfiguration.lookupApiKey = apiKeyDetails.lookupApiKey;
      }

      if(updatedModule.businessUserRoleId === BusinessUserRoles.SAI_PENNY_DROP_ENABLEMENT && updatedModule.enabled && this.isSaiPennyDropValuesUpdated(existingProductConfiguration, saiPennyDropValues)) {
        requestPayload.productConfiguration.saiPreferences = {
          depositorName: saiPennyDropValues.depositorName.value,
          memo: saiPennyDropValues.memo.value,
          physicalAddress: saiPennyDropValues.physicalAddress.value,
          city: saiPennyDropValues.city.value,
          state: saiPennyDropValues.state.value,
          zip: saiPennyDropValues.zip.value,
          country: saiPennyDropValues.country.value
        }
      }

      if (updatedModule.businessUserRoleId === BusinessUserRoles.PII_RETENTION_SCHEDULE && updatedModule.enabled &&
        (existingProductConfiguration.retentionSchedule?.cadence !== retentionDetails.cadence || existingProductConfiguration.retentionSchedule?.routine !== retentionDetails.routine)) {
        requestPayload.productConfiguration.retentionSchedule = { "cadence": Number(retentionDetails.cadence), "routine": retentionDetails.routine }
      }
      if (updatedModule.businessUserRoleId === BusinessUserRoles.ENABLE_DOCV_ORCHESTRA && updatedModule.enabled && (!module.enabled || existingProductConfiguration.docVStrategy !== docVStrategy)) {           // When flag activated for first time, pick the chosen value or the default value
        requestPayload.productConfiguration.docVStrategy = docVStrategy || docVStrategiesMetadata.find(x => x.name === defaultDocVStrategy)?.strategyId;
      }

      // Performance SLA/ WLM Cadence special cases
      if (updatedModule.businessUserRoleId === BusinessUserRoles.PERFORMANCE_SLA) {
        if (updatedModule.enabled && module.enabled && performanceSla) {        // When dropdown value alone gets modified, remove old flag and add new flag
          const updatedFlag = treeFormattedModules.find(x => x.name === performanceSla)?.id as number
          const removedFlag = existingModules.find(x => x.parentId === updatedModule.id && x.enabled)?.id as number
          products.push({ id: updatedFlag, provisioned: true, enabled: true }, { id: removedFlag, provisioned: false, enabled: false })
        }

        if (!updatedModule.enabled && module.enabled) {       // When the FF itself is deactivated, remove the already provisioned SLA value
          const removedFlag = existingModules.find(x => x.parentId === updatedModule.id && x.enabled)?.id as number
          products.push({ id: removedFlag, provisioned: false, enabled: false })
        }

        if (updatedModule.enabled && !module.enabled) {       // When the FF is activated, add the flag chosen in the dropdown or pick the default state flag
          const updatedFlag = (treeFormattedModules.find(x => x.name === performanceSla)?.id
            || treeFormattedModules.find(x => x.defaultState && x.parentId === updatedModule.id)?.id) as number
          products.push({ id: updatedFlag, provisioned: true, enabled: true })
        }
      }

      if (updatedModule.businessUserRoleId === BusinessUserRoles.WLM_CADENCE) {
        if (updatedModule.enabled && module.enabled && wlmCadence) {
          const updatedFlag = treeFormattedModules.find(x => x.name === wlmCadence)?.id as number
          const removedFlag = existingModules.find(x => x.parentId === updatedModule.id && x.enabled)?.id as number
          products.push({ id: updatedFlag, provisioned: true, enabled: true }, { id: removedFlag, provisioned: false, enabled: false })
        }

        if (!updatedModule.enabled && module.enabled) {
          const removedFlag = existingModules.find(x => x.parentId === updatedModule.id && x.enabled)?.id as number
          products.push({ id: removedFlag, provisioned: false, enabled: false })
        }

        if (updatedModule.enabled && !module.enabled) {
          const updatedFlag = (treeFormattedModules.find(x => x.name === wlmCadence)?.id
            || treeFormattedModules.find(x => x.defaultState && x.parentId === updatedModule.id)?.id) as number
          products.push({ id: updatedFlag, provisioned: true, enabled: true })
        }
      }
      if(updatedModule.businessUserRoleId === BusinessUserRoles.WATCHLIST_MONITORING && !updatedModule.enabled && module.enabled){
        requestPayload.unEnrollWatchListMonitors = true; // If watchlist monitoring is being removed backend should trigger job to un-enroll all watchlist monitors
      }

    })
    requestPayload.products = products;
    return requestPayload;
  }

  showUpdateConfirmationHandler = () => {
    const { accountId, loggedinUsername } = this.props;
    this.setState({ showConfirmDialog: true });
    DD_RUM.track(EVENTS.SHOW_UPDATE_CONFIRMATION, { user: loggedinUsername, accountId: accountId, clickCount: ++provisionClicksCount, provType: IP });
  }
  closeWatchListMonitorDialog = () => this.setState({ showWatchListMonitorDialog: false });
  closeUpdateConfirmationHandler = () => this.setState({ showConfirmDialog: false });
  closeToggleFlagHandler = () => this.setState({ showToggleFlagDialog: false });
  resetProvisioning = () => {
    const { existingModules, existingBundle, existingProductConfiguration } = this.state;
    const { isSubaccount, accountId, loggedinUsername } = this.props;
    DD_RUM.track(EVENTS.RESET_PROVISIONING, { user: loggedinUsername, accountId: accountId, clickCount: ++provisionClicksCount, provType: IP });


    this.modifiedFeatureFlags = [];
    this.modifiedProducts = [];
    this.modifiedProvisioning = [];
    this.updateSaiPennyDropValuesFromApi(existingProductConfiguration)
    this.setState({
      selectedBundle: existingBundle,
      treeFormattedModules: getTreeFormattedModules(existingModules, isSubaccount),
      ein: {
        value: existingProductConfiguration.ein || "",
        errorMessageOnValidation: '',
      },
      retentionDetails: {
        cadence: 0,
        routine: '',
        errorMessageOnValidation: ''
      },
      apiKeyDetails: {
        lookupApiKey: existingProductConfiguration.lookupApiKey || '',
        serviceId: existingProductConfiguration.serviceId || '',
        simSwapErrorMessageOnValidation: '',
        lookupApiKeyServiceIdErrorMessageOnValidation: ''
      },
      performanceSla: '',
      wlmCadence: ''
    });
  }

  noErrorInProductConfig = () => {
    const { treeFormattedModules, retentionDetails, ein, docVStrategy, apiKeyDetails } = this.state;
    let errorCount = 0;
    if (treeFormattedModules.find(module => module.businessUserRoleId === BusinessUserRoles.PII_RETENTION_SCHEDULE && module.enabled) && (retentionDetails.errorMessageOnValidation !== '' || retentionDetails.cadence === 0 || retentionDetails.routine === '')) ++errorCount;
    if (treeFormattedModules.find(module => module.businessUserRoleId === BusinessUserRoles.ECBSV && module.enabled) && (ein.errorMessageOnValidation !== '' || ein.value === '')) ++errorCount;
    if (treeFormattedModules.find(module => module.businessUserRoleId === BusinessUserRoles.ENABLE_DOCV_ORCHESTRA && module.enabled) && docVStrategy === '') ++errorCount;
    if (treeFormattedModules.find(module => (module.businessUserRoleId === BusinessUserRoles.MFA_ORCHESTRATION || module.businessUserRoleId === BusinessUserRoles.SILENT_NETWORK_AUTHENTICATION) && module.enabled) && (apiKeyDetails.lookupApiKeyServiceIdErrorMessageOnValidation !== '' || apiKeyDetails.lookupApiKey === '' || apiKeyDetails.serviceId === '')) errorCount++;
    if (treeFormattedModules.find(module => module.businessUserRoleId === BusinessUserRoles.SIM_SWAP_LOOKUP && module.enabled) && (apiKeyDetails.simSwapErrorMessageOnValidation !== '' || apiKeyDetails.lookupApiKey === '')) errorCount++;
    return errorCount === 0;
  }

  isSaiPennyDropValuesUpdated = (existing: SpecialProductsConfiguration, updated: SaiPennyDropValues):Boolean => {
    const { saiPreferences } = existing;
    if(saiPreferences) {
      return updated.depositorName.value !== saiPreferences.depositorName ||
      updated.memo.value !== saiPreferences.memo ||
      updated.physicalAddress.value !== saiPreferences.physicalAddress ||
      updated.city.value !== saiPreferences.city ||
      updated.state.value !== saiPreferences.state ||
      updated.zip.value !== saiPreferences.zip ||
      updated.country.value !== saiPreferences.country;
    } else {
      return (
        updated.depositorName.value ||
          updated.memo.value ||
          updated.physicalAddress.value ||
          updated.city.value ||
          updated.state.value ||
          updated.zip.value ||
          updated.country.value
          ? true : false);
    }
  }

  canEnableSubmission = ():boolean => {
    const { selectedBundle, existingBundle, existingProductConfiguration, retentionDetails, ein, performanceSla, wlmCadence, docVStrategy, saiPennyDropValues, apiKeyDetails } = this.state;

    return (this.modifiedProducts.length > 0 || this.modifiedFeatureFlags.length > 0 || this.modifiedProvisioning.length > 0
      || selectedBundle !== existingBundle || existingProductConfiguration.ein !== ein.value
      || existingProductConfiguration.retentionSchedule?.cadence !== retentionDetails.cadence || existingProductConfiguration.retentionSchedule?.routine !== retentionDetails.routine
      || existingProductConfiguration.lookupApiKey !== apiKeyDetails.lookupApiKey || existingProductConfiguration.serviceId !== apiKeyDetails.serviceId
      || existingProductConfiguration.docVStrategy !== docVStrategy || (performanceSla && true) || (wlmCadence && true) || this.isSaiPennyDropValuesUpdated(existingProductConfiguration, saiPennyDropValues))
      && this.noErrorInProductConfig();
  }

  render(): React.ReactNode {
    const { bundleMetadata, selectedBundle, showConfirmDialog, existingBundle, treeFormattedModules } = this.state;
    const { isReadOnly, isSubaccount } = this.props;
    const bundlesList = bundleMetadata && bundleMetadata.map(x => x.name);
    return (
      <AccountProvisioningView
        isReadOnly={isReadOnly}
        isSubaccount={isSubaccount}
        headerConfig={headerConfig}
        getTreeGridPayload={this.getTreeGridPayload}
        bundlesList={bundlesList}
        updateBundle={this.handleBundleUpdate}
        handleCellAction={this.handleCellAction}
        selectedBundle={selectedBundle}
        modifiedProducts={this.modifiedProducts}
        modifiedFeatureFlags={this.modifiedFeatureFlags}
        showUpdateConfirmationHandler={this.showUpdateConfirmationHandler}
        closeUpdateConfirmationHandler={this.closeUpdateConfirmationHandler}
        showModalDialog={showConfirmDialog}
        updateHandler={this.updateHandler}
        propChanged={this.canEnableSubmission()}
        existingBundle={existingBundle}
        resetProvisioning={this.resetProvisioning}
        selectedBundleDetail={bundleMetadata.find(x => x.name === selectedBundle) as BundleDetails}
        payload={treeFormattedModules}
        showToggleFlagDialog={this.state.showToggleFlagDialog}
        closeToggleFlagHandler={this.closeToggleFlagHandler}
        proceedToggleHandler={this.proceedToggleHandler}
        decisionWaningText={this.state.decisionWaningText}
        showWatchListMonitorDialog={this.state.showWatchListMonitorDialog}
        confirmWatchListMonitorUnEnrollment={this.confirmWatchListMonitorUnEnrollment}
        closeWatchListMonitorDialog={this.closeWatchListMonitorDialog}
      />
    )
  }
}

export default AccountProvisioning;
