$(document).ready(function() {
  showOrHideControlCenter();
  $('#tbl_active_users').dataTable({
		"sPaginationType": "two_button",
		"aaSorting": [[1, 'asc']],
		"bProcessing": true,
		"bpaginate": true,
		"bServerSide": true,
		"bDestroy": true,
		"bRetrieve": true,
		"bResponsive": true,
		"oLanguage": {"sProcessing": '<section id="Loading" class="overlay show"><p>Loading...</p><div class="loader"><img src="'+ bucketurl + '/assets/images/socure-loader.svg" /><span class="dot"></span></div></section>'},
    "aoColumns": getColumnsDef(),
    "sAjaxSource": contexturl + "/superadmin/list/active_account?",
		"fnServerData": function ( sSource, aoData, fnCallback ) {
			$.getJSON( sSource, aoData, function (json) {
				var newJson = {}
				newJson.iTotalRecords = totalRecords
				newJson.iTotalDisplayRecords = totalRecords
				newJson.aaData = json
				fnCallback(newJson)
			});
	}

  })
	
  $('#tbl_active_users_filter input').unbind();
 
	$('#tbl_active_users_wrapper').on('page', function() {
    window.scrollTo(0,0)
  });
	
	const processChange = debounce((val) => search(val));
	$('#tbl_active_users_filter input').bind('keyup', function(e) {
		processChange($(this).val())
		DD_RUM.track("Search Account", {searchVal: $(this).val(), });
	});
});

window.onload = function() {
  DD_RUM && DD_RUM.startView({name: "/active_users"});
  DD_RUM && DD_RUM.setUser({ "id": loggedinUsername});
}

function redirect(id) {
  DD_RUM.track("Open Account Details", { accountId: id});
  if(id) window.location.href = "account_details?"+"accountid="+id;
}

function getFormatedDate(isoDate) {
	var date = new Date(isoDate);
	var dd = String(date.getDate()).padStart(2, '0');
	var mm = date.toLocaleString('default', { month: 'short' });
	var yyyy = date.getFullYear();

	return dd + "-" + mm + "-" + yyyy;
}

function getColumnsDef() {
	var columns = [
		{ "mData": "accountId", "bSortable": true},
		{ "mData": "firstName", "bSortable": true},
		{ "mData": "lastName", "bSortable": true},
		{ "mData": "companyName", "bSortable": true, "mRender": function(data, type, row) {
				return "<a href='javascript:redirect(" + row.accountId + "," + '"'  + row.companyName + '"' + ");'>"  + row.companyName + "</a>"}
		},
		{ "mData": "contactNumber", "bSortable": true},
		{ "mData": "email", "bSortable": true},
		{ "mData": "apiKey", "bSortable": true},
		{ "mData": "roles", "bSortable": true, "mRender": function(data, type, row) {
			var roleList = "<ul>";
			data.length && data.forEach(function( role, index) {
					roleList += "<li>" + role + "</li>";
			})
			roleList += "</ul>";
			return roleList;
		}},
		{ "mData": "isInternalUser", "bSortable": true},
		{ "mData": "registredon", "bSortable": true, "mRender" : function (data, type, row) {
			return getFormatedDate(data);
		}},
		{ "mData": "domainAuthorized", "bSortable": true, "mRender": function(data, type, row) {
			var domains = data.split(',');
			var formattedDomains = "<ul>" ;
			domains.length && domains.forEach(function(domain, index) {
				if(domain) {
					formattedDomains += "<li>" + domain + "</li>";
				}
			})
			formattedDomains += "</ul>";
			return formattedDomains;
		}},
		{ "mData": "encryptionEnabled", "bSortable": true},
		{ "mData": "piiMaskEnabled", "bSortable": true},
		{ "mData": "publicAccountId", "bSortable": true},
		{ "mData": "externalAccountId", "bSortable": true}
	]
	return columns;
}

function showOrHideControlCenter() {
  displayClass = (canAccessControlCentreAccessFlag === controlCenterAccessType.noAccess ? 'hide' : '');
  $('#control_center_span')[0].className = displayClass;
}

function debounce(func, timeout = 300){
  let timer;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => { func.apply(this, args); }, timeout);
  };
}

function search(val) {
	var activeUserTable = $('#tbl_active_users').dataTable()
	activeUserTable.fnFilter(val);
	if(val == '') {
		$("#tbl_active_users_info").show()
	} else {
		$("#tbl_active_users_info").hide()
	}
}