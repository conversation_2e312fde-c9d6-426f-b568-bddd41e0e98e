 var eventType ="DEFAULT";
       $(document).ready(function(){

            $(document).ajaxStart(function() {
               $('#LoadingRefresh').removeClass("loading-overlay-show loading-overlay-hide").addClass("loading-overlay-show");
            });

            $(document).ajaxStop(function() {
                $('#LoadingRefresh').removeClass("loading-overlay-hide loading-overlay-show").addClass("loading-overlay-hide");
            });

            clearPage();

            $("#txnsubmit").click(function () {
              var id = ($("#Id").val() || "").trim();
              var radios = document.getElementsByName("type");
              if (radios[3].checked) {
                id = ($("#uuid").val() || "").trim();
              }
              var accountId;
              clearPage(true);

              if (radios[0].checked) {
                window.location.hash = id;
                DD_RUM.track("Troubleshooting Transaction", { searchId: id });
              }
              $("#documents").attr("href", "#");
              $("#profileImage").attr("href", "#");
              $("#extractedProfileImage").attr("href", "#");
              $("#documents").closest("tr").hide();
              $("#captureChannelValue").closest("tr").hide();
              $("#profileImage").closest("tr").hide();
              $("#extractedProfileImage").closest("tr").hide();

              if (id == "") {
                alert(
                  radios[3].checked
                    ? "Document UUID can't be empty."
                    : "Id can't be empty."
                );
                $("#Id").focus();
              } else {
                var isTxn = true;
                if (radios[1].checked) {
                  isTxn = false;
                  var stepUpType = document.getElementsByName("stepUpType");
                  if (stepUpType[0].checked) {
                    eventType = "EVENT_REFERENCE";
                    DD_RUM.track("Troubleshooting Stepup Event ID", {
                      searchId: id,
                      stepupType: "Webhook Events Details",
                    });
                  } else if (stepUpType[1].checked) {
                    submitStepUpEvent(id);
                    DD_RUM.track("Troubleshooting Stepup Event ID", {
                      searchId: id,
                      stepupType: "StepUp Process Details",
                    });
                    return;
                  } else {
                    submitStepUpEventByDocVTransactionToken(id);
                    DD_RUM.track("Troubleshooting Stepup Event ID", {
                      searchId: id,
                      stepupType: "StepUp Processes by DocVTransactionToken",
                    });
                    return;
                  }
                } else if (radios[2].checked) {
                  eventType = "WATCHLIST_MONITORING";
                  DD_RUM.track("Troubleshooting Watchlist Monitoring", {
                    searchId: id,
                  });
                } else if (radios[3].checked) {
                  eventType = "DOCUMENT_UUID";
                  isTxn = false;
                  accountId = ($("#inputId2").val() || "").trim();
                  if (accountId == "") {
                    alert("accountId can't be empty.");
                    $("#uuid").focus();
                    return false;
                  }
                  var uuid = ($("#uuid").val() || "").trim();
                  DD_RUM.track("Troubleshooting Document UUID", {
                    accountId: accountId,
                    documentUUID: uuid,
                  });
                } else if (radios[4].checked) {
                  isTxn = false;
                  var stepUpType = document.getElementsByName("caseMgmtType");
                  if (stepUpType[0].checked) {
                    submitCaseMgmtTroubleshooting(id, "case");
                    DD_RUM.track("Troubleshooting Case Management", {
                      searchId: id,
                      caseMgmtType: "Case ID",
                    });
                    return;
                  } else {
                    submitCaseMgmtTroubleshooting(id, "txn");
                    DD_RUM.track("Troubleshooting Case Management", {
                      searchId: id,
                      caseMgmtType: "Transaction ID",
                    });
                    return;
                  }
                } else if (radios[5].checked) {
                  isTxn = false;
                  window.open(
                    contexturl + "/docv_bb_transaction?txnId=" + id,
                    "_blank"
                  );
                  return;
                }

                var dat = {
                  txnID: id,
                  type: eventType,
                };
                if (eventType === "DOCUMENT_UUID") {
                  dat.accountId = accountId;
                }
                $.ajax({
                  url: contexturl + "/superadmin/1/troubleshooting",
                  //dataType : 'jsonp',
                  data: dat,
                  type: "GET",
                  error: function (e) {
                    alert("No records found for given Id!");
                    $("#Id, #inputId2, #uuid").val("");
                  },
                  success: function (data) {
                    if (isTxn) {
                      handleTxnResponse(data, id);
                      callThirdPartyApi();
                    } else if (eventType === "DOCUMENT_UUID") {
                      if (data?.api?.status == "Ok") {
                        handleUuidResponse(dat.txnID, dat.accountId); //Sending UUID and accountID
                      } else {
                        alert(data?.api?.msg || "No Records found!");
                      }
                    } else handleEventResponse(data);
                  },
                });
              }
            });

            $('#refreshButton').click(function() {
                callThirdPartyApi();
                DD_RUM.track("Refresh Third Party Calls");
            });

            if(window.location.hash && window.location.hash.trim().length > 1) {
                $('#Id').val(window.location.hash.trim().substr(1));
                $('#txnsubmit').click();
            }

        });

        function callThirdPartyApi(){
            $('#thirdPartyHeader').show();
            $('#refresh').show();

            var payload = {
                type: eventType,
                txnID: $('#resultTxnId').text(),
                transactionDate: $('#transactionDate').val(),
                accountId: $('#resultaccountId').text()
            };

            if (eventType === 'WATCHLIST_MONITORING') {
                payload.referenceId = $('#referenceId').val();
            }


            $.ajax({
                url: contexturl + "/superadmin/1/troubleshooting/thirdPartyTransaction",
                type: 'GET',
                data: payload,
                success: function (data) {
                    // Fetch Source Map first, then process API response
                    getSourceMap().then(sourceMap => {
                        var modifiedData = transformApiResponse(data, sourceMap);
                        handleThirdPartyResponse(modifiedData);
                    }).catch(error => {
                        console.error("Error fetching Source Map:", error);
                        handleThirdPartyResponse(data); // Proceed without modification if source map fails
                    });
                },
                error: function (e) {
                    $('#refresh').hide();
                    $('#thirdPartyHeader').hide();
                    $("#Id, #inputId2, #uuid").val("");
                }
            });
        }

function transformApiResponse(data, sourceMap) {
    if (!data || typeof data !== "object") {
        return data;
    }

    function updatePolicies(policies) {
        if (!Array.isArray(policies)) {
            return []; // Ensure it returns an empty array if policies are not defined
        }
        return policies.map(policy => {
            if (!policy || typeof policy !== "object") return policy;
            const updatedSourceList = Array.isArray(policy.sourceList)
                ? policy.sourceList.map(sourceId => {
                      const sourceName = sourceMap[sourceId] || "UNKNOWN";
                      return `${sourceId} [ ${sourceName} ]`;
                  })
                : [];
            return { ...policy, sourceList: updatedSourceList };
        });
    }

    // Navigate through the API response to find the correct location
    for (const key in data.stats) {
        for (const requestKey in data.stats[key]) {
            if (
                typeof data.stats[key][requestKey]["Request Body"] === "string" &&
                (data.stats[key][requestKey]["Request Body"].includes("WlScreeningCustomSettings") ||
                data.stats[key][requestKey]["Request Body"].includes("WlMonitoringCustomSettings"))
            ) {
                try {
                    let parsedResponse =
                        typeof data.stats[key][requestKey]["Response"] === "string"
                            ? JSON.parse(data.stats[key][requestKey]["Response"])
                            : data.stats[key][requestKey]["Response"]; // Ensure response is an object

                    let modified = {
                        ...parsedResponse,
                        screeningPolicies: updatePolicies(parsedResponse.screeningPolicies),
                        monitoringPolicies: updatePolicies(parsedResponse.monitoringPolicies)
                    };

                    data.stats[key][requestKey]["Response"] = JSON.stringify(modified, null, 2);
                } catch (e) {
                    console.error("Error parsing API response JSON:", e);
                }
            }
        }
    }
    return data;
}

        function getSourceMap() {
            return $.ajax({
                url: "watchlist_source_settings",
                type: "GET",
                dataType: "json"
            }).then(response => {
                if (Array.isArray(response)) { // Ensure response is an array
                    let sourceMap = {};
                    response.forEach(item => {
                        if (item.id && item.name) {
                            sourceMap[item.id] = item.name; // Map id to name
                        }
                    });
                    return sourceMap;
                } else {
                    console.error("Invalid source map response format:", response);
                    return {};
                }
            }).catch(error => {
                console.error("Error fetching source map:", error.statusText || error);
                return {};
            });
        }


        function submitStepUpEvent(eventId) {
            $.ajax({
                        url : contexturl + "/superadmin/stepUp/process/details/events/"+eventId,
                        //dataType : 'jsonp',
                        type : 'GET',
                        error: function(e) {
                            alert("No records found for given Id!");
                            $("#Id, #inputId2, #uuid").val("");
                        },
                        success : function(data) {
                               handleStepUpResponse(data);
                        }

                    });
        }

        function submitStepUpEventByDocVTransactionToken(docVTransactionToken) {
          $.ajax({
            url:
              contexturl +
              "/superadmin/stepUp/process/details/docv/" +
              docVTransactionToken,
            type: "GET",
            error: function (e) {
              alert("No records found for given DocVTransactionId!");
              $("#Id, #inputId2, #uuid").val("");
            },
            success: function (data) {
              handleStepUpResponse(data);
            },
          });
        }

        function handleStepUpResponse(data){
            var jsonResponse = JSON.parse(data).data;
            if(Array.isArray(jsonResponse)) jsonResponse = jsonResponse[0]
            $('#stepUpProcessResult').show();
            $('#stepUpEventId').text(jsonResponse.eventReferenceId);
            $('#stepUpPublicAccountId').text(jsonResponse.publicAccountId);
            $('#stepUpAccountId').text(jsonResponse.accountId);
            $('#stepUpVerificationLevel').text(jsonResponse.verificationLevel);

            for(i=0;jsonResponse.processUploadAuditDetails.length>i;i++){
                jsonResponse.processUploadAuditDetails[i]["createdAt"] = new Date(jsonResponse.processUploadAuditDetails[i]["createdAt"]).toUTCString();
            }

            for(i=0;jsonResponse.processStatusDetails.length>i;i++){
                jsonResponse.processStatusDetails[i]["createdAt"] = new Date(jsonResponse.processStatusDetails[i]["createdAt"]).toUTCString();
            }

            for(i=0;jsonResponse.processIdPlusAuditDetails.length>i;i++){
                jsonResponse.processIdPlusAuditDetails[i]["createdAt"] = new Date(jsonResponse.processIdPlusAuditDetails[i]["createdAt"]).toUTCString();
            }
            if(jsonResponse.processPayloadDetails["payload"] != null){
                jsonResponse.processPayloadDetails["payload"]["createdAt"] = new Date(jsonResponse.processPayloadDetails["payload"]["createdAt"].millis).toUTCString();
                jsonResponse.processPayloadDetails["payload"]["updatedAt"] = new Date(jsonResponse.processPayloadDetails["payload"]["updatedAt"].millis).toUTCString();
            }

            $('#stepUpFlowConfiguration').val(vkbeautify.json(jsonResponse.flowConfig,4));
            $('#stepUpStatusDetails').val(vkbeautify.json(jsonResponse.processStatusDetails,4));
            $('#stepUpPayloadDetails').val(vkbeautify.json(jsonResponse.processPayloadDetails,4));
            $('#stepUpConfigDetails').val(vkbeautify.json(jsonResponse.processConfigDetails,4));
            $('#stepUpIdPlusDetails').val(vkbeautify.json(jsonResponse.processIdPlusAuditDetails,4));
            $('#stepUpUploadDetails').val(vkbeautify.json(jsonResponse.processUploadAuditDetails,4));
            populateUploadAuditData(jsonResponse.processUploadAuditDetails, jsonResponse.accountId);
            textAreaAutoScroll();

        }

        function submitCaseMgmtTroubleshooting(id, idType) {
            var queryParam = idType == "case" ? "caseId="+id : "txnId="+id
            $.ajax({
                        url : contexturl + "/superadmin/1/troubleshooting/case_mgmt?"+queryParam,
                        //dataType : 'jsonp',
                        type : 'GET',
                        error: function(e) {
                            alert("Some error occurred or Data not found");
                        },
                        success : function(data) {
                            handleCaseResponse(data, idType);
                        }

                    });
        }

        function handleCaseResponse(data, idType){
            if(idType === "case"){
                handleCaseManagementResponse(data)
            }else{
                handleCaseListResponse(data)
            }
        }

        function handleCaseManagementResponse(data){
            var jsonResponse = JSON.parse(data.data);
            var caseDetailedInfo = jsonResponse.caseDetailedInfo;
            var auditCommentsResponse = jsonResponse.auditCommentsResponse;
            var userDetails = jsonResponse.userIdToEmailMap;
            $('#caseMgmtTroubleShootingResult').show();
            $('#caseId').text(caseDetailedInfo.caseId);
            $('#caseTransactionId').text(caseDetailedInfo.transactionId);
            $('#caseType').text(caseDetailedInfo.caseType);
            $('#caseAccountId').text(caseDetailedInfo.accountId);
            $('#caseEnvironmentTypeId').text(caseDetailedInfo.environmentTypeId);
            $('#caseWorkflowStatus').text(caseDetailedInfo.caseWorkflowStatus);
            $('#caseAssignedTo').text(userDetails[caseDetailedInfo.assignedTo]);
            $('#caseReferredTo').text(userDetails[caseDetailedInfo.referredTo]);
            $('#caseInvestigateBy').text(userDetails[caseDetailedInfo.investigateBy]);
            $('#caseCreatedAt').text(new Date(parseInt(caseDetailedInfo.createdAt)).toUTCString());
            $('#caseUpdatedAt').text(new Date(parseInt(caseDetailedInfo.updatedAt)).toUTCString());
            $('#caseEntityDetails').val(vkbeautify.json(caseDetailedInfo.entityCaseMetaDataMap));
            $('#caseAllCommentsDetails').val(vkbeautify.json(auditCommentsResponse));
            populateEntityDetails(caseDetailedInfo.entityCaseMetaDataMap);
            if(auditCommentsResponse != undefined){
                populateAuditComments(auditCommentsResponse.comments, caseDetailedInfo.caseId, userDetails);
            }
            textAreaAutoScroll();
        }

        function populateEntityDetails(entityData){
            var trHTML = '';
            var entityIds = Object.keys(entityData);
            for(i = 0;entityIds.length > i; i++){
                var item = entityData[entityIds[i]];
                var entityEventType = item.entityEventType == undefined ? "N/A" : item.entityEventType.replace("Entity_", "");
                var entityDispositionStatus = item.entityDispositionStatus.replace("Entity_", "");
                var entityWorkflowStatus = item.entityWorkflowStatus.replace("Entity_", "");
                var entityEscalationStatus = item.entityEscalationStatus.replace("Entity_", "");
                trHTML += '<tr><td style="padding:5px">' + entityIds[i] + '</td><td style="padding:5px">'+ entityDispositionStatus + '</td><td style="padding:5px">'+ entityWorkflowStatus + '</td><td style="padding:5px">' + entityEscalationStatus +  '</td><td style="padding:5px">' + entityEventType + '</td><td style="padding:5px">' + new Date(parseInt(item.updatedAt)).toUTCString() + '</textarea></tr>';
            }
            var tableNode = document.getElementById("entityDetailsBody");
            tableNode.innerHTML = "";
            $('#entityDetailsBody').append(trHTML);
        }

        function populateAuditComments(auditCommentsResponse, caseId, userDetails){
            var trHTML = '';
            for(i = 0;auditCommentsResponse.length > i; i++){
                var item = auditCommentsResponse[i];
                var itemId = item.domainName == "CASE" ? caseId : item.entityId;
                var value = item.currentValue.replace("Case_","").replace("Entity_","");
                var caseCorrectedValue = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase()
                trHTML += '<tr><td style="padding:5px">' + item.domainName + '</td><td style="padding:5px">'+ itemId + '</td><td style="padding:5px">' + value.toUpperCase() + '</td><td style="padding:5px">' + userDetails[item.userId] + '</td><td style="padding:5px">' + new Date(parseInt(item.createdAt)).toUTCString() + '</textarea></tr>';
            }
            var tableNode = document.getElementById("auditDetailsBody");
            tableNode.innerHTML = "";
            $('#auditDetailsBody').append(trHTML);
        }

        function handleCaseListResponse(data){
            var jsonResponse = JSON.parse(data.data);
            var caseListResponse = jsonResponse.caseListResponse.cases;
            var userDetailsMap = jsonResponse.userIdToEmailMap;
            $('#caseListResponseResult').show();
            populateCaseList(caseListResponse, userDetailsMap);
        }

        function populateCaseList(caseList, userDetails){
            var trHTML = '';
            for(i = 0;caseList.length > i; i++){
                var item = caseList[i];
                var createdAt = new Date(parseInt(item.createdAt)).toUTCString();
                var updatedAt = new Date(parseInt(item.updatedAt)).toUTCString();

                var assignedTo = item.assignedTo == undefined ? "UnAssigned" : userDetails[item.assignedTo] == undefined ? item.assignedTo : userDetails[item.assignedTo];
                var referredTo = item.referredTo == undefined ? "Not referred" : userDetails[item.referredTo] == undefined ? item.referredTo : userDetails[item.referredTo];
                trHTML += '<tr><td style="padding:5px">' + item.caseId + '</td><td style="padding:5px">'+ item.workflowStatus + '</td><td style="padding:5px">' + assignedTo + '</td><td style="padding:5px">' + referredTo + '</td><td style="padding:5px">' + createdAt + '</td><td style="padding:5px">' + updatedAt + '</textarea></tr>';
            }
            var tableNode = document.getElementById("caseListResponseTableBody");
            tableNode.innerHTML = "";
            $('#caseListResponseTableBody').append(trHTML);
        }


        function textAreaAutoScroll(){
            $('textarea').each(function() {
                var lines = $(this).val().split('\n').length;
                if(lines === 1) {
                    $(this).prop('rows',1);
                } else {
                    $(this).height($(this).prop('scrollHeight'));
                }
            });
        }

        function handleEventResponse(data){

            if(data.api.data==null || data.api.data.length > 0){

                $('#eventsbody').children().remove();

                var trHTML = '';
                $.each(data.api.data, function (i, item) {

                    var dateStr = new Date(data.api.data[i].epochTimeStamp).toUTCString(); // Displays UTC time of transaction

                    trHTML += '<tr><td>' + data.api.data[i].id + '</td><td><a href="javascript:;" onclick="fetchEventDetails(\'' + data.api.data[i].eventReferenceId + '\', this)">' + data.api.data[i].eventReferenceId + '</a></td><td>' + data.api.data[i].idPlusReferenceId + '</td><td>' +
                                data.api.data[i].accountId + '</td><td>' + data.api.data[i].eventType + '</td><td>' + data.api.data[i].environmentType + '</td><td>' +
                                data.api.data[i].responseTime + '</td><td>' + data.api.data[i].httpStatus  + '</td><td>' + dateStr + '</td></tr>';

                });

                $('#eventsbody').append(trHTML);
                $('#eventresult').show(true);
            }
            else{
                  alert('No data Found');
                  $('#Id').focus();
            }
        }

    var eventDetailCurrent;

     function fetchEventDetails(eventId, event){
        $.ajax({
            url : contexturl + "/superadmin/1/troubleshooting/event/" + eventId,
            type : 'GET',
            error: function(e) {
                alert(e.responseText);
            },
            success : function(data) {
                $("#eventresult").css({"pointer-events":"none"})
                $("#radiobuttons").css({"pointer-events":"none"})
                $("#Id").css({"pointer-events":"none"})
                $("#txnsubmit").css({"pointer-events":"none"})
                $("#eventDetailResult").css({"top":"40%", "left": "30%"})
                $('#eventDetailResult').show(true);
                var eventDetail = {};
                if(data.result || data.payload){
                    if(data.result){
                        eventDetail.result = data.result;
                    }
                    if(data.payload){
                        eventDetail.payload = JSON.parse(data.payload);
                        if(eventDetail?.payload?.event?.data?.uuid){
                            $('#eventDownloadButton').show(true);
                        }
                    }
                } else {
                    eventDetail.status = "No data to display";
                }
                eventDetailCurrent = eventDetail;
                $('#eventDetailResultText').html(vkbeautify.json(eventDetail, 4));
                $('#eventDetailResultText').css({"height":"450px"})
            }
        });
     }

     function closeEventDetail(){
        $('#eventDetailResult').hide();
        $('#eventDownloadButton').hide();
        $("#eventresult").css({"pointer-events":"all"})
        $("#radiobuttons").css({"pointer-events":"all"})
        $("#Id").css({"pointer-events":"all"})
        $("#txnsubmit").css({"pointer-events":"all"})
     }

     function closeImageDiv(){
         $('#imageDisplayDialog').hide();
         var tableNode = document.getElementById("imagesList");
        tableNode.innerHTML = "";
     }

     function download(){
        if(eventDetailCurrent?.payload?.event?.data?.uuid && eventDetailCurrent?.result?.accountId){
            window.open(contexturl + '/superadmin/1/download_file?eventUuid=' + eventDetailCurrent?.payload?.event?.data?.uuid + '&type=events' + '&publicAccountId=' + eventDetailCurrent?.result?.accountId)
        }
     }

    function handleThirdPartyResponse(data){
        var stats = data.stats;
        var dv_extracted_profile_img_b64 = null;

        if(stats) {
            dv_extracted_profile_img_b64 = try_extract_profile_image(stats);
            if(dv_extracted_profile_img_b64 !== null) {
                dv_extracted_profile_img_b64 = dv_extracted_profile_img_b64.trim();
                    if(dv_extracted_profile_img_b64 !== '') {
                        $('#extractedProfileImage').attr('href', 'data:image/jpeg;base64,' + dv_extracted_profile_img_b64);
                        $('#extractedProfileImage').attr('download', 'extracted_profile_mage.jpg');
                        $('#extractedProfileImage').closest('tr').show()
                }
            }
        }

        $('#stats').html(audit_stats(stats));
        $('#stats').show(true);
        handleDecisionBlock(stats);
        updateResolvedParameterSection();
    }

    function updateResolvedParameterSection() {
        var resolvedParametersEle = $("#resolvedParameters");
        resolvedParametersEle.val();
        resolvedParametersEle.css({
            height: '0px'
        });
        try {
            var bbVendorReqBody = $("#socure_tp_stats_acc_BB_VENDOR_WRAPPER .request_body").val()
            var requestBodyObject = safeJsonParse(bbVendorReqBody);
            var piiStr = JSON.stringify(requestBodyObject.profileInformation, null, 2);
            resolvedParametersEle.val(piiStr);
            if (piiStr && piiStr.length != 1) {
                resolvedParametersEle.css({
                    height: '400px'
                });
            }
        } catch(e) {}
    }

    function handleDecisionBlock(stats){
        var decisionLogicId;
        var accountId;
        var environmentTypeId;
        if(stats['InternalDecisionService']) {
            var decisionNode = stats['InternalDecisionService']
            for(x in decisionNode) {
                if(decisionNode[x]['Response']) {
                    decisionLogicId = JSON.parse(decisionNode[x]['Response']).data.id;
                }
                if(decisionNode[x]['Request Body']) {
                    accountId = JSON.parse(decisionNode[x]['Request Body']).accountId;
                }
                if(decisionNode[x]['Request Body']) {
                    environmentTypeId = JSON.parse(decisionNode[x]['Request Body']).environmentTypeId;
                }
            }
            if(decisionLogicId && accountId && environmentTypeId){
                $('#decision').show(true);
                var text = 'Logic id ' + decisionLogicId + ' <button style="margin-left: 10px" onclick="downloadLogic(' + decisionLogicId + ',' + accountId + ',' + environmentTypeId + ')" > Download Logic </button>';
                $('#decisionBlock').html(text);
            }
        } else {
            $('#decision').hide();
            $('#decisionBlock').html('');
        }
    }

    window.onload = function() {
            DD_RUM && DD_RUM.startView({name: "/troubleshooting"});
            DD_RUM && DD_RUM.setUser({ "id": loggedinUsername});
    }

    function downloadLogic(decisionLogicId, accountId, environmentTypeId){
        var url = contexturl + '/superadmin/1/decision_logic/v2/logic/' + decisionLogicId + '?accountId=' + accountId + '&environmentTypeId=' + environmentTypeId;
        $.ajax(url, {
            type : 'GET',
            success : function(data) {
                if (data['status'].toLowerCase() == 'ok') {
                    var jsonData = vkbeautify.json(data['data'], 4)
                    var textToSaveAsBlob = new Blob([jsonData], {type: "text/json"});
                    var textToSaveAsURL = window.URL.createObjectURL(textToSaveAsBlob);
                    var fileNameToSaveAs = 'logic-' + decisionLogicId + '.json'

                    var downloadLink = document.createElement("a");
                    downloadLink.download = fileNameToSaveAs;
                    downloadLink.innerHTML = "Download File";
                    downloadLink.href = textToSaveAsURL;
                    downloadLink.onclick = destroyClickedElement;
                    downloadLink.style.display = "none";
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    DD_RUM.track("Download Decision Logic", { accountId: accountId, decisionLogicId: decisionLogicId, environmentTypeId: environmentTypeId });
                } else {
                    console.log(`Download logic ${id} failed`)
                }
            }
        });
    }

    function downloadLogicTemplate(decisionLogicId, accountId, environmentTypeId){
        var url = contexturl + '/superadmin/1/decision_logic/v2/logicTemplate/' + decisionLogicId + '?accountId=' + accountId + '&environmentTypeId=' + environmentTypeId;
        $.ajax(url, {
            type : 'GET',
            success : function(data) {
                if (data['status'].toLowerCase() == 'ok') {
                    var jsonData = vkbeautify.json(data['data'], 4)
                    var textToSaveAsBlob = new Blob([jsonData], {type: "text/json"});
                    var textToSaveAsURL = window.URL.createObjectURL(textToSaveAsBlob);
                    var fileNameToSaveAs = 'logic-template-' + decisionLogicId + '.json'

                    var downloadLink = document.createElement("a");
                    downloadLink.download = fileNameToSaveAs;
                    downloadLink.innerHTML = "Download File";
                    downloadLink.href = textToSaveAsURL;
                    downloadLink.onclick = destroyClickedElement;
                    downloadLink.style.display = "none";
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    DD_RUM.track("Download Decision Logic Template", { accountId: accountId, decisionLogicId: decisionLogicId, environmentTypeId: environmentTypeId })
                } else {
                    console.log(`Download logic ${id} failed`)
                }
            }
        });
    }

    function safeJsonParse(response) {
        let safeRes = response || "{}"
        return JSON.parse(safeRes);
    }

    function isDocVTransaction(response) {
        const parsedRes = safeJsonParse(response)
        return (!!parsedRes["documentVerification"] || !!parsedRes["selfieReverification"] || !!parsedRes["secondaryDocument"]);
    }

    function handleTxnResponse(data,id){
        var stats = data.stats;
        var api = data.api;
        var documentOverallMetaInfo = data.documentOverallMetaInfo || {};
        if (api && api['data'] && api['data'].transactionId) {
          var modelExplainerUrl = 'http://internal-llm-app-**********.us-east-1.elb.amazonaws.com/txassistant/?transaction_id=' + api['data'].transactionId + '&launch=1';
          $('#modelExplainerLink').attr('href', modelExplainerUrl);
        }
        if(api.status=="Error"){
            $('#modelExplainer').hide();
            $('#txnresult').hide();
            $('#txnStatusMsg').text(api['msg']);
            $('#Id').focus();
            $('#Id').val('');
            return;
        }
        if (api && api['data'] &&
            (api['data'].environmentType === 1 || api['data'].environmentType === 2) &&
            (api['data'].apiName && (api['data'].apiName.includes("EmailAuthScore") || api['data'].apiName.includes("EncryptedEmailAuthScore")))) {
            $('#modelExplainer').show(true);
        } else {
            $('#modelExplainer').hide();
        }
        $('#txnStatusMsg').text("");
        $('#txnresult').show(true);
        $('#resultId').text(api['data'].id);
        $('#resultTxnId').text(api['data'].transactionId);
        if(api['data'].environmentType == 1) {
            $('#resultEnvironment').text("Production");
        } else if(api['data'].environmentType == 2) {
            $('#resultEnvironment').text("Certification");
        } else {
            $('#resultEnvironment').text("Sandbox");
        }
        $('#transactionDate').val(api['data'].transactionDate);
        $('#resultDate').text(new Date(api['data'].transactionDate).toUTCString()); // Displays UTC time of transaction
        $('#resultTime').text(api['data'].processingTime);
        $('#resultApi').text(api['data'].api);
        $('#captureChannelValue').text((documentOverallMetaInfo.flow));
        var flag = checkJsonString(api['data'].parameters);
        if(flag){
            var jsonParams = JSON.parse(api['data'].parameters);
            var formattedParams = JSON.stringify(jsonParams, null, 2);
            var documentuuidInPayload = jsonParams['documentuuid']
            var documentuuidInRuleCode = '';
            try {
                const { response, debug } = api["data"];
                const hasDocV = checkJsonString(response) && isDocVTransaction(response);
                if (hasDocV) {
                    documentuuidInRuleCode = JSON.parse(debug)[0]?.["categorical_values"]?.find(
                        // this value is equal to the Core Engine ID for DocV Transactions
                        (cval) => cval["rulecode"] === 'CVVAL.301013'
                      )?.value;
                }
            } catch {}
            var finalDocumentUuid = documentuuidInRuleCode || documentuuidInPayload;
            if(finalDocumentUuid) {
                $('#documents').remove();
                $('#downloadMetricLink').remove();
                var buttonHTML = '<button id="documents" onclick="listImageFiles(' + api['data'].accountId + ',\'' + finalDocumentUuid + '\')">View Documents</button>&nbsp;&nbsp;';
                var downloadMetricFileLink = metricFileDownloadLink(api['data'].accountId, finalDocumentUuid, 'Download Metric File');
                $('#docDownload').append(buttonHTML);
                $('#docDownload').append(downloadMetricFileLink);
                //$('#documents').onclick = listImageFiles(api['data'].accountId,jsonParams['documentuuid']);
                $('#documents').closest('tr').show()
                $('#captureChannel').closest('tr').show()
            }
            if(jsonParams['profileimageuuid']) {
                $('#profileImage').attr('href', contexturl + '/superadmin/1/download_file?transactionId=' + id + '&type=profile')
                $('#profileImage').closest('tr').show()
            }
            if(jsonParams['referenceId']){
                $('#referenceId').val(jsonParams['referenceId']);
            }
            $('#resultParam').html(formattedParams);
        }else {
            $('#resultParam').val(api['data'].parameters);
        }

        flag = checkJsonString(api['data'].response);
        if(flag){
            $('#resultResponse').val(vkbeautify.json(api['data'].response, 4));
        }else {
            $('#resultResponse').val(api['data'].response);
        }

        const { response, debug } = api["data"];
        const VendorReferenceIdRuleCode = "CVVAL.300072";
        const hasDocV = checkJsonString(response) && isDocVTransaction(response);
        if (hasDocV) {
          const docVId =
            checkJsonString(debug) &&
            JSON.parse(debug)[0]?.["categorical_values"]?.find(
              // this value is equal to the Core Engine ID for DocV Transactions
              (cval) => cval["rulecode"] === VendorReferenceIdRuleCode
            )?.value;
          if (docVId) {
            $('#docvLink').remove();
            $("#dvLink").append(docvLink(docVId));
            $("#resolvedParameters").val();
          }
        }

        flag = checkJsonString(api['data'].internalWorkLogs);
        if(flag){
            $('#internalWorkLogsResponse').val(vkbeautify.json(api['data'].internalWorkLogs, 4));
        }else {
            $('#internalWorkLogsResponse').val(api['data'].internalWorkLogs);
        }

        populateField('#resultError',api['data'].error);
        populateField('#resultErrorMsg',api['data'].errorMsg);
        populateField('#resultIp',api['data'].accountIpAddress);
        populateField('#resultInvo',api['data'].originOfInvocation);
        populateField('#resultGeo',api['data'].geocode);
        populateField('#resultApiKey',api['data'].apiKey);
        populateField('#resultaccountId',api['data'].accountId);
        populateField('#resultApiName',api['data'].apiName);
        populateField('#resultCache',api['data'].cachesUsed);
        populateField('#resultScore',api['data'].confidence);
        populateField('#resultConf',api['data'].reasonCodes);
        populateField('#resultRisk',api['data'].hasRiskCode);
        populateField('#resultCustomer',api['data'].customerUserId);
        populateField('#resultRun',api['data'].runId);
        populateField('#resultUid',api['data'].uuid);

        flag = checkJsonString(api['data'].debug);
        if(flag){
            $('#resultDebug').val(vkbeautify.json(api['data'].debug, 4));
        }else {
            $('#resultDebug').val(api['data'].debug);
        }

        flag = checkJsonString(api['data'].details);
        if(flag){
            $('#resultDetails').val(vkbeautify.json(api['data'].details, 4));
        }else {
            $('#resultDetails').val(api['data'].details);
        }

        flag = checkJsonString(api['data'].reasonCodes);
        if(flag){
            $('#resultCode').val(vkbeautify.json(api['data'].reasonCodes, 4));
        }else {
            $('#resultCode').val(api['data'].reasonCodes);
        }

        flag = checkJsonString(api['data'].requestURI);
        if(flag){
            $('#resultUri').val(vkbeautify.json(api['data'].requestURI, 4));
        }else {
            $('#resultUri').val(api['data'].requestURI);
        }

        textAreaAutoScroll();
    }

    function metricFileDownloadLink(accountId, uuid, linkText) {
        return '<a id="downloadMetricLink" href="superadmin/files/documents/metrics/accountId/' + accountId + '/uuid/' + uuid +'">'+ linkText + '</a>'
    }

    function docvLink(txnId) {
        const docvLink = `${contexturl}/docv_bb_transaction?txnId=${txnId}`
        return `<a id="docvLink" href="${docvLink}"> DocV 3.0 Transaction </a>`
    }

    function handleUuidResponse(uuid, accountId) {
        $('#uuidResultCont').show();
        var buttonHTML = '<button id="docDownloadBtn" onclick="listImageFiles(' + accountId + ',\'' + uuid + '\')">View Documents</button>&nbsp;&nbsp;';
        var downloadMetricFileLink = metricFileDownloadLink(accountId, uuid, 'Download Metric File');
        $('#docDownloadBtn').remove();
        $('#downloadMetricLink').remove();
        $('#uuidResultViewFiles').append(buttonHTML);
        $('#uuidResultViewFiles').append(downloadMetricFileLink);
        //listImageFiles(accountId,uuid);
        //$("#uuidDownloadLink").attr("href",contexturl + '/superadmin/1/download_file_uuid?accountId=' + accountId + '&amp;amp;uuid=' + uuid);
    }


    function populateUploadAuditData(jsonData, accountId) {
        var trHTML = ''
        for(i =0;jsonData.length > i;i++){
            var item = jsonData[i];
            var metricDownloadElement = metricFileDownloadLink(accountId, item.uuid, 'Download');
            trHTML += '<tr><td>' + item.success + '</td><td>'+ item.referenceId + '</td><td>'
            + item.uuid + '</td><td>' + item.createdAt +  '</td><td><button onclick="listImageFiles('
             + accountId + ',\'' + item.uuid + '\')">View Documents</button>' + '</td><td>' +
             metricDownloadElement + '</td></tr>'
             ;
        }
        var tableNode = document.getElementById("uploadAuditTableBody");
        tableNode.innerHTML = "";
        $('#uploadAuditTableBody').append(trHTML);
    }

    function listImageFiles(accountId, uuid) {
        var listImageFilesURL = contexturl + '/superadmin/files/list/accountId/' + accountId + '/uuid/' + uuid;
        $.ajax({
            url: listImageFilesURL,
            type: 'GET',
            error: function(e) {
            if (e.status === 400) {
                alert(e.responseJSON.msg)
            } else {
                alert(e.responseText);
            }
            },
            success: function(data) {
                var jsonData = JSON.parse(data);
                var documentInfoList = jsonData.data;
                renderImageFilesDialog(accountId, uuid, documentInfoList);
            }
        }
        )
    }

    function renderImageFilesDialog(accountId, uuid, documentInfoList) {
        var renderImagesHTML = '';
        var baseImageFetchURL = contexturl + '/superadmin/files/download/accountId/' + accountId + '/uuid/' + uuid +'?imageType=';
        var firstImageSrc = '';
        for(i=0;documentInfoList.length>i;i++){
            var fileType = documentInfoList[i].fileType.value;
            var imageSrc = baseImageFetchURL + fileType;
            if(i == 0) {
                firstImageSrc = imageSrc;
            }
            renderImagesHTML += '<button style="border-radius: 5px;border-width: medium;padding-right: 15px;" onclick="renderImage(\'' + imageSrc + '\')">' + fileType +'</button>'
        }
        $('#imagesList').append(renderImagesHTML);
        var imageDialog = document.getElementById("imageDisplayDialog");
        imageDialog.style.display = 'block';
        renderImage(firstImageSrc);
    }

    function renderImage(imageSrc){
        document.getElementById("docImage").src = "";
        document.getElementById("docImage").src = imageSrc;
    }


    function clearPage(isSubmit){
        $('#modelExplainer').hide();
        $('#txnresult').hide();
        $('#eventresult').hide();
        $('#uuidResultCont').hide();
        $('#stats').hide();
        $('#stepUpProcessResult').hide();
        $('#caseMgmtTroubleShootingResult').hide();
        $('#caseListResponseResult').hide();
        $('#stepUpEventRadioButtons').hide();
        $("#caseMgmtRadioButtons").hide();
        $('#refresh').hide();
        $('#thirdPartyHeader').hide();
        if(!isSubmit)
            $("#Id, #inputId2, #uuid").val("");
        var radios = document.getElementsByName('type');
        if(radios[3].checked) {
            $("#idInputCont").hide();
            $("#idInput2Cont").show();
        }else if(radios[1].checked) {
            $("#stepUpEventRadioButtons").show();
            $("#idInput2Cont").hide();
            $("#idInputCont").show();
        }else if(radios[4].checked) {
            $("#caseMgmtRadioButtons").show();
            $("#idInput2Cont").hide();
            $("#idInputCont").show();
        }
        else {
            $("#idInputCont").show();
            $("#idInput2Cont").hide();
        }
    }

    function format(json){
       var str = JSON.stringify(json, null, 2);
       return str;
    }

    function populateField(tag , val){
        $(tag).text(val);
    }