package me.socure.application

import me.socure.types.scala.Enum

import scala.beans.BeanProperty

case class ThrottledServices(groupName : String,
                             flagName : String,
                             serviceName : String,
                             endpoint1 : String,
                             endpoint2: String)

case class NonThrottledServices(serviceName : String,
                                endpoint1 : String,
                                endpoint2: String
                               )

case class ApplicationEnvironment(@BeanProperty name : String, @BeanProperty canDeploy : Boolean)
case class ApplicationInfoWithEnvironment(applicationName: String, serviceType: String, environments: Seq[ApplicationEnvironment])

case class ApplicationInfo(@BeanProperty applicationName: String, @BeanProperty serviceType: String)

object ServiceTypes extends Enum {
  type ServiceType = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value
  val THROTTLE_SERVICE: ServiceType = EnumVal(0, "throttle")
  val WORKER_SERVICE: ServiceType = EnumVal(1, "worker")
  val PUBLIC_SERVICE: ServiceType = EnumVal(2, "public")

}
