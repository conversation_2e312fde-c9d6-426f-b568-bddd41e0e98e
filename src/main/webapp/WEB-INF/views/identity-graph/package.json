{"name": "identity-graph", "version": "0.1.0", "private": true, "homepage": "/resources/identity_graph", "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.104", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "reagraph": "^4.19.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"react-scripts": "^5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && npm run build-rename && npm run build-mv", "build-mv": "rm -rf ../../../resources/identity_graph && mv -f build '../../../resources/identity_graph'", "build-rename": "npm run build-rename-js && npm run build-rename-css", "build-rename-js": "mv ./build/static/js/main*.js ./build/static/js/main.js && mv ./build/static/js/main*.js.map ./build/static/js/main.js.map ", "build-rename-css": "mv ./build/static/css/main*.css ./build/static/css/main.css && mv ./build/static/css/main*.css.map ./build/static/css/main.css.map", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.38", "http-proxy-middleware": "^2.0.7", "react-scripts": {"nth-check": "^2.1.1", "postcss": "^8.4.38"}, "css-select": {"nth-check": "^2.1.1"}, "resolve-url-loader": {"postcss": "^8.4.38"}}, "resolutions": {"http-proxy-middleware": "^2.0.7", "postcss": "^8.4.38", "nth-check": "^2.1.1"}}