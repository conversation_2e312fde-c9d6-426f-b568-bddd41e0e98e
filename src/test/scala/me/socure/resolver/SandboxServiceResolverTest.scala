package me.socure.resolver

import me.socure.model.ErrorResponse
import me.socure.sandbox.common.SandboxServiceClient
import me.socure.sandbox.common.model._
import org.mockito.Mockito.when
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.Future

class SandboxServiceResolverTest extends FunSuite with MockitoSugar with Matchers {
  val sandboxServiceClient = mock[SandboxServiceClient]

  val service = new SandboxServiceResolver
  service.sandboxServiceClient = sandboxServiceClient

  val sandboxRevisions = List(ListConfigurationResponse(configName = "sandbox",
                                                       configVersion = "1.0",
                                                       revision = 1,
                                                       isLive = true,
                                                       createdAt = "01-01-2020",
                                                       markedLiveAt = Some("01-01-2020"),
                                                       markedLiveBy = Some("dummy"),
                                                       createdBy = "dummy"),
                            ListConfigurationResponse(configName = "sandbox",
                                                      configVersion = "2.0",
                                                      revision = 1,
                                                      isLive = false,
                                                      createdAt = "01-01-2021",
                                                      markedLiveAt = Some("11-01-2021"),
                                                      markedLiveBy = Some("dummy"),
                                                      createdBy = "dummy"))

  val configFile = getClass.getResourceAsStream("/test_file.csv")

  val addConfiguration = AddConfigurationRequest(configName = "dummy1",
                                                 configVersion = "dummy2",
                                                 isLive = false,
                                                 configuration = configFile,
                                                 createdBy = "premkumar",
                                                 markedLiveBy = None)
  val addResponse = AddConfigurationResponse(revision = 1)
  val error = ErrorResponse(101, "dummy error")

  test("list all configurations"){
    when(sandboxServiceClient.listConfigurations()) thenReturn (Future.successful(Right(sandboxRevisions)))
    val res = service.getAllConfigurations()
    res shouldBe Right(sandboxRevisions)
  }

  test("list all configurations error"){
    when(sandboxServiceClient.listConfigurations()) thenReturn(Future.successful(Left(error)))
    val res = service.getAllConfigurations()
    res shouldBe Left(error)
  }

  test("add configuration error"){
    when(sandboxServiceClient.addConfiguration(addConfiguration)) thenReturn(Future.successful(Left(error)))
    val res = service.addConfiguration(addConfiguration)
    res shouldBe Left(error)
  }

  test("add configuration success"){
    when(sandboxServiceClient.addConfiguration(addConfiguration)) thenReturn(Future.successful(Right(addResponse)))
    val res = service.addConfiguration(addConfiguration)
    res shouldBe Right(addResponse)
  }

  test("mark-as-live"){
    val markAsLiveRequest = MarkAsLiveRequest(markedLiveBy = "dummy", revision = 1)
    val markAsLiveResponse = MarkAsLiveResponse(status = "ok")
    when(sandboxServiceClient.markAsLive(markAsLiveRequest)) thenReturn(Future.successful(Right(markAsLiveResponse)))
    val res = service.markAsLive(markAsLiveRequest)
    res shouldBe Right(markAsLiveResponse)
  }

  test("download a configuration"){
    when(sandboxServiceClient.getConfiguration(1)) thenReturn(Future.successful(Right(configFile)))
    val res = service.getConfiguration(1)
    res shouldBe Right(configFile)
  }

}
