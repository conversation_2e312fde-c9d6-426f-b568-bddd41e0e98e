<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
	<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
	<script src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/bootstrap.min.js"></script>

	<script type="text/javascript">
		bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		loggedinUsername = "#{loggedinUsername}";
	</script>

    <script type="text/javascript">

        function unlock(me) {
            var id = $(me).attr('data-user-id');
            var $chkbox = $("input[data-chk-box='" + id + "']");
            if(!$chkbox.is(':checked')) {
                $chkbox.click();
            }
            DD_RUM.track("Unlock User", { userEmailToUnlock: id });
            $('form')[0].method = "post";
		    $('form')[0].action = contexturl + "/account/lockout/unlock";
            $('#unlock-form').submit();
        }

        function selectAll() {
            var checkAll = $('#selectall').is(':checked');
            $('#unlock-form').find('input[type=checkbox]').each(function(){
                $(this)[0].checked = checkAll;
            });
        }

        function submitHandler() {
           var selectedUsersNodes = document.querySelectorAll('input[type="checkbox"]:checked');
           var selectedUsersEmailList = [];
           for(var i=0; i&lt;selectedUsersNodes.length; i++) {
               selectedUsersEmailList.push(selectedUsersNodes[i].value);
           }
           DD_RUM.track("Unlock Selected Users", { selectedUsersList: selectedUsersEmailList.join("\n")});

		   $('form')[0].method = "post";
		   $('form')[0].action = contexturl + "/account/lockout/unlock";
        };

        window.onload = function() {
           if(DD_RUM) {
             DD_RUM.startView({ name: "/locked_user" });
             DD_RUM.setUser({ id: loggedinUsername });
           }
        }

       $(function(){
         document.getElementById("unlock-form").addEventListener("submit", submitHandler);
       });

    </script>

</h:head>

<h:body>
	<div align="center">
		<font color="blue"><h3 id="notification">
				<h:outputText value="#{param['msg']}" />
			</h3> </font>
	</div>
	<div align="right">
		<h:outputLink value="#{request.contextPath}/industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="#{request.contextPath}/fraudmodel">Manage Fraud Model</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="#{request.contextPath}/account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="#{request.contextPath}/delegated_admin">Promote Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="#{request.contextPath}/active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
        <h:outputLink value="#{request.contextPath}/inactive_users">Inactive Users</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="#{request.contextPath}/test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="#{request.contextPath}/fraudmanager">Fraud Model Manager</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="#{request.contextPath}/logout">Logout</h:outputLink>
	</div>
	<div id="user_content" class="p-4">
	<fieldset>
		<legend>Locked Users</legend>
        <form id="unlock-form">
		<table id="myTable" class="tablesorter table border-left border-right border-bottom border-top">
			<thead>
				<th scope="col"><input type="checkbox" id="selectall" onclick="selectAll()"/></th>
				<th scope="col">First Name</th>
				<th scope="col">Last Name</th>
				<th scope="col">Company</th>
				<th scope="col">Locked On</th>
				<th scope="col">Contact Number</th>
				<th scope="col">Email</th>
                <th scope="col">Action</th>
			</thead>
			<tbody>

				<ui:repeat var="user" value="#{list}" varStatus="status">
					<tr>
						<td align="center"><input type="checkbox" class="clz-ids" name="emails" value="#{user.email}" data-chk-box="#{user.email}"/></td>
						<td>#{user.firstname}</td>
						<td>#{user.surname}</td>
						<td>#{user.company}</td>
						<td>#{user.lastLockedOn}</td>
						<td>#{user.contact}</td>
						<td>#{user.email}</td>
                        <td><input type="button" data-user-id="#{user.email}" value="Unlock" onclick="unlock(this);" class="btn btn-link text-primary"/></td>
					</tr>
				</ui:repeat>

				<input type="submit" value="Unlock Selected" class="mb-3 float-right btn btn-primary" />
			</tbody>

		</table>
        </form>
	</fieldset>
	</div>
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
</h:body>
</html>