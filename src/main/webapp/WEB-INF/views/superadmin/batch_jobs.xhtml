<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:c="http://java.sun.com/jsp/jstl/core">
<h:head>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
    <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

    <link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
    <link rel="stylesheet"
          href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />
    <script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
    <script type="text/javascript" src="#{bucket.resourceURL}/scripts/batchJobs.js" />
    <script src="#{bucket.resourceURL}/scripts/chosen.jquery.min.js"></script>
    <link rel="stylesheet" href="#{bucket.resourceURL}/styles/chosen.min.css"/>
    <script>
		bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		loggedinUsername = "#{loggedinUsername}";
		appenv = "#{bucket.appEnv}";
	</script>

    <style>
        #workspace-container {
            padding: 24px;
        }

        .btn-orange {
            box-sizing: border-box;
            border: 1px solid transparent;
            border-radius: 3px;
            color: #fff!important;
            background: #f68e29;
            text-transform: uppercase;
            display: block;
            padding: 6px 10px;
            float: left;
            text-align: center;
        }

        .btn-orange:disabled:hover {
            cursor: not-allowed;
        }

        .btn-in-group {
            margin: 0 10px;
        }

        .btn-orange:hover {
            background: #EFA155!important;
            cursor: pointer;
        }

        #results-table {
            border-collapse: collapse;
            border-spacing: 0;
            color: #49424f;
            width: 100%;
        }

        #table-header {
            background: #635B69;
            color: #fff;
        }

        #results-table .data-row {
            display: table-row;
            vertical-align: inherit;
            border-color: inherit;
            border-collapse: collapse;
            border-spacing: 0;
            color: #49424f;
        }

        #results-table th, #results-table td {
            border: 1px solid #d2d3d6;
            font-size: 13px;
            font-weight: 400;
            padding: 20px 10px;
            display: table-cell;
            vertical-align: inherit;
            text-align: center;
        }

        .grey-row {
            background: rgba(209,199,230,.4);
        }

        .btn-container {
            display: flex;
            flex: 1;
            justify-content: flex-end;
            padding: 24px 0;
        }

        .socure-downlaod-button {
            color: #ee8e3c;
            cursor: pointer;
        }

        .socure-downlaod-button:hover {
            text-decoration: underline;
        }

        #create-section {
            display: flex;
            flex-direction: column;
            border: 1px solid #ee8e3c;
            padding: 8px;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        #create-section input {
            margin: 12px 0;
        }

        #create-section-header {
            display: flex;
            width: 100%;
            justify-content: space-between;
        }

        	.chosen-search input{
			margin-left:0 !important;
			margin-bottom:10px;
			min-width:200px;
		}

        #decision_migration .chosen-container {
        min-width:200px;
        }

        #decision_migration .search-field {
        min-width:200px;
        }
    </style>
</h:head>


<h:body>
    <div align="center">
        <font color="blue"><h3>
            <h:outputText value="#{param['message']}" />
        </h3> </font>
    </div>
    <div align="right">
        <h:outputLink value="active_users">Active Users</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
        <h:outputLink value="inactive_users">Inactive Users</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
        <h:outputLink value="logout">Logout</h:outputLink>
    </div>

    <h:outputText id="result-heading" />

    <div id="workspace-container">
        <div id="create-section">
            <div id="create-section-header">
                <h4>Create New Job</h4>
                <button id="template_download_action" class="btn-dark" onclick="downloadTemplate()" style="display: none;">Download template</button>
            </div>
            <div>
                <div class="d-flex flex-row align-items-center">
                    <label for="job_types">Job Type</label>
                    <div>
                        <select id="job_types" class="ml-4" onchange="updateJobType()" />
                    </div>
                </div>
            </div>
            <div id="cip-report-options" class="ml-3 mt-3">
                Note: The dates are considered in <b>America/New_York</b> timezone
                <div class="d-flex flex-row align-items-center">
                    <label for="cip-report-date">Date<span style="color:red;">*</span>&nbsp;&nbsp;</label>
                    <div class="ml-4">
                        <input name="cip-report-date" id="cip-report-date" type="date" />
                    </div>
                </div>
            </div>
            <div id="mvb-report-options" class="ml-3 mt-3">
                Note: The dates are considered in <b>America/New_York</b> timezone
                <div class="d-flex flex-row align-items-center">
                    <label for="mvb-report-start">Start<span style="color:red;">*</span>&nbsp;</label>
                    <div class="ml-4">
                        <input name="mvb-report-start" id="mvb-report-start" type="date" />
                    </div>
                </div>
                <div class="d-flex flex-row align-items-center">
                    <label for="mvb-report-end">End&nbsp;&nbsp;&nbsp;&nbsp;</label>
                    <div class="ml-4">
                        <input name="mvb-report-end" id="mvb-report-end" type="date" />
                    </div>
                </div>
            </div>
            <div id="account_administration_action" class="ml-3 mt-3">
                <div class="d-flex flex-row align-items-center">
                    <label for="job_type_action">Action</label>
                    <div>
                        <select id="job_type_action" class="ml-4" />
                    </div>
                </div>
            </div>
            <input id="batch-job-input" type="file" accept=".csv,.json" style="line-height: normal;" name="Upload" />
            <div  id="decision_migration"  class="ml-1 mt-3">
            <div class="d-flex flex-row align-items-center">
                <label for="parent_account_list">Select Account</label>
                <div class="ml-2">
                    <select name="accountid" id="parent_account_list" class="chosen" multiple="true"/>
                </div>
            </div>
            </div>
            <button disabled="true" class="btn-orange" id="create-job-btn" onclick="handleCreateJob()">Create Job</button>
        </div>
        <b id="job_type_heading">Account Administration</b>
        <table id="results-table">
            <thead id="table-header">
                <tr>
                    <th>S. No.</th>
                    <th>Job Id</th>
                    <th>Created by</th>
                    <th>Created at</th>
                    <th>Status</th>
                    <th id="action_or_details">Actions</th>
                </tr>
            </thead>
            <tbody id="results-body">
            </tbody>
        </table>

        <div class="btn-container">
            <button class="btn-orange btn-in-group" onclick="refreshJobs()">Refresh</button>
            <button class="btn-orange btn-in-group" onclick="getMoreJobList()" id="load-more">Load more</button>
        </div>

    </div>
</h:body>
</html>