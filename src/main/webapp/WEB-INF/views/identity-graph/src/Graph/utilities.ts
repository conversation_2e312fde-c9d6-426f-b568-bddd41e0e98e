import { GraphEdge, GraphNode } from "reagraph";

export interface IdentityGraphNodesEdges {
    nodes: Array<GraphNode>;
    edges: Array<GraphEdge>;
}

export const deepClone = (o:any) => {
    return JSON.parse(JSON.stringify(o));
}

const piiFields = [
    "firstName",
    "middleName",
    "surName",
    "suffixName",
    "dob",
    "phoneNumber",
    "email",
    "ssn",
    "address"
];

export const sharedPiiFields = new Set([
    "phoneNumber",
    "email",
    "ssn",
    "address"
]);

const getConcatenatedAddress = (addresses: Array<any>) => {
    const count = addresses.length;
    const data: Map<string, any> = new Map();
    addresses.forEach(address => {
        const { street, city, state, zipCode } = address;
        const zip = zipCode.length > 5 ? zipCode.substring(0, 5) : zipCode;
        const value = `${street},\n${city}, ${state} - ${zip}`;
        if (!data.has(value)) {
            data.set(value, { count: 0, occurances: [] });
        }
        const inserter = data.get(value);
        inserter.count = inserter.count + 1;
        inserter.occurances.push(address);
    });
    return {
        count,
        data,
        emptyCount: 0
    }
}

const getConcatenatedPii = (piiArray: Array<any>) => {
    const count = piiArray.length;
    const data: Map<string, any> = new Map();
    let emptyCount = 0;
    piiArray.forEach(pii => {
        const { value } = pii;
        if (value && value.trim() !== "") {
            if (!data.has(value)) {
                data.set(value, { count: 0, occurances: [] });
            }
            const inserter = data.get(value);
            inserter.count = inserter.count + 1;
            inserter.occurances.push(pii);
        } else {
            emptyCount = emptyCount + 1;
        }
    });
    return {
        count,
        data,
        emptyCount
    }
}


const concatenateEntitiesForSocureID = (vArray: Array<any>) => {
    const concatenated = {
        firstName: [],
        middleName: [],
        surName: [],
        suffixName: [],
        dob: [],
        phoneNumber: [],
        email: [],
        ssn: [],
        address: []
    };

    vArray.forEach(v => {
        piiFields.forEach(f => {
            const piiArray = (v.entity[f] as Array<any>);
            if (piiArray && Array.isArray(piiArray) && piiArray.length !== 0) {
                piiArray.forEach(val => {
                    (concatenated as any)[f].push(val);
                })
            }
        })
    });
    return {
        firstName: getConcatenatedPii(concatenated.firstName),
        middleName: getConcatenatedPii(concatenated.middleName),
        surName: getConcatenatedPii(concatenated.surName),
        suffixName: getConcatenatedPii(concatenated.suffixName),
        dob: getConcatenatedPii(concatenated.dob),
        phoneNumber: getConcatenatedPii(concatenated.phoneNumber),
        email: getConcatenatedPii(concatenated.email),
        ssn: getConcatenatedPii(concatenated.ssn),
        address: getConcatenatedAddress(concatenated.address)
    };
}

const getGroupMappedBySocureID = (apiData: Array<any>): Map<string, Array<any>> => {
    const result: Map<string, Array<any>> = new Map();
    apiData.forEach((v: any) => {
        if (!result.has(v.socureId)) {
            result.set(v.socureId, [])
        }
        result.get(v.socureId)?.push(v);
    });
    return result;
}

export function buildGraphRawData(apiData: Array<any>): Map<string, any> {
    const mappedBySocureID = getGroupMappedBySocureID(apiData)
    const result: Map<string, any> = new Map();
    mappedBySocureID.forEach((v: any, k: string) => {
        result.set(k, concatenateEntitiesForSocureID(v));
    });
    return result;
}

const getFirstPiiElement = (mp: Map<string, any>): string => {
    const arr = Array.from(mp.keys());
    return (arr.length > 0 ? arr[0] : "");
};

const computeFullName = (underlying: any, identity: string): string => {
    let fullName = "";
    fullName += getFirstPiiElement(underlying.firstName.data);
    fullName = fullName.trim();
    fullName += (" " + getFirstPiiElement(underlying.middleName.data));
    fullName = fullName.trim();
    fullName += (" " + getFirstPiiElement(underlying.surName.data));
    fullName = fullName.trim();
    fullName += (" " + getFirstPiiElement(underlying.suffixName.data));
    fullName = fullName.trim();
    // fullName = `(${identity})\n${fullName}`
    return fullName;
}

const getRootNode = (identity: string, primaryIdentity: string, underlying: any): GraphNode => {
    const fullName: string = computeFullName(underlying, identity);
    const [color, fill] = (identity === primaryIdentity ? ["green", "MediumSeaGreen"] : ["tomato", "Tomato"])
    return {
        id: identity,
        label: fullName,
        fill,
        data: {
            color,
            type: "node",
            nodeType: "root",
            underlying
        }
    };
}

const getFieldNode = (identity: string, fieldName: string, underlying: any): GraphNode => {
    return {
        id: `${identity}|${fieldName}`,
        label: fieldName,
        fill: "DodgerBlue",
        data: {
            color: "blue",
            type: "node",
            nodeType: "field",
            underlying
        }
    };
}

const getDataNode = (id: string, value: string, fieldName: string, underlying?: any): GraphNode => {
    const tenancy = (underlying ? "individual" : "shared")
    return {
        id: id,
        label: value,
        fill: "Orange",
        data: {
            color: "orange",
            type: "node",
            nodeType: fieldName,
            tenancy,
            value,
            underlying: (underlying || {})
        }
    };
}

const getEdge = (src: string, dest: string): GraphEdge => {
    const id = `${btoa(src)}_${btoa(dest)}`;
    return {
        source: src,
        target: dest,
        id,
        size: 1,
        data: {
            type: "edge"
        }
    };
}

export function buildGraph(primarySocureID: string, graphRawData: Map<string, any>): IdentityGraphNodesEdges {
    const nodes: Array<GraphNode> = [], edges: Array<GraphEdge> = [];
    const sharedPiiNodeMap: Map<string, any> = new Map<string, any>();

    graphRawData.forEach((summary: any, socureId: string) => {
        const rootNode = getRootNode(socureId, primarySocureID, summary)
        nodes.push(rootNode);
        piiFields.forEach(field => {
            const fieldData = summary[field];
            const fieldNode = getFieldNode(socureId, field, fieldData)
            nodes.push(fieldNode);
            const fieldNodeId = `${socureId}|${field}`;
            edges.push(getEdge(socureId, fieldNodeId));
            if (sharedPiiFields.has(field)) {
                fieldData.data.forEach((v: any, key: string) => {
                    const dataNodeID = `${field}_${key}`;
                    if (!sharedPiiNodeMap.has(dataNodeID)) {
                        const dataNode = getDataNode(dataNodeID, key, field);
                        nodes.push(dataNode);
                        sharedPiiNodeMap.set(dataNodeID, dataNode);
                    }
                    const dataNode = sharedPiiNodeMap.get(dataNodeID);
                    dataNode.data.underlying[socureId] = v;
                    edges.push(getEdge(fieldNodeId, dataNodeID));
                });
            } else {
                fieldData.data.forEach((v: any, key: string) => {
                    const dataNodeID = `${fieldNodeId}|${key}`;
                    const dataNode = getDataNode(dataNodeID, key, field, v);
                    nodes.push(dataNode);
                    edges.push(getEdge(fieldNodeId, dataNodeID));
                });
            }
        });
    })


    return {
        nodes,
        edges
    };
}