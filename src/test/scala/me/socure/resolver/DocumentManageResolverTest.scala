package me.socure.resolver

import me.socure.document.manager.client.DocumentManagerClientImpl
import me.socure.document.manager.model.doctype.{DocumentCategories, DocumentSubCategories, DtoDocumentType, DtoDocumentTypeAccount, DtoProvisionAccount}
import org.mockito.Mockito.when
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.Future

class DocumentManageResolverTest extends FunSuite with MockitoSugar with Matchers {
  val documentManagerClient = mock[DocumentManagerClientImpl]

  val service = new DocumentManageResolver
  service.documentManagerClient = documentManagerClient

  val docType = DtoDocumentType(
      id = 1,
      publicId = "DocType-Ll4sFuAfaJ17IJZgzsUD",
      name = "license",
      category = DocumentCategories.Govt.id,
      subCategory = DocumentSubCategories.License.id,
      tags = None,
      description = None
    )

  val provisionAccount = DtoProvisionAccount (
    docTypePublicId = "DocType-Ll4sFuAfaJ17IJZgzsUD",
    accountPublicId = "public-12345"
  )

  val docTypeAccount = DtoDocumentTypeAccount (
    id=1234,
    docTypePublicId = "DocType-Ll4sFuAfaJ17IJZgzsUD",
    accountPublicId = "public-12345"
  )

  test("list all document types"){
    val documentTypes = List(docType)
    when(documentManagerClient.listDocTypes()) thenReturn(Future.successful(Right(documentTypes)))
    val res = service.listDocTypes()
    res shouldBe Right(documentTypes)
  }

  test("list all document types provisioned for an account"){
    val accountPublicId = "public-12345"
    val documentTypes = Seq(docType)
    when(documentManagerClient.listDocTypeByAccountPublicId(accountPublicId)) thenReturn(Future.successful(Right(documentTypes)))
    val res = service.listDocTypes()
    res shouldBe Right(documentTypes)
  }

  test("create or update a document type"){
    when(documentManagerClient.createDocumentType(docType)) thenReturn(Future.successful(Right(docType)))
    val res = service.createDocumentType(docType)
    res shouldBe Right(docType)
  }

  test("provision a document type"){
    when(documentManagerClient.provisionDocumentType(provisionAccount)) thenReturn(Future.successful(Right(docTypeAccount)))
    val res = service.provisionDocumentType(provisionAccount)
    res shouldBe Right(docTypeAccount)
  }

  test("provision all document type"){
    when(documentManagerClient.provisionAllDocumentTypes("public-12345")) thenReturn(Future.successful(Right("migration completed")))
    val res = service.provisionAllDocumentType("public-12345")
    res shouldBe Right("migration completed")
  }

  test("deprovision a document type"){
    val data = "Removed"
    when(documentManagerClient.deProvisionDocumentType(provisionAccount)) thenReturn(Future.successful(Right(data)))
    val res = service.deProvisionDocumentType(provisionAccount)
    res shouldBe Right(data)
  }


}
