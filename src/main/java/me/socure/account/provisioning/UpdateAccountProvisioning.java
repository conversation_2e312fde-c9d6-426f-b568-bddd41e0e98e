package me.socure.account.provisioning;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateAccountProvisioning {
    String bundleReference;
    List<DtoUpdateProduct> products;
    ProductConfiguration productConfiguration;

    public UpdateAccountProvisioning() {
    }

    public UpdateAccountProvisioning(String bundleReference, List<DtoUpdateProduct> products) {
        this.bundleReference = bundleReference;
        this.products = products;
    }

    public UpdateAccountProvisioning(String bundleReference, List<DtoUpdateProduct> products, ProductConfiguration productConfiguration) {
        this.bundleReference = bundleReference;
        this.products = products;
        this.productConfiguration = productConfiguration;
    }

    public String getBundleReference() {
        return bundleReference;
    }

    public void setBundleReference(String bundleReference) {
        this.bundleReference = bundleReference;
    }

    public List<DtoUpdateProduct> getProducts() {
        return products;
    }

    public void setProducts(List<DtoUpdateProduct> products) {
        this.products = products;
    }

    public ProductConfiguration getProductConfiguration() {
        return productConfiguration;
    }

    public void setProductConfiguration(ProductConfiguration productConfiguration) {
        this.productConfiguration = productConfiguration;
    }
}
