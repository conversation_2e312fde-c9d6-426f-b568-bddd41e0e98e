<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="/resources/identity_graph/favicon.ico" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Web site created using create-react-app" />
    <link rel="apple-touch-icon" href="/resources/identity_graph/logo192.png" />
    <link rel="manifest" href="/resources/identity_graph/manifest.json" />
    <title>React App</title>
    <script defer="defer" src="/resources/identity_graph/static/js/main.js"></script>
    <link href="/resources/identity_graph/static/css/main.css" rel="stylesheet" />
</head>
<body>
<noscript>You need to enable JavaScript to run this app.</noscript>
<div style="height:98vh;width:99vw;overflow-y:scroll" id="root"></div>
</body>
</html>