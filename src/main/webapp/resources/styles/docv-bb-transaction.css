:root {
  --theme-blue: #237791;
  --theme-white: #c3c4c5;
}

#docv-bb-txn {
  padding: 1em;
  display: none;
  width: 100vw;
}

/**
* Overview Section
*/

#docv-bb-txn-overview {
  display: flex;
}

#docv-bb-txn-overview-info {
  flex-basis: 50%;
}

#docv-bb-txn-overview-additional {
  flex-basis: 50%;
}

#docv-bb-txn-overview-additional-photo-id-data {
  color: var(--theme-blue);
  background: none;
  border: 1px solid var(--theme-white);
  padding: 0.5em 1em 0.5em 1em;
  width: 90%;
  text-align: left;
}

#docv-bb-txn-overview-additional-photo-id-data-collapsible {
  display: none;
  border: 1px solid var(--theme-white);
  height: 20em;
  width: 90%;
  overflow-y: scroll;
  margin-bottom: 0.5em;
}

#docv-bb-txn-overview-additional-steps-completed-container {
  margin-top: 1em;
  display: flex;
}

#docv-bb-txn-overview-additional-steps-completed {
  font-weight: bold;
}

/**
* Verification Section
*/

#docv-bb-txn-verifications {
  margin-top: 3em;
}

.docv-bb-txn-data-container {
  border: 1px solid var(--theme-white);
  background-color: white;
  padding-top: 1em;
}

#docv-bb-txn-verifications-photo-container-container {
  width: 100%;
  display: flex;
  padding-left: 1em;
  padding-right: 1em;
}

.docv-bb-txn-verifications-photo-container {
  flex: 1;
}

.docv-bb-txn-verifications-photo-container-title {
  font-weight: normal;
}

.docv-bb-txn-verifications-photo-container-verification-photo-info {
  padding-right: 1em;
}

.docv-bb-txn-verifications-photo-container-verification-photo-info-image-container {
  position: relative;
}

.docv-bb-txn-verifications-photo-container-arrow-container {
  position: absolute;
  bottom: 5px;
  height: 3em;
  width: 7em;
  display: flex;
}

.docv-bb-txn-verifications-photo-container-warning {
  text-align: justify;
  white-space: pre-line;
}

.docv-bb-txn-verifications-photo-container-arrow-icon {
  border-radius: 50%;
  background-color: white;
  opacity: 70%;
  width: 3em;
  height: 3em;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
}

#docv-bb-txn-verifications-toggle-container {
  margin-top: 1em;
  display: flex;
  align-items: center;
}

#docv-bb-txn-verifications-data-toggle {
  border-radius: 5px;
  border: 1px solid black;
  padding: 2px;
}

.docv-bb-txn-verifications-toggle-type {
  background-color: white;
  text-transform: uppercase;
  font-weight: bold;
  border: none;
  border-radius: 4px;
  padding: 0.3em 0.6em;
}

#docv-bb-txn-verifications-data-tab-container {
  display: flex;
  border-bottom: 1px solid var(--theme-white);
  padding-left: 0.5em;
  margin-top: 1em;
}

.docv-bb-txn-verifications-data-tab {
  color: var(--theme-blue);
  border: none;
  border-radius: 0;
  background-color: white;
  border-bottom: 2px solid white;
}

#docv-bb-txn-verifications-toggle-container {
  padding-left: 1em;
}

/**
* PII / API Section
*/

#docv-bb-txn-pii-and-api-title {
  margin-top: 3em;
}

#docv-bb-txn-pii-and-api-tabs {
  border-bottom: 1px solid var(--theme-white);
}

.docv-bb-txn-pii-and-api-tab {
  color: var(--theme-blue);
  border: none;
  border-radius: 0;
  background-color: white;
  border-bottom: 2px solid white;
}

#docv-bb-txn-pii-and-api-pii-tab {
  color: black;
  border-bottom: 2px solid black;
}

#docv-bb-txn-pii-and-api-pii-data {
  height: 60vh;
  overflow-y: scroll;
}

#docv-bb-txn-pii-and-api-api-data {
  display: none;
  height: 60vh;
  overflow-y: scroll;
}

#docv-bb-txn-pii-and-api-pii-data-header > tr {
  position: sticky;
  top: 0;
  box-shadow: 0 0 0 1px var(--theme-white);
}

#docv-bb-txn-pii-and-api-pii-data-table {
  width: 100%;
  overflow-x: scroll;
  table-layout: fixed;
}

#docv-bb-txn-pii-and-api-pii-data-header > tr > th {
  padding: 1em;
  background-color: white;
}

#docv-bb-txn-pii-and-api-pii-data-table-body > tr > th,
#docv-bb-txn-pii-and-api-pii-data-table-body > tr > td {
  padding: 1em;
  border-right: 1px solid var(--theme-white);
}

#docv-bb-txn-pii-and-api-pii-data-table-body
  .docv-bb-txn-pii-and-api-pii-data-table-mrz-col {
  border-right: 0;
}
