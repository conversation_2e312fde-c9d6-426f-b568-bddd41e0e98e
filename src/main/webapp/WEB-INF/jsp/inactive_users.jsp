<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>

<html>
<head>
<link rel="stylesheet" href="css/jq.css" type="text/css"
	media="print, projection, screen" />
<link rel="stylesheet" href="resources/css/style.css" type="text/css"
	id="" media="print, projection, screen" />
<script type="text/javascript" src="resources/js/jquery-latest.js"></script>
<script type="text/javascript"
	src="resources/js/__jquery.tablesorter.js"></script>
<script type="text/javascript"
	src="resources/js/jquery.tablesorter.pager.js"></script>
<script type="text/javascript" src="resources/js/jquery.min.js"></script>
<script language="javascript">
	$(function() {

		// add multiple select / deselect functionality
		$("#selectall").click(function() {
			$('.case').attr('checked', this.checked);
		});

		// if all checkbox are selected, check the selectall checkbox
		// and viceversa
		$(".case").click(function() {

			if ($(".case").length == $(".case:checked").length) {
				$("#selectall").attr("checked", "checked");
			} else {
				$("#selectall").removeAttr("checked");
			}

		});
	});
</script>
<script type="text/javascript" id="js">
	$(document).ready(function() {
		$("table").tablesorter();
		$("#trigger-link").click(function() {
			// set sorting column and direction, this will sort on the first and third column the column index starts at zero
			var sorting = [ [ 0, 0 ], [ 2, 0 ] ];
			// sort on the first column
			$("table").trigger("sorton", [ sorting ]);
			// return false to stop default link action
			return false;
		});
	});

	function validate() {
		var selected = new Array();
		var email = '';
		$("input:checkbox[name=case]:checked").each(function() {
			//emails += $(this).val() + ' ';
			selected.push($(this).val());
		});
		email = selected.toString();
		if (email != '') {
			$.ajax({
				url : "send_activation",
				data : {
					email : email,
					async : false
				},
				type : 'GET',
				success : function(data) {
					$('#notification').text(data);
					$('input:checkbox[name=case]').attr('checked', false);
				}

			});
		} else {
			alert('Please select atleast one user to activate');
		}
	}
</script>

</head>

<body>
	<div align="center">
		<font color="red"><h3 id="notification"></h3> </font>
	</div>
	<form:form commandName="user" action="">
		<fieldset>
			<legend>In active Users</legend>
			<table id="myTable" class="tablesorter">
				<thead>
					<th><input type="checkbox" id="selectall" /></th>
					<th>First Name</th>
					<th>Last Name</th>
					<th>Company</th>
					<th>Contact Number</th>
					<th>Email</th>
					<th>ApiKey</th>
					<th>Registered On</th>
					<th>Address</th>

				</thead>
				<tbody>
					<c:forEach items="${registred_user}" var="user" begin="0"
						end="${registred_user.size()}" varStatus="rowCount">
						<tr>
							<td align="center"><input type="checkbox" class="case"
								name="case" value="${user.email}" /></td>
							<td>${user.firstName}</td>
							<td>${user.lastName}</td>
							<td>${user.companyName}</td>
							<td>${user.contactNumber}</td>
							<td>${user.email}</td>
							<td>${user.apiKey}</td>
							<td><fmt:formatDate type="both"
									value="${user.registredon.getTime()}" /></td>
							<td>${user.address}</td>

							<%-- 
							<td><a href="send_activation?email=${user.email}">Send
									Activation Link </a></td> --%>
						</tr>
					</c:forEach>

				</tbody>

			</table>
			<input type="button" value="Send Activation Link"
				onclick="validate();" />
		</fieldset>
	</form:form>
</body>
</html>