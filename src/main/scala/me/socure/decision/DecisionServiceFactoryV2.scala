package me.socure.decision

import com.typesafe.config.{Config, ConfigFactory}
import me.socure.decision.service.client.DecisionServiceClientV2
import me.socure.decision.service.client.factory.DecisionServiceClientFactory
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}

import scala.concurrent.ExecutionContext

class DecisionServiceFactoryV2 extends FactoryBean[DecisionServiceClientV2]{
  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  override def getObject: DecisionServiceClientV2 = {

    val config: Config  = ConfigFactory.parseProperties(envUtil.getPropertiesPlain("decision.service"))
    DecisionServiceClientFactory.createV2(config)(executionContext)
  }

  override def getObjectType: Class[DecisionServiceClientV2] = classOf[DecisionServiceClientV2]

  override def isSingleton: Boolean = true
}
