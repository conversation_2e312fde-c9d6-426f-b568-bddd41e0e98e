import React from 'react';
import styled from 'styled-components';
import { Icon, dimensions } from '@socure/components';
import { BundleDetails, TreeFormattedModule } from './AccountProvisioning.type';

const ModuleList = styled.div`
    display: flex;
    align-items: center;
    padding: 0 5px 0 5px;
`

const Header = styled.span`
    font-weight: bold;
    padding: 9px;
`

const Label = styled.span`
    padding-left: 5px;
`

const BundleLabel = styled.span`
    padding: 5px;
`

export interface Modifications {
    update: string,
    name: string
}

export interface UpdateConfirmProps {
    payload: TreeFormattedModule[],
    modifiedFeatureFlags: Modifications[],
    modifiedProducts: Modifications[],
    selectedBundleDetail: BundleDetails,
    existingBundle: string
}

const renderUpdates = (updates: Modifications[]) => {
    return (<div>
    {updates && updates.map( itemUpdated => <ModuleList>
        <Icon name={itemUpdated.update === 'ADD' ?  'plus' : itemUpdated.update === 'REMOVE' ? 'minus' : 'check'} 
        color={itemUpdated.update === 'ADD' ?  'green' : itemUpdated.update === 'REMOVE' ? 'red' : 'gray'} size={dimensions.XS} />
        <Label>{itemUpdated.name}</Label>
    </ModuleList>
    )}
    </div>);
}


const UpdateConfirmationDialog: React.FC<UpdateConfirmProps> = ({ payload, modifiedFeatureFlags, selectedBundleDetail, existingBundle, modifiedProducts }): React.ReactElement  => {

    let productsOfSelectedBundle:Modifications[]  = [];
    let otherProducts: Modifications[] = [];
    let productsRemovedFromBundle: Modifications[] = [];

    payload?.map( module => {
        if(!module.indentLevel) {
            if(selectedBundleDetail) {
                if(selectedBundleDetail?.resources.includes(module.businessUserRoleId)) {
                    let foundBundleProduct = modifiedProducts.find(x => x.name === module.name);
                    if(foundBundleProduct && module.enabled) {
                        productsOfSelectedBundle.push(foundBundleProduct);
                    } else if(foundBundleProduct && !module.enabled) {
                        productsRemovedFromBundle.push(foundBundleProduct);
                    } else {
                        productsOfSelectedBundle.push({ name: module.name, update: "check"});
                    }
                }
                if(!selectedBundleDetail?.resources.includes(module.businessUserRoleId)) {
                    let foundNonBundleProduct = modifiedProducts.find(x => x.name === module.name);
                    if(foundNonBundleProduct) {
                        otherProducts.push(foundNonBundleProduct);
                    } else if(module.enabled) {
                        otherProducts.push({ name: module.name, update: "check"});
                    }
                }
            } else {
                // when  bundle is not associated with the account
                if(modifiedProducts) {
                    const moduleFound = modifiedProducts.find(p => p.name === module.name);
                    if(moduleFound) {
                        otherProducts.push(moduleFound);
                    } else if(module.provisioned) {
                        otherProducts.push({name: module.name, update: "check"});
                    }
                }
            }
        }
     });

     const bundleChanged = selectedBundleDetail && selectedBundleDetail?.name !== existingBundle;
      
    return( 
    <>
      {  selectedBundleDetail && <BundleLabel><Icon name='edit-2' color='gray' size={dimensions.XS} />
         {bundleChanged ?
         <>
         <Label>Changed from</Label>
         <Header>{existingBundle}</Header>
         <Label>to</Label>
         <Header>{selectedBundleDetail?.name }</Header>
         </>
         :
         <>
         <Label>Selected </Label>
         <Header>{selectedBundleDetail?.name}</Header>
         </>
        }       
      </BundleLabel>
      }
      {productsOfSelectedBundle.length && productsRemovedFromBundle.length ? <Header>Bundle Modules</Header> : <></>}
        {renderUpdates([...productsOfSelectedBundle, ...productsRemovedFromBundle ])}
        {otherProducts.length > 0 && <Header>Other Modules</Header>}
        {renderUpdates(otherProducts)}
        {modifiedFeatureFlags.length ? <Header>Feature Flags</Header> : <></>}
        {renderUpdates(modifiedFeatureFlags)}
    </>
    )
}

export default UpdateConfirmationDialog;