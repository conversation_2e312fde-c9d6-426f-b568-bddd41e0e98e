import React, { useEffect, useRef } from 'react';
import { Grid } from '@mui/material';
import { Route, Routes } from "react-router-dom";
import { SocureToaster, updateToasterReference } from '../Components/Toaster';
import Layout from './Layout';
import { GlobalPageList } from './Layout/data';
import RouteRenderer from './RouteRenderer';

const Application: React.FC = function (props) {

  const toasterRef = useRef<SocureToaster>(null);

  useEffect(() => {
    updateToasterReference(toasterRef);
  }, [toasterRef]);

  return (
    <React.Fragment>
      <Layout />
      <SocureToaster ref={toasterRef} />
      <Grid container direction="column" spacing={1}>
        <Grid item key="body">
          <Routes>
            {
              GlobalPageList
                .map(_ => _.pages)
                .flat()
                .map(p => {
                  if (p.type === "legacy") return null;
                  else {
                    const { display, route, component } = p;
                    return <Route
                      key={route}
                      path={route}
                      Component={(props) => <RouteRenderer title={display} Component={component} />}
                    />
                  }
                })
            }
          </Routes>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default Application;
