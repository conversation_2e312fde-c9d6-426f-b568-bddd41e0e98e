<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core" xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Document Types Manager</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css" media="print" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

	<h:outputStylesheet>
		.truncate {
		width: 150px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		}
	</h:outputStylesheet>

	<script>
		var bucketurl = "#{bucket.resourceURL}";
		var contexturl = "#{request.contextPath}";
		var marketingurl = "#{bucket.marketingSite}";
		var appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px;">
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>
	<div class="accordion" id="accordion2">
		<div class='container' style='margin-top: 10px'>
			<table cellpadding='0' cellspacing='0' border='0' class='table table-striped table-bordered' id='document_types_table'>
				<thead>
					<tr>
						<th>Id</th>
						<th>Public Id</th>
						<th>Name</th>
						<th>Description</th>
						<th>Category</th>
						<th>Sub category</th>
						<th>Tags</th>
						<th></th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<div style="padding-left: 215px;">
		<a href="javascript:show_docType_add_form();">Add New</a>&nbsp;&nbsp; | &nbsp;
	</div>

	<div class="modal fade" id="AddDocumentType" tabindex="-1" role="dialog" aria-labelledby="AddDocumentTypeLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="AddDocumentTypeLabel">Document Type Changes</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="message" class="alert alert-error"></div>
					<form id="add_document_type" action="#" method="post">
						<div class="form-group row">
							<label for="docId" class="col-sm-3 col-form-label">Id</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="docId" readonly="true"/>
							</div>
						</div>

						<div class="form-group row">
							<label for="publicId" class="col-sm-3 col-form-label">Public Id</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="publicId" placeholder="System generated" readonly="true"/>
							</div>
						</div>

						<div class="form-group row">
							<label for="docName" class="col-sm-3 col-form-label">Name*</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="docName" placeholder="LICENSE" />
							</div>
						</div>

						<div class="form-group row">
							<label for="docDescription" class="col-sm-3 col-form-label">Description</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="docDescription" placeholder="add more details about document type" />
							</div>
						</div>

						<div class="form-group row">
							<label for="docCategory" class="col-sm-3 col-form-label">Category*</label>
							<div class="col-sm-9 model-format-dropdown format-selector">
								<select id="docCategory">
									<option value="">-</option>
									<option value="1">Govt</option>
								</select>
								<div class="error-msg" id="identityerrormsg"></div>
							</div>
						</div>

						<div class="form-group row">
							<label for="docSubCategory" class="col-sm-3 col-form-label">Sub Category*</label>
							<div class="col-sm-9 model-format-dropdown format-selector">
								<select id="docSubCategory">
									<option value="">-</option>
									<option value="1">License</option>
								</select>
								<div class="error-msg" id="identityerrormsg"></div>
							</div>
						</div>

						<div class="form-group row">
							<label for="docTags" class="col-sm-3 col-form-label">Tags</label>
							<div class="col-sm-9">
								<span id="docTags">
								<input type="checkbox" name="docTagsCheckbox" id="utility" value="1"/> utility<br/>
								<input type="checkbox" name="docTagsCheckbox" id="legal" value="2"/> legal<br/>
								<input type="checkbox" name="docTagsCheckbox" id="govt" value="3"/> govt<br/>
								</span>
							</div>
						</div>


					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					<button type="button" class="btn btn-primary" onclick="save_document_type(null)">Save</button>
				</div>
			</div>
		</div>
	</div>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="resources/scripts/bootstrap.min.js"></script>
	<script type="text/javascript" src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/document_types.js"></script>
	<script>
		$(document).ready(function() {
			get_documentTypes();
		});
	</script>
</h:body>

</html>
