package me.socure.resolver

import java.io.InputStream

import me.socure.model.ErrorResponse
import me.socure.sandbox.common.SandboxServiceClient
import me.socure.sandbox.common.model._
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import scala.concurrent.Await
import scala.concurrent.duration._

@Component
class SandboxServiceResolver {

  @Autowired
  var sandboxServiceClient: SandboxServiceClient = _

  def getAllConfigurations(): Either[ErrorResponse, List[ListConfigurationResponse]] = {
    val response = sandboxServiceClient.listConfigurations()
    Await.result(response, Duration.Inf)
  }

  def addConfiguration(addConfigurationRequest: AddConfigurationRequest): Either[ErrorResponse, AddConfigurationResponse] = {
    val response = sandboxServiceClient.addConfiguration(addConfigurationRequest)
    Await.result(response, Duration.Inf)
  }

  def markAsLive(markAsLiveRequest: MarkAsLiveRequest): Either[ErrorResponse, MarkAsLiveResponse] = {
    val response = sandboxServiceClient.markAsLive(markAsLiveRequest)
    Await.result(response, Duration.Inf)
  }

  def getConfiguration(revision: Long): Either[ErrorResponse, InputStream] = {
    val response = sandboxServiceClient.getConfiguration(revision)
    Await.result(response, Duration.Inf)
  }
}
