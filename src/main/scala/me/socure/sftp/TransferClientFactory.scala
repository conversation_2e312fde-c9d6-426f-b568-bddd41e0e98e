package me.socure.sftp

import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.transfer.TransferClient

class TransferClientFactory extends FactoryBean[TransferClient]{
  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  override def getObject: TransferClient = {
    val sftpRegion = Region.of(envUtil.getProperty("sftp.region"))
    TransferClient
      .builder()
      .region(sftpRegion)
      .build()
  }

  override def getObjectType: Class[TransferClient] = classOf[TransferClient]

  override def isSingleton: Boolean = true

}