import React, { useCallback } from 'react';

const Topbar = ({ menus, onClick }) => {
  const handleClick = useCallback((menu) => {
    onClick && onClick(menu);
  }, [ onClick ])

  return <div className='socure-c-topbar-container'>
    {menus && menus.map((menu, idx) =>
      <div className='socure-c-each-menu' key={idx} onClick={() => handleClick(menu)}>
        <div className='socure-c-topbar-menu' >{menu}</div>
        {idx !== menus.length -1 && <div className='socure-c-menu-separator' />}
      </div>
    )}
  </div>
}

export default Topbar;