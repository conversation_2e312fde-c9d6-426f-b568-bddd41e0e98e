var app = app || {};
app.subscription = [];

app.subscribe = function(callback) {
    app.subscription.push(callback);
}

app.publish = function() {
    app.subscription.forEach(function(callback) {
        callback();
    });
}

app.loadConfig = function(filename)
{
	$.ajax(filename,
		{
			dataType: 'json',
			error: function()
			{
				if (filename != bucketurl + '/config-default.json')
				{
					app.loadConfig(bucketurl + '/config-default.json');
				}
			},
			success: function(data)
			{
				app.host = data.host;
				app.loginUrl = data.loginUrl;
				app.apiHost = data.apiHost;
				app.local = data.local;
				app.modelManagement = data.modelManagement === "true";
				app.defaultModel = data.defaultModel === "true";
				app.checkSession(data);
				app.publish()
			}
		})
}

app.checkSession = function(configData)
{
//	$.ajax(app.host + '/saml2/check_session',
//		{
//			type: 'GET',
//			dataType: 'json',
//			success: function(data)
//			{
//				console.log('Session valid')
//			},
//			error: function()
//			{
//			    console.log('Invalid session', arguments)
//				window.location = app.host + '/unauthorized';
//			},
//			// Timeout needed, otherwise error not called for JSONP
//			timeout: 5000
//		});
}

// Polyfill for Cross-domain Ajax requests for IE8/9.

// jQuery.XDomainRequest.js
// Author: Jason Moon - @JSONMOON
// IE8+
if (!jQuery.support.cors && jQuery.ajaxTransport && window.XDomainRequest)
{
	var httpRegEx = /^https?:\/\//i;
	var getOrPostRegEx = /^get|post$/i;
	var sameSchemeRegEx = new RegExp('^' + location.protocol, 'i');
	var htmlRegEx = /text\/html/i;
	var jsonRegEx = /\/json/i;
	var xmlRegEx = /\/xml/i;

	// ajaxTransport exists in jQuery 1.5+
	jQuery
		.ajaxTransport(
		'text html xml json',
		function(options, userOptions, jqXHR)
		{
			// XDomainRequests must be: asynchronous, GET or POST
			// methods, HTTP or HTTPS
			// protocol, and same scheme as calling page
			if (options.crossDomain && options.async && getOrPostRegEx.test(options.type) &&
				httpRegEx.test(options.url) && sameSchemeRegEx.test(options.url))
			{
				console.info('IE XDR Request')
				var xdr = null;
				var userType = (userOptions.dataType || '')
					.toLowerCase();
				return {
					send: function(headers, complete)
					{
						xdr = new XDomainRequest();
						if (/^\d+$/.test(userOptions.timeout))
						{
							xdr.timeout = userOptions.timeout;
						}
						xdr.ontimeout = function()
						{
							complete(500, 'timeout');
						};
						xdr.onload = function()
						{
							var allResponseHeaders = 'Content-Length: ' + xdr.responseText.length +
								'\r\nContent-Type: ' + xdr.contentType;
							var status = {
								code: 200,
								message: 'success'
							};
							var responses = {
								text: xdr.responseText
							};
							try
							{
								if (userType === 'html' || htmlRegEx
										.test(xdr.contentType))
								{
									responses.html = xdr.responseText;
								}
								else if (userType === 'json' || (userType !== 'text' && jsonRegEx
										.test(xdr.contentType)))
								{
									try
									{
										responses.json = jQuery
											.parseJSON(xdr.responseText);
									}
									catch (e)
									{
										status.code = 500;
										status.message = 'parseerror';
										// throw 'Invalid JSON: ' +
										// xdr.responseText;
									}
								}
								else if (userType === 'xml' || (userType !== 'text' && xmlRegEx
										.test(xdr.contentType)))
								{
									var doc = new ActiveXObject(
										'Microsoft.XMLDOM');
									doc.async = false;
									try
									{
										doc
											.loadXML(xdr.responseText);
									}
									catch (e)
									{
										doc = undefined;
									}
									if (!doc || !doc.documentElement || doc
											.getElementsByTagName('parsererror').length)
									{
										status.code = 500;
										status.message = 'parseerror';
										throw 'Invalid XML: ' + xdr.responseText;
									}
									responses.xml = doc;
								}
							}
							catch (parseMessage)
							{
								throw parseMessage;
							}
							finally
							{
								complete(status.code,
									status.message, responses,
									allResponseHeaders);
							}
						};
						// set an empty handler for 'onprogress' so
						// requests don't get
						// aborted
						xdr.onprogress = function() {};
						xdr.onerror = function()
						{
							complete(500, 'error',
								{
									text: xdr.responseText
								});
						};
						var postData = '';
						if (userOptions.data)
						{
							postData = (jQuery
								.type(userOptions.data) === 'string') ? userOptions.data : jQuery
								.param(userOptions.data);
						}
						xdr.open(options.type, options.url);
						xdr.send(postData);
					},
					abort: function()
					{
						if (xdr)
						{
							xdr.abort();
						}
					}
				};
			}
		});
}

$(function()
{
	// Attempting cross-domain ajax in IE9
	$.support.cors = true;
	console.log("appEnv=>");
	console.log(appenv);

	var filename = "";
	if(appenv != null && appenv.length > 0) {
	    filename = "/config-" + appenv + '.json';
	} else {
	    filename = "/config.json"
	}
	app.loadConfig(bucketurl + filename);
});

show_industry_delete_dialog = function()
{
	var emails = window.delegated_admin_email;
	if (emails != null && emails.length > 0)
	{
		var msg = "";
		if (emails.length < 2)
		{
			msg = "Are you sure you want to delete " + emails + "?";
		}
		else
		{
			msg = "Are you sure to delete the folowing Industries?";
			var email = "";
			for (var i = 0; i < emails.length; i++)
			{
				msg = msg + "<br />" + emails[i];
			}
		}
		$('#delete_msg').html(msg);
		$('#MessageModal').modal('show');
	}
	else
	{
		$('#admin_message').html('Please select Industry to delete');
	}
}

delete_industry = function(isSuperAdmin)
{
	var emails = window.delegated_admin_email;
	var sector = "";
	for (var i = 0; i < emails.length; i++)
	{
		sector = emails[i] + "," + sector;
	}
	var url = 'api/1/industry/delete';
	$.ajax(url,
		{
			type: 'POST',
			data: (
			{
				sector: sector,
			}),

			success: function(data)
			{
				$('#MessageModal').modal('hide');
				$('#super_admin_msg').html(data['msg']);
				get_industry();
			}
		});
}

get_industry = function()
{
	// var url = app.host + '/api/1/industry/list';
	var url = 'api/1/industry/list';
	$.ajax(url,
		{
			type: 'GET',
			success: function(data)
			{
				if (data != null)
				{
					data = data['data'];

					show_Industry(data);

					$("#IndustryAll").click(function()
					{
						if ($(this).is(':checked'))
						{
							$('.case').prop('checked', true);
						}
						else
						{
							$('.case').prop('checked', false);
						}
					});
				}
			}
		});
}

get_rate_limit_users = function(list_name)
{
    var url = 'superadmin/1/rate_limiter_accounts';
    $.ajax(url,
        {
            type: 'GET',
            success: function(data)
            {
                if (data != null)
                {
                    data = Object.values(data['data']);
                    var primaryAdmin = {};
                    for (var i = 0; i < data.length; i++)
                    {
                        primaryAdmin[data[i]['id']] = data[i]['id'] + ' - ' + data[
                            i]['name'];
                    }
                    var select = $('#' + list_name);
                    if (select.prop)
                    {
                        var options = select.prop('options');
                    }
                    else
                    {
                        var options = select.attr('options');
                    }
                    $('option', select).remove();

                    $.each(primaryAdmin, function(val, text) {
                        options[options.length] = new Option(text, val);
                    });
                    select.trigger("chosen:updated");
                }
            }
        });
}

/* Formating function for row details */
fnFormatIndustry = function(nTr)
{
	var data = oTable.fnGetData(nTr);
	if (data != null)
	{
		sOut = "<fieldset>" +
		"<legend style='font-size:13px;'>Update Industry</legend>" +
		"<form id='update_industry' action='#' method='post'>" +
		"<table border='0'>" + "	<tr>" + "		<td align='right'>Sector </td>" +
		"		<td><input id='sector' type='text' required='true' value='" + data[1] +
		"'/></td>" + "		<td align='right'>Description </td>" +
		"		<td><input id='description' type='text' value='" + data[2] + "'/></td>" +
		"</table>" + "<div align='right'>" +
		"<a  href='javascript:save_industry(update_industry)' class='button submit'>Update</a>" +
		"</div>" + "</form>" + "</fieldset>"

		return sOut;
	}
	else
	{
		return "";
	}
}

show_Industry = function(data)
{
	window.delegated_admin_email = [];
	if (data != null)
	{
		var aaData = [];
		var imgPath = bucketurl + '/assets/images/details_open.png';
		for (var j = 0; j < data.length; j++)
		{
			var rowData = data[j];
			var a = [];
			a.push(imgPath);
			a.push(rowData['sector']);
			a.push(rowData['description']);
			a.push(rowData['sector']);
			aaData.push(a);
		}
		console.log(aaData);

		oTable = $('#industry_table')
			.dataTable(
			{
				"aaData": aaData,
				"bProcessing": true,
				"bDestroy": true,
				"bAutoWidth": true,
				"aoColumnDefs": [
					{
						"bSortable": false,
						"aTargets": [0, -1]
					}],
				"aaSorting": [
					[1, 'asc']
				],
				"aoColumns": [
					{
						"fnRender": function(oObj)
						{
							return "<img src='" + oObj.aData[0] +
								"' width='20px' height='20px' />"
						},
						"aTargets": [0]
					},
					null,
					null,
					{
						"fnRender": function(oObj)
						{
							return "<input type='checkbox' class='case' name='case' value='" +
								oObj.aData[1] + "'/>";
						},
						"aTargets": [0]
					}
				],
			});

		$('#industry_table').off( 'click' ).on('click', "tbody tr td img", function ()  {
			var nTr = $(this).parents('tr')[0];
			if (oTable.fnIsOpen(nTr))
			{
				// This row is already open
				// - close it
				this.src = bucketurl + '/assets/images/details_open.png';
				oTable.fnClose(nTr);
			}
			else
			{
				// Open this row
				this.src = bucketurl + '/assets/images/details_close.png';
				oTable.fnOpen(nTr, fnFormatIndustry(nTr), 'details');
				// show_delegated_admin(email);
			}
		});
	}
}


save_industry = function(myform)
{
	var url = app.host + '/api/1/industry/create';
	var form = null;
	if (myform == null)
	{
		form = document.getElementById('add_industry');
	}
	else
	{
		form = myform;
	}

	$
		.ajax(
		url,
		{
			type: 'POST',
			data: (
			{
				sector: form.sector.value,
				description: form.description.value,
			}),

			success: function(data)
			{
				var name = form.description.value
				if (data['status'].toLowerCase() == 'ok')
				{
					get_industry();
					$("#AddIndustryModal").modal("hide");
					$('#super_admin_msg')
						.html(data['msg']);
					form.reset();
					$('#message').html('');
					$('#IndustryModal').modal('show');
				}
				else
				{
					$("#AddIndustryModal").modal("show");
					$('#message').html(data['msg']);
				}
			}
		});
}

show_industy_add_form = function()
{
	$('#AddIndustryModal').modal('show');
}

show_industy_delete_form = function()
{
	window.delegated_admin_email = [];
	$('.case').each(function()
	{
		var sThisVal = (this.checked ? $(this).val() : "");
		if (sThisVal != "")
		{
			window.delegated_admin_email.push(sThisVal);
		}
	});
	if (window.delegated_admin_email.length < 1)
	{
		$('#msg_font').prop('color', 'red');
		$('#super_admin_msg').html('Please select Industry delete.');
	}
	else
	{
		$('#msg_font').prop('color', 'blue');
		$('#super_admin_msg').html('');
		show_delete_dialog(true);
	}
}

bulk_blacklist = function()
{
	//var url = app.host + '/api/1/blacklist/addtoBulkBlackList.jsonp';
	//var url = servicehost + 'http://dev.socure.com:9090/socure-web/api/1/blacklist/addtoBulkBlackList.jsonp';
	var url = servicehost + '/api/1/blacklist/addtoBulkBlackList.jsonp';
	var form = document.getElementById('bulkblacklistform');
	var formData = new FormData();
	formData.append('myfile', $(form.myfile).get(0).files[0]);
	formData.append('socurekey', form.socurekey.value);
	$.ajax(
		{
			url: url,
			type: 'POST',
			data: formData,
			cache: false,
			contentType: false,
			processData: false,
			success: function(data)
			{
				console.log(data);
			},
			error: function(data)
			{
				console.log(data);
			}
		});
}


var idleTime = 0;
var timeout;

//Increment the idle time counter every minute.
var idleInterval = setInterval(timerIncrement,60000);

//Zero the idle timer on mouse movement.
$(this).mousemove(function (e) {
	idleTime = 0;
});
$(this).keypress(function (e) {
	idleTime = 0;
});

function timerIncrement() {
	idleTime = idleTime + 1;
	//console.log(idleTime)

	if (idleTime > timeout) {
		window.location.reload();
	}
}
function audit_stats(stats){

	var accordianId = 'socure_tp_stats_acc';

	var constructAccordian = function(key, val, parentId, $parent) {
		var keyWithoutSpace = key.replace(/\W/g, '_').toLowerCase().trim();
		var keyId = parentId + '_' + key.replace(/\W/g, '_');
		var $bodyHtml = '';
		if(jQuery.isPlainObject(val)) {
			var newParentId = parentId + '_' + keyId;
			$bodyHtml = $('<div class="accordion" id="' + newParentId + '"/>');
			for(var i in val) {
				if(val.hasOwnProperty(i)) {
					constructAccordian(i, val[i], newParentId, $bodyHtml);
				}
			}
		} else {
			var trimmedVal = val.toString().trim();
			if(trimmedVal.indexOf('{') === 0 || trimmedVal.indexOf('[') === 0) { //Json
				try {
                    $bodyHtml = $('<pre><textarea class="beautified-value ' + keyWithoutSpace + '" style="width: 800px;" readonly >' + vkbeautify.json(trimmedVal, 4) + '</textarea></pre>');
				} catch(e) {
					$bodyHtml = $('<span><textarea class="beautified-value ' + keyWithoutSpace + '" style="width: 800px;" readonly >' + trimmedVal + '</textarea></span>');
				}
			} else if(trimmedVal.indexOf('<') === 0) { //xml
				try {
                    $bodyHtml = $('<pre><textarea class="beautified-value ' + keyWithoutSpace + '" style="width: 800px;" readonly >' + vkbeautify.xml(trimmedVal, 4) + '</textarea></pre>');
                } catch(e) {
					$bodyHtml = $('<pre> <textarea  style="width: 800px;height: 200px;" readonly >' + trimmedVal + '</textarea> </pre>');
				}
			} else {
				$bodyHtml = $('<pre> <textarea class="beautified-value ' + keyWithoutSpace + '" style="width: 800px;" readonly >' + trimmedVal + '</textarea> </pre>');
			}
		}
		var $accHtml = $('<div class="accordion-group card-body">' +
			'  <div class="accordion-heading card">' +
			'    <a class="accordion-toggle" data-toggle="collapse" href="#' + keyId + '">' +
			key +
			'    </a>' +
			'  </div>' +
			'  <div id="' + keyId + '" class="accordion-body collapse">' +
			'    <div class="accordion-inner">' +
			$bodyHtml.html() +
			'  </div>' +
			'  </div>' +
			'</div>');
		$parent.append($accHtml);
	};

	var $accParent = $('<div class="accordion" id="' + accordianId + '"/>');
	for(var j in stats) {
		if (stats.hasOwnProperty(j)) {
			constructAccordian(j, stats[j], accordianId, $accParent);
		}
	}

	return $accParent.html();

    //
    //
    //
	//var main = '<ul class="accordion"> ';
	//$.each(stats, function(key, value) {
    //
	//	main +=  '<li>' +
	//	' <a href="#'+ key +'">'+ key + '</a><ul>';
	//	var accounts = value;
    //
    //
	//	$.each(accounts, function(k, v) {
    //
	//		main +=  ' <li> ' +
	//		' <a  href="#'+key + "-"+ k +'">'+ k + '</a><div>';
    //
	//		main += ' <table border="1px solid black">';
    //
	//		$.each(v, function(rkey, rvalue) {
	//			if (rkey == "Request"){
	//				main += '<tr><td>Request:</td><td> <textarea  style="width: 800px;" readonly >' + rvalue  + '</textarea></td></tr>';
	//			} else {
	//				if(key == "ACXIOM"){
	//				 main += '<tr><td>Response:</td><td id="' + key +k + rkey+ '"> <textarea  style="width: 800px;" readonly >' + vkbeautify.xml(rvalue, 4)  + '</textarea></td></tr>';
	//				 } else {
	//					rvalue = rvalue.replace(/\\\"/g, '\"');
	//					rvalue = rvalue.replace(/^"(.*)"$/, '$1');
	//					//	rvalue = trimText(rvalue);
	//					var flag = checkJsonString(rvalue);
	//					if (flag) {
	//						main += '<tr><td>Response:</td><td id="' + key + k + rkey + '"> <textarea  style="width: 800px;" readonly >' + vkbeautify.json(rvalue, 4) + '</textarea></td></tr>';
	//					} else {
	//						main += '<tr><td>Response:</td><td id="' + key + k + rkey + '"> <textarea  style="width: 800px;" readonly >' + rvalue + '</textarea></td></tr>';
	//					}
	//					//main += '<tr><td>Response:</td><td id="' + key +k + rkey+ '"> <textarea  style="width: 800px;" readonly >' + vkbeautify.json(rvalue, 4)  + '</textarea></td></tr>';
	//				}
	//			}
	//		});
    //
	//		main += '</table></div></li>';
    //
    //
	//	});
	//	main += '</ul></li>';
	//	//main += '</li>';
    //
	//});
	//main += "</ul>";
    //
	//return main;
}


function checkJsonString(value){
	try {
		json = $.parseJSON(value);
		return true;
	} catch (e) {
		return false;
	}
}

function isArray(obj) {
    return Object.prototype.toString.call(obj) === '[object Array]';
}
function isBoolean(obj) {
    return Object.prototype.toString.call(obj) === '[object Boolean]';
}
function isNumber(obj) {
    return Object.prototype.toString.call(obj) === '[object Number]';
}

function formatRequestParameters(obj){
    var result = JSON.stringify(obj, null, 2);
    return result;
}

function trimText(value){
	var n = value.indexOf("[");
	var n1 = value.indexOf("{");
	var lastindex;
	//var flag = false;
	if(n != -1 && n1 != -1){
		if( n>n1){
			//flag = true;
			lastindex = value.lastIndexOf("}");
			if (lastindex != -1) {
				value = value.substr(n1, lastindex );
			} else {
				value = value.substr(n1, value.length);
			}
		} else {
			lastindex = value.lastIndexOf("]");
			if (lastindex != -1) {
				value = value.substr(n, lastindex + 1);
			} else {
				value = value.substr(n, value.length);
			}
		}
		return value;
	}

	if (n == -1){
		n = value.indexOf("{")
	} else {
		lastindex = value.lastIndexOf("]");
		if (lastindex != -1) {
			value = value.substr(n, lastindex + 1);
		} else {
			value = value.substr(n, value.length);
		}
	}
	if (n == -1){
		return value;
	} else {
		lastindex = value.lastIndexOf("}");
		if (lastindex != -1) {
			value = value.substr(n, lastindex );
		} else {
			value = value.substr(n, value.length);
		}
	}
	return value;
}

function textToHtml(value){
	var pr_amp = /&/g;
	var pr_lt = /</g;
	var pr_gt = />/g;
	var pr_quot = /\"/g;
	/** escapest html special characters to html. */
	return value.replace(pr_amp, '&amp;')
		.replace(pr_lt, '&lt;')
		.replace(pr_gt, '&gt;');
}
save_delegate_admin = function(myform, isSuperAdmin) {
	var url = app.host + '/delegated_adminapi/1/create_delegated_admin';
	var form = null;
	if (myform == null) {
		form = document.getElementById('delegated_admin1');
		if (isSuperAdmin) {
			adminEmail = $("#account_admin_list").val();
		}
	} else {
		form = myform;
	}

	var permission = get_permission(form);
	$
		.ajax(
		url,
		{
			type : 'POST',
			data : ({
				id : form.id.value,
				firstName : form.firstName.value,
				lastName : form.lastName.value,
				companyName : form.companyName.value,
				contactNumber : form.contactNumber.value,
				email : form.email.value,
				confirmEmail : form.email.value,
				password : form.password.value,
				permission : permission,
				admin_email:adminEmail
			}),

			success : function(data) {
				var id = form.id.value;
				var name = form.firstName.value
				if (isSuperAdmin) {
					if (data['status'].toLowerCase() == 'ok') {
						$("#AddDelegatedAdminModal").modal("hide");
						show_delegated_admin($("#admin_list").val());
						form.reset();
						var msg = 'New delegated admin ' + name
							+ ' has been created successfully.';
						if (id != null && id > 0) {
							msg = name
							+ '\'s information has been updated successfully.';
						}
						$('#super_admin_msg').html(msg);
					} else {
						$("#AddDelegatedAdminModal").modal("show");
						var obj = data['data'];
						if (obj instanceof Array) {
							$('#message').html(obj[0]['errorMsg']);
						} else {
							$('#message').html(data['msg']);
						}
					}
				} else {
					if (data['status'].toLowerCase() == 'ok') {
						list_delegate_admin();
						$("#AddDelegatedAdminModal").modal("hide");
						if (id != null && id > 0) {
							$('#admin_message')
								.html(
								name
								+ '\'s information has been updated successfully.');
						} else {
							$('#admin_message')
								.html(
								'New delegate admin '
								+ name
								+ ' has been created successfully.');

						}
						form.reset();
						$('#message').html('');
						$('#DelegatedAdminModal').modal('show');
					} else {
						$("#AddDelegatedAdminModal").modal("show");
						var obj = data['data'];
						if (obj instanceof Array) {
							$('#message').html(obj[0]['errorMsg']);
						} else {
							$('#message').html(data['msg']);
						}
					}
				}
			}
		});
}

update_basic_info = function(form, isSuperAdmin) {
	var url = app.host + '/delegated_adminapi/1/update_info';
	$.ajax(url, {
		type : 'POST',
		data : ({
			email : form.email.value,
			firstname : form.firstName.value,
			lastname : form.lastName.value,
			company : form.companyName.value,
			contact : form.contactNumber.value
		}),

		success : function(data) {
			if (isSuperAdmin) {
				if (data['status'].toLowerCase() == 'ok') {
					$('#super_admin_msg').html(
						'user information has been updated successfully.');
				}
				show_delegated_admin($("#admin_list").val());
			} else {
				list_delegate_admin();
				$('#admin_message').html(data['msg']);
				$('#DelegatedAdminModal').modal('show');
			}
		}
	});
}

update_password = function(form, isSuperAdmin) {
	var url = app.host + '/delegated_adminapi/1/update_password';
	$.ajax(url, {
		type : 'POST',
		data : ({
			email : form.email.value,
			password : form.password.value,
		}),

		success : function(data) {
			if (isSuperAdmin) {
				if (data['status'].toLowerCase() == 'ok') {
					$('#super_admin_msg').html(
						'password has been updated successfully.');
				}
				show_delegated_admin($("#admin_list").val());
			} else {
				list_delegate_admin();
				$('#admin_message').html(data['msg']);
				$('#DelegatedAdminModal').modal('show');
			}
		}
	});
}

update_permission = function(form, isSuperAdmin) {
	var url = app.host + '/delegated_adminapi/1/update_permission';
	var permission = get_permission(form);
	$.ajax(url, {
		type : 'POST',
		data : ({
			email : form.email.value,
			permission : permission,
		}),

		success : function(data) {
			var name = form.firstName.value
			if (isSuperAdmin) {
				if (data['status'].toLowerCase() == 'ok') {
					$('#super_admin_msg')
						.html(
						'permission has been updated successfully.');
				} else {
					var obj = data['data'];
					if (obj instanceof Array) {
						$('#super_admin_msg').html(
							obj[0]['errorMsg']);
					} else {
						$('#super_admin_msg').html(data['msg']);
					}
				}
				show_delegated_admin($("#admin_list").val());
			} else {
				list_delegate_admin();
				if (data['status'].toLowerCase() == 'ok') {
					$('#admin_message').html(
						name + '\'s permission has been updated successfully.');
				} else {
					var obj = data['data'];
					if (obj instanceof Array) {
						$('#admin_message').html(obj[0]['errorMsg']);
					} else {
						$('#admin_message').html(data['msg']);
					}
				}
			}
		}
	});
}

get_permission = function(form) {
	var chk = form.getElementsByClassName('permission');
	var len = chk.length;
	var permission = "";
	for ( var i = 0; i < len; i++) {
		if ($(chk[i]).is(':checked')) {
			if (chk[i].checked == true) {
				permission = chk[i].value + "," + permission;
			}
		}
	}
	return permission;
}

show_empty_msg = function() {
	$('#admin_message').html('No delegated admin found.');
}

show_delete_dialog = function(isSuperAdmin) {
	var emails = window.delegated_admin_email;
	if (emails != null && emails.length > 0) {
		var msg = "";
		if (emails.length < 2) {
			msg = "Are you sure you want to delete " + emails + "?";
		} else {
			msg = "Are you sure to delete the folowing users?";
			var email = "";
			for ( var i = 0; i < emails.length; i++) {
				msg = msg + "<br />" + emails[i];
			}
		}
		if (!isSuperAdmin) {
			$('#DelegatedAdminModal').modal('hide');
		}
		$('#delete_msg').html(msg);
		$('#DelegatedAdminModal').modal('hide');
		$('#MessageModal').modal('show');
	} else {
		if (!isSuperAdmin) {
			$('#admin_message').html('Please select delegate admin to delete');
		}
	}
}

delete_delegate_admin = function(isSuperAdmin) {
	var emails = window.delegated_admin_email;
	var email = "";
	for ( var i = 0; i < emails.length; i++) {
		email = emails[i] + "," + email;
	}
	var url = app.host + '/delegated_adminapi/1/delete_delegate_admin';
	$.ajax(url, {
		type : 'POST',
		data : ({
			email : email,
		}),

		success : function(data) {
			$('#MessageModal').modal('hide');
			if (isSuperAdmin) {
				if (data['status'].toLowerCase() == 'ok') {
					$('#super_admin_msg').html(
						"delegate admin(s) deleted successfully.");
				} else {
					var obj = data['data'];
					$('#super_admin_msg').html(html(obj[0]['errorMsg']));
				}
				show_delegated_admin($("#admin_list").val());
			} else {
				list_delegate_admin();
				if (data['status'].toLowerCase() == 'ok') {
					$('#admin_message').html(
						"Delegate admin(s) deleted successfully.");
				} else {
					var obj = data['data'];
					$('#admin_message').html(html(obj[0]['errorMsg']));
				}
			}
		}
	});
}

promote_delegate_admin = function() {
	var emails = window.delegated_admin_email;
	var email = "";
	for ( var i = 0; i < emails.length; i++) {
		email = emails[i] + "," + email;
	}
	var url = app.host + '/superadmin/1/promote_delegated_admin';
	$.ajax(url, {
		type : 'POST',
		data : ({
			email : email,
		}),

		success : function(data) {
			$('#MessageModalPromote').modal('hide');
			if (data['status'].toLowerCase() == 'ok') {
				$('#promote_admin_message').html(data['msg']);
			} else {
				var obj = data['data'];
				$('#promote_admin_message').html(data['msg']);
			}
			show_delegated_admin($("#admin_list").val());
		}
	});
}

handleClick = function(cb) {
	if (cb != null) {
		if ($(cb).is(':checked')) {
			window.delegated_admin_email.push(cb.value);
		} else {
			window.delegated_admin_email.splice(window.delegated_admin_email
				.indexOf(cb.value), 1)
		}
	}
}

set_permission = function(form_name, roles) {
	var form = document.getElementById(form_name);
	var chk = form.getElementsByClassName('permission');
	var len = chk.length;
	permission(chk[0], 'Reports', roles);
	permission(chk[1], 'Settings', roles);
	permission(chk[2], 'Developer', roles);
	permission(chk[3], 'Feedback', roles);
}

check_permission = function(role_name, roles) {
	for ( var i = 0; i < roles.length; i++) {
		var role = roles[i];
		if (role.indexOf(role_name) != -1) {
			return true;
		}
	}
	return false;
}

permission = function(chk, role_name, roles) {
	for ( var i = 0; i < roles.length; i++) {
		var role = roles[i];
		if (role.indexOf(role_name) != -1) {
			chk.checked = true;
		}
	}
}

toggleChecked = function(elem) {
	var chk = document.getElementsByClassName('mycheckbox');
	var len = chk.length;
	for ( var i = 0; i < len; i++) {
		if (chk[i].type === 'checkbox') {
			chk[i].checked = elem.checked;
			if (chk[i].value.indexOf('@') > 0) {
				if (chk[i].checked == true) {
					window.delegated_admin_email.push(chk[i].value);
				} else {
					window.delegated_admin_email.splice(
						window.delegated_admin_email.indexOf(chk[i].value),
						1);
				}
			}
		}
	}
}

toggleDelegatedAdmin = function(elemAdmin, dev) {
	var chk = dev.getElementsByClassName('mycheckbox');
	var len = chk.length;
	for ( var i = 0; i < len; i++) {
		if (chk[i].type === 'checkbox') {
			chk[i].checked = elemAdmin.checked;
			if (chk[i].value.indexOf('@') > 0) {
				if (chk[i].checked == true) {
					window.delegated_admin_email.push(chk[i].value);
				} else {
					window.delegated_admin_email.splice(
						window.delegated_admin_email.indexOf(chk[i].value),
						1);
				}
			}
		}
	}
}

list_delegate_admin = function() {
	window.delegated_admin_email = [];
	var url = app.host + '/delegated_adminapi/1/list_delegated_admin';
	$
		.ajax(
		url,
		{
			type : 'GET',
			async : false,
			success : function(data) {
				var $sel = $("#accordion2");
				$sel.empty();
				console.log(data);
				if (data && data['status'].toLowerCase() == 'ok') {
					var data = data['data'];
					$my_div = $("<div id='admin_message' class='alert alert-error'></div>");
					$my_div.appendTo($sel);
					if (data && data.length > 0) {
						$my_table = $("<table border='0'>");
						$my_table.appendTo($sel);
						for ( var i = 0; i < data.length; i++) {
							var basic_info = "delegate_admin_basic"
								+ i;
							var password_info = "delegate_admin_password"
								+ i;

							var permission_info = "delegate_admin_permission"
								+ i;

							var expand = "expand" + i;
							var message = "message" + i;
							var link = "#" + expand;
							var sno = i + 1;
							if (i == 0) {
								$header = $("<tr>"
								+ " <td><strong>First Name</strong></td>"
								+ " <td><strong>Last Name</strong></td>"
								+ " <td><strong>Company Name</strong></td>"
								+ " <td><strong>Contact Number</strong></td>"
								+ " <td><strong>Email</strong></td>"
								+ " <td height='25'><input type='checkbox' id='selectall' onclick='javascript:toggleChecked(selectall)'/></td>"
								+ " </tr>");
								$header.appendTo($sel);
							}
							$html = $("<tr>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ data[i]['firstName']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ data[i]['lastName']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ data[i]['companyName']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ data[i]['contactNumber']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ data[i]['email']
							+ "</td>"
							+ "<td><input type='checkbox' id='cb' name='cb' class='mycheckbox'  value='"
							+ data[i]['email']
							+ "' onclick='javascript:handleClick(this)'/>"
							+ "</td>"
							+ " </tr>"
							+ " <tr >"
							+ "<td colspan='6' class='hiddenRow'><div class='accordian-body collapse' id='"
							+ expand
							+ "'>"
							+ "<fieldset>"
							+ "<legend style='font-size:13px;'>User information</legend>"
							+ "<form id='"
							+ basic_info
							+ "' action='#' method='post'>"
							+ "<div>"
							+ "<input type='hidden' id='id' value='"
							+ data[i]['id']
							+ "'/>"
							+ "	<font color='red'></font>"
							+ "</div>"
							+ "<table border='0'>"
							+ "	<tr>"
							+ "		<td align='right'>First Name </td>"
							+ "		<td><input id='firstName' type='text' required='true' value='"
							+ data[i]['firstName']
							+ "'/></td>"
							+ "		<td align='right'>Last Name </td>"
							+ "		<td><input id='lastName' type='text' value='"
							+ data[i]['lastName']
							+ "'/></td>"
							+ "	</tr>"
							+ "	<tr>"
							+ "		<td align='right'>Company Name </td>"
							+ "		<td><input id='companyName' type='text' value='"
							+ data[i]['companyName']
							+ "'/></td>"
							+ "		<td align='right'>Contact Number </td>"
							+ "		<td><input id='contactNumber' type='text' value='"
							+ data[i]['contactNumber']
							+ "'/></td>"
							+ "	</tr>"
							+ "	<tr>"
							+ "		<td align='right'>Email </td>"
							+ "		<td><input id='email' type='text' value='"
							+ data[i]['email']
							+ "'/></td>"
							+ "	</tr>"
							+ "	<input id='password' type='hidden' value=''/>"
							+ "	<input id='account_admin_list' type='hidden' value=''/>"
							+ "</table>"
							+ "<div align='right'>"
							+ "<a  href='javascript:save_delegate_admin("
							+ basic_info
							+ ",false)' class='button submit'>Update</a>"
							+ "</div>"
							+ "</form>"
							+ "</fieldset>"
							+ "<fieldset>"
							+ "<legend style='font-size:13px;'>Update Password</legend>"
							+ "<form id='"
							+ password_info
							+ "' action='#' method='post'>"
							+ "<input id='email' type='hidden' value='"
							+ data[i]['email']
							+ "'/>"
							+ "Password <input id='password' type='password' value=''/>"
							+ "<div align='right'>"
							+ "<a  href='javascript:update_password("
							+ password_info
							+ ",false)' class='button submit'>Update</a>"
							+ "</div>"
							+ "</form>"
							+ "</fieldset>"

							+ "<fieldset>"
							+ "<legend style='font-size:13px;'>Update Permission</legend>"
							+ "<form id='"
							+ permission_info
							+ "' action='#' method='post'>"
							+ "<input id='firstName' type='hidden' value='"
							+ data[i]['firstName']
							+ "'/>"
							+ "<input type='hidden' id='email' value='"
							+ data[i]['email']
							+ "'/>"
							+ "<table border='0'>"
							+ "<tr>"
							+ "<td height='75' width='100%' align='right'>"
							+ "<div width='100%'>"
							+ "Role(s) <input type='checkbox' class='permission' value='reports'/>"
							+ "Reports &nbsp;&nbsp;&nbsp;&nbsp; <input"
							+ "	type='checkbox' class='permission' value='settings' />"
							+ "	Settings &nbsp; &nbsp;&nbsp;&nbsp;<input"
							+ "		type='checkbox' class='permission' value='developer' />"
							+ "	Developer &nbsp; &nbsp;&nbsp;&nbsp;<input"
							+ "		type='checkbox' class='permission' value='feedback' />"
							+ "	Feedback"
							+ "</div>"
							+ "</td "
							+ "</tr>"
							+ "</table>"
							+ "<div align='right'>"
							+ "<a  href='javascript:update_permission("
							+ permission_info
							+ ",false)' class='button submit'>Update</a>"
							+ "</div>"
							+ "</form>"
							+ "</fieldset>"
							+ "</div> </td></tr>");
							$html.appendTo($sel);
							set_permission(permission_info,
								data[i]['roles']);
						}
						$my_div_end = $("</table>");
						$my_div_end.appendTo($sel);
					} else {
						show_empty_msg();
					}
				} else {
					show_empty_msg();
				}
			}
		});
	$('#DelegatedAdminModal').modal('show');
}
// list of delegated admin based on accounts

get_delegated_admin = function() {
	window.delegated_admin_email = [];
	// var url = app.host + '/superadmin/1/delegated_admin';
	var url = 'superadmin/1/delegated_admin';
	$
		.ajax(
		url,
		{
			type : 'GET',
			async : false,
			success : function(data) {
				var $sel = $("#accordion2");
				$sel.empty();
				console.log(data);
				if (data && data['status'].toLowerCase() == 'ok') {
					var data = data['data'];

					var inner_html = "<div align='center' style='padding-top: 30px;'>"
						+ "<font color='red' id='promote_msg_font'>"
						+ "<div id='promote_admin_message'></div>"
						+ "</font>" + "</div>";
					if (data && data.length > 0) {

						inner_html = inner_html
						+ "<table border='0' width='100%'>";
						for ( var i = 0; i < data.length; i++) {
							var praimaryAdmin = data[i]['primaryAdmin'];
							var delegatedAdmins = data[i]['delegateAdmins'];
							var primary_admin = "primary_admin_"
								+ i;
							var delegated_admin = "delegate_admin_"
								+ i;
							var expand = "expand" + i;
							var link = "#" + expand;
							var sno = i + 1;
							var chkDelegate = 'delegateAdmin_' + i;
							if (i == 0) {
								inner_html = inner_html
								+ "<tr bgcolor='#A4DDF7'>"
								+ " <td><strong>First Name</strong></td>"
								+ " <td><strong>Last Name</strong></td>"
								+ " <td><strong>Company Name</strong></td>"
								+ " <td><strong>Contact Number</strong></td>"
								+ " <td><strong>Email</strong></td>"
								+ " <td><strong>Address</strong></td>"
									// + " <td><strong>API
									// Key</strong></td>"
								+ " <td height='25'><input type='checkbox' id='checkAll' onclick='javascript:toggleChecked(checkAll)'/></td>"
								+ " </tr>";
							}
							if (i % 2 == 0) {
								inner_html = inner_html + "<tr>"
							} else {
								inner_html = inner_html
								+ "<tr bgcolor=' #A4DDF7'>"
							}
							inner_html = inner_html
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ praimaryAdmin['firstName']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ praimaryAdmin['lastName']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ praimaryAdmin['companyName']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ praimaryAdmin['contactNumber']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ praimaryAdmin['email']
							+ "</td>"
							+ " <td data-toggle='collapse' data-target='#"
							+ expand
							+ "' class='accordion-toggle'>"
							+ praimaryAdmin['address']
							+ "</td>"
								// + " <td
								// data-toggle='collapse'
								// data-target='#"
								// + expand
								// + "'
								// class='accordion-toggle'>"
								// + praimaryAdmin['apiKey']
								// + "</td>"
								// + "<td><input type='checkbox'
								// class='mycheckbox' value='"
								// + praimaryAdmin['email']
								// + "'
								// onclick='javascript:toggleChecked(this)'/>"
								// + "</td>"
							+ " </tr>"
							+ " <tr >"
							+ "<td colspan='8' class='hiddenRow' align='center'><div class='accordian-body collapse' id='"
							+ expand
							+ "' width='75%' align='center'>";

							inner_html = inner_html
							+ "<table border='0' width='75%' style='border-width: 1px; border-color:#000000;border-style: solid;'>"
							for ( var j = 0; j < delegatedAdmins.length; j++) {
								var delegatedAdmin = delegatedAdmins[j];
								var sno = j + 1;

								if (j == 0) {
									inner_html = inner_html
									+ "<tr bgcolor='#B8B8B8'>"
									+ " <td width='20%'><strong>First Name</strong></td>"
									+ " <td width='20%'><strong>Last Name</strong></td>"
									+ " <td width='25%'><strong>Contact Number</strong></td>"
									+ " <td width='25%'><strong>Email</strong></td>"
									+ " <td width='10%'><input type='checkbox' class='mycheckbox' id='"
									+ chkDelegate
									+ "' onclick='javascript:toggleDelegatedAdmin("
									+ chkDelegate + ","
									+ expand + ")'/></td>"
									+ " </tr>"
								}
								inner_html = inner_html
								+ "<tr>"
								+ " <td  width='20%'>"
								+ delegatedAdmin['firstName']
								+ "</td>"
								+ " <td  width='20%'>"
								+ delegatedAdmin['lastName']
								+ "</td>"
								+ " <td width='25%'>"
								+ delegatedAdmin['contactNumber']
								+ "</td>"
								+ " <td width='25%'>"
								+ delegatedAdmin['email']
								+ "</td>"
								+ "<td width='10%'><input type='checkbox' class='mycheckbox'  value='"
								+ delegatedAdmin['email']
								+ "' onclick='javascript:handleClick(this)'/>"
								+ "</td>" + " </tr>";
							}
							inner_html = inner_html
							+ "</table></div></td></tr>";
						}
						inner_html = inner_html + "</table>";
						$my_html = $(inner_html);
						console.log();
						$my_html.appendTo($sel);
					} else {
						show_empty_msg();
					}
				} else {
					show_empty_msg();
				}

			}
		});
};

/* Formating function for row details */
fnFormatDetails = function(nTr) {
	var data = oTable.fnGetData(nTr);
	if (data != null) {
		var basic_info = 'basic_info';
		var permission_info = 'permission_info';
		var password_info = 'password_info';
		var roles = data[8];
		sOut = "<fieldset>"
		+ "<legend style='font-size:13px;'>User information</legend>"
		+ "<form id='"
		+ basic_info
		+ "' action='#' method='post'>"
		+ "<div>"
		+ "<input type='hidden' id='id' value='"
		+ data[7]
		+ "'/>"
		+ "</div>"
		+ "<table border='0'>"
		+ "	<tr>"
		+ "		<td align='right'>First Name </td>"
		+ "		<td><input id='firstName' type='text' required='true' value='"
		+ data[1]
		+ "'/></td>"
		+ "		<td align='right'>Last Name </td>"
		+ "		<td><input id='lastName' type='text' value='"
		+ data[2]
		+ "'/></td>"
		+ "	</tr>"
		+ "	<tr>"
		+ "		<td align='right'>Company Name </td>"
		+ "		<td><input id='companyName' type='text' value='"
		+ data[3]
		+ "'/></td>"
		+ "		<td align='right'>Contact Number </td>"
		+ "		<td><input id='contactNumber' type='text' value='"
		+ data[4]
		+ "'/></td>"
		+ "	</tr>"
		+ "	<tr>"
		+ "		<td align='right'>Email </td>"
		+ "		<td><input id='email' type='text' value='"
		+ data[5]
		+ "' readonly /></td>"
		+ "	</tr>"
		+ "	<input id='password' type='hidden' value=''/>"
		+ "</table>"
		+ "<div align='right'>"
		+ "<a  href='javascript:update_basic_info("
		+ basic_info
		+ ",true)' class='button submit'>Update</a>"
		+ "</div>"
		+ "</form>"
		+ "</fieldset>"
		+ "<fieldset>"
		+ "<legend style='font-size:13px;'>Update Password</legend>"
		+ "<form id='"
		+ password_info
		+ "' action='#' method='post'>"
		+ "<input id='email' type='hidden' value='"
		+ data[5]
		+ "'/>"
		+ "Password <input id='password' type='password' value=''/>"
		+ "<div align='right'>"
		+ "<a  href='javascript:update_password("
		+ password_info
		+ ",true)' class='button submit'>Update</a>"
		+ "</div>"
		+ "</form>"
		+ "</fieldset>"

		+ "<fieldset>"
		+ "<legend style='font-size:13px;'>Update Permission</legend>"
		+ "<form id='"
		+ permission_info
		+ "' action='#' method='post'>"
		+ "<input id='firstName' type='hidden' value='"
		+ data[1]
		+ "'/>"
		+ "<input type='hidden' id='email' value='"
		+ data[5]
		+ "'/>"
		+ "<table border='0'>"
		+ "<tr>"
		+ "<td height='75' width='100'>Role(s)</td>"
		+ "<td height='75' colspan='3'>"
		+ "<div>"
		+ "<input type='checkbox' class='permission' value='reports'";
		if (check_permission('Reports', roles)) {
			sOut = sOut + " checked"
		}
		sOut = sOut + "/>Reports &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <input"
		+ "	type='checkbox' class='permission' value='settings'";
		if (check_permission('Settings', roles)) {

			sOut = sOut + " checked"
		}
		sOut = sOut + "/>Settings &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<input"
		+ "		type='checkbox' class='permission' value='developer' ";
		if (check_permission('Developer', roles)) {
			sOut = sOut + " checked"
		}
		sOut = sOut
		+ "/> Developer &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;<input"
		+ "		type='checkbox' class='permission' value='feedback' ";
		if (check_permission('Feedback', roles)) {
			sOut = sOut + " checked"
		}
		sOut = sOut + "/> Feedback </div>" + "</td> " + "</tr>" + "</table>"
		+ "<div align='right'>"
		+ "<a  href='javascript:update_permission(" + permission_info
		+ ",true)' class='button submit'>Update</a>" + "</div>"
		+ "</form>" + "</fieldset>";

		return sOut;
	} else {
		return "";
	}
};

show_delegated_admin = function(email) {
	window.delegated_admin_email = [];
	// var url = app.host + '/superadmin/1/delegated_admin';
	var url = 'superadmin/1/delegated_admin';
	$
		.ajax(
		url,
		{
			type : 'GET',
			data : ({
				email : email,
			}),
			async : false,
			success : function(data) {
				var data = data['data'];
				var aaData = [];
				var imgPath = bucketurl
					+ '/assets/images/details_open.png';
				for ( var i = 0; i < data.length; i++) {
					var rowData = data[i];
					var a = [];
					a.push(imgPath);
					a.push(rowData['firstName']);
					a.push(rowData['lastName']);
					a.push(rowData['companyName']);
					a.push(rowData['contactNumber']);
					a.push(rowData['email']);
					a.push(rowData['address']);
					a.push(rowData['id']);
					a.push(rowData['roles']);
					a.push(rowData['email']);
					aaData.push(a);
				}
				console.log(aaData);

				oTable = $('#example')
					.dataTable(
					{
						"aaData" : aaData,
						"bProcessing" : true,
						"bDestroy" : true,
						"bAutoWidth" : true,
						"aoColumnDefs" : [ {
							"bSortable" : false,
							"aTargets" : [ 0, -1 ]
						} ],
						"aaSorting" : [ [ 1, 'asc' ] ],
						"aoColumns" : [
							{
								"fnRender" : function(
									oObj) {
									return "<img src='"
										+ oObj.aData[0]
										+ "' width='20px' height='20px' />"
								},
								"aTargets" : [ 0 ]
							},
							null,
							null,
							null,
							null,
							null,
							null,
							{
								"bVisible" : false
							},
							{
								"bVisible" : false
							},
							{
								"fnRender" : function(
									oObj) {
									return "<input type='checkbox' class='case' name='case' value='"
										+ oObj.aData[9]
										+ "'/>";
								},
								"aTargets" : [ 0 ]
							} ],
					});

				/*
				 * oTable = $('#example').dataTable({ "aaData" :
				 * aaData, "bProcessing" : true, "bDestroy" : true,
				 * "bAutoWidth" : true, "aoColumns" : [ null, null,
				 * null, null, null, null, { "bVisible" : false }, {
				 * "bVisible" : false } ], });
				 */

				$('#example').off( 'click' ).on('click', "tbody tr td img", function () {
						var nTr = $(this).parents('tr')[0];
						if (oTable.fnIsOpen(nTr)) {
							// This row is already open
							// - close it
							this.src = bucketurl
							+ '/assets/images/details_open.png';
							oTable.fnClose(nTr);
						} else {
							// Open this row
							this.src = bucketurl
							+ '/assets/images/details_close.png';
							oTable
								.fnOpen(
								nTr,
								fnFormatDetails(nTr),
								'details');
							// show_delegated_admin(email);
						}
					});

			}
		});
};

show_add_form = function() {
	get_account_admin('account_admin_list');
	$('#AddDelegatedAdminModal').modal('show');
};

show_delete_form = function() {
	window.delegated_admin_email = [];
	$('.case').each(function() {
		var sThisVal = (this.checked ? $(this).val() : "");
		if (sThisVal != "") {
			window.delegated_admin_email.push(sThisVal);
		}
	});
	if (window.delegated_admin_email.length < 1) {
		$('#msg_font').prop('color', 'red');
		$('#super_admin_msg').html('Please select delegated admin to delete.');
	} else {
		$('#msg_font').prop('color', 'blue');
		$('#super_admin_msg').html('');
		show_delete_dialog(true);
	}
};

show_rate_limit_delete_dialog = function(toBeDeleted) {

	if (toBeDeleted != null && toBeDeleted.length > 0) {
		var msg = "Are you sure you want to delete selected ?";
		$('#DelegatedAdminModal').modal('hide');
		$('#delete_msg').html(msg);
		$('#DelegatedAdminModal').modal('hide');
		$('#MessageModal').modal('show');
	}
}

show_promote_form = function() {
	window.delegated_admin_email = [];
	$('.case').each(function() {
		var sThisVal = (this.checked ? $(this).val() : "");
		if (sThisVal != "") {
			window.delegated_admin_email.push(sThisVal);
		}
	});
	if (window.delegated_admin_email.length < 1) {
		$('#msg_font').prop('color', 'red');
		$('#super_admin_msg').html('Please select delegated admin to delete.');
	} else {
		$('#msg_font').prop('color', 'blue');
		$('#super_admin_msg').html('');
		show_promote_dialog(true);
	}
};

show_promote_dialog = function(isSuperAdmin) {
	var emails = window.delegated_admin_email;
	if (emails != null && emails.length > 0) {
		var msg = "";
		if (emails.length < 2) {
			msg = "Are you sure you want to promte " + emails + "?";
		} else {
			msg = "Are you sure to promote the folowing users?";
			var email = "";
			for ( var i = 0; i < emails.length; i++) {
				msg = msg + "<br />" + emails[i];
			}
		}
		if (!isSuperAdmin) {
			$('#DelegatedAdminModalPromote').modal('hide');
		}
		$('#promote_msg').html(msg);
		$('#DelegatedAdminModalPromote').modal('hide');
		$('#MessageModalPromote').modal('show');
	} else {
		if (!isSuperAdmin) {
			$('#admin_message').html('Please select delegate admin to promote');
		}
	}
};

// RATE LIMIT

show_add_rate_limit_form = function() {
//    get_rate_limit_users('account_admin_list');
    $('#AddDelegatedAdminModal').modal('show');
};

var toBeDeleted ;
show_delete_rate_limit_form = function(arg) {
	toBeDeleted = []
	toBeDeleted.push(arg);
    $('#msg_font').prop('color', 'blue');
	$('#super_admin_msg').html('');
	show_rate_limit_delete_dialog(toBeDeleted);
};

get_account_admin_dumy = function(list_name) {
	var url = 'superadmin/1/account_admin';
	$.ajax(url, {
		type : 'GET',
		success : function(data) {
			if (data != null) {
				data = data['data'];
				var primaryAdmin = {};
				for ( var i = 0; i < data.length; i++) {
					primaryAdmin[data[i]['email']] = data[i]['company'] + ' - ' + data[i]['email'];
				}
				var select = $('#' + list_name);
				if (select.prop) {
					var options = select.prop('options');
				} else {
					var options = select.attr('options');
				}
				$('option', select).remove();

				$.each(primaryAdmin, function(val, text) {
					options[options.length] = new Option(text, val);
				});
				$("#admin_list").change(function() {
					var email = $(this).val();
					show_delegated_admin_dumy(email);
				}).change();
				$("#delegatedAdminAll").click(function() {
					if ($(this).is(':checked')) {
						$('.case').prop('checked', true);
					} else {
						$('.case').prop('checked', false);
					}
				});
				select.trigger("chosen:updated");
			}
		}
	});
};

var apiMap = {};
show_rate_limit_list = function() {
	var dateFormat = 'yyyy-MM-dd HH:mm:ss';
	window.delegated_admin_email = [];
	var url = 'superadmin/rate-limiter/v1/limits';
	$
		.ajax(
			url,
			{
				type : 'GET',
				async : false,
				beforeSend: function() {
                    $('#Loading').show();
                },
                complete: function() {
                    $('#Loading').hide();
                },
				success : function(data) {
					var aaData = [];
					var imgPath = bucketurl
						+ '/assets/images/details_open.png';
					for ( var i = 0; i < data.length; i++) {
						var rowData = data[i];
						var a = [];
						var envType = rowData['environmentTypeId'];
						var rateLimit = rowData['rateLimit'];
						var dayLimit = rowData['dayLimit'];
						var api = rowData['api'];
						var accountId = rowData['accountId']
						if(rateLimit===null)
						    rateLimit=10;
						if(dayLimit===null)
						    dayLimit="N/A";
						a.push(imgPath);
						a.push(accountId);
						a.push(rowData['name']);
						a.push(apiMap[api]);
						a.push(envType);
						a.push(rateLimit);
						a.push(dayLimit);
						a.push(jQuery.format.date(rowData['createdAt'], dateFormat));
						a.push(jQuery.format.date(rowData['updatedAt'], dateFormat));
						var uniqueKey = accountId +'|'+envType+'|'+api ;
						a.push(uniqueKey);
						aaData.push(a);
					}
					//console.log(aaData);

					oTable = $('#example')
						.dataTable(
							{
								"aaData" : aaData,
								"bProcessing" : true,
								"bDestroy" : true,
								"bAutoWidth" : true,
								"aoColumnDefs" : [ {
									"bSortable" : false,
									"aTargets" : [ 0, -1 ]
								} ],
								"aaSorting" : [ [ 1, 'asc' ] ],
								"aoColumns" : [
									{
										"fnRender" : function(
											oObj) {
											return "<img src='"
												+ oObj.aData[0]
												+ "' width='20px' height='20px' />"
										},
										"aTargets" : [ 0 ]
									},
									null,
									null,
									null,
									null,
									null,
									null,
									null,
									null,
									{
										"bVisible" : false
									}],
							});
					/*
					 * oTable = $('#example').dataTable({ "aaData" :
					 * aaData, "bProcessing" : true, "bDestroy" : true,
					 * "bAutoWidth" : true, "aoColumns" : [ null, null,
					 * null, null, null, null, { "bVisible" : false }, {
					 * "bVisible" : false } ], });
					 */
                    $('#example').off( 'click' ).on('click', "tbody tr td img", function () {
								var nTr = $(this).parents('tr')[0];
								if (oTable.fnIsOpen(nTr)) {
									// This row is already open
									// - close it
									this.src = bucketurl
										+ '/assets/images/details_open.png';
									oTable.fnClose(nTr);
								} else {
									// Open this row
									this.src = bucketurl
										+ '/assets/images/details_close.png';
									oTable
										.fnOpen(
											nTr,
											fnFormatRateLimitDetails(nTr),
											'details');
									// show_delegated_admin(email);
								}
							});

				}
			});
};

delete_rate_limit = function() {

    var arrToBeDeleted = [];

	for ( var i = 0; i < toBeDeleted.length; i++) {

		var arr = toBeDeleted[i].split("|");
		var d = arr[4];
		if(d!=='N/A'){
            var data = {
                accountId:arr[0],
                environmentTypeId : arr[1],
                api: arr[2],
                rateLimit : null,
                dayLimit : d
            }

            arrToBeDeleted.push(data);
		}
	}

    if(arrToBeDeleted.length>0){
        var url = 'superadmin/rate-limiter/v1/limits/bulk';
        $.ajax(url, {
            type : 'DELETE',
            contentType: "application/json",
            data : JSON.stringify(arrToBeDeleted),
            beforeSend: function() {
                $('#Loading').show();
            },
            complete: function() {
                $('#Loading').hide();
            },
            error: function(e) {
                $("#MessageModal").modal("show");
                $('#message').html('Unable to delete Rate limit config!');
            },
            success : function(data) {
                $('#MessageModal').modal('hide');
                show_rate_limit_list();
            }
        });
        DD_RUM.track("Delete Rate Limit(s)", { rateLimitsToBeDeleted: JSON.stringify(arrToBeDeleted) });
    }
    else
        $('#MessageModal').modal('hide');

};

add_rate_limit = function(myform) {
	var url = 'superadmin/rate-limiter/v1/limits/upsert';
	var form = null;
	if (myform == null) {
		form = document.getElementById('delegated_admin1');
	} else {
		form = myform;
	}

    var rateLimit = $("#rateLimit").val();
    if(!rateLimit){
         $("#AddDelegatedAdminModal").modal("show");
         $('#message').html('RateLimit is Mandatory!');
         return;
    }

    var accountId = $('#account_admin_list option:selected').val();
    var envTypeId = $('#env_list option:selected').val();
    var api = $('#publicApi  option:selected').val();
    var rateLimit = $("#rateLimit").val();
    var dayLimit = $("#dayLimit").val();
	$
		.ajax(
			url,
			{
				type : 'PUT',
				contentType: "application/json",
				data : JSON.stringify({
					accountId : accountId,
					environmentTypeId : envTypeId,
                    api: api,
					rateLimit : rateLimit,
					dayLimit : dayLimit
				}),
				beforeSend: function() {
                    $('#Loading').show();
                },
                complete: function() {
                    $('#Loading').hide();
                },
                error: function(e) {
                    $("#AddDelegatedAdminModal").modal("show");
                    $('#message').html('Unable to add Rate limit config!');
                },
				success : function(data) {
					show_rate_limit_list();
					$("#AddDelegatedAdminModal").modal("hide");
					$('#message').html('');
					$('#DelegatedAdminModal').modal('show');
					$("#rateLimit").val('');
					$("#dayLimit").val('');
				}
			});
			DD_RUM.track("Add Rate Limit", { accountId: accountId, environmentTypeId: envTypeId, api: api, rateLimit: rateLimit, dayLimit: dayLimit });
};

update_rate_limit = function(form) {
	var url = 'superadmin/rate-limiter/v1/limits/upsert';

	var arr = form.unique_id.value.split("|");

	var r = form.rateLimit.value, d = form.dayLimit.value;
    if(r===10)
        r='';
    if(d==='N/A')
    	d='';

	$.ajax(url, {
	    type : 'PUT',
        contentType: "application/json",
        data : JSON.stringify({
            accountId : arr[0] ,
        	environmentTypeId : arr[1] ,
            api: arr[2],
        	rateLimit : r,
        	dayLimit : d
        }),
        beforeSend: function() {
            $('#Loading').show();
        },
        complete: function() {
            $('#Loading').hide();
        },
        error: function(e) {
            $("#AddDelegatedAdminModal").modal("show");
            $('#message').html('Unable to Update Rate limit config!');
        },
		success : function(data) {
			show_rate_limit_list();
			$('#admin_message').html("Successfully Updated");
			$('#DelegatedAdminModal').modal('show');
		}
	});
	DD_RUM.track("Update Rate Limit", { accountId: arr[0], environmentTypeId: arr[1], api: arr[2], rateLimit: r, dayLimit: d });

};

loadRateLimitApis = function(){

    $.ajax({
        url: 'superadmin/rate-limiter/v1/apis',
        type: 'GET',
        async: false,
        error: function(e) {
            alert('Unable to fetch apis. Please refresh the Page!')
        },
        success: function(data) {
            var select = document.getElementById("publicApi");
                for (index in data) {
                    var key = data[index].name;
                    var option = new Option(key, data[index].publicId)
                    apiMap[data[index].publicId] = key
                    select.add(option);
                }
            }
        });
}

/* Formating function for row details */
fnFormatRateLimitDetails = function(nTr) {
	var data = oTable.fnGetData(nTr);
	if (data != null) {
		var basic_info = 'basic_info';
        var permission_info = 'permission_info';
        var password_info = 'password_info';
        var roles = data[8];
        var deleteArg = data[9]+'|'+data[5]+'|'+data[6];
        var showDelete = '';
        if(data[6]!=='N/A')
            showDelete = "   <a href='javascript:show_delete_rate_limit_form(\""+ deleteArg + "\")'>Delete Day Limit</a>&nbsp;&nbsp;"

		sOut = "<fieldset>"
			+ "<legend style='font-size:13px;'>Rate Limit Information</legend>"
			+ "<form id='"
			+ basic_info
			+ "' action='#' method='post'>"
			+ "<table border='0'>"
			+ "	<tr>"
			+ "		<td align='right'>Rate Limit</td>"
			+ "		<td><input id='rateLimit' type='text' required='true' value='"
			+ data[5]
			+ "'/></td>"
			+ "		<td align='right'>Day Limit</td>"
			+ "		<td><input id='dayLimit' type='text' value='"
			+ data[6]
			+ "'/>"
			+ showDelete
            +"</td>"
			+ "		<td><input id='unique_id' type='hidden' value='"
			+ data[9]
			+ "'/></td>"
			+ "</tr>"
			+ "</table>"
			+ "<div align='right'>"
            + "<a  href='javascript:update_rate_limit("
            + basic_info
            + ")' class='button submit'>Update</a>"
            + "</div>"
			+ "</form>"
			+ "</fieldset>"
			+ "<fieldset>";
		return sOut;
	} else {
		return "";
	}
};

get_industry_list = function(list_name)
{
    var url = 'api/1/industry/list';
    $.ajax(url,
        {
            type: 'GET',
            success: function(data)
            {
                if (data != null)
                {
                    data = data['data'];

                    var industriesList = {};
                    for (var i = 0; i < data.length; i++)
                    {
                        industriesList[data[i]['sector']] = data[i]['description'] ;
                    }
                    var select = $('#' + list_name);
                    if (select.prop)
                    {
                        var options = select.prop('options');
                    }
                    else
                    {
                        var options = select.attr('options');
                    }
                    $('option', select).remove();

                    $.each(industriesList, function(val, text)
                    {
                        options[options.length] = new Option(text, val);
                    });

                    $("#IndustryAll").click(function()
                    {
                        if ($(this).is(':checked'))
                        {
                            $('.case').prop('checked', true);
                        }
                        else
                        {
                            $('.case').prop('checked', false);
                        }
                    });
                }
            }
        });
};

get_parent_accounts = function(list_name)
{
    var url = 'adminapi/1/parent_accounts';
    $.ajax(url,
        {
            type: 'GET',
            success: function(data)
            {
                if (data != null)
                {
                    data = data['data'];
                    var parentAccounts = {};

                    for (var type in data) {
                        parentAccounts[type] = { id: data[type].id,
												  text: data[type].id + "-" + data[type].name,
												  isInternal: data[type].internal
                        						};
                    }

                    var select = $('#' + list_name);
                    if (select.prop)
                    {
                        var options = select.prop('options');
                    }
                    else
                    {
                        var options = select.attr('options');
                    }
                    $('option', select).remove();
                    options[0] = new Option("None", 0);
                    $.each(parentAccounts, function(val, obj)
                    {
                        options[options.length] = new Option(obj.text, JSON.stringify({id: obj.id, isInternal: obj.isInternal}));
                    });

                }
            }
        });
};

get_parent_account_no_primary_list = function(list_name)
{
    var url = 'get_parent_accounts_no_prim_user';
    $.ajax(url,
        {
            type: 'GET',
            success: function(data)
            {
                if (data != null)
                {
                    data = data['data'];

                    var accountsList = {};
                    for (var i = 0; i < data.length; i++)
                    {
                        accountsList[data[i]['id']] = data[i]['id'] + ' - ' + data[i]['name'];
                    }
                    var select = $('#' + list_name);
                    var options;
                    if (select.prop)
                    {
                         options = select.prop('options');
                    }
                    else
                    {
                         options = select.attr('options');
                    }
                    $('option', select).remove();

					for (var i = 0; i < data.length; i++) {
						options[options.length] = (new Option(accountsList[data[i]['id']], data[i]['id']));
					}
                }
            }
        });
};

create_primary_parent_account = function(form_name, selectionName) {
    var url = app.host + '/adminapi/1/register_primary_user_parent_account';

    var form = document.getElementById(form_name);

    if (form.firstName.value.trim() == "" ||
		form.lastName.value.trim() == "" ||
		form.email.value.trim() == "" ||
		form.contactNumber.value.trim() == "") {
        $('#message').addClass("alert-error").html('All fields are mandatory.');
    } else {
        $.ajax(
                url,
                {
                    type: 'POST',
                    data: ({
                        accountId: $('#' + selectionName).val(),
                        firstName: form.firstName.value,
                        lastName: form.lastName.value,
                        contactNumber: form.contactNumber.value,
                        email: form.email.value
                    }),
                    success: function (data) {
                        if (data['status'].toLowerCase() == 'ok') {
                            $('#message').addClass("alert-success").html('New primary user saved with email : ' +
								form.email.value + ' created successfully.');
                            form.reset();
                            get_parent_account_no_primary_list(selectionName);
                        } else {
                            $('#message').addClass("alert-error").html(data['msg']);
                        }
                    }
                });
    }
};

reset_invite_primary_form = function(form_name) {
    var form = document.getElementById(form_name);
	form.reset();
};

show_pgp_signature_public_keys = function(reload) {
    var dateFormat = 'yyyy-MM-dd HH:mm:ss';
    window.pgp_signature_public_keys = [];
    var url = 'superadmin/1/get_accounts_with_signature_public_key';
    $.ajax(
        url,
        {
            type : 'GET',
            async : false,
            success : function(data) {
                var data = data['data'];
                var aaData = [];
                for ( var i = 0; i < data.length; i++) {
                    var rowData = data[i];
                    var a = [];
                    a.push(rowData['accountId']);
                    a.push(rowData['accountId']);
                    a.push(rowData['accountName']);
                    a.push(jQuery.format.date(rowData['createdAt'], dateFormat));
                    aaData.push(a);
				}
				if (reload) {
					oTable.fnClearTable();
					oTable.fnAddData(aaData, true);
				} else {
					oTable = $('#example').dataTable({
						"aaData": aaData,
						"bProcessing": true,
						"bDestroy": true,
						"bAutoWidth": true,
						"aoColumnDefs": [{
							"bSortable": false,
							"aTargets": [0, -1]
						}],
						"select": {
							"style": 'single'
						},
						"aaSorting": [[0, 'asc']],
						"aoColumns": [
							{
								"fnRender": function (
									oObj) {
									return "<input type='radio' class='case' name='case' value='"
										+ oObj.aData[0]
										+ "'/>";
								},
								"aTargets": [0]
							},
							null,
							null,
							null],
					});
				}
            }
        });
};

show_add_pgp_signature_public_key_form = function() {
    $('#AddPgpSignaturePublicKeyModal').modal('show');
    get_accounts('parent_account_list', '/adminapi/1/get_active_pgp_account_list', 'accountId', 'accountName', []);
};

function readFileInBase64(file) {
    return new Promise(function (resolve, reject) {
        var reader = new FileReader();
        reader.readAsDataURL(file);

        reader.onload = function () {
            return resolve(reader.result.split(',')[1]);
        };

        reader.onerror = function (error) {
            return reject(error);
        };
    });
}

add_pgp_signature_public_key = function() {
    var url = 'superadmin/1/create_pgp_signature_public_key';
    var form = document.getElementById('add-pgp-signature-public-key');
    if($.trim($(form.file).val()).length == 0){
        $('#message').html("Please choose a PGP signature public key file");
        return;
    }
    readFileInBase64($(form.file).get(0).files[0]).then(function(fileContent){
        var formData = new FormData();

        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(fileContent.length);
        var ia = new Uint8Array(ab);
        for (var i = 0; i < fileContent.length; i++) {
          ia[i] = fileContent.charCodeAt(i);
        }

        // write the ArrayBuffer to a blob, and you're done
        var bb = new Blob([ab]);
        formData.append('file', bb, 'file');
        formData.append('accountid', form.accountid.value);
        $.ajax(
            {
                url: url,
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function(data) {
                    if (data['status'].toLowerCase() == 'ok') {
                        show_pgp_signature_public_keys(true);
                        $("#AddPgpSignaturePublicKeyModal").modal("hide");
                        $('#super_admin_msg').html('Pgp Signature public key is added.');
                        form.reset();
                    } else {
                        $("#AddPgpSignaturePublicKeyModal").modal("show");
                        var obj = data['data'];
                        $('#message').html(data['msg']);
                    }
                },
                error: function(data)
                {
                    $("#AddPgpSignaturePublicKeyModal").modal("show");
                    var obj = data['data'];
                    $('#message').html(data['msg']);
                }
            });
    });
};


import_watchlist_sources = function() {
    var url = 'watchlist_source/import';
    var form = document.getElementById('sourceListUploadForm');
    if($.trim($(form.file).val()).length == 0){
        alert("Please choose a Source List file to import");
        return;
    }
    $('#message').attr("style", "display:block")
    $('#message').html("<b>Uploading .....Please wait. This might take a few seconds.</b>")
    var formData = new FormData();
    formData.append('file', $(form.file).get(0).files[0]);
    $.ajax(
        {
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                if (data['status'].toLowerCase() == 'ok') {
                    $('#message').html(data['msg'])
                    $('#message').attr("style", "display:block;color:green")
                    form.reset();
                    DD_RUM.track("Watchlist import Success");
                } else {
                    var errorString = ""
                    var errorRecords = data['data']
                    for(i = 0;i<errorRecords.length;i++){
                    errorString += "<br/>" + JSON.stringify(errorRecords[i])
                    }
                    $('#message').html(data['msg'] + errorString)
                    $('#message').attr("style", "display:block;color:red")

                    form.reset();
                     DD_RUM.track("Watchlist import Failed");
                }
            },
            error: function(data)
            {
            alert("Error");
                alert("Unable to import Watch list sources.")
                 DD_RUM.track("Unable to do Watchlist import");
            }
        });
};


show_delete_pgp_signature_public_key_form = function() {
    window.pgp_signature_public_keys = [];
    $('.case').each(function() {
        var sThisVal = (this.checked ? $(this).val() : "");
        if (sThisVal != "") {
            window.pgp_signature_public_keys.push(sThisVal);
        }
    });
    if (window.pgp_signature_public_keys.length < 1) {
        $('#msg_font').prop('color', 'red');
        $('#super_admin_msg').html('Please select an account to remove the Pgp signature public key.');
    } else {
        $('#msg_font').prop('color', 'blue');
        $('#super_admin_msg').html('');
        show_confirm_pgp_signature_public_key_delete_dialog(true);
    }
};

show_confirm_pgp_signature_public_key_delete_dialog = function(isSuperAdmin) {
    var accountIds = window.pgp_signature_public_keys;
    if (accountIds != null && accountIds.length > 0) {
        var msg = "Are you sure you want to delete Pgp signature Public key for " + accountIds + "?";
        if (!isSuperAdmin) {
            $('#DeletePgpSignaturePublicKey').modal('hide');
        }
        $('#delete_msg').html(msg);
        $('#DeletePgpSignaturePublicKey').modal('hide');
        $('#MessageModal').modal('show');
    } else {
        if (!isSuperAdmin) {
            $('#admin_message').html('Please select an account to delete the Pgp Public key');
        }
    }
};

delete_pgp_signature_public_key = function() {
    var accountId = window.pgp_signature_public_keys;
    var url = 'superadmin/1/delete_pgp_signature_public_key';
    $.ajax(url, {
        type : 'POST',
        data : ({
            accountid : accountId[0]
        }),
        success : function(data) {
            $('#MessageModal').modal('hide');
            show_pgp_signature_public_keys(true);
            if (data['status'].toLowerCase() == 'ok') {
                $('#admin_message').html(
                    "Pgp signature public key removed successfully.");
            } else {
                var obj = data['data'];
                $('#admin_message').html(html(obj[0]['errorMsg']));
            }
        }
    });
};

get_accounts = function(list_name, endpoint, idProp, nameProp, customProps) {
    if(!idProp) idProp = 'id';
    if(!nameProp) nameProp = 'name';
    var url = app.host + endpoint;
    $.ajax(url, {
        type : 'GET',
        beforeSend: function() {
          $('#Loading').show();
        },
        complete: function() {
          $('#Loading').hide();
        },
        success : function(data) {
            if (data != null) {
                data = data['data'];
                var select = $('#' + list_name);
                if (select.prop) {
                    var options = select.prop('options');
                } else {
                    var options = select.attr('options');
                }
                $('option', select).remove();
                $.each(data, function(index, val) {
                    var id = val[idProp];
                    var name = id + ' - ' + val[nameProp];
                    var option = new Option(name, id);
                    if(customProps) {
                        for(var i in customProps) {
                            var customProp = customProps[i];
                            option.setAttribute('data-' + customProp, val[customProp]);
                        }
                    }
                    options[options.length] = option;
                });
                select.trigger("change");
                select.trigger("chosen:updated");
            }
        }
    });
};

show_pgp_keys = function() {
    var dateFormat = 'yyyy-MM-dd HH:mm:ss';
    window.pgp_keys = [];
    var url = 'adminapi/1/get_active_pgp_account_list';
    $.ajax(
        url,
        {
            type : 'GET',
            async : false,
            success : function(data) {
                var data = data['data'];
                var aaData = [];
                for ( var i = 0; i < data.length; i++) {
                    var rowData = data[i];
                    var a = [];
                    a.push(rowData['accountId']);
                    a.push(rowData['accountId']);
                    a.push(rowData['accountName']);
                    a.push(jQuery.format.date(rowData['createdAt'], dateFormat));
                    aaData.push(a);
                }
                oTable = $('#example').dataTable({
                    "aaData" : aaData,
                    "bProcessing" : true,
                    "bDestroy" : true,
                    "bAutoWidth" : true,
                    "aoColumnDefs" : [ {
                        "bSortable" : false,
                        "aTargets" : [ 0, -1 ]
                    } ],
                    "select": {
                        "style": 'single'
                    },
                    "aaSorting" : [ [ 0, 'asc' ] ],
                    "aoColumns" : [
                        {
                            "fnRender" : function(
                                oObj) {
                                return "<input type='radio' class='case' name='case' value='"
                                    + oObj.aData[0]
                                    + "' data-accountname='"
									+ oObj.aData[2]
									+ "'/>";
                            },
                            "aTargets" : [ 0 ]
                        },
                        null,
                        null,
                        null],
                });
            }
        });
};

show_add_pgp_key_form = function() {
    $('#AddPgpKeyModal').modal('show');
    $("#add-pgp-key")[0].reset();
    $('#message').html("");
    get_accounts('parent_account_list', '/adminapi/1/get_active_account_wo_pgp_list', 'accountId', 'accountname', ['primaryemail']);
};

add_pgp_key = function() {
    var url = 'superadmin/1/generate_pgp_keys';
    var form = document.getElementById('add-pgp-key');
	if(form.accountid.value.trim() == "") {
		alert("Select an account");
		return;
	}
    var formData = new FormData();
    formData.append('accountid', form.accountid.value);

    if(form.shouldExpire.checked){
        var defaultExpireTime = 2 * 365 * 24 * 60 * 60;
        formData.append('expiryTimeInSeconds', defaultExpireTime)
    }

    $.ajax(
        {
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                $('#AddMessageModal').modal('hide');
                if (data['status'].toLowerCase() == 'ok') {
                    show_pgp_keys();
                    $('#super_admin_msg').html('Pgp keys are generated.');
                    form.reset();
                    DD_RUM.track("Add PGP Key success", formData );
                } else {
                    $("#AddPgpKeyModal").modal("show");
                    var obj = data['data'];
                    $('#message').html(data['msg']);
                    DD_RUM.track("Add PGP Key failed", formData );
                }
            },
            error: function(data)
            {
                $("#AddPgpKeyModal").modal("show");
                var obj = data['data'];
                $('#message').html(data['msg']);
                DD_RUM.track("Add PGP Key failed", formData );
            }
        });
};

show_confirm_pgp_keys_add_dialog = function() {
    var msg = "Are you sure you want to generate pgp keys for this account?";
    $('#add_msg').html(msg);
    $('#AddPgpKeyModal').modal('hide');
    $('#AddMessageModal').modal('show');
};

show_delete_pgp_key_form = function() {
    window.pgp_keys = [];
    $('.case').each(function() {
        var sThisVal = (this.checked ? $(this).val() : "");
        if (sThisVal != "") {
            window.pgp_keys.push(sThisVal);
        }
    });
    if (window.pgp_keys.length < 1) {
        $('#msg_font').prop('color', 'red');
        $('#super_admin_msg').html('Please select an account to remove the Pgp key.');
    } else {
        $('#msg_font').prop('color', 'blue');
        $('#super_admin_msg').html('');
        show_confirm_pgp_key_delete_dialog(true);
    }
};

show_confirm_pgp_key_delete_dialog = function(isSuperAdmin) {
    var accountIds = window.pgp_keys;
    if (accountIds != null && accountIds.length > 0) {
        var msg = "Are you sure you want to deactivate Pgp key for " + accountIds + "?";
        if (!isSuperAdmin) {
            $('#DeletePgpKey').modal('hide');
        }
        $('#delete_msg').html(msg);
        $('#DeletePgpKey').modal('hide');
        $('#MessageModal').modal('show');
    } else {
        if (!isSuperAdmin) {
            $('#admin_message').html('Please select an account to deactivate the Pgp key');
        }
    }
};

delete_pgp_key = function() {
    var accountId = window.pgp_keys;
    var url = 'superadmin/1/deactivate_pgp_keys';
    $.ajax(url, {
        type : 'POST',
        data : ({
            accountid : accountId[0]
        }),
        success : function(data) {
            $('#MessageModal').modal('hide');
            show_pgp_keys();
            if (data['status'].toLowerCase() == 'ok') {
                $('#super_admin_msg').html(
                    "Pgp keys deactivated successfully.");
                DD_RUM.track("PGP Key Deactivation success", { accountId: accountId[0] });
            } else {
                var obj = data['data'];
                $('#super_admin_msg').html(html(obj[0]['errorMsg']));
                DD_RUM.track("PGP Key Deactivation failed", { accountId: accountId[0] });
            }
        }
    });
};

show_download_pgp_key_form = function() {
    window.pgp_keys = [];
    $('.case').each(function() {
		if(this.checked){
            var accountId = $(this).val();
            var accountName = this.getAttribute("data-accountname");
            if (accountId != "" && accountName != "") {
                window.pgp_keys.push(accountId);
                window.pgp_keys.push(accountName);
            }
		}

    });
    if (window.pgp_keys.length < 1) {
        $('#msg_font').prop('color', 'red');
        $('#super_admin_msg').html('Please select an account to download its Pgp key.');
    } else {
        $('#msg_font').prop('color', 'blue');
        $('#super_admin_msg').html('');
        var accountInfo = window.pgp_keys;
        download_pgp_public_key(accountInfo[0], accountInfo[1]);
    }
};

download_pgp_public_key = function(accountId, accountName) {
    var url = app.host + '/superadmin/1/get_pgp_public_key';
    $.ajax(url, {
        type : 'GET',
        data : {
            accountId : accountId
        },
        success : function(data) {
            if (data['status'].toLowerCase() == 'ok') {
                var textToSaveAsBlob = new Blob([data['data']], {type: "text/plain"});
                var textToSaveAsURL = window.URL.createObjectURL(textToSaveAsBlob);
                var fileNameToSaveAs = accountName.replace(/\s+/g, '-').toLowerCase()+"-public-key.asc";

                var downloadLink = document.createElement("a");
                downloadLink.download = fileNameToSaveAs;
                downloadLink.innerHTML = "Download File";
                downloadLink.href = textToSaveAsURL;
                downloadLink.onclick = destroyClickedElement;
                downloadLink.style.display = "none";
                document.body.appendChild(downloadLink);
                downloadLink.click();
                $('#super_admin_msg').html(
                    "Pgp signature public key downloaded successfully.");
                DD_RUM.track("PGP Key download success", { accountId: accountId });
            }else{
                var obj = data['data'];
                $('#super_admin_msg').html(html(obj['msg']));
                DD_RUM.track("PGP Key download failed", { accountId: accountId });
			}
        }
    });
};

function destroyClickedElement(event)
{
    document.body.removeChild(event.target);
}
show_accounts_with_idp_metadata = function() {
    var dateFormat = 'yyyy-MM-dd HH:mm:ss';
    window.idp_metadata = [];
    var url = 'superadmin/1/get_accounts_with_idp_metadata';
    $.ajax(
        url,
        {
            type : 'GET',
            async : false,
            success : function(data) {
                var data = data['data'];
                var aaData = [];
                for ( var i = 0; i < data.length; i++) {
                    var rowData = data[i];
                    var a = [];
                    a.push(rowData['accountId']);
                    a.push(rowData['accountId']);
                    a.push(rowData['accountName']);
                    a.push(jQuery.format.date(rowData['createdAt'], dateFormat));
                    a.push(rowData['accountId']);
                    aaData.push(a);
                }
                console.log(aaData);
                oTable = $('#example').dataTable({
                    "aaData" : aaData,
                    "bProcessing" : true,
                    "bDestroy" : true,
                    "bAutoWidth" : true,
                    "aoColumnDefs" : [ {
                        "bSortable" : false,
                        "aTargets" : [ 0, -1 ]
                    } ],
                    "select": {
                        "style": 'single'
                    },
                    "aaSorting" : [ [ 0, 'asc' ] ],
                    "aoColumns" : [
                        {
                            "fnRender" : function(
                                oObj) {
                                return "<input type='radio' class='case' name='case' value='"
                                    + oObj.aData[0]
                                    + "'/>";
                            },
                            "aTargets" : [ 0 ]
                        },
                        null,
                        null,
                        null,
                        {
                            "fnRender" : function(oObj) {
                                return '<a href="' + contexturl + '/superadmin/1/download_sp_metadata?accountId=' + oObj.aData[4] + '" class="btn btn-primary">Download SP Metadata</a>'
                            },
                            "aTargets" : [ 4 ]
                        }]
                });
            }
        });
};

show_add_idp_metadata_form = function() {
    $('#AddIdpMetadataModal').modal('show');
    $("#add-idp-metadata")[0].reset();
    $('#message').html("");
    get_accounts('parent_account_list', '/superadmin/1/get_accounts_with_roles?roles=SAML_2_0&onlyParent=false', 'id', 'name', []);
};

add_idp_metadata = function() {
    $('#AddMessageModal').modal('hide');
    var url = 'superadmin/1/insert_idp_metadata';
    var form = document.getElementById('add-idp-metadata');
    if($.trim($(form.file).val()).length == 0){
        $('#message').html("Please choose a Idp metadata file");
        return;
    }
    var formData = new FormData();
    formData.append('file', $(form.file).get(0).files[0]);
    formData.append('accountid', form.accountid.value);
    $.ajax(
        {
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                if (data['status'].toLowerCase() == 'ok') {
                    show_accounts_with_idp_metadata();
                    $("#AddIdpMetadataModal").modal("hide");
                    $('#super_admin_msg').html('Idp metadata is added.');
                    form.reset();
                    DD_RUM.track("Add IDP Metadata success", { accountId: form.accountid.value });
                } else {
                    $("#AddIdpMetadataModal").modal("show");
                    var obj = data['data'];
                    $('#message').html(data['msg']);
                    DD_RUM.track("Add IDP Metadata failed", { accountId: form.accountid.value });
                }
            },
            error: function(data)
            {
                $("#AddIdpMetadataModal").modal("show");
                var obj = data['data'];
                $('#message').html(data['msg']);
                DD_RUM.track("Add IDP Metadata failed", { accountId: form.accountid.value });
            }
        });
};


show_delete_idp_metadata_form = function() {
    window.idp_metadata = [];
    $('.case').each(function() {
        var sThisVal = (this.checked ? $(this).val() : "");
        if (sThisVal != "") {
            window.idp_metadata.push(sThisVal);
        }
    });
    if (window.idp_metadata.length < 1) {
        $('#msg_font').prop('color', 'red');
        $('#super_admin_msg').html('Please select an account to remove the Idp metadata.');
    } else {
        $('#msg_font').prop('color', 'blue');
        $('#super_admin_msg').html('');
        show_confirm_idp_metadata_delete_dialog(true);
    }
};

show_confirm_idp_metadata_delete_dialog = function(isSuperAdmin) {
    var accountIds = window.idp_metadata;
    if (accountIds != null && accountIds.length > 0) {
        var msg = "Are you sure you want to remove idp metadata for " + accountIds + "?";
        if (!isSuperAdmin) {
            $('#DeleteIdpMetadata').modal('hide');
        }
        $('#delete_msg').html(msg);
        $('#DeleteIdpMetadata').modal('hide');
        $('#MessageModal').modal('show');
    } else {
        if (!isSuperAdmin) {
            $('#admin_message').html('Please select an account to remove the Idp metadata');
        }
    }
};

show_confirm_idp_metadata_add_dialog = function() {
    var form = document.getElementById('add-idp-metadata');
    if($.trim($(form.file).val()).length == 0){
        $('#message').html("Please choose a Idp metadata file");
        return;
    }
	var msg = "Are you sure you want to add idp metadata for this account?";
	$('#add_msg').html(msg);
    $('#AddIdpMetadataModal').modal('hide');
	$('#AddMessageModal').modal('show');
};

delete_idp_metadata = function() {
    var accountId = window.idp_metadata;
    var url = 'superadmin/1/delete_idp_metadata';
    $.ajax(url, {
        type : 'POST',
        data : ({
            accountid : accountId[0]
        }),
        success : function(data) {
            $('#MessageModal').modal('hide');
            show_accounts_with_idp_metadata();
            var messageElement = $('#admin_message') || $('#super_admin_msg');
            if (data['status'].toLowerCase() == 'ok') {
                messageElement.html("Idp metadata removed successfully.");
                DD_RUM.track("Delete IDP Metadata success", { accountId: accountId[0] });
            } else {
                var obj = data['data'];
                messageElement.html(html(obj[0]['errorMsg']));
                DD_RUM.track("Delete IDP Metadata failed", { accountId: accountId[0] });
            }
        }
    });
};
