<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
    <style>
        /* The popup form - hidden by default */
        .form-popup-eventrules {
          display: none;
          position: fixed;
          border: 3px solid #f1f1f1;
          left: 400px;
          top: 150px;
          width:500px;
          height:200px;
          z-index: 9;
        }

        /* Add styles to the form container */
        .form-container {
          padding: 10px;
          background-color: white;
        }

        /* Full-width input fields */
        .form-container input[type=text], .form-container select {
          width: 100%;
          padding: 5px;
          margin: 5px 0 22px 0;
          border: none;
          background: #f1f1f1;
        }

        /* When the inputs get focus, do something */
        .form-container input[type=text]:focus, .form-container select:focus {
          background-color: #ddd;
          outline: none;
        }

        /* Set a style for the submit/login button */
        .form-container .btn {
          background-color: #555;
          color: white;
          padding: 16px 20px;
          border: none;
          cursor: pointer;
          width: 50%;
          margin-bottom:10px;
          opacity: 0.8;
        }

        /* Add a red background color to the cancel button */
        .form-container .cancel {
          background-color: #555;
        }

        /* Add some hover effects to buttons */
        .form-container .btn:hover, .open-button:hover {
          opacity: 1;
        }

        /* Add style for info text */
        .form-container .info {
            color: gray;
        }

        /* Set bottom space for input wrapped inside input-field class */
        .form-container .input-field input {
            margin-bottom: 4px;
        }

        .form-container .input-field {
            margin-bottom: 22px;
        }
    </style>
    <script type="text/javascript" src="resources/scripts/eventrules.js"></script>
</h:head>
<h:body>

    <button id="create_eventrule_popup" class="button-pad" onclick="addEditEventRules()" > Create New Event Rule </button>

    <table border="2" style="margin-top:20px">
        <thead>
        <tr>
            <th>Name</th> <th>Label</th> <th>Schedule Type</th> <th>Expression</th> <th></th>
        </tr>
        </thead>
        <tbody id="eventRulesTable"></tbody>
    </table>

    <div class="form-popup-eventrules" id="create_eventrule_container">
        <form action="" class="form-container">
            <label><b>Event Rule Name</b></label>
            <div class="input-field">
                <input type="text" id="eventruleName"/>
                <label class="info"><b>Format: </b>socure-batch-&lt;name&gt;-&lt;environment&gt;</label>
            </div>

            <label><b>Label</b></label>
            <input type="text" id="eventruleLabel"/>

            <label><b>Schedule Type</b></label>
            <select id="eventruleScheduleType">
                <option value="cron" selected="selected">Cron</option>
                <option value="rate">Rate</option>
            </select>

            <label><b>Expression</b></label>
            <input type="text" id="eventruleExpression"/>

            <button type="button" class="btn" id="create_eventrule_submit_btn" value="" onclick="createEventRule()" >Submit</button>
            <button type="button" class="btn cancel" onclick="closeCreateEventRuleForm()">Close</button>
        </form>
    </div>

</h:body>
</html>
