<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Rate Limit Manager</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<link rel="shortcut icon" type="image/ico"
		href="http://www.datatables.net/favicon.ico" />
	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		media="print" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/chosen.min.css"/>
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
	<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
	<style>
		.chosen-search input{
			margin-left:0 !important;
		}
	</style>

	<script>
		var bucketurl = "#{bucket.resourceURL}";
		var contexturl = "#{request.contextPath}";
		var marketingurl = "#{bucket.marketingSite}";
		loggedinUsername = "#{loggedinUsername}";
		appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
	<script language="javascript">
	    window.onload = function() {
	       if(DD_RUM) {
	         DD_RUM.startView({name: "/rate_limit"});
             DD_RUM.setUser({ "id": loggedinUsername});
	       }
	    }
	</script>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px;">
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<!--<div align="left" style="padding-left: 215px;">
		Account Admin <select name="menu" id="admin_list" />
	</div>-->
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>
	<div id="MessageModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Rate Limit</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="delete_msg"></label>
				</div>
				<div class="modal-footer">
					<button type="button" onclick="delete_rate_limit()" class="btn btn-primary">Delete</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div class="accordion" id="accordion2">
		<div class='container' style='margin-top: 10px'>
			<table cellpadding='0' cellspacing='0' border='0'
				class='table table-striped table-bordered' id='example'>
				<thead>
					<tr>
						<th>Action</th>
						<th>Account ID</th>
						<th>Account Name</th>
						<th>Public API</th>
						<th>Environment</th>
						<th>Rate Limit</th>
						<th>Day Limit</th>
						<th>Created</th>
						<th>Updated</th>
						<th>Unique ID</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<div style="padding-left: 215px;">
		<a href="javascript:show_add_rate_limit_form();">Add New</a>&nbsp;&nbsp;
	</div>
	<h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Note: Delete is applicable only for DayLimit.</h6>
	<section id="Loading" class="overlay show">
		<p>Loading ...</p>
		<div class="loader">
			<img src="#{bucket.resourceURL}/assets/images/socure-loader.svg" /> <span
				class="dot"></span>
		</div>
	</section>

	<div id="AddDelegatedAdminModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Rate Limiter</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="message" class="alert alert-error"></div>
					<form>
						<div class="form-group row">
							<label for="account_admin_list" class="col-sm-4 col-form-label">Account Admin</label>
							<div class="col-sm-8">
								<select name="menu" id="account_admin_list" class="chosen"/>
							</div>
						</div>

						<div class="form-group row">
							<label for="publicApi" class="col-sm-4 col-form-label">Public API </label>
							<div class="col-sm-8">
								<select id="publicApi" />
							</div>
						</div>

						<div class="form-group row">
							<label for="rateLimit" class="col-sm-4 col-form-label">Rate Limit(TPS)</label>
							<div class="col-sm-8">
								<input id="rateLimit" type="text" />
							</div>
						</div>

						<div class="form-group row">
							<label for="dayLimit" class="col-sm-4 col-form-label">Day Limit</label>
							<div class="col-sm-8">
								<input id="dayLimit" type="text" />
							</div>
						</div>

						<div class="form-group row">
							<label for="env_list" class="col-sm-4 col-form-label">Environments</label>
							<div class="col-sm-8">
								<select id="env_list">
									<option selected="true" value="1">Production</option>
									<option value="2">Certification</option>
									<option value="3">Sandbox</option>
								</select>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" onclick="add_rate_limit(null,true);" class="btn btn-primary">Save</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>


	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/chosen.jquery.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/lib.js"></script>

	<script src="resources/scripts/socure-dashboard.js"></script>
	<script src="#{bucket.resourceURL}/scripts/jquery-dateFormat.min.js"></script>

	<script type="text/javascript"
		src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script>
		$(document).ready(function() {
			loadRateLimitApis();
			get_rate_limit_users('account_admin_list');
			show_rate_limit_list();

			$(".chosen").chosen({
				width:"55%",
				search_contains : true
			});
		});
	</script>
</h:body>
</html>