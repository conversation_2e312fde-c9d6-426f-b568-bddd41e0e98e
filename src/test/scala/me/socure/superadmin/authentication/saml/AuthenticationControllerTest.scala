package me.socure.superadmin.authentication.saml

import me.socure.common.clock.FakeClock
import me.socure.common.saml.SamlClient
import me.socure.control.center.constants.ControlCenterPermission
import me.socure.service.constants.Countries
import me.socure.superadmin.authentication.SamlAttribute
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}
import org.springframework.security.authentication.AuthenticationManager

class AuthenticationControllerTest extends FreeSpec with Matchers with MockitoSugar with BeforeAndAfter {
  private val samlClient = mock[SamlClient]
  private val authenticationManager = mock[AuthenticationManager]

  private val controller = new AuthenticationController
  controller.samlClient = samlClient
  controller.authenticationManager = authenticationManager

  private val clock = new FakeClock(DateTime.now(DateTimeZone.UTC).getMillis)
  val samlAttribute: SamlAttribute = SamlAttribute(Countries.INDIA, ControlCenterPermission.NO_ACCESS, accountProvisioningAccess = true, piiRetentionAccessPermission = "Full Access")

  before {
    Mockito.reset(
      samlClient,
      authenticationManager
    )
  }
/*
  "should authenticate properly" in {
    val request = mock[HttpServletRequest]
    val response = mock[HttpServletResponse]
    val username = Random.alphaNumeric(50)
    withAuthRes(username) { authResponse =>
      Mockito.when(saml2Authenticator.authenticate(request, response)).thenReturn(authResponse)
      val unAuthToken = SimpleUnauthenticatedToken(
        username = username,
        expirationTime = Some(clock.now()),
        samlAttribute
      )
      val superAdminAuth = createSuperAdminAuth(username)
      Mockito.when(authenticationManager.authenticate(unAuthToken)).thenReturn(superAdminAuth)
      controller.authenticate(request, response) shouldBe "redirect:/active_users"
      Mockito.verify(saml2Authenticator).authenticate(request, response)
      Mockito.verify(authenticationManager).authenticate(unAuthToken)
    }
  }

  "authentication should fail when there is an error" in {
    val request = mock[HttpServletRequest]
    val response = mock[HttpServletResponse]
    Mockito.when(saml2Authenticator.authenticate(request, response)).thenThrow(new RuntimeException("authentication failed"))
    controller.authenticate(request, response) shouldBe "unauthenticated"
    Mockito.verify(saml2Authenticator).authenticate(request, response)
  }

  "should check session when authenticated" in {
    withAuthCtx(authenticated = true) {
      val res = controller.checkSession()
      res.getStatusCode shouldBe HttpStatus.OK
      res.getBody.getStatus shouldBe IConstants.API_OUTPUT_STATUS_OK
    }
  }

  "should check session when not authenticated" in {
    withAuthCtx(authenticated = false) {
      val res = controller.checkSession()
      res.getStatusCode shouldBe HttpStatus.UNAUTHORIZED
      res.getBody.getStatus shouldBe IConstants.API_OUTPUT_STATUS_ERROR
    }
  }

  private def withAuthCtx[T](authenticated: Boolean)(action: => T): T = {
    val existingAuthentication = SecurityContextHolder.getContext.getAuthentication
    val authentication = if (authenticated) {
      new UsernamePasswordAuthenticationToken(Random.alphaNumeric(50), Random.alphaNumeric(50), AuthorityUtils.NO_AUTHORITIES) //authenticated
    } else {
      new UsernamePasswordAuthenticationToken(Random.alphaNumeric(50), Random.alphaNumeric(50)) //unauthenticated
    }
    SecurityContextHolder.getContext.setAuthentication(authentication)
    try {
      action
    } finally {
      SecurityContextHolder.getContext.setAuthentication(existingAuthentication)
    }
  }

  private def withAuthRes[T](username: String)(action: AuthenticationResponse => T): T = {
    withMockSamlCreds(username) { samlCredential =>
      val authResponse = AuthenticationResponse(
        samlCredential = samlCredential,
        expiration = Some(clock.now())
      )
      action(authResponse)
    }
  }

  private def withMockSamlCreds[T](username: String)(action: SAMLCredential => T): T = {
    val samlCredential = mock[SAMLCredential]
    val nameId = mock[NameID]
    Mockito.when(nameId.getValue).thenReturn(username)
    Mockito.when(samlCredential.getNameID).thenReturn(nameId)
    try {
      action(samlCredential)
    } finally {
      Mockito.verify(nameId).getValue
      Mockito.verify(samlCredential).getNameID
    }
  }

  private def createSuperAdminAuth(username: String): Authentication = {
    val userDetails = new UserDetailsWithoutCredentials(
      username = username,
      authorities = java.util.Arrays.asList(
        new SimpleGrantedAuthority(BusinessUserRoles.SUPER_ADMIN.name)
      )
    )
    SimpleAuthenticatedToken(
      username = username,
      userDetails = userDetails,
      expirationTime = Some(clock.now()),
      samlAttribute
    )
  }

 */
}
