$(document).ajaxStart(function() {
    $("#Loading").show();
});

$(document).ajaxStop(function() {
    $("#Loading").hide();
});

$(document).ready(function() {
    getJobDefinitions();
//    getRules();
});

var jobdefIdToDefinition;
var arnToResponse;

function getJobDefinitions() {

    $('#jobdefinitionsTable').children().remove();
    var html = ""
    $.ajax({
        url: contexturl + "/superadmin/job_definitions/details",
        type: 'GET',
        async: false,
        error: function(e) {
            alert('Unable to fetch job definitions. Please refresh the Page!');
        },
        success: function(data) {
            jobdefIdToDefinition = data.reduce((map, obj) => (map[obj.id] = obj, map), {});
            $.each(data, function(i, item) {
                html += "<tr><td>" + item.type + "</td><td>" + item.jobDefinitionName + "</td><td>" + item.jobDefinitionImage + "</td><td>" + item.revision + "</td><td>" + item.description + "</td><td>"
                + item.serviceAccount + "</td><td>" + item.eventRuleId + "</td>" +
                "<td><input type=\"button\" value=\"Edit\" class=\"pad\" onClick=\"addEditJobDefinitions(\'" + item.id + "\')\" /></td></tr>";
            });
            $('#jobdefinitionsTable').append(html);
        }
    });
}

//function getRules() {
//
//    $.ajax({
//        url: contexturl + "/superadmin/event_rules/list",
//        type: 'GET',
//        async: false,
//        error: function(e) {
//            alert('Unable to fetch event rules. Please refresh the Page!');
//        },
//        success: function(data) {
//            var eventRules = data.data;
//            var select = document.getElementById("jobDefinitionEventRule");
//            select.add(new Option("Choose Schedule Type",0));
//            for (index in eventRules) {
//                select.add(new Option(eventRules[index].name, eventRules[index].id));
//            }
//        }
//    });
//}

function addEditJobDefinitions(id) {
    var jobdefinition = jobdefIdToDefinition[id];
    if (!jobdefinition) {
        $('#jobdefinitionName').val("");
        $('#jobdefinitionType').val("")
        $('#jobdefinitionDescription').val("");
        $('#jobDefinitionEventRule').val("");
        $('#jobdefinitionServiceAccount').val("")
        $('#jobdefinitionIamRole').val("")
        $('#jobdefinitionImage').val("")
        $('#jobdefinitionMSCVVersion').val("")
        $('#jobdefinitionSpecifier').val("")
        $('#jobdefinitionParameterTemplate').val("")
        $('#jobdefinitionServiceAccount').prop("readonly",false)
        $('#jobdefinitionIamRole').prop("readonly",false);
    } else {
      $('#jobdefinitionServiceAccount').prop("readonly",true)
      $('#jobdefinitionIamRole').prop("readonly",true);
        var actualDefinitionName = jobdefinition.jobDefinitionArn.substring(jobdefinition.jobDefinitionArn.indexOf("/")+1, jobdefinition.jobDefinitionArn.lastIndexOf(":"))

        $.ajax({
            url: contexturl + "/superadmin/job_definitions/describe?jobDefinitionName=" + actualDefinitionName + "&maxResults=" + 50,
            type: 'GET',
            async: false,
            error: function(e) {
                alert('Unable to fetch job definitions. Please refresh the Page!');
            },
            success: function(data) {

                $('#jobdefinitionName').val(jobdefinition.jobDefinitionName);
                $('#jobdefinitionType').val(jobdefinition.type);
                $('#jobdefinitionDescription').val(jobdefinition.description);
                $('#jobdefinitionServiceAccount').val(jobdefinition.serviceAccount)
                $('#jobdefinitionImage').val(jobdefinition.jobDefinitionImage)
                $('#jobdefinitionMSCVVersion').val(jobdefinition.revision)
                $('#jobdefinitionSpecifier').val(jobdefinition.jobDefinitionArn)
                $('#jobdefinitionParameterTemplate').val(jobdefinition.parameterTemplate)
                $('#jobdefinitionIamRole').val(jobdefinition.iamRole);
                $('#jobDefinitionEventRule').val(jobdefinition.eventRuleId);

                arnToResponse = data.data.reduce((map, obj) => (map[obj.jobDefinitionArn] = obj, map), {});


                $('#create_jobdefinition_submit_btn').val(jobdefinition.id);
            }
        });
    }
    document.getElementById("create_jobdefinition_container").style.display = "block";
}

function closeCreateJobDefinitionForm() {
    document.getElementById("create_jobdefinition_container").style.display = "none";
    $('#create_eventrule_submit_btn').val('');
}

function createJobDefinition() {

    var val = $('#create_jobdefinition_submit_btn').val();
    var payload = getJobDefinitionPayload(val);
    console.log(payload)
    if(!payload){
        return
    }

   payload.cpu = "2",
   payload.memory = "512"

   makeAPICall(payload);
}

function makeAPICall(payload){

    $.ajax({
        url: contexturl + "/superadmin/job_definitions/update",
        data: JSON.stringify(payload),
        type: 'PUT',
        contentType: 'application/json',
        async: false,
        error: function(e) {
            if(payload.id){
                alert('Unable to update job definition. Please refresh the Page and try again!');
            } else {
                alert('Unable to insert job definition. Please refresh the Page and try again!');
            }
            closeCreateJobDefinitionForm();
            getJobDefinitions();
        },
        success: function(data) {

            if(data.data > 0){
                if(payload.id){
                    alert('Job definition Edited Successfully!');
                } else {
                    alert('Job definition Added Successfully!');
                }
            } else {
                if(payload.id){
                    alert('Unable to update job definition. Please refresh the Page and try again!');
                } else {
                    alert('Unable to insert job definition. Please refresh the Page and try again!');
                }
            }
            closeCreateJobDefinitionForm();
            getJobDefinitions();
        }
    });
}

function getJobDefinitionPayload(jobDefinitionId) {

    var jobdefinitionName = ($("#jobdefinitionName").val()).trim();
    if (jobdefinitionName == '') {
        alert('Job Definition Name is empty!');
        return;
    }

    var jobdefinitionType = ($("#jobdefinitionType").val()).trim();
    if (jobdefinitionType == '') {
        alert('Job Definition Type is empty!');
        return;
    }

    var jobDefinitionImage = ($("#jobdefinitionImage").val()).trim();
    if (jobdefinitionImage == '') {
            alert('Job Definition Image is empty!');
            return;
    }

     var jobdefinitionMSCVVersion = ($("#jobdefinitionMSCVVersion").val()).trim();
        if (jobdefinitionMSCVVersion == '') {
                alert('Job Definition mscv version is empty!');
                return;
        }

     var jobdefinitionSpecifier = ($("#jobdefinitionSpecifier").val()).trim();

    var jobdefinitionDescription = ($("#jobdefinitionDescription").val()).trim();

    var jobDefinitionEventRule = ($("#jobDefinitionEventRule").val()).trim();

    var jobDefinitionIamRole = ($("#jobdefinitionIamRole").val()).trim()

    var jobDefinitionServiceAccount = ($("#jobdefinitionServiceAccount").val()).trim()

    var jobdefinitionParameterTemplate = ($("#jobdefinitionParameterTemplate").val()).trim()


    var payload = {
        type: parseInt(jobdefinitionType),
        name: jobdefinitionName,
        description: jobdefinitionDescription,
        image:jobDefinitionImage,
        revision: jobdefinitionMSCVVersion,
        specifier: jobdefinitionSpecifier,
        parameterTemplate: jobdefinitionParameterTemplate,
        iamRole:jobDefinitionIamRole,
        eventRuleId: jobDefinitionEventRule,
        serviceAccount:jobDefinitionServiceAccount
    }

    if(jobDefinitionId != ''){
        payload.id = parseInt(jobDefinitionId)
    }

    return payload;
}