package me.socure.sftp

import me.socure.account.client.sftp.{AccountSftpUserClient, AccountSftpUserClientFactory}
import me.socure.common.hmac.factory.HMACEncrypterFactory
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}

import scala.concurrent.ExecutionContext

class AccountSftpUserClientFactory extends FactoryBean[AccountSftpUserClient]{
  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  override def getObject: AccountSftpUserClient = {

    val hmacEncrypter = HMACEncrypterFactory.get(
      secretKey = conf("hmac.secret.key"),
      keyStrength = conf("hmac.strength").toInt
    )

    val client = AccountSftpUserClientFactory.create(
      realm = conf("hmac.realm"),
      version = conf("hmac.version"),
      endpoint = conf("endpoint"),
      encrypter = hmacEncrypter
    )(executionContext)

    client
  }

  private def conf(prop: String): String = {
    envUtil.getProperty(s"account.service.$prop")
  }

  override def getObjectType: Class[AccountSftpUserClient] = classOf[AccountSftpUserClient]

  override def isSingleton: Boolean = true
}
