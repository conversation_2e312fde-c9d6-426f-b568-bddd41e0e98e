//Java script for fraud service model

const CORRELATION_SCORE = "correlation scores";
const FRAUD ="fraud";
const SYNTHETIC = "synthetic";
const RISK_SCORE = "risk score";
const POS_NEG_THRESHOLD = [CORRELATION_SCORE,SYNTHETIC,FRAUD]

window.onload = function() {
  if(DD_RUM) {
    DD_RUM.startView({ name: "/fraudmodel" });
    DD_RUM.setUser({ id: loggedinUsername });
  }
}

showActive = function () {
  if (document.getElementById('showActive').checked)
  {
      get_fraudmodel("active")
      DD_RUM.track("Show Active Fraud Models");
  } else {
      get_fraudmodel();
      DD_RUM.track("Show All Fraud Models");
  }
};

show_fraudmodel_add_form = function () {
    $('#AddFraudModal').off('shown.bs.modal').on('shown.bs.modal', function() {
        setTimeout(change_item);
    }).modal('show');
    this.get_identifiers(document.getElementById("fraudmodelformat"));
    DD_RUM.track("Show Add Fraudmodel form");
};

update_identifier = function (nTr) {
    this.get_identifiers(document.getElementById("fraudmodelformat"));
    var dataBase = oTable.fnGetData(nTr);
    var data;
    if (app.modelManagement) {
        data = get_original_data_for_public_id(dataBase[2]);
    } else {
        data = get_original_data_for_id(dataBase[10]);
    }
    document.getElementById("fraudidentity").value = data.identifier;
}

render_description = function (selectObject) {
    var url = "";
    if (app.modelManagement) {
        url = '/api/1/fraudmodel/models';
        $.ajax(url, {
            type: 'GET',
            success: function (data) {
                if (data !== null) {
                    data = data['data'];

                    if (data !== null) {
                        window.original_model_data = data;
                        data = get_original_data_for_public_id(selectObject.value);
                        document.getElementById("description").innerHTML = "Description: " + data.description;
                    }
                }
            }
        });
    }
}

get_fraudmodel = function (category = "") {
    var url = "";
    if (app.modelManagement) {
        url = '/api/1/fraudmodel/models';
        const updatedAt = document.createElement("th");
        updatedAt.setAttribute("id", "updatedAt");
        updatedAt.innerHTML = "Last Updated At";
        document.getElementById('headers').insertBefore(updatedAt, document.getElementById('headers').children[11]);
    } else {
        url = '/api/1/fraudmodel/list';
        const idValue = document.createElement("th");
        idValue.setAttribute("id", "id");
        idValue.innerHTML = "Id";
        document.getElementById('headers').insertBefore(idValue, document.getElementById('headers').children[11]);
    }
    var data = ((category !== "") ? ({category}) : ({}));
    $.ajax(url, {
        type: 'GET',
        data: data,
        success: function (data) {
            if (data !== null) {
                data = data['data'];

                show_FraudModel(data);

                $("#FraudModelAll").click(function () {
                    if ($(this).is(':checked')) {
                        $('.case').prop('checked', true);
                    } else {
                        $('.case').prop('checked', false);
                    }
                });
            }
        }
    });
    DD_RUM.track("List all fraud models");
};

get_original_data_for_id = function (id) {
    if(window.original_model_data) {
        for (var i in window.original_model_data) {
            var d = window.original_model_data[i];
            if(d.id === id) {
                return d;
            }
        }
    }
    return null;
};

get_original_data_for_public_id = function (publicId) {
    if(window.original_model_data) {
        for (var i in window.original_model_data) {
            var d = window.original_model_data[i];
            if(d.publicId === publicId) {
                return d;
            }
        }
    }
    return null;
};

show_FraudModel = function (data) {
    set_checked = function(isActive) {
        return isActive ? 'checked' : ''
    };

    window.delegated_admin_email = [];
    if (data !== null) {
        window.original_model_data = data;
        var aaData = [];
        var imgPath = bucketurl + '/assets/images/details_open.png';
        var infoImgPath = bucketurl + '/assets/images/info.png';
        for (var j = 0; j < data.length; j++) {
            var rowData = data[j];
            var a = [];
            a.push('<label class="switch">'
                      +'<input type="checkbox" id="deactivateToggle" '
                      + set_checked(rowData.isActive)
                      + '>'
                      +'<span class="slider round"></span>'
                    +'</label>'); //0
            a.push(imgPath); //1
            a.push(rowData['publicId']); //2
            a.push(rowData['name']); //3
            a.push(rowData['identifier']); //4
            a.push(rowData['commonName']); // 5
            a.push(rowData['scoreName']); // 6
            a.push(rowData['version']); // 7
            if (app.modelManagement) {
                a.push(rowData['featureLabel']); // 8
            } else {
                a.push(rowData['feature']); // 8
            }
            a.push(rowData['quantileMapping']); // 9
            a.push(rowData['id']); // 10
            a.push(rowData['id']); // 11
            if (app.modelManagement) {
                a.push(rowData['updatedAt']); // 12
            } else {
                a.push(""); // 12
            }
            a.push(rowData['params']); // 13
            a.push(rowData['config']); // 14
            a.push(rowData['description']); // 15
            a.push(infoImgPath); // 16

            aaData.push(a);
        }

        oTable = $('#industry_table')
            .dataTable(
                {
                    "aaData": aaData,
                    "bProcessing": true,
                    "bDestroy": true,
                    "bAutoWidth": false,
                    "iDisplayLength": 25,
                    "aoColumnDefs": [{
                        "bSortable": false,
                        "aTargets": [0, 1, -1]
                        },
                        {
                            "aTargets": [12,13],
                            "bVisible": false
                        }],
                    "aaSorting": [[2, 'asc']],
                    "aoColumns": [
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[0];
                            },
                            "aTargets": [0]
                        },
                        {
                            "fnRender": function (oObj) {
                                return "<img id='expandIcon' src='"
                                    + oObj.aData[1]
                                    + "' width='20px' height='20px' />"
                            },
                            "aTargets": [0]
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[2];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                var result = oObj.aData[3];
                                if (oObj.aData[15]) {
                                    result += '<a href="#" data-toggle="tooltip" title="'
                                              + oObj.aData[15]
                                              +'">'
                                              + "<img style='margin-left:5px;margin-bottom:2px' src='"
                                              + oObj.aData[16]
                                              + "' width='15px' height=15px' />"
                                              +'</a>';
                                }
                                return result;
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[4];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                if (oObj.aData[5] != null) {
                                    return oObj.aData[5];
                                } else {
                                    return "--";
                                }
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[6];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[7];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[8];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                if(oObj.aData[9] && oObj.aData[9].length > 0) {
                                    var lines = oObj.aData[9].split(/(?:\r\n|\r|\n)/g);
                                    var tmp = lines.join("</br>");

                                    var sOut = "<a href='javascript:viewQuantileMapping(\"" + tmp + "\")' class='button submit'>View</a>"
                                        + "</br>"
                                        +" <a href='data:text/plain;charset=UTF-8,"
                                        + oObj.aData[9]
                                        +"' download=\"quantileMapping.csv\">Download</a>";
                                    return sOut;
                                }
                                else {
                                    return "--";
                                }
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                if(app.modelManagement) {
                                    return "<input type='checkbox' class='case' name='case' value='"
                                        + oObj.aData[2] + "' modelname='" + oObj.aData[2] + "'/>";
                                } else {
                                    return "<input type='checkbox' class='case' name='case' value='"
                                        + oObj.aData[11] + "' modelname='" + oObj.aData[11] + "'/>";
                                }
                            },
                            "aTargets": [0]
                        },
                        {
                            "fnRender": function (oObj) {
                                if (app.modelManagement) {
                                    return oObj.aData[12];
                                } else {
                                    return oObj.aData[11];
                                }
                            }
                        },
                    ]
                });

        $(document).off('click', '#industry_table tbody td #expandIcon').on('click', '#industry_table tbody td #expandIcon', function () {
            var nTr = $(this).parents('tr')[0];
            if (oTable.fnIsOpen(nTr)) {
                // This row is already open
                // - close it
                this.src = bucketurl + '/assets/images/details_open.png';
                oTable.fnClose(nTr);
            } else {
                // Open this row
                this.src = bucketurl + '/assets/images/details_close.png';
                oTable.fnOpen(nTr, fnFormatFraudModel(nTr), 'details');
                update_identifier(nTr);
                change_item();
            }
        });

        $(document).off('click', '#industry_table tbody td span').on('click', '#industry_table tbody td span', function () {
            var nTr = $(this).parents('tr')[0];
            var toggle = $(this).siblings()[0];
            if(toggle.checked) {

                deactivate_fraudmodel(nTr, toggle);
            } else {
                activate_fraudmodel(nTr, toggle);
            }
        });
    }
};

change_item = () => {
isSyntheticScore($('#fraudfeature').find(":selected").text().toLowerCase()) ? showImplicitElements() : isCorrelationScore($('#fraudfeature').find(":selected").text().toLowerCase()) ? (showCorrelationElements(), hideRiskElements(),hideImplicitElements()) : isRiskScore($('#fraudfeature').find(":selected").text().toLowerCase()) ? (showRiskElements(), hideImplicitElements()) : (hideRiskElements(), hideImplicitElements());
}


hideRiskElements = () => { $("#lt_container").hide(); $("#ut_container").hide();}

showCorrelationElements = () => { $("#pt_container").show(); $("#nt_container").show();}

showRiskElements = () => { $("#lt_container").show(); $("#ut_container").show();}

showImplicitElements = () => {
 $("#implicit_container").show();}
hideImplicitElements = () => {
$("#implicit_container").hide();}

isCorrelationScore = (obj) => {
    for (i = 0; i < POS_NEG_THRESHOLD.length; i++) {
         if(obj.indexOf(POS_NEG_THRESHOLD[i]) != -1)
            return true;
    }
  return false;
}

isRiskScore = (obj) => obj.indexOf(RISK_SCORE) != -1;
isSyntheticScore = (obj) => {
 return (obj.indexOf(SYNTHETIC) != -1);
}

deactivate_fraudmodel = function (nTr, toggle) {
    var url = '/api/1/fraudmodel/deactivate';
    var dataBase = oTable.fnGetData(nTr);
    $.ajax(url, {
        type: 'POST',
        data: ({
            model: dataBase[2]
        }),

        success: function (data) {
            $('#super_admin_msg').html(data['msg']);
            if (data['msg'] !== "Models Successfully Deactivated") {
                toggle.checked = true;
            }
            if (document.getElementById('showActive').checked){
                get_fraudmodel("active");
            }
        }
    });
    DD_RUM.track("Deactivate Fraudmodel", { model: dataBase[2] });
};

activate_fraudmodel = function (nTr, toggle) {
    var url = '/api/1/fraudmodel/activate';
    var dataBase = oTable.fnGetData(nTr);
    $.ajax(url, {
        type: 'POST',
        data: ({
            model: dataBase[2]
        }),

        success: function (data) {
            $('#super_admin_msg').html(data['msg']);
            if (data['msg'] !== "Model Successfully Activated") {
                toggle.checked = false;
            }
        }
    });
    DD_RUM.track("Activate Fraudmodel", { model: dataBase[2] });
};

delete_fraudmodel = function (isSuperAdmin) {
    var emails = window.delegated_admin_email;
    var model = "";
    for (var i = 0; i < emails.length; i++) {
        model = emails[i] + "," + model;
    }
    var url = ""
    if(app.modelManagement) {
        url = '/api/1/fraudmodel/deactivate';
    } else {
        url = '/api/1/fraudmodel/delete';
    }
    $.ajax(url, {
        type: 'POST',
        data: ({
            model: model
        }),

        success: function (data) {
            $('#MessageModal').modal('hide');
            $('#super_admin_msg').html(data['msg']);
            if (document.getElementById('showActive').checked)
            {
                get_fraudmodel("active")
            } else {
                get_fraudmodel();
            }
        }
    });
    DD_RUM.track("Delete fraudmodel", { model: model });
};

show_fraudmodel_delete_form = function () {
    window.delegated_admin_email = [];
    window.fraudModel = [];

    $('.case').each(function () {
        var sThisVal = (this.checked ? $(this).val() : "");
        if (sThisVal !== "") {
            window.delegated_admin_email.push(sThisVal);
            window.fraudModel.push($(this).attr("modelname"));
        }
    });
    if (window.delegated_admin_email.length < 1) {
        $('#msg_font').prop('color', 'red');
        $('#super_admin_msg').html('Please select Fraud Service Model delete.');
    } else {
        $('#msg_font').prop('color', 'blue');
        $('#super_admin_msg').html('');
        show_fraudmodel_delete_dialog(true);
    }
}

var RESPONSE_NODE_MAP = {
    "negativethreshold": "negative_threshold",
    "positivethreshold": "positive_threshold",
    "lowerthreshold": "low_risk_threshold",
    "upperthreshold": "high_risk_threshold",
    "mutedparams":  "muted_params"
}
/* Formating function for row details */
fnFormatFraudModel = function (nTr) {

    set_selected = function(efid, afid) {
        return efid === afid ? 'selected="selected"' : ''
    };

    var dataBase = oTable.fnGetData(nTr);
    var data;
    if (app.modelManagement) {
        data = get_original_data_for_public_id(dataBase[2]);
    } else {
        data = get_original_data_for_id(dataBase[10]);
    }

    var configObject = JSON.parse(data.config);
    function getThresholdConfigValue(key) {
        var value = configObject[RESPONSE_NODE_MAP[key]];
        return value ? "value=\"" + value + "\"" : "";
    }

    if (data !== null) {
        var description = "";
        if (data.description != null) {
            description = data.description ;
        }
        var commonName = "";
        if (data.commonName != null) {
            commonName = data.commonName;
        }
        sOut = "<fieldset>"
            + "<legend style='font-size:13px;'>Update Fraud Service Model</legend>"
            + "<form id='update_fraudmodel' action='#' method='post'>"
            + "<input id='idvalue' type='hidden' value='"
            + data.id
            + "'/>"
            + "<input id='publicid' type='hidden' value='"
            + data.publicId
            + "'/>"
            + "<input id='qm' type='hidden' value='"
            + data.quantileMapping
            + "'/>"
            + "<table border='0'>"
            + " <tr>"
            + "     <td align='right'>Fraud Model </td>"
            + "     <td><input id='fraudmodel' type='text' required='true' value='"
            + data.name
            + "'/></td>"
            + "</tr><tr>"
            + "     <td align='right'>Common Name </td>"
            + "     <td><input id='commonname' type='text' value='"
            + commonName
            + "'/></td>"
            + "</tr><tr>"
            + "<td align='right'>Description: </td>"
            + "<td><textarea id='description' rows=4 cols=2>"
            + description
            + "</textarea></td>"
            + "</td>"
            + "</tr><tr>"
            + "     <td align='right'>Model Format </td>"
            + "     <td><select id='fraudmodelformat' onchange='javascript:get_identifiers(this);'>"
            + '         <option value="1" ' + set_selected("pojo", data.modelFormat) + '>POJO</option>'
            + '         <option value="2" ' + set_selected("mojo", data.modelFormat) + '>MOJO</option>'
            + "     </select><div class='error-msg' id='formaterrormsg'></div>"
            + "</td>"
            + "</tr><tr>"
            + "     <td align='right'>Identifier </td>"
            + '     <td><input id="fraudidentity" class="form-control identifier-width" list="fraudidentitylist" />'
            + '        <datalist id="fraudidentitylist"></datalist><div class="error-msg" id="identityerrormsg"></div>'
            + "</td>"
            + "</tr><tr>"
            + "     <td align='right'>Score Name </td>"
            + "     <td><input id='fraudscorename' type='text' value='"
            + data.scoreName
            + "'/></td>"
            + "</tr><tr>"
            + "     <td align='right'>Version </td>"
            + "     <td><input id='fraudversion' type='text' value='"
            + data.version
            + "'/></td>"
            + "</td>"
            + "</tr><tr>"
            + "</tr><tr>"
            + "     <td align='right'>Parameters </td>"
            + "     <td><textarea id='fraudparams' rows=4 cols=2 readonly>"
            + data.params
            + "</textarea></td>"
            + "</td>"
            + "</tr><tr>"
            + "     <td align='right'>Feature </td>"
            + "     <td>"
            + '<select id="fraudfeature" onchange="javascript:change_item();">'
            + '<option value="1" ' + set_selected("Fraud", data.featureLabel) + '>Sigma Identity</option>'
            + '<option value="2" ' + set_selected("Fraud (US)", data.featureLabel) + '>Sigma Identity (US)</option>'
            + '<option value="3" ' + set_selected("Fraud (Int)", data.featureLabel) + '>Sigma Identity (Int)</option>'
            + '<option value="4" ' + set_selected("Correlation Scores - Name vs Address (US)", data.featureLabel) + '>Correlation Scores - Name vs Address (US)</option>'
            + '<option value="5" ' + set_selected("Correlation Scores - Name vs Email (US)", data.featureLabel) + '>Correlation Scores - Name vs Email (US)</option>'
            + '<option value="6" ' + set_selected("Correlation Scores - Name vs Phone (US)", data.featureLabel) + '>Correlation Scores - Name vs Phone (US)</option>'
            + '<option value="7" ' + set_selected("Correlation Scores - Name vs Address (International)", data.featureLabel) + '>Correlation Scores - Name vs Address (International)</option>'
            + '<option value="8" ' + set_selected("Correlation Scores - Name vs Email (International)", data.featureLabel) + '>Correlation Scores - Name vs Email (International)</option>'
            + '<option value="9" ' + set_selected("Correlation Scores - Name vs Phone (International)", data.featureLabel) + '>Correlation Scores - Name vs Phone (International)</option>'
            + '<option value="19" ' + set_selected("Correlation Scores - Device vs Email", data.featureLabel) + '>Correlation Scores - Device vs Email</option>'
            + '<option value="10" ' + set_selected("Address Risk Score (US)", data.featureLabel) + '>Address Risk Score (US)</option>'
            + '<option value="11" ' + set_selected("Email Risk Score (US)", data.featureLabel) + '>Email Risk Score (US)</option>'
            + '<option value="12" ' + set_selected("Phone Risk Score (US)", data.featureLabel) + '>Phone Risk Score (US)</option>'
            + '<option value="13" ' + set_selected("Address Risk Score (Int)", data.featureLabel) + '>Address Risk Score (Int)</option>'
            + '<option value="14" ' + set_selected("Email Risk Score (Int)", data.featureLabel) + '>Email Risk Score (Int)</option>'
            + '<option value="15" ' + set_selected("Phone Risk Score (Int)", data.featureLabel) + '>Phone Risk Score (Int)</option>'
            + '<option value="18" ' + set_selected("Device Risk Score", data.featureLabel) + '>Device Risk Score</option>'
            + '<option value="20" ' + set_selected("Synthetic", data.featureLabel) + '>Synthetic</option>'
            + '<option value="16" ' + set_selected("Synthetic (US)", data.featureLabel) + '>Synthetic (US)</option>'
            + '<option value="17" ' + set_selected("Synthetic (International)", data.featureLabel) + '>Synthetic (International)</option>'
            + '<option value="21" ' + set_selected("First Party Fraud (US)", data.featureLabel) + '>First Party Fraud (US)</option>'
            + '</select>'
            + "</td>"

            + "</tr><tr id=\"lt_container\">"
            + "<td>Lower Threshold</td>"
            + "<td> <input type=\"text\" class=\"form-control\" id=\"lowerthreshold\" placeholder=\"Lower Threshold\"" + getThresholdConfigValue("lowerthreshold") + " /></td>"
            + "</tr><tr>"

            + "</tr><tr id=\"ut_container\">"
            + "<td>Upper Threshold</td>"
            + "<td> <input id=\"upperthreshold\" type=\"number\" min=\"0.0\" max=\"1.0\" placeholder=\"0.0 - 1.0\"" + getThresholdConfigValue("upperthreshold") + " /></td>"
            + "</tr><tr>"

            + "</tr><tr id=\"pt_container\">"
            + "<td>Positive Threshold</td>"
            + "<td> <input type=\"text\" class=\"form-control\" id=\"positivethreshold\" placeholder=\"Positive Threshold\"" + getThresholdConfigValue("positivethreshold") + " /></td>"
            + "</tr><tr>"

            + "</tr><tr id=\"nt_container\">"
            + "<td>Negative Threshold</td>"
            + "<td> <input id=\"negativethreshold\" type=\"number\" min=\"0.0\" max=\"1.0\" placeholder=\"0.0 - 1.0\"" + getThresholdConfigValue("negativethreshold") + " /></td>"
            + "</tr><tr>"

            + "</tr><tr id=\"mute_params_container\">"
            + "<td>Params to be muted</td>"
            + "<td> <input id=\"mutedparams\" placeholder=\"comma separated rulecodes in the format FMVAL.xxxx,FMVAL.yyyy,...\"" + getThresholdConfigValue("mutedparams") + " /></td>"
            + "</tr><tr>"

            + "</tr><tr>"
            + "<td align='right'>Model Type </td>"
            + "<td>"
            + '<select id="fraudmodelType">'
            + '<option value="h2o">'
            + data.modelType
            + '</option>'
            + '</select>'
            + "</td>"
            + "</tr><tr>"
            + "<tr id=\"isDefault_container\">"
            + "<td align='right'>Default Model </td>"
            + "<td>"
            + "<select id=\"isDefault\">"
            + "<option value=\"false\"" + set_selected(false, data.isDefault) + ">False</option>"
            + "<option value=\"true\"" + set_selected(true, data.isDefault) + ">True</option>"
            + "</select>"
            + "</td>"
            + "</tr><tr>"
            + "<td align='right'>Quantile Mapping: "
            + "</td>"
            + "<td>"
            + "<div align='left'>"
            + "<input type='file' id='file' name='file' required='true'/>"
            + "</div>"
            + "</td>"
            + "</tr>"
            + "<tr id=\"implicit_container\">"
            + "<td align='right'>Implicit </td>"
            + "<td>"
            + "     <select id=\"implicit\">"
            + "     <option value=\"false\"" + set_selected(false, data.implicit) + ">False</option>"
            + "     <option value=\"true\"" + set_selected(true, data.implicit) + ">True</option>"
            + "     </select>"
            + "</td>"
            + "</tr>"
            +"</table>"
            + "<div align='left'>"
            + "<a href='javascript:showConfirmUpdateQuantileMappingDialog()' class='button submit'>Update</a>"
            + "</div>"
            + "</form>"
            + "</fieldset>";

        return sOut;
    } else {
        return "";
    }
};

var POJO_MODELS = [];
var MOJO_MODELS = [];

render_identifier_dropdown = function (data) {
    var dropdown = document.getElementById('fraudidentitylist');
    dropdown.innerHTML = "";
    for (var i = 0; i < data.length; i++) {
        var option = document.createElement('option');
        option.value = data[i];
        dropdown.appendChild(option);
    }
}

get_identifiers = function (selectObject) {
  if (selectObject.value === "1") {
     var url = '/api/1/fraudmodel/list_pojo';
     $.ajax(
        url,
        {
           type: 'GET',
           success: function (data) {
              POJO_MODELS = data.data.sort();
              render_identifier_dropdown(POJO_MODELS);
           }
        }
     );
  } else if (selectObject.value === "2") {
     var url = '/api/1/fraudmodel/list_mojo';
     $.ajax(
        url,
        {
           type: 'GET',
           success: function (data) {
              MOJO_MODELS = data.data.sort();
              render_identifier_dropdown(MOJO_MODELS);
           }
        }
     );
  }
};

save_fraudmodel = function (myform) {
    var obj = {};
    var url = "";
    if (app.modelManagement) {
        url = contexturl + '/api/1/fraudmodel/register';
    } else {
        url = contexturl + '/api/1/fraudmodel/create';
    }
    var form = null;
    var formData = new FormData();
    if (myform === null) {
        form = document.getElementById('add_fraud_model');
    } else {
        $('#UpdateQuantileMappingModal').modal('hide');
        form = myform;
        if(form.qm.value && form.qm.value.trim().toLowerCase() !== 'null') {
            formData.append('qm', form.qm.value);
        }
    }

    if($.trim($(form.file).val()).length !== 0){
        formData.append('file', $(form.file).get(0).files[0]);
    }

    if (isRiskScore($('#fraudfeature').find(":selected").text().toLowerCase())) {
        obj["low_risk_threshold"] = parseFloat($("#lowerthreshold").val());
        obj["high_risk_threshold"] = parseFloat($("#upperthreshold").val());
    }
    obj["negative_threshold"] = parseFloat($("#negativethreshold").val());
    obj["positive_threshold"] = parseFloat($("#positivethreshold").val());
    obj["muted_params"] = $("#mutedparams").val();
    formData.append('config', JSON.stringify(obj));


    formData.append('id', form.idvalue.value);
    var publicId = document.getElementById("publicid");
    if (publicId != null) {
        formData.append('publicId', publicId.value);
    }
    formData.append('name', form.fraudmodel.value);
    formData.append('commonName', form.commonname.value);
    formData.append('mutedparams', form.mutedparams.value);

    var formatErrorMsg = document.getElementById("formaterrormsg");
    var identityErrorMsg = document.getElementById("identityerrormsg");
    var identifier = "";
    if (form.fraudmodelformat.value === "1") {
        if (form.elements["fraudidentity"].value!== null) {
            identifier = form.elements["fraudidentity"].value;
        } else {
            identityErrorMsg.innerHTML = "Identifier required.";
            return;
        }
        if (POJO_MODELS.includes(identifier)) {
            formatErrorMsg.innerHTML = "";
            identityErrorMsg.innerHTML = "";
            formData.append('modelFormat', "pojo");
            formData.append('identifier', identifier);
        } else {
            identityErrorMsg.innerHTML = "Please select a valid identifier.";
            return;
        }
    } else if (form.fraudmodelformat.value === "2") {
        if (form.elements["fraudidentity"].value!== null) {
            identifier = form.elements["fraudidentity"].value;
        } else {
            identityErrorMsg.innerHTML = "Identifier required.";
            return;
        }
        if (MOJO_MODELS.includes(identifier)) {
            formatErrorMsg.innerHTML = "";
            identityErrorMsg.innerHTML = "";
            formData.append('modelFormat', "mojo");
            formData.append('identifier', identifier);
        } else {
            identityErrorMsg.innerHTML = "Please select a valid identifier.";
            return;
        }
    } else {
        formatErrorMsg.innerHTML = "Please select a model format.";
        return;
    }
    formData.append('scoreName', form.fraudscorename.value);
    formData.append('version', form.fraudversion.value);
    formData.append('feature', form.fraudfeature.value);
    formData.append('modelType', form.fraudmodelType.value);
    formData.append('description', form.description.value);
    formData.append('isDefault', form.isDefault.value);
    if(form.fraudfeature.value=="17" | form.fraudfeature.value=="16" | form.fraudfeature.value=="20"){
        formData.append('implicit', form.implicit.value);
    }else {
        formData.append('implicit', false);
    }

    $.ajax(
            url,
            {
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,

                success: function (data) {
                    if (data['status'].toLowerCase() === 'ok') {
                        $('#message').html('');
                        $("#AddFraudModal").modal("hide");
                        form.reset();
                        $('#super_admin_msg').html(data['msg']);
                        if (document.getElementById('showActive').checked)
                        {
                            get_fraudmodel("active")
                        } else {
                            get_fraudmodel();
                        }
                    } else {
                        $("#AddFraudModal").modal("show");
                        $('#message').html(data['msg']);
                    }
                }
    });
    DD_RUM.track("Save Fraud Model", { modelName: form.fraudmodel.value, modelType: form.fraudmodelType.value });
};

show_fraudmodel_delete_dialog = function () {
    var emails = window.fraudModel;
    if (emails !== null && emails.length > 0) {
        var msg = "";
        if (emails.length < 2) {
            msg = "Are you sure you want to delete " + emails + "?";
        } else {
            msg = "Are you sure to delete the following Fraud Service Model?";
            var email = "";
            for (var i = 0; i < emails.length; i++) {
                msg = msg + "<br />" + emails[i];
            }
        }
        $('#delete_msg').html(msg);
        $('#MessageModal').modal('show');
    } else {
        $('#admin_message').html('Please select Fraud Service Model to delete');
    }
};


//Java script for FRAUD MODEL MAPPING TABLE

get_fraudmodel_account_list = function () {
    // var url = '/api/1/fraudmodel/getaccfraudlist';
    // $.ajax(url, {
    //     type: 'GET',
    //     success: function (data) {
    //         if (data !== null) {
    //             data = data['data'];

    //             construct_mapping_table(data);
    //         }
    //     }
    // });
    window.location.reload();
    DD_RUM.track("Load fraud model Mapping");
};

construct_mapping_table = function (data) {
    window.delegated_admin_email = [];
    if (data !== null) {
        var aaData = [];
        var imgPath = bucketurl + '/assets/images/details_close.png';
        for (var j = 0; j < data.length; j++) {
            var rowData = data[j];
            var a = [];
            a.push(rowData['accountName']);
            a.push(rowData['primaryEmail']);
            a.push(rowData['fraudModels']);
            a.push(imgPath);
            a.push(rowData['fraudModels']);
            aaData.push(a);
        }

        oTable = $('#fraudaccount_table')
            .dataTable(
                {
                    "aaData": aaData,
                    "bPaginate": false,
                    "bFilter": false,
                    "bInfo": false,
                    "bProcessing": true,
                    "bDestroy": true,
                    "bAutoWidth": false,
                    "aoColumnDefs": [{
                        "bSortable": false,
                        "aTargets": [0, -1]
                    }],
                    "aaSorting": [[1, 'asc']],
                    "aoColumns": [

                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[0];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return oObj.aData[1];
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return (oObj.aData[2])[0].name;
                            }
                        },
                        {
                            "fnRender": function (oObj) {
                                return "<img src='"
                                    + oObj.aData[3]
                                    + "' width='20px' height='20px' id='"
                                    + (oObj.aData[4])[0].id
                                    + "' />"
                            },
                            "aTargets": [0]
                        }
                    ]
                });

        $(document).off('click', '#fraudaccount_table tbody td img').on('click', '#fraudaccount_table tbody td img', function () {
            var nTr = $(this).parents('tr')[0];
            var data = oTable.fnGetData(nTr);
            show_fraudmodel_mapping_delete_dialog(data[1], this.id);
        });
    }
};

show_fraudmodel_mapping_delete_dialog = function (email, modelid) {
    alert(email + modelid);
    var msg = "";
    if (email !== null) {
        msg = "Are you sure you want to delete mapping with " + email + "and " + modelid + "?";

        $('#delete_msg').html(msg);
        $('#MessageModal').modal('show');
    }
};

viewQuantileMapping = function(qm) {
    if(qm && qm.length>0) {
        $('#quantile_mappings').html(qm);
    }
    else{
        $('#quantile_mappings').html("Quantile Mapping not set!!!")
    }

    $('#ViewQuantileMappingModal').modal('show');
    DD_RUM.track("View Quantile Mapping")
};

downloadQuantileMapping = function(qm) {
    var textToSaveAsBlob = new Blob(qm, {type: "text/plain;charset=utf-8"});
    var textToSaveAsURL = window.URL.createObjectURL(textToSaveAsBlob);
    var fileNameToSaveAs = "quantileMapping.csv";
    var downloadLink = document.createElement("a");
    downloadLink.download = fileNameToSaveAs;
    downloadLink.innerHTML = "Download File";
    downloadLink.href = textToSaveAsURL;
    downloadLink.onclick = destroyClickedElement;
    downloadLink.style.display = "none";
    document.body.appendChild(downloadLink);
    downloadLink.click();
    DD_RUM.track("Download Quantile Mapping");
};

showConfirmUpdateQuantileMappingDialog = function() {
    var msg = "Are you sure you want to update the quantile mapping?";
    $('#update_msg').html(msg);
    $('#UpdateQuantileMappingModal').modal({backdrop: 'static', keyboard: false});
};

unmapModel = function(accountId, publicId, environment, associationType) {
    if(parseInt(accountId) === NaN) {
        alert("Invalid Account Id");
        return;
    }

    if(parseInt(associationType) === NaN){
     alert("Invalid Association Type");
     return;
    }

    var confirm = window.confirm("Are you sure to delete this model mapping ?");
    if (confirm) {
        var url = "";
        if (app.modelManagement) {
            url = "/api/1/fraudmodel/unmap?accountId=" + accountId + "&modelId=" + publicId + "&environment=" + environment + "&associationType=" + associationType;
        } else {
            url = "/api/1/fraudmodel/unassociate?accountId=" + accountId + "&modelId=" + publicId + "&environment=" + environment + "&associationType=" + associationType;
        }
        window.location.replace(url);
    }
    DD_RUM.track("Unmap Model", { accountId: accountId, env: environment, associationType: associationType});
};