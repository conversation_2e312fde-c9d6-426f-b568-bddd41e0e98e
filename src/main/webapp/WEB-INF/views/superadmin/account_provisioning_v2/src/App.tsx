import React, { FC } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import "@socure/components/src/components/Alert/scss/main.scss";
import AccountProvisioning from './pages/AccountProvisioning';

type AppProps = {
    isReadOnly: boolean;
    isSubaccount: boolean;
    loggedinUsername: string;
    piiRetentionAccessPermission: string;
    isInternal: boolean;
}

const App: FC<AppProps> = ({ isReadOnly, isSubaccount, loggedinUsername, piiRetentionAccessPermission, isInternal }) => {
    const queryString = window.location.search;
    const queryParams = new URLSearchParams(queryString);
    const accountId = queryParams.get("accountid");
    toast.configure();

    return (
        <>
            <ToastContainer className='socure-c-toast-container' />
            <AccountProvisioning accountId={accountId as string} isReadOnly={isReadOnly} isSubaccount={isSubaccount} loggedinUsername={loggedinUsername} piiRetentionAccessPermission={piiRetentionAccessPermission} isInternal={isInternal} />
        </>
    )
}

export default App;