package me.socure.bean

import com.typesafe.config.Config
import me.socure.common.launchdarkly.client.LaunchDarklyClientWrapper
import me.socure.common.launchdarkly.client.factory.LaunchDarklyClientFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.annotation.{Bean, Configuration, Scope}

import scala.concurrent.ExecutionContext

@Configuration
class ControlCenterBeanScala {

  @Autowired
  var config: Config = _

  @Autowired
  implicit var executionContext: ExecutionContext = _

  @Bean
  @Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
  def getLaunchDarklyClient: LaunchDarklyClientWrapper = {
    val sdkKey = config.getString("launchdarkly.sdk.key")
    if (config.getBoolean("launchdarkly.use.fedramp.version")) {
      LaunchDarklyClientFactory.getFedrampLDClientWrapper(sdkKey)
    } else {
      LaunchDarklyClientFactory.getLDClientWrapper(sdkKey)
    }
  }
}
