import React from 'react';
import styled from 'styled-components';
import { Icon, dimensions } from '@socure/components';

const WarningText = styled.div`
    display: flex;
    align-items: center;
    border: 1px solid rgba(230, 26, 26, 0.1);;
    background: rgba(230, 26, 26, 0.1);
    width: fit-content;
    padding: 8px;
    margin-top: 28px;
`

export type BundleUpdateWarningProps = {
    currentBundle: string;
    existingBundle: string;
}

const BundleUpdateWarning: React.FC<BundleUpdateWarningProps> = ({ currentBundle, existingBundle }) => {
    return existingBundle && currentBundle !== existingBundle ? <WarningText>
        <Icon name='alert-triangle' size={dimensions.XS} />
        <span>You are modifying from <b>{existingBundle}</b> to <b>{currentBundle}</b>. Upon confirmation, changes will be updated.</span>
    </WarningText> : <></>
}

export default BundleUpdateWarning;