## Reviewer Checklist

All reviewers can help ensure accuracy, clarity, completeness, and adherence to the [Code Review Guidelines](https://docs.google.com/document/d/1TXDycqJMTvLPrjAC7xx9zVHm_HxL-nIFc_a3r67O8J4/edit).

The code reviewer needs to ensure the following:

* **Implementation Quality** - Best practices
    - [ ] Yes 
    - [ ] No
    - [ ] NA
* **Modularity**
    - [ ] Yes 
    - [ ] No
    - [ ] NA
* **Correctness** - Business logic check
    - [ ] Yes 
    - [ ] No
    - [ ] NA
* **Comments** - Javadoc for business logic functions
    - [ ] Yes 
    - [ ] No
    - [ ] NA
* **Unit Tests**
    - [ ] Yes 
    - [ ] No
    - [ ] NA
* **Security** - [Owasp Top 10](https://owasp.org/www-project-top-ten/)
    - [ ] Yes 
    - [ ] No
    - [ ] NA
* **Monitoring**
    - [ ] Yes 
    - [ ] No
    - [ ] NA
