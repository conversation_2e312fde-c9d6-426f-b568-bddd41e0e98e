function getPrefixSuccessCallback(data) {
    var select = document.getElementById("DependencyPrefix");
    for (index in data) {
        var option = new Option(data[index])
        select.add(option);
    }
}

function getPrefixSuccessCallbackV2(data) {
    debugger;
    var select = document.getElementById("DependencyPrefixV2");
    for (index in data) {
        var option = new Option(data[index])
        select.add(option);
    }
}

function getPrefix(selectedVendor, version) {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+selectedVendor+"/prefix",
        data: {version},
        dataType: "json",
        type: 'GET'
    });
}

function fetchExistingAssociations(version) {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+selectedVendor+"/prefix/"+selectedPrefix,
        data: {version},
        dataType: 'json',
        type: 'GET'
    });
}

function fetchSupportedTables() {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/table/supported",
        type: 'GET'
    });
}

var selectedPrefix;
function selectDependencyPrefix(selectObject) {
    debugger;
    selectedPrefix = selectObject.value;
    if(selectedPrefix=='')
      return;

    $('#prefixSelection').show();

    fetchExistingAssociations().then((data) => {
        var $currentAssociations = $("#currentAssociations");
        $currentAssociations.empty();
        for (index in data.dependencies) {
            $currentAssociations.append($("<label>" +data.dependencies[index].name +"</label>"))
        }
    },() => alert('Unable to fetch existing associations. Please refresh the Page!'));

    fetchSupportedTables().then((data) => {
        var $associationCheckboxes = $("#associationCheckboxes");
        $associationCheckboxes.empty();
         for (index in data) {
            $associationCheckboxes.append($("<input type='checkbox' value='" +data[index].name +"'/>  " +data[index].name +"</br>"))
         }
    }, () => alert('Unable to fetch supported tables. Please refresh the Page!'));
}

function selectDependencyPrefixV2(selectObject) {
    selectedPrefix = selectObject.value;
    if(selectedPrefix=='')
      return;

    $('#prefixSelectionV2').show();

    fetchExistingAssociations("v2").then((data) => {
        var $currentAssociations = $("#currentAssociationsV2");
        $currentAssociations.empty();
        for (index in data.dependencies) {
            $currentAssociations.append($("<label>" +data.dependencies[index].name +"</label>"))
        }
    },() => alert('Unable to fetch existing associations. Please refresh the Page!'));

    fetchSupportedTables().then((data) => {
        var $associationCheckboxes = $("#associationCheckboxesV2");
        $associationCheckboxes.empty();
         for (index in data) {
            $associationCheckboxes.append($("<input type='checkbox' value='" +data[index].name +"'/>  " +data[index].name +"</br>"))
         }
    }, () => alert('Unable to fetch supported tables. Please refresh the Page!'));
}


function saveAssociations() {

    var prefix  = $('#DependencyPrefix').val();
    var checkedCheckBoxes = $("#associationCheckboxes > :checkbox:checked");

    var dependencies = [];
    for(var i=0;i<checkedCheckBoxes.length;i++){
        dependencies.push(checkedCheckBoxes[i].value);
    }

    if(!dependencies){
        alert('Please select some dependency to associate');
        return;
    }

    $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+selectedVendor+"/prefix/"+prefix,
            type: 'PATCH',
            data: JSON.stringify(dependencies),
            contentType: "application/json",
            error: function(e) {
                 alert('Unable to Save Associations!')
            },
            success: function(data) {
                var object = {
                    value: prefix
                }
                selectDependencyPrefix(object);
                alert('Saved Associations Successfully!');
            }
        });
}

function saveAssociationsV2() {
    var prefix  = $('#DependencyPrefixV2').val();
    var checkedCheckBoxes = $("#associationCheckboxesV2 > :checkbox:checked");
    var dependencies = [];
    for(var i=0;i<checkedCheckBoxes.length;i++){
        dependencies.push(checkedCheckBoxes[i].value);
    }
    if(!dependencies){
        alert('Please select some dependency to associate');
        return;
    }
    $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+selectedVendor+"/prefix/"+prefix + "?version=v2",
            type: 'PATCH',
            data: JSON.stringify(dependencies),
            contentType: "application/json",
            error: function(e) {
                 alert('Unable to Save Associations!')
            },
            success: function(data) {
                var object = {
                    value: prefix
                }
                selectDependencyPrefixV2(object);
                alert('Saved Associations Successfully!');
            }
        });
}