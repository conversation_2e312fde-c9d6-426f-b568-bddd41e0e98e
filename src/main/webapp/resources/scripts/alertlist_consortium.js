function alert_msg(msg, type) {
    var element = $('<div class="alert alert-dismissable alert-' + type + '"> ' +
                                     '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">' +
                                         '×' +
                                     '</button>' +
                                     '<h4>' +
                                         'Status:' +
                                     '</h4>' + msg +
                                 '</div>')
    $('#alert_holder').append(element)
}

function alert_success(msg) {
    alert_msg(msg, 'success')
}

function alert_danger(msg) {
    alert_msg(msg, 'danger')
}

function blob_as_text(blb, cb) {
    var reader = new FileReader();
    reader.addEventListener('loadend', (e) => {
      var text = e.srcElement.result;
      cb(text)
    });
    reader.readAsText(blb);
}

function import_consortium_data() {
    var xhr = new XMLHttpRequest();
    xhr.onreadystatechange = function(){
        if (this.readyState == 4){
            try {
                $('#confirm-import-modal').modal('hide')
                if(this.status === 200) {
                    $('#confirm-import-modal').modal('hide')
                    blob_as_text(this.response, function(text) {
                        alert_success(JSON.parse(text).msg);
                    })
                    DD_RUM.track("Alertlist consortium upload success");
                } else if(this.status === 422) {
                    alert_danger('There are validation failures. Please see the downloaded file for the list failed records.');
                    var __a = document.createElement('a')
                    __a.download = 'validation_failures.csv.gz'
                    var url = window.URL || window.webkitURL;
                    __a.href = url.createObjectURL(this.response);
                    __a.click();
                     DD_RUM.track("Alertlist consortium upload failed due to validation error");
                } else {
                    blob_as_text(this.response, function(text) {
                        console.log('Import error', text)
                        alert_danger(JSON.parse(text).msg);
                    })
                     DD_RUM.track("Alertlist consortium upload failed due to import error");
                }
            } catch (e) {
                alert_danger('Unknown error')
                console.log('Error importing', e)
            }
        }
    }
    xhr.open('POST', contexturl + '/superadmin/1/consortium/import');
    xhr.responseType = 'blob';
    var formData = new FormData();
    formData.append('file', $('#fileInput')[0].files[0]);
    xhr.send(formData);
}

window.onload = function() {
    if(DD_RUM) {
      DD_RUM.startView({name: "/alertlist_consortium_import"});
      DD_RUM.setUser({ "id": loggedinUsername});
    }
}
