
  (function(h,o,u,n,d) {
    h=h[d]=h[d]||{q:[],onReady:function(c){h.q.push(c)}}
    d=o.createElement(u);d.async=1;d.src=n
    n=o.getElementsByTagName(u)[0];n.parentNode.insertBefore(d,n)
  })(window,document,'script','https://www.datadoghq-browser-agent.com/datadog-rum-v4.js','DD_RUM')
  DD_RUM.onReady(function() {

    var APP_ID = "99c1482b-d48e-4e0f-ac6e-dba2bc7c8d1d";
    var CLIENT_TOKEN = "pub3cd44939478d454736c764aeacd40f36";

    var client_token, app_id, environment;
    var href = window.location.href;
    if(href.includes('product-dev.socure.link')) {
      environment = "dev";
    } else if(href.includes('prod.socure.link')) {
      environment = "prod";
    } else if(href.includes('product-ds.socure.link') || href.includes('datasci-superadmin')){
      environment = "ds";
    } else {
      environment = "dev";
    }
    
    DD_RUM.init({
      clientToken: CLIENT_TOKEN,
      applicationId: APP_ID,
      site: 'datadoghq.com',
      service: 'superadmin-' + environment,
      env: environment,
      
      // Specify a version number to identify the deployed version of your application in Datadog 
      // version: '1.0.0',
      trackUserInteractions: true,
      trackViewsManually: true,
      trackResources: true,
      trackLongTasks: true,
      silentMultipleInit: true,
      trackFrustrations: true,
      defaultPrivacyLevel: 'mask-user-input',
      beforeSend: (event, context) => {
         var actionType = event.type;
         if(actionType && (actionType === "action" || actionType === "view" || actionType === "user")) {
              event.context["loggedInUser"] = loggedinUsername;
              event.context["environment"] = environment;
         }

      }
    });

    DD_RUM.track = function(name, props) {
      DD_RUM.addAction(name, props);
    }
  })