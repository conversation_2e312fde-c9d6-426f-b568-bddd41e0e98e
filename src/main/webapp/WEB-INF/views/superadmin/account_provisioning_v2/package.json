{"name": "account-provisioning-v2", "version": "0.1.0", "private": true, "dependencies": {"@socure/components": "git+ssh://**************************************:product/ux/socure-reactjs-components.git#main", "@types/jest": "^29.2.2", "@types/node": "^18.11.9", "@types/react": "^18.0.25", "@types/react-dom": "^18.0.8", "axios": "^1.8.2", "lodash-es": "4.x", "mixpanel-browser": "^2.41.0", "react": "^16.9.0", "react-dom": "^16.9.0", "react-toastify": "^6.2.0", "styled-components": "^5.3.6"}, "scripts": {"start": "NODE_ENV=development && NODE_OPTIONS=--max-old-space-size=4096 ts-node -O '{\"module\":\"commonjs\"}' ./node_modules/.bin/webpack serve", "build-dev": "export NODE_ENV=development && NODE_OPTIONS=--max-old-space-size=4096 ts-node -O '{\"module\":\"commonjs\"}' ./node_modules/.bin/webpack --mode=production && yarn run build-mv", "build": "NODE_OPTIONS=--max-old-space-size=4096 ts-node -O '{\"module\":\"commonjs\"}' ./node_modules/.bin/webpack --mode=production && yarn run build-mv", "build-mv": "rm -rf ../../../../resources/account_provisioning && mv -f public/dist '../../../../resources/account_provisioning'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@datadog/browser-rum": "^4.34.1", "@svgr/webpack": "^6.5.1", "@types/copy-webpack-plugin": "^6.4.0", "@types/html-webpack-plugin": "^3.2.4", "@types/lodash-es": "4.x", "@types/mini-css-extract-plugin": "^1.2.1", "@types/mixpanel-browser": "^2.35.7", "@types/styled-components": "^5.1.4", "@types/webpack-dev-server": "^3.11.1", "chart.js": "^3.9.1", "copy-webpack-plugin": "^5.1.1", "css-loader": "^3.4.1", "file-loader": "1.x", "html-webpack-plugin": "4.0.0", "mini-css-extract-plugin": "0.4.x", "moment": "2.29.x", "node-sass": "^9.0.0", "react-chartjs-2": "^4.3.1", "sass-loader": "^6.0.7", "ts-loader": "^6.2.2", "ts-node": "5.x", "typescript": "^4.8.4", "url-loader": "^4.1.1", "webpack": "^5.94.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.15.2"}, "resolutions": {"http-proxy-middleware": "^2.0.7", "postcss": "^8.4.38", "cross-spawn": "^7.0.5", "path-to-regexp": "^0.1.12", "axios": "^1.8.2", "nanoid": "^3.3.8", "cookie": "^0.7.0"}}