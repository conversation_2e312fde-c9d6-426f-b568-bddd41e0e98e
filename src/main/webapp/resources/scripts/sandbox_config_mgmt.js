$(document).ready(function() {

    /*-------------------------------------Job Definitions setup------------------------*/
        $('#sandbox_config').click(function(event) {
            $('#SandboxConfig').load("sandbox_config", function() {
                openTab(event, 'SandboxConfig');
            });
        });
    $('#sandbox_config').click();

});

function openTab(evt, tabName) {
    // Declare all variables
    var i, tabcontent, tablinks;

    // Get all elements with class="tabcontent" and hide them
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }

    // Get all elements with class="tablinks" and remove the class "active"
    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Show the current tab, and add an "active" class to the button that opened the tab
    document.getElementById(tabName).style.display = "block";
    evt.target.className += " active";
}
