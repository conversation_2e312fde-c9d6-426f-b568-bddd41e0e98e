package me.socure.resolver

import me.socure.decision.service.client.DecisionServiceClient
import me.socure.decision.service.common._
import me.socure.decision.service.common.model.DecisionSimulateRequest
import me.socure.model.ErrorResponse
import org.apache.commons.io.IOUtils
import org.mockito.Mockito.when
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.Future

class DecisionServiceResolverTest extends FunSuite with MockitoSugar with Matchers {
  val decisionServiceClient = mock[DecisionServiceClient]

  val service = new DecisionServiceResolver
  service.decisionServiceClient = decisionServiceClient

  val decisionRevision = List(ListLogicResponse(modelName = "dummyName1", modelVersion = "dummyVersion1", revision = 1, isLive = Some(false), tag = "None"),
    ListLogicResponse(modelName = "dummyName2", modelVersion = "dummyVersion2", revision = 2, isLive = Some(true), tag = "None"))

  val addLogic = AddLogicRequest(accountId = 1, modelName = "dummy1", modelVersion = "dummy2", isLive = Some(false), tag = "dummy3", logic = "testlogic", emailId = "", decisionDetailsEnabled = Some(false))
  val addLogicResponse = AddLogicResponse(revision = 1)
  val error = ErrorResponse(101, "dummyE error")

  test("list all revisions"){
    when(decisionServiceClient.listLogic("1")) thenReturn (Future.successful(Right(decisionRevision)))
    val res = service.listLogic("1")
    res shouldBe Right(decisionRevision)
  }

  test("list all revisions error"){
    when(decisionServiceClient.listLogic("1")) thenReturn(Future.successful(Left(error)))
    val res = service.listLogic("1")
    res shouldBe Left(error)
  }

  test("add revisions error"){
    when(decisionServiceClient.addLogic(addLogic)) thenReturn(Future.successful(Left(error)))
    val res = service.addLogic(addLogic)
    res shouldBe Left(error)
  }

  test("add revisions"){
    when(decisionServiceClient.addLogic(addLogic)) thenReturn(Future.successful(Right(addLogicResponse)))
    val res = service.addLogic(addLogic)
    res shouldBe Right(addLogicResponse)
  }

  val markAsLiveLogicRequest = MarkAsLiveLogicRequest(accountId = 1, revision = 1)
  val markAsLiveLogicResponse = MarkAsLiveLogicResponse(status = "ok")
  test("mark-as-live"){
    when(decisionServiceClient.markAsLive(markAsLiveLogicRequest)) thenReturn(Future.successful(Right(markAsLiveLogicResponse)))
    val res = service.markAsLive(markAsLiveLogicRequest)
    res shouldBe Right(markAsLiveLogicResponse)
  }

  var input = DecisionSimulateRequest(accountId = 1, revision = Some(1), testFile = Option(IOUtils.toInputStream("dummy")), trxEndDate = None, trxMaxCount = Option(10), trxStartDate = None, decision = None)
  var output = IOUtils.toInputStream("dummy")
  test("simulate"){
    when(decisionServiceClient.simulateDecisionLogic(input)) thenReturn(Future.successful(Right(output)))
    val res = service.simulate(input)
    res shouldBe Right(output)
  }

  test("error"){
    when(decisionServiceClient.simulateDecisionLogic(input)) thenReturn(Future.successful(Left(error)))
    val res = service.simulate(input)
    res shouldBe Left(error)
  }

}
