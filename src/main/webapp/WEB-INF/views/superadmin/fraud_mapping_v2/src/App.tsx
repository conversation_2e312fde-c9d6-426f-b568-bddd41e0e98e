import FraudMappingContainer from "./components/Container";
import React from "react";
import {
  createTheme,
  StyledEngineProvider,
  ThemeProvider,
} from "@mui/material/styles";
import { ThemeProvider as EmotionThemeProvider } from "@emotion/react";
import { BaseStyles, EffectivTheme, ToastContainer } from "@socure/components";

const theme = createTheme();

const App: React.FC = () => {
  return (
    <StyledEngineProvider injectFirst>
      <BaseStyles />
      <ThemeProvider theme={theme}>
        <EmotionThemeProvider theme={EffectivTheme}>
          <ToastContainer
            position="bottom-left"
            newestOnTop={false}
            closeOnClick={false}
            rtl={false}
            pauseOnFocusLoss
            draggable={false}
            hideProgressBar={true}
            toastClassName="effectiv-toast"
          />
          <FraudMappingContainer />
        </EmotionThemeProvider>
      </ThemeProvider>
    </StyledEngineProvider>
  );
};

export default App;
