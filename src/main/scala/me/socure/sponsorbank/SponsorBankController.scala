package me.socure.sponsorbank

import com.fasterxml.jackson.module.scala.DefaultScalaModule
import me.socure.common.json.bean.APIOutput
import me.socure.model.sponsor.bank.SponsorBankProgramLinkRequest
import me.socure.service.constants.IConstants
import me.socure.superadmin.authentication.UserDetailsWithoutCredentials
import me.socure.superadmin.factory.ObjectMapperFactory
import me.socure.util.AccountUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.{HttpHeaders, HttpStatus, MediaType, ResponseEntity}
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{PathVariable, RequestMapping, RequestMethod}

@Controller
@RequestMapping(Array("/superadmin/sponsorbank"))
class SponsorBankController {
  private val logger = LoggerFactory.getLogger(getClass)

  @Autowired
  var sponsorBankResolver: SponsorBankResolver = _

  @Autowired
  var accountUtil: AccountUtil = _

  @RequestMapping(value = Array("/non-linked"), method = Array(RequestMethod.GET))
  def getNonSponsorBankPrograms(): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = sponsorBankResolver.getNonSponsorBankPrograms()
    result match {
      case Right(accounts) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(accounts)
      case Left(errorResponse) =>
        logger.info(s"error - getNonSponsorBankPrograms: $errorResponse")
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(Seq.empty)
    }
    pushResponseToCaller(apiOutput)
  }

  @RequestMapping(value = Array("/program/{programId}"), method = Array(RequestMethod.GET))
  def getProgram(@PathVariable("programId") programId: Long): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    sponsorBankResolver.getSponsorBank(programId) match {
      case Right(response) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response)
      case Left(errorResponse) =>
        logger.info(s"Error - getProgram: $errorResponse")
        apiOutput.setMsg(errorResponse)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
    }
    pushResponseToCaller(apiOutput)
  }

  @RequestMapping(value = Array("/linked/programs/{sponsorBankId}"), method = Array(RequestMethod.GET))
  def getLinkedPrograms(@PathVariable("sponsorBankId") sponsorBankId: Long): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
    sponsorBankResolver.getLinkedPrograms(sponsorBankId) match {
      case Right(response) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response)
      case Left(errorResponse) =>
        logger.info(s"Error - getLinkedPrograms: $errorResponse")
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(Seq.empty)
    }
    pushResponseToCaller(apiOutput)
  }

  @RequestMapping(value = Array("/link/{sponsorBankId}/program/{programId}"), method = Array(RequestMethod.POST), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def linkSponsorBankProgram(@PathVariable("sponsorBankId") sponsorBankId: Long,
                                           @PathVariable("programId") programId: Long): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val initiatedBy = userDetails.getUsername
    sponsorBankResolver.linkSponsorBankProgram(SponsorBankProgramLinkRequest(sponsorBankId, programId, initiatedBy)) match {
      case Right(response) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response)
      case Left(errorResponse) =>
        logger.info(s"Error - linkSponsorBankProgram: $errorResponse")
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(Seq.empty)
    }
    pushResponseToCaller(apiOutput)
  }

  @RequestMapping(value = Array("/unlink/{sponsorBankId}/program/{programId}"), method = Array(RequestMethod.POST), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def unlinkSponsorBankProgram(@PathVariable("sponsorBankId") sponsorBankId: Long,
                             @PathVariable("programId") programId: Long): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val initiatedBy = userDetails.getUsername
    sponsorBankResolver.unlinkSponsorBankProgram(SponsorBankProgramLinkRequest(sponsorBankId, programId, initiatedBy)) match {
      case Right(response) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response)
      case Left(errorResponse) =>
        logger.info(s"Error - unlinkSponsorBankProgram: $errorResponse")
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(Seq.empty)
    }
    pushResponseToCaller(apiOutput)
  }

  private def pushResponseToCaller(apiOutput : APIOutput): ResponseEntity[Object]  = {
    val headers = new HttpHeaders()
    val mapper = ObjectMapperFactory.get()
    headers.add("Content-Type", "application/json")
    mapper.registerModule(DefaultScalaModule)
    new ResponseEntity[Object](mapper.writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }
}