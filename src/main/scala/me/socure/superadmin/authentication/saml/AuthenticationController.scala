package me.socure.superadmin.authentication.saml

import com.typesafe.config.ConfigFactory
import me.socure.common.clock.RealClock
import me.socure.common.json.bean.APIOutput
import me.socure.common.saml.{AuthenticationResponse, SamlClient}
import me.socure.control.center.constants.ControlCenterPermission
import me.socure.service.constants.{Countries, IConstants}
import me.socure.superadmin.authentication.{SamlAttribute, SimpleUnauthenticatedToken}
import me.socure.util.{EnvUtil, SecureUtil}
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier, Value}
import org.springframework.context.annotation.Bean
import org.springframework.http.{HttpStatus, ResponseEntity}
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.core.{Authentication, AuthenticationException}
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{RequestMapping, RequestMethod, ResponseBody}

import javax.annotation.PostConstruct
import javax.servlet.http.{HttpServletRequest, HttpServletResponse}
import scala.util.Try

@Controller
@RequestMapping(Array("/saml2"))
@Autowired
class AuthenticationController() {

  private[saml] var samlClient: SamlClient = null
  @Autowired
  private[saml] var authenticationManager: AuthenticationManager = _

  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  @Bean
  def getClock : RealClock = {
    new RealClock()
  }

  private val logger = LoggerFactory.getLogger(getClass)
  private var maxAuthenticationAge:Int = _
  private val SamlAttributeCountry: String = "country"
  private val SamlAttributeCanAccessControlCentre: String = "CanAccessControlCentre"
  private val SamlAttributeCanModifyAccountProvisioning: String = "account_auto_provisioning"
  private val SamlAttributePiiRetentionAccessPermission: String = "piiRetentionAccessPermission";

  /*@RequestMapping(value = Array("/SSO"), method = Array(RequestMethod.POST))
  def authenticate(request: HttpServletRequest, response: HttpServletResponse): String = {
    try {
      val samlResponse = samlClient.processPostFromIdentityProvider(request)
      getClock.now.plusSeconds(maxAuthenticationAge)
      samlResponse.getAssertion.getConditions.setNotOnOrAfter(getClock.now.plusSeconds(maxAuthenticationAge))
      val authenticationResponse: AuthenticationResponse = SamlClient.getAuthenticationResponse(samlResponse)
      val attributes = authenticationResponse.attributes
      val countryAttributeValueOpt: Option[String] = Option(attributes(SamlAttributeCountry))
      val canAccessControlCentreValueOpt: Option[String] = Option(attributes(SamlAttributeCanAccessControlCentre))
      val canModifyAccountProvisioningOpt: Option[String] = Option(attributes(SamlAttributeCanModifyAccountProvisioning))
      val piiRetentionAccessPermission: String = attributes(SamlAttributePiiRetentionAccessPermission)
      val username: String = authenticationResponse.nameId

      val countryValue: String = countryAttributeValueOpt match {
        case Some(countryCode2) => countryCode2
        case None =>
          logger.error(s"country attribute not found for $username")
          Countries.UNITED_STATES.code2
      }

      Countries.byCode2(countryValue) match {
        case Some(country) =>
          val samlAttribute: SamlAttribute = SamlAttribute(country, resolveAccess(canAccessControlCentreValueOpt), resolveAccountProvisioningAccess(canModifyAccountProvisioningOpt), piiRetentionAccessPermission)

          val unauthenticatedToken: SimpleUnauthenticatedToken = SimpleUnauthenticatedToken(
            username = username,
            expirationTime = Some(authenticationResponse.expiry),
            samlAttribute
          )
          val authentication: Authentication = authenticationManager.authenticate(unauthenticatedToken)
          SecurityContextHolder.getContext.setAuthentication(authentication)
          "redirect:/active_users"
        case None =>
          logger.error(s"Given country= $countryValue is Invalid")
          "redirect:/unauthorized"
      }

    } catch {
      case e: Throwable =>
        logger.error("Super-Admin SAML2 Authentication error", e)
        "redirect:/unauthorized"
    }
  }
*/

  @RequestMapping(value = Array("/SSO"), method = Array(RequestMethod.GET))
  def authenticate(request: HttpServletRequest, response: HttpServletResponse): String = {
    try {
      //val authenticationResponse: AuthenticationResponse = saml2Authenticator.authenticate(request, response)
      val countryAttributeValueOpt: Option[String] = Some(Countries.UNITED_STATES.code2.toString)

      val countryValue: String = countryAttributeValueOpt match {
        case Some(countryCode2) => countryCode2
        case None =>
          val username: String = "dev";
          logger.error(s"country attribute not found for $username")
          Countries.UNITED_STATES.code2.toString
      }

      Countries.byCode2(countryValue) match {
        case Some(country) =>
          val samlAttribute: SamlAttribute = SamlAttribute(country, ControlCenterPermission.FULL_ACCESS, true, "")
          val username: String = "dev";
          val unauthenticatedToken: SimpleUnauthenticatedToken = SimpleUnauthenticatedToken(
            username = username,
            expirationTime = None,
            samlAttribute
          )
          val authentication: Authentication = authenticationManager.authenticate(unauthenticatedToken)
          SecurityContextHolder.getContext.setAuthentication(authentication)
          "redirect:/active_users"
        case None =>
          logger.error(s"Given country= $countryValue is Invalid")
          "redirect:/unauthorized"
      }

    } catch {
      case e: Throwable =>
        logger.error("Super-Admin SAML2 Authentication error", e)
        "redirect:/unauthorized"
    }
  }

  @RequestMapping(value = Array("/check_session"), method = Array(RequestMethod.GET))
  @ResponseBody
  def checkSession(): ResponseEntity[APIOutput] = {
    Option(SecureUtil.getUsername) match {
      case Some(_) if SecurityContextHolder.getContext.getAuthentication.isAuthenticated => checkSessionResponse(authenticated = true)
      case _ => checkSessionResponse(authenticated = false)
    }
  }

  private def checkSessionResponse(authenticated: Boolean): ResponseEntity[APIOutput] = {
    val apiOutput = new APIOutput()
    apiOutput.setStatus(if (authenticated) IConstants.API_OUTPUT_STATUS_OK else IConstants.API_OUTPUT_STATUS_ERROR)
    new ResponseEntity[APIOutput](
      apiOutput,
      if (authenticated) HttpStatus.OK else HttpStatus.UNAUTHORIZED
    )
  }

  private def resolveAccess(canAccessControlCentreValueOpt: Option[String]): ControlCenterPermission = {
    val isEnabled = envUtil.getProperty("access.control.enabled")
    (isEnabled, canAccessControlCentreValueOpt) match {
      case ("false", _) => ControlCenterPermission.FULL_ACCESS
      case (_, Some(access)) if ControlCenterPermission.getByName(access) != null => ControlCenterPermission.getByName(access)
      case (_, _) => ControlCenterPermission.NO_ACCESS
    }
  }

  private def resolveAccountProvisioningAccess(canModifyAccountProvisioningOpt: Option[String]): Boolean = {
    val isEnabled = envUtil.getProperty("access.control.enabled")
    (isEnabled, canModifyAccountProvisioningOpt) match {
      case ("false", _) => true
      case (_, Some(access)) => Try(access.toBoolean).getOrElse(false)
      case (_, _) => false
    }
  }

  @PostConstruct
  def initializeSftpConfig(): Unit = {
    maxAuthenticationAge = ConfigFactory.parseProperties(envUtil.getProperties).getInt("saml.maxAuthenticationAge")
  }

}

object AuthenticationController {

  case class AuthenticationExceptionWithUsername(username: String, cause: AuthenticationException) extends Exception("Authentication exception", cause)

}
