package me.socure.superadmin.provider

import me.socure.superadmin.authentication.{SamlAttribute, SimpleAuthenticatedToken}
import org.springframework.security.core.context.SecurityContextHolder

object SamlPayloadProvider {

  def provide(): SamlAttribute = {
    val token: SimpleAuthenticatedToken = SecurityContextHolder.getContext.getAuthentication.asInstanceOf[SimpleAuthenticatedToken]
    token.samlAttribute
  }

}
