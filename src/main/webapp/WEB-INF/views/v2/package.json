{"name": "v2", "version": "0.1.0", "private": true, "homepage": "/resources/v2", "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.8", "@mui/icons-material": "^5.15.8", "@mui/material": "^5.15.7", "@mui/x-tree-view": "^7.7.1", "@reduxjs/toolkit": "^2.2.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.79", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "axios": "^1.8.2", "idb": "^8.0.0", "localforage": "^1.10.0", "match-sorter": "^6.3.4", "react": "^18.2.0", "react-diff-viewer": "^3.1.1", "react-dom": "^18.2.0", "react-redux": "^9.1.2", "react-router-dom": "^6.22.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"react-scripts": "^5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && npm run build-rename && npm run build-mv", "build-mv": "rm -rf ../../../resources/v2 && mv -f build '../../../resources/v2'", "build-rename": "yarn run build-rename-js && yarn run build-rename-css", "build-rename-js": "mv ./build/static/js/main*.js ./build/static/js/main.js && mv ./build/static/js/main*.js.map ./build/static/js/main.js.map ", "build-rename-css": "mv ./build/static/css/main*.css ./build/static/css/main.css && mv ./build/static/css/main*.css.map ./build/static/css/main.css.map", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"nth-check": "^2.1.1", "http-proxy-middleware": "^2.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "postcss": "^8.4.38", "react-scripts": {"nth-check": "^2.1.1", "postcss": "^8.4.38"}, "css-select": {"nth-check": "^2.1.1"}, "resolve-url-loader": {"postcss": "^8.4.38"}}, "resolutions": {"http-proxy-middleware": "^2.0.7", "postcss": "^8.4.38", "nth-check": "^2.1.1"}}