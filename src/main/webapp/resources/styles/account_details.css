.email-row .email-editable {
    display: none;
}

.email-row.edit-mode .email-editable {
    display: block;
}

.email-row.edit-mode .email-label {
    display: none;
}

.nowrap {
    white-space: nowrap;
}

.icon {
    padding-left: 3px;
}

span.save-icon:after {
    content: "\2713";
    padding-left: 5px;
    font-size: 20px;
    border: 1px solid #ccc;
    padding-right: 5px;
    border-radius: 3px;
    vertical-align: sub;
}

span.cancel-icon:after {
    content: "\2715";
    padding-left: 5px;
    font-size: 20px;
    border: 1px solid #ccc;
    padding-right: 5px;
    border-radius: 3px;
    vertical-align: sub;
}

span.edit-icon:after {
    content: "\270E";
    padding-left: 5px;
    font-size: 20px;
    border: 1px solid #ccc;
    padding-right: 5px;
    border-radius: 3px;
    vertical-align: sub;
}

.st_container .control_group {
    display: flex;
}

.st_container .control_group input[type=checkbox] {
    margin-right: 5px;
}

#sub-accounts-tree .node circle {
    stroke: steelblue;
    stroke-width: 3px;
}

#sub-accounts-tree path {
    fill: none;
    stroke: #ccc;
    stroke-width: 2px;
}

.file-caption {
    padding-top:15px;
    font-style: italic;
    color:grey;
    position: relative;
    width: 100%;
    padding-right: 15px;
}

.socure-downlaod-button {
    color: #ee8e3c;
    cursor: pointer;
}

.socure-downlaod-button:hover {
    text-decoration: underline;
}

#settings_table {
    table-layout: fixed;
    width: 650px;
}

#settings_table tr td {
    border: 1px solid grey;
    padding: 5px;
    width: 315px;
    overflow-wrap: break-word;
}

.font-orange {
   color: #ee8e3c;
}

#settings_table_dv {
     table-layout: auto;
     width: 650px;
}

#settings_table_dv tr td {
    border: 1px solid grey;
    padding: 5px;
    width: 315px;
    overflow-wrap: break-word;
}

#settings_table_dv tr td:first-child {
    width: 470px;
}