/* Load this script using conditional IE comments if you need to support IE 7 and IE 6. */

window.onload = function() {
	function addIcon(el, entity) {
		var html = el.innerHTML;
		el.innerHTML = '<span style="font-family: \'Dashboard-Icons\'">' + entity + '</span>' + html;
	}
	var icons = {
			'icn-export' : '&#xe001;',
			'icn-print' : '&#xe002;',
			'icn-calendar' : '&#xe003;',
			'icn-check' : '&#xe004;',
			'icn-notch-right' : '&#xe005;',
			'icn-notch-left' : '&#xe006;',
			'icn-notch-down' : '&#xe007;',
			'icn-ridges' : '&#xe008;',
			'icn-fb' : '&#xe009;',
			'icn-tw' : '&#xe00a;',
			'icn-in' : '&#xe00b;',
			'icn-gp' : '&#xe00c;',
			'icn-user' : '&#xe000;',
			'icn-multi-user' : '&#xe00d;'
		},
		els = document.getElementsByTagName('*'),
		i, attr, c, el;
	for (i = 0; ; i += 1) {
		el = els[i];
		if(!el) {
			break;
		}
		attr = el.getAttribute('data-icon');
		if (attr) {
			addIcon(el, attr);
		}
		c = el.className;
		c = c.match(/icn-[^\s'"]+/);
		if (c && icons[c[0]]) {
			addIcon(el, icons[c[0]]);
		}
	}
};