package me.socure.superadmin.validator

import me.socure.control.center.constants.ControlCenterPermission
import me.socure.model.BusinessUserRoles
import me.socure.model.account.AccountInfoWithPermission
import me.socure.service.constants.Countries
import me.socure.superadmin.authentication.SamlAttribute
import org.scalatest.{FreeSpec, Matchers}

class USOnlyAccessValidatorTest extends FreeSpec with Matchers {

  private val accountInfo: AccountInfoWithPermission = AccountInfoWithPermission(
    id = 1L,
    publicId = "acc-l1lIuHEMhB",
    name = "Socure Test Case",
    isInternal = false,
    roles = Set.empty
  )

  "Allow Edit if internal= true" in {
    val attribute: SamlAttribute = SamlAttribute(Countries.UNITED_STATES, ControlCenterPermission.NO_ACCESS, accountProvisioningAccess = true, piiRetentionAccessPermission = "Full Access")
    val internalAccount: AccountInfoWithPermission = accountInfo.copy(isInternal = true)
    USOnlyAccessValidator.canEditAccountInfo(attribute, internalAccount) shouldBe true
  }

  "Allow Edit if internal= true, US_ONLY_ACCESS, Other than US" in {
    val attribute: SamlAttribute = SamlAttribute(Countries.INDIA, ControlCenterPermission.NO_ACCESS, accountProvisioningAccess = true, piiRetentionAccessPermission = "Full Access")
    val internalAccount: AccountInfoWithPermission = accountInfo.copy(isInternal = true, roles = Set(BusinessUserRoles.US_ONLY_ACCESS.id))
    USOnlyAccessValidator.canEditAccountInfo(attribute, internalAccount) shouldBe true
  }

  "Allow Edit if internal= true, US_ONLY_ACCESS, US" in {
    val attribute: SamlAttribute = SamlAttribute(Countries.INDIA, ControlCenterPermission.NO_ACCESS, accountProvisioningAccess = true, piiRetentionAccessPermission = "Full Access")
    val internalAccount: AccountInfoWithPermission = accountInfo.copy(isInternal = true, roles = Set(BusinessUserRoles.US_ONLY_ACCESS.id))
    USOnlyAccessValidator.canEditAccountInfo(attribute, internalAccount) shouldBe true
  }

  "Allow Edit if internal=false" in {
    val attribute: SamlAttribute = SamlAttribute(Countries.INDIA, ControlCenterPermission.NO_ACCESS, accountProvisioningAccess = true, piiRetentionAccessPermission = "Full Access")
    USOnlyAccessValidator.canEditAccountInfo(attribute, accountInfo) shouldBe true
  }

  "Allow Edit if internal=false, US_ONLY_ACCESS, US" in {
    val attribute: SamlAttribute = SamlAttribute(Countries.UNITED_STATES, ControlCenterPermission.NO_ACCESS, accountProvisioningAccess = true, piiRetentionAccessPermission = "Full Access")
    val customerAccount: AccountInfoWithPermission = accountInfo.copy(isInternal=false, roles = Set(BusinessUserRoles.US_ONLY_ACCESS.id))
    USOnlyAccessValidator.canEditAccountInfo(attribute, customerAccount) shouldBe true
  }

  "Don't allow Edit if internal=false, US_ONLY_ACCESS, INDIA" in {
    val attribute: SamlAttribute = SamlAttribute(Countries.INDIA, ControlCenterPermission.NO_ACCESS, accountProvisioningAccess = true, piiRetentionAccessPermission = "Full Access")
    val customerAccount: AccountInfoWithPermission = accountInfo.copy(isInternal=false, roles = Set(BusinessUserRoles.US_ONLY_ACCESS.id))
    USOnlyAccessValidator.canEditAccountInfo(attribute, customerAccount) shouldBe false
  }

}
