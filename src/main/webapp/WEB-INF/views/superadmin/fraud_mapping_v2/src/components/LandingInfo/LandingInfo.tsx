import { FC } from "react";

const LandingInfo: FC = () => {
  return (
    <ul>
      <li>
        While mapping a model to an account, you can select between
        &apos;Primary&apos;, &apos;Sigma&apos;, &apos;Custom&apos; &amp;
        &apos;Shadow&apos;.
      </li>
      <li>Only one primary and one sigma models can be mapped to an account</li>
      <li>Sigma model is returned only on 3.0 calls</li>
      <li>
        Sigma models always have the name &apos;sigma&apos; in the response,
        like generic models have the name &apos;generic&apos; (not in debug
        node)
      </li>
      <li>
        3.0 require minimum one model mapped in the account. It can be any of
        the 3 model types mentioned above.
      </li>
      <li>
        3.0 calls with &apos;Legacy Associations 3.0&apos; calls use
        &apos;generic&apos; model from config if not explicitly mapped through
        super-admin as primary model (not a new change, but worth mentioning)
      </li>
      <li>
        &apos;Sigma&apos; models can only be mapped through super-admin. They
        cannot be configured in microservice-configurations.
      </li>
      <li>
        Shadow model scores will only appear in the debug node and not in the
        response.
      </li>
      <li>
        Filter Criteria can be filled with a configuration following the syntax
        in{" "}
        <a
          href="https://www.notion.so/Model-Filter-Criteria-1431e4e9397380609dacd297f339428c"
          target="_blank"
          rel="noreferrer"
        >
          here
        </a>{" "}
        to use different models under different input conditions. This only
        works for Sigma Identity models and Sigma Synthetic models.
      </li>
    </ul>
  );
};

export default LandingInfo;
