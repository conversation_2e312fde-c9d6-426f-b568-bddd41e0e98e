package me.socure.filter

import me.socure.actionaudit.model.Action
import me.socure.common.random.Random
import org.joda.time.DateTime
import org.scalatest.{FreeSpec, Matchers}

class ProcessFilterTest extends FreeSpec with Matchers {
  "should not audit response body for document download" in {
    val action = randomAction()
    val newAction = ProcessFilter.fixDocDownloadResponseBody(
      requestUri = ProcessFilter.documentsDownloadUri,
      action = action
    )
    newAction shouldBe action.copy(responseData = "")
  }

  "should audit response body when document download endpoint is not called" in {
    val action = randomAction()
    val newAction = ProcessFilter.fixDocDownloadResponseBody(
      requestUri = ProcessFilter.documentsDownloadUri + "abc",
      action = action
    )
    newAction shouldBe action
  }

  private def randomAction(): Action = {
    Action(
      id = Random.nextInt(),
      userId = Random.alphaNumeric(50),
      source = Random.alphaNumeric(50),
      actionName = Random.alphaNumeric(50),
      actionParameters = Random.option(Random.alphaNumeric(50)),
      responseStatus = Random.alphaNumeric(50),
      responseData = Random.alphaNumeric(50),
      originIP = Random.alphaNumeric(50),
      processTime = Random.nextLong(),
      createdAt = Random.option(DateTime.now()),
      accountId = Some("account_id"),
      publicAccountId = Some("public_account_id"),
      details = Some("")
    )
  }
}
