#Spring social configuration
application.secureUrl="https://service-stage.socure.com"
facebook.longlived.accesstoken.url="https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token"
facebook.appid.url="https://graph.facebook.com/app?access_token="

#Mysql db configuration

db.driver="com.mysql.cj.jdbc.Driver"


db.url="**************************************************************************************************************************************************************************************************************************************************************************************"
db.username="socure"
db.password="""ENC(JYaENlfbS9vQRltvrGCHX4bwjlkJHmqgmL1LAdQvenB6g9jIyg5Ik5M=)"""
db.defaultAutoCommit="true"

# Model Management DB config
mmdb.driver="com.amazonaws.secretsmanager.sql.AWSSecretsManagerMySQLDriver"
mmdb.jdbcUrl="jdbc-secretsmanager:mysql://model-management-stage.cluster-cvuthvaf2kni.us-gov-west-1.rds.amazonaws.com:3306/model_management"
mmdb.dataSourceName=model-management-db
mmdb.user="rds-model-management-stage-e294db-app"
mmdb.maxIdleTime=900
mmdb.maxConnectionAge=3600
mmdb.forceUseNamedDriverClass="true"

mmdb.maxPoolSize=50
mmdb.minPoolSize=3
mmdb.initialPoolSize=3
mmdb.testConnectionOnCheckIn="true"
mmdb.testConnectionOnCheckOut="false"
mmdb.idleConnectionTestPeriod=20

mmdb.useNewDB="true"
mmdb.modelIdSize=10

#Hibernate pool
hibernate.c3p0.initialPoolSize=10
hibernate.c3p0.min_size=10
hibernate.c3p0.max_size=25

hibernate.connection.provider_class="org.hibernate.connection.C3P0ConnectionProvider"
hibernate.c3p0.timeout=100
#you need to consider the number of frequently used PreparedStatements in your application, and multiply that by the number of Connections you expect in the pool (maxPoolSize in a busy application)
hibernate.c3p0.max_statements=500
hibernate.c3p0.idle_test_period=90

#Newly added attribute
hibernate.c3p0.unreturnedConnectionTimeout=10
hibernate.c3p0.debugUnreturnedConnectionStackTraces="true"
hibernate.c3p0.acquireincrement=8
hibernate.c3p0.maxconnectionage=3600
hibernate.c3p0.validationQuery="SELECT 1"
hibernate.c3p0.testOnBorrow="true"
hibernate.c3p0.numHelperThreads=200
hibernate.c3p0.automaticTestTable="tbl_hibernate_c3p0TestTable"
hibernate.c3p0.testOnCheckin=true

#Hibernate JConsole
hibernate.generate_statistics="false"

#Memcache Servers
memcache.servers="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com:11211"
opt.timeout=500

#Static resources like js, css, images url from s3 bucket
static.resource.url="/resources"

#Debug Settings
debugMode="false"

#Scoring Cache Properties
#1 day(in seconds)
authscore.memcached.timout=86400
#1 day (in seconds)
authscore.low_confidence.memcached.timeout=86400
#7 days(in seconds)
authscore.high_confidence.memcached.timeout=604800
# The timeout(in days) for the scoring of a user. This is used for the second level cache.
authscore.last_scanned_days.cache.timeout=2


#Facebook throttling mail ID
fb.throttling.mail.to="Engineering <<EMAIL>>"


#Factual Credentials
factual.oauth.key="g1tCPVR41NlU96STdd5kw1LnYzHkCd5aAGoBfY3w"
factual.oauth.secret="GMHuY7NpNcNjnioQCepzhOSqKjynMaSZhLAXrot3"

#Scoring Component Memcache
component.email.memcached.timeout=15552000
component.form.memcached.timeout=15552000
component.fb.memcached.timeout=15552000
component.pipl.email.memcached.timeout=15552000
component.pipl.address.memcached.timeout=15552000
component.gplus.memcached.timeout=15552000
component.twitter.memcached.timeout=15552000
component.fullcontact.memcached.timeout=15552000
component.yahoo.memcached.timeout=15552000

#Scoring Component Database
db.component.email.timeout=15552000
db.component.form.timeout=15552000
db.component.fb.timeout=15552000
db.component.pipl.email.timeout=15552000
db.component.pipl.address.timeout=15552000
db.component.gplus.timeout=15552000
db.component.twitter.timeout=15552000
db.component.fullcontact.timeout=15552000
db.yahoo.memcached.timeout=15552000

#===============Mapquest Credentials===============
mapquest.oauth.key="""ENC(ip4+SjQiSYuUxbUIlF3OKqZ9ZQzhw0vK2hBvVF873BePHdNY21K4ar53uyYNB+IpNIuJHywjQnPPLLLIexvJGg==)"""

#=============TowerData Integration=================
towerdata.apikey="""ENC(ip4+SjQiSYuUxbUIlF3OKqZ9ZQzhw0vK2hBvVF873BePHdNY21K4ar53uyYNB+IpNIuJHywjQnPPLLLIexvJGg==)"""

#==============FullContact Integration===============
fullcontact.apikey="""ENC(ip4+SjQiSYuUxbUIlF3OKqZ9ZQzhw0vK2hBvVF873BePHdNY21K4ar53uyYNB+IpNIuJHywjQnPPLLLIexvJGg==)"""
fullcontact.webhook.base.url="https://stage.socure.com/"

#AWS Profile Image Bucket

#============== Admin Specific Properties =================

application.authScore.baseUrl="https://service.idvidm-stage.webapps.internal.socure.us"

marketing.url="http://www.socure.com/"

application.logoutUrl="http://admin.socure.com.s3-website-us-east-1.amazonaws.com"

application.activation.url="https://admin.socure.com/#/activate"
application.forget.pass.url="https://admin.socure.com/reset_password/"

application.set.pass.url="https://admin.socure.com/#!/reset_password_token"

#static resources


#============== Admin Specific Properties =================

#============== Empire properties ================
#annotation.index = annotation.config

#0.name = activity
#0.factory = sesame

# You can obtain this file at http://inkdroid.org/journal/2009/12/22/hacking-oreilly-rdfa/
#0.files = catalog.rdf

#0.name = Empire-JPA-Repo
#0.factory = sesame
#0.repo=activity
#0.url=http://ec2-107-22-72-245.compute-1.amazonaws.com:8080/openrdf-sesame/
#============== Empire properties ================

#==== Fraud Score Service API =======

fraud.score.service="http://fraudscore-prd1v.elasticbeanstalk.com/fraudscore/generic/20150527"

#============= Entity Image ==============
#In Seconds
cache.image.timeout=86400
#============= Entity Image ==============

#============= Hibernate Search ==============
hibernate.search.fuzzy.threshold=15
#============= Hibernate Search ==============


#===Component Timeout Config=================
component.default.timeout=2
fmval.timeout=2
fcval.timeout=2
yoval.timeout=2
pival.timeout=2
paval.timeout=2
boval.tiemout=2
lival.timeout=2
gpval.timeout=2
fbval.timeout=3
twval.timeout=2
kycofacval.timeout=2

api.v2.scheduler.timeout ="2400"
api.v2.overall.timeout ="3000"
api.system.overall.timeout=3200


#=============== Thread pool configuration ==================
thread.pool.executor.pool.size=400
thread.pool.executor.max.pool.size=700
thread.pool.executor.queue.capacity=0
thread.pool.executor.keep.alive.seconds=1

#=============== Thread pool configuration ==================

future.util.wait.duration=50

trace.authscore.factory="false"

#configure search for depth
#SAEARCH_LEVEL_DEPTH=1
#============= SLA ROLE =====================

component.default.timeout=4

default.sla.fmval.timeout=4
default.sla.fcval.timeout=4
default.sla.yoval.timeout=4
default.sla.pival.timeout=4
default.sla.paval.timeout=4
default.sla.boval.tiemout=4
default.sla.lival.timeout=4
default.sla.gpval.timeout=4
default.sla.fbval.timeout=4
default.sla.twval.timeout=4
default.sla.kycofacval.timeout=4

default.sla.api.v2.scheduler.timeout ="5000"
default.sla.api.v2.overall.timeout ="5400"
default.sla.api.system.overall.timeout=6000

#=======================================

highperformace.sla.fmval.timeout=2
highperformace.sla.fcval.timeout=2
highperformace.sla.yoval.timeout=2
highperformace.sla.pival.timeout=2
highperformace.sla.paval.timeout=2
highperformace.sla.boval.tiemout=2
highperformace.sla.lival.timeout=2
highperformace.sla.gpval.timeout=2
highperformace.sla.fbval.timeout=2
highperformace.sla.twval.timeout=2
highperformace.sla.kycofacval.timeout=2

highperformace.sla.api.v2.scheduler.timeout ="2400"
highperformace.sla.api.v2.overall.timeout ="2800"
highperformace.sla.api.system.overall.timeout=2900

#====================================================

#=============== PIPL Credentials ============
pipl.key="mau3erfkn3ek2qw5j5j4bd89"
pipl.premium.key="""ENC(ip4+SjQiSYuUxbUIlF3OKqZ9ZQzhw0vK2hBvVF873BePHdNY21K4ar53uyYNB+IpNIuJHywjQnPPLLLIexvJGg==)"""
pipl.match.criteria="matching"
pipl.partial.threshold=5
#=============== PIPL Credentials ============

#=============== DSTK ============
data.science.toolkit.url="http://internal-prod-dstk-vpc-*********.us-east-1.elb.amazonaws.com/"
data.science.toolkit.fetch.timeout=1500
data.science.toolkit.fetch.timeunit="MILLISECONDS"
data.science.toolkit.sleep.duration.ms=20
#=============== DSTK ============


#============= Entity Resolution Filter Config =============#
default.er.component.filter.name=0.5
default.er.component.filter.email=0.5
default.er.component.filter.address=0.5
default.er.component.filter.geocode=0.5
default.er.component.filter.companyname=0.5
default.er.component.filter.nationalid=0.5
default.er.component.filter.dob=0.5
default.er.component.filter.mobilenumber=0.5
default.er.component.filter.username=0.5
default.er.component.filter.gender=0.5
default.er.component.filter.image=0.5

#============= Entity Resolution Filter Config =============#

#======App Environment==========#
appEnv="govcloud-stage"
#======App Environment==========#

#======TPAudit Stats========================#
aws.tp.stats.bucket="thirdparty-stats-stage-************-us-gov-west-1"

#=======TPAudit Stats=======================#


#Data Warehouse configuration
dwauditdb.driver="org.postgresql.Driver"
dwauditdb.url="********************************************************************************************"
dwauditdb.username="redshift_2019"
dwauditdb.password="""ENC(ip4+SjQiSYuUxbUIlF3OKqZ9ZQzhw0vK2hBvVF873BePHdNY21K4ar53uyYNB+IpNIuJHywjQnPPLLLIexvJGg==)"""

#============= Super Admin Specific Config =============
#static resources
staticurl.super.admin="/resources"
isexternal.app="false"
#=====================================================

#================= Mail Config =================#
socure.support.email="<EMAIL>"
socure.sales.email="<EMAIL>"
mailgun.apikey="""ENC(mVhjxUdjgfuL9hgTS8Z7hB3Vckdi+Q2p+zv+oEuxpmJTx1c8t7sglh12xp++bbDjxXjHEIk/cWFyZUXTQiShFXW102Y=)"""
mailgun.domain="socure.com"
mailgun.from="Socure Support <<EMAIL>>"
#================= Mail Config =================#
## account service
account.service.url="http://account-service"

#============= Rate Limit Service Configuration =============
rate.limit.service.url="https://socure-rate-limiting-prod.us-east-1.elasticbeanstalk.com"
rate.limit.service.disabled="true"
rate.limit.service.apis.deprecated=false

#============= Datadog config =============#
datadog.series_url="https://app.datadoghq.com/api/v1/series"
datadog.api_key="ENC(mVhjxUdjgfuL9hgTS8Z7hB3Vckdi+Q2p+zv+oEuxpmJTx1c8t7sglh12xp++bbDjxXjHEIk/cWFyZUXTQiShFXW102Y=)"
#============= Datadog config =============#
#============= Mailgun config =============#
mailgun.endpoint="https://api.mailgun.net/v2/socure.com/messages"
mailgun.key="ENC(mVhjxUdjgfuL9hgTS8Z7hB3Vckdi+Q2p+zv+oEuxpmJTx1c8t7sglh12xp++bbDjxXjHEIk/cWFyZUXTQiShFXW102Y=)"
mailgun.domain_name="socure.com"
#============= Datadog config =============#
#============= Model monitoring error reporting =============#
error.reporter.model.monitoring.subject="Model Monitoring Metrics Reporting Failure - Stage"
error.reporter.model.monitoring.from="<EMAIL>"
error.reporter.model.monitoring.to="<EMAIL>"
error.reporter.model.monitoring.cc=""
error.reporter.model.monitoring.bcc=""
#============= Model monitoring error reporting =============#

#============= Model Mapping =============#
h2opredictor.service.url="http://h2o-predictor-service"
h2opredictor.service.endpoint2="http://h2o-predictor-service"
h2omlpredictor.service.url="http://h2o-ml-predictor"
h2omlpredictor.service.endpoint2 = "http://h2o-ml-predictor"
modelmanagement {
  endpoint="http://model-management"
  endpoint2="http://model-management"
  hmac {
    realm = "Socure"
    version = "1.0"
    strength = 512
    secret.refresh.interval = 5000
    aws.secrets.manager.id="model-management/stage/hmac-22ba33"
  }
  enableModelManagement = "true"
}

#============= Audit Actioning =============#
sqsEndpoint="https://sqs.us-gov-west-1.amazonaws.com/"
actionAuditS3BucketName=action-auditing-stage-************-us-gov-west-1
actionAuditQueueName=action-auditing-stage
region="us-gov-west-1"
# batchrun-prod
kmsKey="arn:aws-us-gov:kms:us-gov-west-1:049776172552:key/972b4f9e-b56c-4ea7-a412-5cf234e1629e"

#============= Audit Actioning =============#

s3.transaction_error_bucket="stage-audit-errors-************-us-gov-west-1"

#============= Client specific encryption =============#
# prod-kms-client
client.specific.encryption.aws.access.key="ENC(mVhjxUdjgfuL9hgTS8Z7hB3Vckdi+Q2p+zv+oEuxpmJTx1c8t7sglh12xp++bbDjxXjHEIk/cWFyZUXTQiShFXW102Y=)"
client.specific.encryption.aws.secret.key="ENC(mVhjxUdjgfuL9hgTS8Z7hB3Vckdi+Q2p+zv+oEuxpmJTx1c8t7sglh12xp++bbDjxXjHEIk/cWFyZUXTQiShFXW102Y=)"
client.specific.encryption.encryption.context.account_id.key="socure_account_id"
client.specific.encryption.operation.timeout.ms=20000
client.specific.encryption.kms.ids.us-east-1="arn:aws-us-gov:kms:us-gov-west-1:************:alias/socure/client-specific-encryption-stage"
client.specific.encryption.kms.ids.us-west-1="arn:aws-us-gov:kms:us-gov-west-1:************:alias/socure/client-specific-encryption-stage"
#============= Client specific encryption =============#

#============= Image auditing bucket configuration =============#
idplus.audit.image.bucket="idplus-audit-stage"
idplus.audit.image.encryption.mode="aws:kms"
idplus.audit.image.kms.id="arn:aws-us-gov:kms:us-gov-west-1:************:key/972b4f9e-b56c-4ea7-a412-5cf234e1629e"
#============= Image auditing bucket configuration =============#

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {
    maxRetries = 10

    primary {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-stage-************-us-gov-west-1"
      }
      third-party {
        region=us-gov-west-1
        bucket="thirdparty-stats-stage-************-us-gov-west-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

#================ SP metadata endpoint ================#
saml2.sp.metadata {
  endpoint = "http://authentication-service"
  path = "ENC(dz6wLN9mHF3tFf27POfKdJNMrKNu55kbyigEqsN5MyijhGy6hm1yJFbV25ovA4yryZ9IPqKNvS6e4dx8WIklDQ==)"
  token = "ENC(dz6wLN9mHF3tFf27POfKdJNMrKNu55kbyigEqsN5MyijhGy6hm1yJFbV25ovA4yryZ9IPqKNvS6e4dx8WIklDQ==)"
}
#================ SP metadata endpoint ================#

transaction.auditing {
  endpoint = "http://transaction-auditing-service"
  endpoint2 = "http://transaction-auditing-service"

  hmac {
    secret.key="ENC(fMDAyyhC6RcP5WixuuDNUlXFbAKTxf7/3dEPBx2MV9F5Gx3BLVCsa0LWuH+5KjTnJjsJET9mhba+)"
    strength=512
    realm="Socure"
    version = "1.0"
  }
}

#================ File storage download config ================#
file.storage.download {
  endpoint = "http://file-storage-download"

    dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-gov-west-1"
      kms.key="""ENC(mnQUY+4TqGVwR7GqU4LGDVHUwbZCYF2c8Ufmfk/+3pK7tZUbr7adOIhvsXwROcq9AMDChRMtd40QemEktg3J/ugSbVn8fV5S0iOmDvbDIemvSMhqKkF7jTlG5I7BynIYbMwUs5aUjl1KWdcvdwUuX2XFROz2GA==)"""
    }
    memcached {
      host="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    secret.key = "ENC(sFpOcvrW1/fGdriOx2NPOrWcDiU8UQAJ1Zc6mQ30WpvV3Ia4IJRB6R12qxmBbnx8HGGwGmYkUC7gr2y9TsV2zA==)"
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
}
#================ File storage download config ================#

#================ AlertList Maintainer =================#
alertlist.maintainer {
  endpoint = "http://alertlist-maintainer"
  hmac {
    secret.key = "ENC(P+ujKznlxV+AeHrbPDfvVqcxxJ0GXGj4GjcWMLH5lt02VKVMozmnvxszz7o2IR07xhvzW3oHcq40eQ==)"
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ AlertList Maintainer =================#

#================ SAML ================#
saml {
  assertionConsumerServiceUrl = "https://superadmin.webapps.idvidm-stage.internal.socure.us/saml2/SSO"
  maxAuthenticationAge = 43200 # 12 hours
}
#================ SAML ================#
rehearsal.configurator {
  endpoint="https://rehearsal-configurator-rehearsal-prod.us-east-1.elasticbeanstalk.com"
  hmac {
    secret.key="ENC(dz6wLN9mHF3tFf27POfKdJNMrKNu55kbyigEqsN5MyijhGy6hm1yJFbV25ovA4yryZ9IPqKNvS6e4dx8WIklDQ==)"
    strength=512
    realm="Socure"
    version = "1.0"
  }
}

authentication.service {
    endpoint = "http://authentication-service"
    endpoint2="http://authentication-service"
    groupName="UXServicesRamp"
    flagName="AuthenticationService_Ramp"

    hmac {
      secret.key="ENC(1muRgZZxItth88bfzxbg5CBXRMZ1IiI7FRBSbhZVq/pg0yNsiRrOV5+h60jxvL/GRWrfH3mempgJ2XywG4uLSA==)"
      strength=512
      realm="Socure"
      version = "1.0"
    }
}

#================ Account Service config ================#
account.service {
  endpoint="http://account-service"
  endpoint2="http://account-service"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"

  hmac {
    secret.key="ENC(9XDolTgXmvchUH6JLZe1uPa1xemvTPFrkd1hU1q3wSlxntaxuL875MdQYJCiAUWRtii/m90moJ4=)"
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ Account Service config ================#

#================ Reasoncode Service config ================#
reasoncode.service {
  endpoint="http://reasoncode-service"
  hmac {
    secret.key="ENC(Cq1trjR7j3REaWGjpUyYzqyD0aZrzdcGLk/qdaYrkPR8BQJASpgbh084MpsNZqRM+g0sEeTkdJAdxsxw/xeUzA==)"
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ Reasoncode Service config ================#

#================ Event Auditing Service =================#
event.auditing{
  security{
    endpoint = "http://event-auditing-service/api"
    hmac {
      realm="Socure"
      version=1.0
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="event-auditing/stage/hmac-7c324c"
      secret.refresh.interval=5000
    }
  }
}

#================ Event Auditing Service =================#

#================ Batch Job Service ================#

batch.job {
  endpoint="http://batch-job-service"
  endpoint2="http://batch-job-service"
  groupName="UXServicesRamp"
  flagName="BatchJobService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-gov-west-1"
      kms.key="""ENC(mnQUY+4TqGVwR7GqU4LGDVHUwbZCYF2c8Ufmfk/+3pK7tZUbr7adOIhvsXwROcq9AMDChRMtd40QemEktg3J/ugSbVn8fV5S0iOmDvbDIemvSMhqKkF7jTlG5I7BynIYbMwUs5aUjl1KWdcvdwUuX2XFROz2GA==)"""
    }
    memcached {
      host="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "batch-job-service/stage/hmac-5171f919cc"
    secret.refresh.interval = 5000
    realm="Socure"
    version = "1.0"
  }
}

#================ Batch Job Service ================#

#================ Rulecode Service config ================#
rulecode.service {
  endpoint = "http://rulecode-service"
  endpoint2 = "http://rulecode-service"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="rulecode-service/stage/hmac-514a56"
    secret.refresh.interval=5000
  }
}
#================ Rulecode Service config ================#
#=================Rulecode Ingestion config =============#
rulecode.ingestion{
    aws{
        s3{
            bucketname="rulecode-audit-stage-************-us-gov-west-1"
            kmsKey="arn:aws-us-gov:kms:us-gov-west-1:049776172552:key/972b4f9e-b56c-4ea7-a412-5cf234e1629e"
        }
    }
}
#=================Rulecode Ingestion config =============#
#=================Rulecode Studio config =============#

rulecode.studio {
    git{
        projectId = "235"
        access_token = "ENC(dz6wLN9mHF3tFf27POfKdJNMrKNu55kbyigEqsN5MyijhGy6hm1yJFbV25ovA4yryZ9IPqKNvS6e4dx8WIklDQ==)"
        rulecodePath = "rulecode_definitions/"
        rulecodePathV2 = "rulecode_definitions_v2/"
        dependencyPath = "table_definitions/"
        branchName = "master"
        baseUrl = "https://gitlab-ee.us-east-vpc.socure.be/api/v4/projects/"
    }
   s3{
        audit{
            bucketname="rulecode-audit-stage-************-us-gov-west-1"
            kmsKey="arn:aws-us-gov:kms:us-gov-west-1:049776172552:key/972b4f9e-b56c-4ea7-a412-5cf234e1629e"
            rulecodePath = "studio/rulecode_definitions/"
            rulecodePathV2 = "studio/rulecode_definitions_v2/"
            dependencyPath = "studio/table_definitions/"
        }
        config{
            bucketname="rulecode-config-stage-************-us-gov-west-1"
            rulecodePath = "rulecode_definitions/"
            rulecodePathV2 = "rulecode_definitions_v2/"
            dependencyPath = "table_definitions/"
        }
        data{
            bucketname="rulecode-data-stage-************-us-gov-west-1"
            bulkFilePath = "rulecode_create_data/"
        }
   }
   test{
        envUrl = "https://service.webapps.idvidm-stage.internal.socure.us"
        inputBucketname="rulecode-config-stage-************-us-gov-west-1"
        outputBucketname="batch-job-storage-stage-************-us-gov-west-1"
        prefix = "studio-test/"
   }
}
#=================Rulecode Studio config =============#

#=================Document Manager config =============#
document.manager {
  document.manager {
    endpoint="http://document-manager"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager-service/stage/hmac-809038"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
      port=11211
    }
  }

  dynamic.control.center {
    s3 {
      bucketName="globalconfig-************-us-gov-west-1"
      kms.key="""ENC(mnQUY+4TqGVwR7GqU4LGDVHUwbZCYF2c8Ufmfk/+3pK7tZUbr7adOIhvsXwROcq9AMDChRMtd40QemEktg3J/ugSbVn8fV5S0iOmDvbDIemvSMhqKkF7jTlG5I7BynIYbMwUs5aUjl1KWdcvdwUuX2XFROz2GA==)"""
    }
    memcached {
      host="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }
}
#=================Document Manager config =============#
#=================Decision Service Config===============#
decision.service {
  endpoint = "http://decision-service"
  endpoint2="http://decision-service"
  groupName="UXServicesRamp"
  flagName="DecisionService_Ramp"
}
#=================Decision Service Config===============#
#=================Sandbox Service Config===============#
sandbox.service {
  endpoint = "http://idplus-sandbox"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="idplus-sandbox/stage/hmac-e0ac25/query/ba"
    secret.refresh.interval=5000
  }
}
#=================Sandbox Service Config===============#

#=================StepUp Service=====================#
stepUp.service {
  endpoint="http://step-up-service"
  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "step-up-service-service/stage/hmac-a723e4"
    secret.refresh.interval = 5000
    realm = "Socure"
    version = "1.0"
  }
}
#=================StepUp Service=====================#
#=================Docv Orchestra Service=====================#
 docvOrchestra {
     endpoint = "http://document-orchestra"
     hmac {
        realm="Socure"
        version = "1.0"
        strength=512
        aws.secrets.manager.id="docv-orchestra-service/stage/hmac"
      }
      metrics.enabled = false
  }
#=================Docv Orchestra Service=====================#

#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "idplus-global-settings-************-us-gov-west-1"
    kms.key = "arn:aws-us-gov:kms:us-gov-west-1:049776172552:key/972b4f9e-b56c-4ea7-a412-5cf234e1629e"
  }
  memcached {
    host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
    port=11211
    ttl="24 hours"
  }
}

#===================Control Center==========================#

#===================batchjob==========================#
batch.job {
  aws {
    s3 {
      bucket.name = "batch-job-storage-stage-************-us-gov-west-1"
      kms.key = """ENC(a8IAMnrMm69qU3RtcqPfh49QbL2K6nUGx8rS3LV7RPdxOt1zAc/TSwtz9x9U1V/V4ZaW9gWlLjLEdgV0S/64Di6ZM7O8ilT+51l4WCseEKun3UjZDbvDtClIsJ1qqwDfSK6xlGN6IZlDjyKXgwjPfTJ1tnmmZA==)"""
    }
  }
}

#===================batchjob==========================#

#===================Dynamic Control Center==========================#

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-************-us-gov-west-1"
    kms.key="""ENC(mnQUY+4TqGVwR7GqU4LGDVHUwbZCYF2c8Ufmfk/+3pK7tZUbr7adOIhvsXwROcq9AMDChRMtd40QemEktg3J/ugSbVn8fV5S0iOmDvbDIemvSMhqKkF7jTlG5I7BynIYbMwUs5aUjl1KWdcvdwUuX2XFROz2GA==)"""
  }
  memcached {
    host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

#================ ThirdParty Reader Service config ================#
thirdparty.reader.service {
  endpoint = "http://thirdparty-reader-service"

  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="thirdparty-reader-service/stage/hmac-102ad631"
    secret.refresh.interval=5000
  }
}
#================ ThirdParty Reader Service config ================#

#===================sftp==========================#
sftp {
 role="arn:aws:iam::112942558241:role/socure-transfer-deliveries-users"
 homeDirectoryType="LOGICAL"
 region="us-gov-west-1"
 serverId="s-1edb1c1a1fc442da8"
 target="/client-datafile-prod/"
 entry="/"
}
#===================sftp==========================#
idm {
  tenant.management {
    endpoint = "https://ingress-private.us-east-1.pbls-prod.socure.link"
  }
}

txn.case.workflow.service {
  endpoint = "http://event-auditing-service"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "event-auditing/stage/hmac-7c324c"
    secret.refresh.interval = 5000
  }
}

globalwatchlist.service {
  endpoint = "http://watchlist-private-service/api"
  endpoint2="http://watchlist-private-service/api"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="watchlist-private/stage/hmac-424796"
    secret.refresh.interval=5000
  }
}

entity.feedback.reader {
  endpoint = "http://event-auditing-service"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "event-auditing/stage/hmac-7c324c"
    secret.refresh.interval = 5000
  }
}

#===================docv-berbix==========================#
berbix.v1 {
   	endpoint="http://docv-bb-backend"
    hmac {
        secret.key="""ENC(s53ZfwobOqXcWVnQa/SScReNIVQ2N0JS/yLoFUGnhwyUJ1Gw/eVdXWokKJJ4qHi/+oCPDeDAdloj2SCxhBfYFJY2t0gcDbwOfEdv39nieEIPRV3T/FA=)"""
        strength=512
        realm="Socure"
        version="1.0"
    }
}

#Recaptcha Configuration
recaptcha.privatekey = "6LfGEwgTAAAAAFNaWC9Wwumw-HBqioJceicBR5s_"
recaptcha.publickey = "6LfGEwgTAAAAAPithJiufWOSfCifzwKN28Rgb_Yq"

cors {
    alloweddomains = ["https://superadmin.webapps.idvidm-stage.internal.socure.us/"]
}
launchdarkly {
  sdk.key = "missing_not_certified_yet"
  use.fedramp.version = false
}

kyc {
  endpoint = ""
  recordcorrection.endpoint = ""
}
