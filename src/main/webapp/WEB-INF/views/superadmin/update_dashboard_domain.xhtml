<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
<link rel="stylesheet" href="resources/styles/socure-dashboard.css" ></link>
<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
<script src="#{bucket.resourceURL}/scripts/jquery.min.js" ></script>
<script src="#{bucket.resourceURL}/scripts/bootstrap.min.js"></script>
<meta charset="UTF-8"/>
<title>Add Dashboard Domain(s)</title>

<script>
    bucketurl = "#{bucket.resourceURL}";
    contexturl = "#{request.contextPath}";
    marketingurl = "#{bucket.marketingSite}";
    loggedinUsername = "#{loggedinUsername}";
    appenv = "#{bucket.appEnv}";
</script>

<script language="javascript">
    window.onload = function() {
      if(DD_RUM) {
        DD_RUM.startView({name: "/update_dashboard_domain"});
        DD_RUM.setUser({ "id": loggedinUsername});
      }
    }
	$(function(){
		$('#updatedomain').click(function() {
            var accountId =$('#emailLists').val();
            var newdomains = $('#domainlist').val();

            if(accountId != "NONE") {
                var dat = {
                    accountid: accountId,
                    domain: newdomains
                };

                if (newdomains == null || newdomains.trim() == "") {
                    alert('Please keyin domain to update');
                } else {
                    $('#resetpasswordresult').text("Updating...");
                    $.ajax({
                        url: "superadmin/1/add_dashboard_domain_to_list",
                        data: dat,
                        type: 'POST',
                        success: function (data) {
                            if (data == 1) {
                                $('#resetpasswordresult').text("Domain is Updated Sucessfully!");
                            }
                            $('#domainlist').val("");
                        }

                    });
                    DD_RUM.track("Update Dashboard Domains", { accountId: accountId, newDomain: newdomains });
                }
            } else {
                alert('Please select email');
            }
        });
	});

    $(function(){
       $('#emailLists').change(function(){
           var accountId = $('#emailLists').val();
           if(accountId != "NONE"){
               var dat = {
                   accountid: accountId
               };

               $.ajax({
                   url: "superadmin/1/get_dashboard_domain_to_list",
                   data: dat,
                   type: 'GET',
                   success: function (data) {
                       $('#domainlist').val(data);
                   }
               });
           }
       });
    });
	
</script>

</h:head>
<h:body>

<div align="center">
		<font color="blue"><h3>
				<h:outputText value="#{param['message']}" />
			</h3> </font>
	</div>
	<br/>
	<br/>
	<div align="right">
	   <h5>
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		
		<h:outputLink value="logout">Logout</h:outputLink>
		&nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
	</div>
	<br/>
	<br/>
	
      <center><h3>Add Dashboard Domains</h3></center>
     <hr/>
            <center><b><div id="resetpasswordresult"></div></b></center>
            <div class="container">
                <div class="row">
                    <div class="span12">
							Select Account Email&nbsp;&nbsp;
						   <select id="emailLists">
						   <option value="NONE"> -- Select Email -- </option>
						   <ui:repeat var="acc" value="#{accountname}" varStatus="status">
								<option value="#{acc.id}">#{acc.id} - #{acc.name}</option>
						   </ui:repeat>

							 </select>
							 <div>
								Add New Domain(s) &nbsp; <input id="domainlist" type="text"/>
							 </div>
							 <input class="btn btn-primary" type="button" value="Update" id="updatedomain" />
                    </div>
                </div>
            </div>
</h:body>
</html>