package me.socure.sandbox

import com.typesafe.config.{Config, ConfigFactory}
import me.socure.sandbox.common.SandboxServiceClient
import me.socure.sandbox.common.factory.SandboxServiceClientFactory
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}

import scala.concurrent.ExecutionContext

class SandboxServiceClientFactory extends FactoryBean[SandboxServiceClient] {

  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  override def getObject: SandboxServiceClient = {
    val config: Config  = ConfigFactory.parseProperties(envUtil.getPropertiesPlain("sandbox.service"))
    SandboxServiceClientFactory.create(config)(executionContext)
  }

  override def isSingleton: Boolean = true

  override def getObjectType: Class[SandboxServiceClient] = classOf[SandboxServiceClient]
}
