{"compilerOptions": {"baseUrl": "src", "module": "esnext", "moduleResolution": "node", "outDir": "./dist/build", "target": "es5", "lib": ["dom", "es2015", "es2016.array.include", "es2017.string", "ESNext", "dom.iterable"], "jsx": "react", "allowJs": true, "resolveJsonModule": true, "downlevelIteration": true, "esModuleInterop": true, "experimentalDecorators": true, "sourceMap": true, "noUnusedLocals": true, "strict": true, "skipLibCheck": true, "types": ["jest"], "paths": {"actions": ["actions"], "components": ["components"], "pages": ["pages"], "services": ["services"], "store": ["store"], "reducers": ["reducers"], "types": ["types"], "utils": ["utils"]}}, "exclude": [".yarn", "**/node_modules", "public/dist", "__coverage__"], "include": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"]}