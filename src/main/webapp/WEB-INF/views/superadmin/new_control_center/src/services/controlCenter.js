import axios from "axios";

/*
For testing locally
prefix = '/socure-super-admin';
*/
// const prefix = '/socure-super-admin';
const prefix = '';

export const getConfigList = () => {
  return axios.get(`${prefix}/superadmin/dynamic_control_center/v2/list`)
    .then(response => response.data)
    .catch(err => Promise.reject(err));
}

export const uploadConfig = (data) => {
  return axios.post(`${prefix}/superadmin/dynamic_control_center/v2/upload`, data)
    .then(response => response.data)
    .catch(err => Promise.reject(err));
}

export const downloadConfig = (name) => {
  return axios.get(`${prefix}/superadmin/dynamic_control_center/v2/download?configName=${encodeURIComponent(name)}`)
    .then(response => response.data)
    .catch(err => Promise.reject(err));
}
