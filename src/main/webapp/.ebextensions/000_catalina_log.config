files:
    "/etc/rsyslog.d/catalina.conf":
        mode: "0655"
        owner: root
        group: root
        content: |
            #redirect tomcat logs to /var/log/tomcat/catalina.out discarding timestamps since the messages already have them
            template(name="catalinalog" type="string"
                string="%msg%\n")
            if $programname  == 'server' then {
              *.=warning;*.=err;*.=crit;*.=alert;*.=emerg /var/log/tomcat/catalina.out;catalinalog
              *.=info;*.=notice /var/log/tomcat/catalina.out;catalinalog
             }
commands:
    restart_rsyslog:
        command: systemctl restart rsyslog && touch /var/log/tomcat/catalina.out &&  chmod 644 /var/log/tomcat/catalina.out
