package me.socure.decision

import java.io.File
import java.util
import com.google.common.base.Strings
import me.socure.common.json.bean.APIOutput
import me.socure.decision.service.common.model.DecisionSimulateRequest
import me.socure.decision.service.common.models.v2.{BulkAddLogicRequest, DecisionLogicStates, DecisionLogicWithMappings}
import me.socure.decision.service.common.{AddLogicRequest, ListLogicResponse, MarkAsLiveLogicRequest}
import me.socure.resolver.DecisionServiceResolver
import me.socure.service.constants.IConstants
import me.socure.superadmin.authentication.UserDetailsWithoutCredentials
import me.socure.superadmin.factory.ObjectMapperFactory
import org.apache.commons.io.{FileUtils, IOUtils}
import org.apache.commons.lang3.StringUtils
import org.joda.time.DateTime
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.FileSystemResource
import org.springframework.http.{HttpHeaders, HttpStatus, ResponseEntity}
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{PathVariable, RequestMapping, RequestMethod, RequestParam}
import org.springframework.web.multipart.MultipartFile

import scala.collection.JavaConversions
import scala.collection.JavaConverters._
import scala.io.Source


@Controller
@RequestMapping(Array("/superadmin/1/decision_logic/*"))
class DecisionLogicController {

  val logger = LoggerFactory.getLogger(getClass)

  val objectMapperFactory = ObjectMapperFactory.get()

  @Autowired
  var decisionServiceResolver : DecisionServiceResolver = _

  @Deprecated
  @RequestMapping(value = Array("{accountId}/revision"), method = Array(RequestMethod.GET))
  def getAllRevisions(@PathVariable("accountId") accountId : Long): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = decisionServiceResolver.listLogic(accountId.toString)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    result match {
      case Right(response) => {
        var javaObject = new util.ArrayList[DecisionLogicRevision]()
        response.foreach(x => javaObject.add(getListLogicAsJava(x)));
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(javaObject);
        new ResponseEntity(apiOutput, headers, HttpStatus.OK)
      }
      case Left(error) => {
        logger.error("Error fetching all the revisions", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity(apiOutput, headers, HttpStatus.INTERNAL_SERVER_ERROR)
      }
    }
  }

  def getListLogicAsJava(eachData : ListLogicResponse) : DecisionLogicRevision = {
    val decisionLogicRevision = new DecisionLogicRevision()
    if (eachData.isLive.isDefined) decisionLogicRevision.setLive(eachData.isLive.get)
    decisionLogicRevision.setModelName(eachData.modelName)
    decisionLogicRevision.setModelVersion(eachData.modelVersion)
    decisionLogicRevision.setRevision(eachData.revision)
    decisionLogicRevision.setTag(eachData.tag)
    decisionLogicRevision
  }

  @RequestMapping(value = Array("{accountId}/revision"), method = Array(RequestMethod.POST))
  def createNewRevision(@PathVariable("accountId") accountId : Long,
                        @RequestParam("logic") multipartFile: MultipartFile,
                        @RequestParam("modelName") modelName: String,
                        @RequestParam("modelVersion") modelVersion: String,
                        @RequestParam("tag") tag: String,
                        @RequestParam("isLive") isLive: Boolean,
                        @RequestParam("isDetailsEnabled") isDetailsEnabled: Boolean): ResponseEntity[Object] = {

    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]

    val addLogicRequest = AddLogicRequest(accountId = accountId,
      modelName = modelName,
      modelVersion = modelVersion,
      tag = tag,
      isLive = Some(isLive),
      logic = Source.fromInputStream(multipartFile.getInputStream).mkString,
      emailId = userDetails.getUsername,
      decisionDetailsEnabled = Some(isDetailsEnabled)
    )

    val apiOutput = new APIOutput()
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    decisionServiceResolver.addLogic(addLogicRequest) match {
      case Right(response) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response);
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(apiOutput), headers, HttpStatus.OK)
      }
      case Left(error) => {
        logger.error("Error creating revision", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(apiOutput), headers, HttpStatus.INTERNAL_SERVER_ERROR)
      }
    }
  }

  @RequestMapping(value = Array("{accountId}/revision/{revision}/mark-as-live"), method = Array(RequestMethod.GET))
  def markAsLive(@PathVariable("accountId") accountId: Long,
                 @PathVariable("revision") revision: Long): ResponseEntity[Object] = {
    val markAsLiveLogicRequest = MarkAsLiveLogicRequest(accountId = accountId, revision = revision)
    val apiOutput = new APIOutput()
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    decisionServiceResolver.markAsLive(markAsLiveLogicRequest) match {
      case Right(response) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response);
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(apiOutput), headers, HttpStatus.OK)
      }
      case Left(error) => {
        logger.error("Error marking the revision live", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(apiOutput), headers, HttpStatus.INTERNAL_SERVER_ERROR)
      }
    }
  }

  @RequestMapping(value = Array("{accountId}/simulate/csv"), method = Array(RequestMethod.POST))
  def simulateCsv(@PathVariable("accountId") accountId : Long,
                  @RequestParam(value = "inputCSV", required = false) multipartFile: MultipartFile,
                  @RequestParam(value = "revision", required = false) revision: String): ResponseEntity[Object] = {
    val simulationRequest = DecisionSimulateRequest(accountId = accountId,
      revision = if(Strings.isNullOrEmpty(revision)) None else Some(revision.toLong),
      testFile = Some(multipartFile.getInputStream),
      trxEndDate = None,
      trxStartDate = None,
      trxMaxCount = None,
      decision = None)
    decisionServiceResolver.simulate(simulationRequest) match {
      case Left(error) => {
        logger.error("Error testing revision", error)
        val headers = new HttpHeaders()
        headers.add("Content-Type", "application/json")
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(error.message), headers, HttpStatus.valueOf(error.code))
      }
      case Right(res) => {
        val targetFile = File.createTempFile("decision_logic", "tmp")
        FileUtils.copyInputStreamToFile(res, targetFile)
        val headers = new HttpHeaders()
        headers.add("Content-Type", "application/octet-stream")
        headers.add("Content-Disposition", "attachment; filename=output.csv")
        new ResponseEntity(new FileSystemResource(targetFile), headers, HttpStatus.OK)
      }
    }

  }

  @RequestMapping(value = Array("{accountId}/simulate/pasttransactions"), method = Array(RequestMethod.POST))
  def simulatePastTransactions(@PathVariable("accountId") accountId : Long,
                               @RequestParam(value = "revision", required = false) revision: String,
                               @RequestParam(value = "transactionsCount", required = false) transactionsCount: Long,
                               @RequestParam(value = "transactionsStartDate", required = false) transactionsStartDate: String,
                               @RequestParam(value = "transactionsEndDate", required = false) transactionsEndDate: String,
                               @RequestParam(value = "decision", required = false) decision: String
                             ): ResponseEntity[Object] = {

    val simulationRequest = DecisionSimulateRequest(accountId = accountId,
      revision = if(Strings.isNullOrEmpty(revision)) None else Some(revision.toLong),
      testFile = Some(IOUtils.toInputStream(StringUtils.EMPTY)),
      trxEndDate = if (Strings.isNullOrEmpty(transactionsEndDate)) None else Some(DateTime.parse(transactionsEndDate)),
      trxStartDate = if (Strings.isNullOrEmpty(transactionsStartDate)) None else Some(DateTime.parse(transactionsStartDate)),
      trxMaxCount = Some(transactionsCount.toInt),
      decision = if (Strings.isNullOrEmpty(decision)) None else Some(decision))
    decisionServiceResolver.simulate(simulationRequest) match {
      case Left(error) => {
        logger.error("Error testing revisions", error)
        val headers = new HttpHeaders()
        headers.add("Content-Type", "application/json")
        new ResponseEntity[Object](objectMapperFactory.writeValueAsString(error.message), headers, HttpStatus.valueOf(error.code))
      }
      case Right(file) => {
        val targetFile = File.createTempFile("decision_logic", "tmp")
        FileUtils.copyInputStreamToFile(file, targetFile)
        val headers = new HttpHeaders()
        headers.add("Content-Type", "application/octet-stream")
        headers.add("Content-Disposition", "attachment; filename=output.csv")
        new ResponseEntity(new FileSystemResource(targetFile), headers, HttpStatus.OK)
      }
    }

  }

  @RequestMapping(value = Array("v2/list/{accountId}"), method = Array(RequestMethod.GET))
  def getAllLogics(@PathVariable("accountId") accountId : Long, @RequestParam(value = "envId", defaultValue = "1") envId : Int): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = decisionServiceResolver.listLogicByAccountV2(accountId.toString, envId)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    result match {
      case Right(response) =>
        val javaObject = new util.ArrayList[DecisionLogicV2]()
        response.logics.foreach(x => javaObject.add(getListLogicAsJava(x)));
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(javaObject);
        new ResponseEntity(apiOutput, headers, HttpStatus.OK)
      case Left(error) =>
        logger.error("Error fetching all the logics", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity(apiOutput, headers, HttpStatus.BAD_REQUEST)
    }
  }

  def getListLogicAsJava(eachData : DecisionLogicWithMappings) : DecisionLogicV2 = {
    val decisionLogicV2 = new DecisionLogicV2()
    val mappings = eachData.mappings.map { m =>
      s"${m.eventType.name}${if (m.eventSubType.id == 0) "" else s" - ${m.eventSubType.name}"}"
    }
    if (eachData.state == DecisionLogicStates.ACTIVE.id) decisionLogicV2.setLive(true)
    if(eachData.id.isDefined) decisionLogicV2.setId(eachData.id.get)
    decisionLogicV2.setModelName(eachData.modelName)
    decisionLogicV2.setModelVersion(eachData.modelVersion)
    decisionLogicV2.setRevision(eachData.revision)
    if(eachData.tag.isDefined) decisionLogicV2.setTag(eachData.tag.get)
    if(eachData.version.isDefined) decisionLogicV2.setVersion(eachData.version.get)
    decisionLogicV2.setCreatedAt(eachData.createdAt.getMillis)
    decisionLogicV2.setCreatedBy(eachData.createdBy)
    decisionLogicV2.setLastUpdatedAt(eachData.lastUpdatedAt.getMillis)
    decisionLogicV2.setLastUpdatedBy(eachData.lastUpdatedBy)
    decisionLogicV2.setEventMappings(mappings.asJava)
    decisionLogicV2
  }

  @RequestMapping(value = Array("v2/logic/{logicId}"), method = Array(RequestMethod.GET))
  def getLogic(@PathVariable("logicId") logicId : Long, @RequestParam("accountId") accountId : Long, @RequestParam(value = "environmentTypeId", defaultValue = "1") environmentTypeId : Int): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = decisionServiceResolver.getLogicV2(logicId, accountId, environmentTypeId)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    result match {
      case Right(response) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response);
        new ResponseEntity(apiOutput, headers, HttpStatus.OK)
      case Left(error) =>
        logger.error("Error fetching the logic", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity(apiOutput, headers, HttpStatus.BAD_REQUEST)
    }
  }

  @RequestMapping(value = Array("v2/logicTemplate/{logicId}"), method = Array(RequestMethod.GET))
  def getLogicTemplate(@PathVariable("logicId") logicId : Long, @RequestParam("accountId") accountId : Long, @RequestParam(value = "environmentTypeId", defaultValue = "1") environmentTypeId : Int): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = decisionServiceResolver.getLogicTemplateV2(logicId, accountId, environmentTypeId)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    result match {
      case Right(response) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response)
        new ResponseEntity(apiOutput, headers, HttpStatus.OK)
      case Left(error) =>
        logger.error("Error fetching the logic", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity(apiOutput, headers, HttpStatus.BAD_REQUEST)
    }
  }

  @RequestMapping(value = Array("v2/logic/bulk"), method = Array(RequestMethod.POST))
  def createBulkRevision(@RequestParam("accountIds") accountIds : String,
                       @RequestParam("envIds") envIds : String,
                       @RequestParam("logic") multipartFile: MultipartFile,
                       @RequestParam("modelName") modelName: String,
                       @RequestParam("modelVersion") modelVersion: String,
                       @RequestParam("tag") tag: String,
                       @RequestParam("isLive") isLive: Boolean): ResponseEntity[Object] = {

    val accounts = accountIds.split(",").map(id => id.toLong).toSeq
    val envs = envIds.split(",").map(id => id.toInt).toSeq
    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val bulkAddLogicRequest = BulkAddLogicRequest(accounts, envs, modelName, modelVersion,
      Source.fromInputStream(multipartFile.getInputStream).mkString, userDetails.getUsername, isLive, Some(tag))
    val result = decisionServiceResolver.addBulkLogic(bulkAddLogicRequest)
    val apiOutput = new APIOutput()
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    result match {
      case Right(response) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(response)
        new ResponseEntity(apiOutput, headers, HttpStatus.OK)
      case Left(error) =>
        logger.error("Error adding logic", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity(apiOutput, headers, HttpStatus.BAD_REQUEST)
    }
  }

  @RequestMapping(value = Array("v2/logic/{logicId}/activate"), method = Array(RequestMethod.POST))
  def activateLogic(@PathVariable("logicId") logicId : Long): ResponseEntity[Object] = {

    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val result = decisionServiceResolver.activateLogic(logicId, userDetails.getUsername)
    val apiOutput = new APIOutput()
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    result match {
      case Right(response) =>
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        new ResponseEntity(apiOutput, headers, HttpStatus.OK)
      case Left(error) =>
        logger.error("Error activating logic", error)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
        new ResponseEntity(apiOutput, headers, HttpStatus.BAD_REQUEST)
    }
  }

}
