const JOBTYPES = {
    Account_Administration: 22,
    Replay_Transaction: 28,
    Replay_Intelligence_Unified_Entity: 30,
    EXPORT_EVENT_AUDIT: 21,
    CIP_Report: 34,
    MVB_Report: 35,
    DECISION_MIGRATION:42,
    UNENROLL_WATCHLIST_MONITORS:130
 };
Object.freeze(JOBTYPES);

var isFileSelected = false;
var currentPage = 1;
var size = 10;
var jobType = JOBTYPES.Account_Administration;

var rowCount = 1;

var newDataCell = '<td></td>';
var newDiv = '<div></div>';
var resultsBody;

function getStatusString(status) {
    switch(status) {
        case 0:
            return 'CREATED';
        case 1:
            return 'SUBMITTED';
        case 2:
            return 'PENDING';
        case 3:
            return 'RUNNABLE';
        case 4:
            return 'STARTING';
        case 5:
            return 'RUNNING';
        case 6:
            return 'SUCCEEDED';
        case 7:
            return 'FAILED';
        default:
            return 'UNKNOWN';
    }
}

function handleDownload(publicId, accountId, outputFileName, useOutputFile) {
    const url = useOutputFile ? `${contexturl}/superadmin/batchjobs/${encodeURIComponent(publicId)}/result?accountId=${accountId}&fileName=${outputFileName}` : `${contexturl}/superadmin/batchjobs/${encodeURIComponent(publicId)}/result?accountId=${accountId}`
    fetch(url)
      .then(resp => resp.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = outputFileName ? outputFileName : 'result.csv';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        DD_RUM.track("Batch Job download success", { accountId: accountId, publicId: publicId });
      })
      .catch(() =>  {
        alert("Error! couldn't download'");
        DD_RUM.track("Batch Job download failed", { accountId: accountId, publicId: publicId });
      });
}

function updateJobType() {
    $("#account_administration_action").hide();
    $("#template_download_action").hide();
    $("#mvb-report-options").hide()
    $("#cip-report-options").hide()
    $('#decision_migration').hide()
    $("#action_or_details").html("Actions")
    const newjobType = $('#job_types').val();
    $("#batch-job-input").show()
    if(!isFileSelected) {
        $("#create-job-btn").prop("disabled", true)
    }
    jobType = parseInt(newjobType);
    if(jobType === JOBTYPES.Account_Administration) {
        $("#job_type_heading").text("Account Administration")
        $("#account_administration_action").show()
    } else if(jobType === JOBTYPES.Replay_Transaction) {
        $("#job_type_heading").text("Replay Transaction")
    } else if(jobType === JOBTYPES.Replay_Intelligence_Unified_Entity) {
        $("#job_type_heading").text("Replay Intelligence Unified Entity")
    } else if(jobType === JOBTYPES.EXPORT_EVENT_AUDIT) {
        $("#job_type_heading").text("Replay Webhook Transactions")
        $("#template_download_action").show();
    } else if(jobType === JOBTYPES.MVB_Report) {
        $("#create-job-btn").prop("disabled", false)
        $("#job_type_heading").text("MVB Reports")
        $("#mvb-report-options").show()
        $("#batch-job-input").hide()
        $("#action_or_details").html("Payload")
    } else if(jobType === JOBTYPES.CIP_Report) {
        $("#create-job-btn").prop("disabled", false)
        $("#job_type_heading").text("CIP Reports")
        $("#cip-report-options").show()
        $("#batch-job-input").hide()
        $("#action_or_details").html("Payload")
    } else if(jobType === JOBTYPES.DECISION_MIGRATION) {
        $("#job_type_heading").text("Decision Migration")
        $("#batch-job-input").hide()
        $('#decision_migration').show()
        $("#create-job-btn").prop("disabled", false)
    } else if(jobType === JOBTYPES.UNENROLL_WATCHLIST_MONITORS) {
              $("#job_type_heading").text("Un-enroll watchlist monitors")
              $("#batch-job-input").hide()
              $("#create-job-btn").prop("disabled", true)
   }
    refreshJobs()
}

function getAction(publicId, status, accountId, outputFileName1, outputFileName2) {
    var row = $('<td></td>');
    if(status === 6 && (jobType === JOBTYPES.Account_Administration || jobType === JOBTYPES.Replay_Transaction || jobType === JOBTYPES.Replay_Intelligence_Unified_Entity || jobType === JOBTYPES.EXPORT_EVENT_AUDIT )) {
        const buttonName1 = jobType === JOBTYPES.EXPORT_EVENT_AUDIT ? "Summary report" : "Download"
        var downloadButton1 = $('<span class="socure-downlaod-button">'+buttonName1+'</span>');
        downloadButton1.on('click', function() {
            handleDownload(publicId, accountId, outputFileName1, false)
        });
        row.append(downloadButton1);

        if(outputFileName2 !== null) {
            const buttonName2 = jobType === JOBTYPES.EXPORT_EVENT_AUDIT ? "Detailed report" : "Download"
            var downloadButton2 = $('<span class="socure-downlaod-button" style="margin-left: 5px;">'+buttonName2+'</span>');
            downloadButton2.on('click', function() {
                handleDownload(publicId, accountId, outputFileName2, true)
            });
            row.append(downloadButton2);
        }
    }

    return row;
}

function disableLoadMore() {
    $('#load-more').prop('disabled', true);
}

function enableLoadMore() {
    $('#load-more').prop('disabled', false);
    currentPage++;
}

function refreshJobs() {
    currentPage = rowCount = 1;
    allJobs = [];
    resultsBody && resultsBody.empty();
    getMoreJobList();
}

function getAccountIdForJobDownload(job) {
    switch (jobType) {
        case JOBTYPES.EXPORT_EVENT_AUDIT:
            return JSON.parse(job.parameter)?.publicAccountId;
        default:
            return job.accountId;
    }
}

function getOutputFileForJobDownload1(job) {
    switch (jobType) {
        case JOBTYPES.EXPORT_EVENT_AUDIT:
            return 'replay-webhook-transactions.csv';
        default:
            return job.outputFileName;
    }
}

function getOutputFileForJobDownload2(job) {
    if(jobType == JOBTYPES.EXPORT_EVENT_AUDIT) {
        const parameter = JSON.parse(job.parameter)
        if(parameter.hasOwnProperty("replaySuccessfulTransactions") && parameter.hasOwnProperty("startDate") && parameter.hasOwnProperty("endDate")) {
            return `replayed_failed_transactions_from_${parameter.startDate}_to_${parameter.endDate}.csv`
        } else {
            return null;
        }
    } else {
        return null;
    }
}

function getJobPayload_CIP_MVB(job) {
    try {
        let dataCell = $(newDataCell);
        let payload = job.parameter;
        let obj = JSON.parse(payload);
        Object.entries(obj).forEach(([k,v]) => dataCell.append($(newDiv).text(k+ " : "+ v)))
        return dataCell;
        } catch(e) {
        return $(newDataCell).text("");
    }
}

function addJobRow(job) {
    const accountId = getAccountIdForJobDownload(job)
    const outputFileName1 = getOutputFileForJobDownload1(job)
    const outputFileName2 = getOutputFileForJobDownload2(job)
    var row = $(`<tr class='data-row ${rowCount%2 === 0 ? 'grey-row' : ''}'></tr>`);
    row.append($(newDataCell).text(rowCount++));
    row.append($(newDataCell).text(job.publicId));
    row.append($(newDataCell).text(job.createdBy || 'Unknown'));
    row.append($(newDataCell).text(job.createdAt && job.createdAt.trim() ? new Date(parseInt(job.createdAt)).toLocaleString() : '-    '));
    row.append($(newDataCell).text(getStatusString(job.status)));
    if(jobType === JOBTYPES.MVB_Report || jobType === JOBTYPES.CIP_Report) {
        row.append(getJobPayload_CIP_MVB(job))
    } else {
        row.append(getAction(job.publicId, job.status, accountId, outputFileName1, outputFileName2));
    }
    resultsBody.append(row);
}

function processResults(results) {
    results.length < size ? disableLoadMore() : enableLoadMore();
    results.map(function(obj) {
        addJobRow(obj);
    });
}



function get_Accounts(list_name, endpoint, idProp, nameProp, customProps) {
     if(!idProp) idProp = 'id';
     if(!nameProp) nameProp = 'name';
     const url = contexturl + endpoint;
     $.ajax(url, {
         type : 'GET',
         beforeSend: function() {
           $('#Loading').show();
         },
         complete: function() {
           $('#Loading').hide();
         },
         success : function(data) {
             if (data != null) {
                 data = data['data'];
                 var select = $('#' + list_name);
                 if (select.prop) {
                     var options = select.prop('options');
                 } else {
                     var options = select.attr('options');
                 }
                 $('option', select).remove();
                 $.each(data, function(index, val) {
                     var id = val[idProp];
                     var name = id + ' - ' + val[nameProp];
                     var option = new Option(name, id);
                     if(customProps) {
                         for(var i in customProps) {
                             var customProp = customProps[i];
                             option.setAttribute('data-' + customProp, val[customProp]);
                         }
                     }
                     options[options.length] = option;
                 });
                 select.trigger("change");
                 select.trigger("chosen:updated");
             }
         }
     });
 };

function getMoreJobList() {
    $.ajax({
        url: contexturl + `/superadmin/batchjobs`,
        type: 'GET',
        data: jQuery.param({
            jobType: jobType,
            page: currentPage,
            size: size
        }),
        async: false,
        error: function(e) {
            alert('Unable to fetch batch jobs. Please refresh the Page!');
            DD_RUM.track("Failed to load more jobs", { jobType: jobType });
        },
        success: function(data) {
            processResults(data);
            DD_RUM.track("Load more jobs success", { jobType: jobType });
        }
    });
}

function getUrl() {
    switch (jobType) {
        case JOBTYPES.Replay_Intelligence_Unified_Entity:
            return `${contexturl}/superadmin/batchjobs/replay_intelligence_unified_entity/create`;
        case JOBTYPES.Replay_Transaction:
            return `${contexturl}/superadmin/batchjobs/transaction/replay/create`;
        case JOBTYPES.EXPORT_EVENT_AUDIT:
            return `${contexturl}/superadmin/batchjobs/export_event_audit/create`;
        case JOBTYPES.CIP_Report:
            return `${contexturl}/superadmin/batchjobs/report/cip`;
        case JOBTYPES.MVB_Report:
            return `${contexturl}/superadmin/batchjobs/report/mvb`;
        case JOBTYPES.DECISION_MIGRATION:
             return `${contexturl}/superadmin/batchjobs/decision_migration/create`;
        case JOBTYPES.Account_Administration:
        default:
            return `${contexturl}/superadmin/batchjobs/account/administration/create`;
    }
 }

function validateGetMvpCipJobParams() {
    var result = true;
    if(jobType === JOBTYPES.CIP_Report) {
        const inputDate = $("#cip-report-date").val()
        if(inputDate === "") {
            alert("Please enter report date to submit the job!")
            return false;
        }
        return JSON.stringify({date: inputDate})
    } else if(jobType === JOBTYPES.MVB_Report) {
        const startDate = $("#mvb-report-start").val()
        const endDate = $("#mvb-report-end").val()
        if(startDate === "") {
            alert("Please enter report start date to submit the job!");
            return false;
        }
        const parameters = {
            startDate
        }
        if(endDate !== "") {
            parameters["endDate"] = endDate;
        }
        return JSON.stringify(parameters);
    }
    return result;
}

function handleCreateJob() {
    const mvbCipParams = validateGetMvpCipJobParams()
    if(mvbCipParams === false) {
        return;
    }
    var createButton = $('#create-job-btn');
    createButton.text('Creating...')
    createButton.prop('disabled', true);
    var fileInputField = $('#batch-job-input');
    var jobTypeInputField = $('#job_type_action')
    var jobAction = jobTypeInputField.val();
    var mfData = new FormData();
    if(jobType !== JOBTYPES.CIP_Report && jobType !== JOBTYPES.MVB_Report) {
        mfData.append('file', fileInputField[0].files[0]);
    } else {
        mfData.append('payload', mvbCipParams);
    }
    if(jobType === JOBTYPES.Account_Administration) mfData.append('action', jobAction);

    const url = getUrl();

    if(jobType === JOBTYPES.DECISION_MIGRATION){

        var selectedAccountIds = $('#parent_account_list').val();
        console.log('selected Account',selectedAccountIds.toString())

        const Parameters = {
            "accountIds": selectedAccountIds.toString(),
            "issuedBy":loggedinUsername
        }

        mfData.append('parameters', JSON.stringify(Parameters));
    }
    $.ajax({
        url:   url,
        type: 'POST',
        data : mfData,
        async: false,
        processData: false,
        contentType: false,
        error: function(e) {
            alert(e.responseText || 'Unable to create new batch job please try again!');
            createButton.text('Create job');
            createButton.prop('disabled', false);
            DD_RUM.track("Create batch job failed", { action: jobAction, jobType: $('#job_types').val() });
        },
        success: function(data) {
            fileInputField.val('');
            alert('Batch job created successfully!');
            createButton.text('Create job');
            if(jobType === JOBTYPES.DECISION_MIGRATION){
                $('#parent_account_list').val(null).trigger("chosen:updated");
                createButton.prop('disabled', false);
            }
            refreshJobs();
            DD_RUM.track("Create batch job success", { action: jobAction, jobType: $('#job_types').val() });
        }
    });
}

function initialiseJobActions() {
    var jobTypes = [
        { text: "Create Account", action: "CREATE", },
        { text: "Edit Account", action: "UPDATE", }
    ]

    var select = $('#job_type_action');
    var options = select.prop('options')
    $.each(jobTypes, function(index, value)
    {
        options[options.length] = new Option(value.text, value.action);
    });
}

function initialiseJobTypes() {
    var jobTypes = [
        { id: JOBTYPES.Account_Administration, text: "Account Administration" },
        { id: JOBTYPES.Replay_Transaction, text: "Replay Transaction" },
        { id: JOBTYPES.Replay_Intelligence_Unified_Entity, text: "Replay Intelligence Unified Entity" },
        { id: JOBTYPES.EXPORT_EVENT_AUDIT, text: "Replay Webhook Transactions" },
        { id: JOBTYPES.MVB_Report, text: "MVB Report" },
        { id: JOBTYPES.CIP_Report, text: "CIP Report" },
        {id:JOBTYPES.DECISION_MIGRATION, text: "Decision Migration"},
        { id: JOBTYPES.UNENROLL_WATCHLIST_MONITORS, text: "Un-enroll watchlist monitors"}
    ]
    var select = $('#job_types');
    var options = select.prop('options')
    $.each(jobTypes, function(index, value)
    {
        options[options.length] = new Option(value.text, value.id);
    });
}


function downloadURI(uri, name) {
    let link = document.createElement("a");
    link.download = name;
    link.href = uri;
    link.click();
}

function downloader(data, type, name) {
    let blob = new Blob([data], {type});
    let url = window.URL.createObjectURL(blob);
    downloadURI(url, name);
    window.URL.revokeObjectURL(url);
}

function downloadTemplate() {
    switch(jobType) {
        case JOBTYPES.EXPORT_EVENT_AUDIT:
            const data = {
                "publicAccountId": "acc-VSb8SWjw6j",
                "privateAccountId": 1792,
                "eventTypes": "DocV",
                "startDate": "2022-05-15",
                "endDate": "2022-05-24",
                "generateCSV": true,
                "pushReplay": true,
                "dryRun": false,
                "httpStatuses": [400, 401, 403, 500],
                "replaySuccessfulTransactions": true
              };
            var json = JSON.stringify(data);
            downloader(json, "application/json", "ReplayWebhookTemplate")
            break;
      }
}

window.onload = function() {
  DD_RUM && DD_RUM.startView({name: "/batch_jobs"});
  DD_RUM && DD_RUM.setUser({ "id": loggedinUsername});
}

$(document).ready(function() {
    resultsBody = $('#results-body');
    $('#batch-job-input').on('change', function(event) {
        const fileInput = $("#batch-job-input");
        if(fileInput.val() !== "") {
            $('#create-job-btn').prop('disabled', false);
            isFileSelected = true;
        } else {
            $('#create-job-btn').prop('disabled', true);
            isFileSelected = false;
        }
    });
    initialiseJobTypes();
    initialiseJobActions();
    getMoreJobList();
    $("#mvb-report-options").hide()
    $("#cip-report-options").hide()
    $('#decision_migration').hide()
    $(".chosen").chosen({
      width:"100%",
       search_contains : true
    });

    get_Accounts('parent_account_list', '/superadmin/1/get_accounts_with_and_without_permission?with_permission_id=103&with_out_permission_id=247', 'id', 'name', []); //decision enabled & multimodel disabled

})