save_document_type = function (myform) {
    var obj = {};
    var url = contexturl + '/superadmin/1/document_manager/types';
    var form = document.getElementById('add_document_type');;
    var formData = new FormData();
    obj['id'] = form.docId.value;
    obj['publicId']=form.publicId.value;
    if (form.docName.value.trim() === "") {
        alert("Please fill the name of the document");
        return;
    }
    obj['name']= form.docName.value;
    obj['description']= form.docDescription.value;
    if (form.docCategory.value === "-") {
        alert("Please select the category of the document");
        return;
    }
    obj['category']= form.docCategory.value;
    if (form.docSubCategory.value === "-") {
        alert("Please select the sub-category of the document");
        return;
    }
    obj['subCategory']= form.docSubCategory.value;
     obj['tags'] = $("input[name='docTagsCheckbox']:checked").map(function(){
        return this.value;
    }).get()
    var json = JSON.stringify(obj);
    $.ajax(
            url,
            {
                type: 'POST',
                data: json,
                contentType: "application/json",

                success: function (data) {
                    if (data['status'].toLowerCase() === 'ok') {
                        $('#message').html('Updated Successfully!!!');
                        $("#AddDocumentType").modal("hide");
                        form.reset();
                        get_documentTypes();
                    } else {
                        $("#AddDocumentType").modal("show");
                        $('#message').html(data['msg']);
                    }
                }
    });
};

show_docType_add_form = function () {
    var form = document.getElementById('add_document_type');
    form.reset()
    $('#AddDocumentType').modal('show');
};

get_documentTypes = function() {
  var url = 'superadmin/1/document_manager/types';
  $.ajax(url, {
    type: 'GET',
    success: function(result) {
      if (result['status'].toLowerCase() === 'ok') {
        show_DocumentTypes(result['data'])
      } else {
        alert("Problem in fetching document types. Please try again later")
      }
    }
  });
};

show_DocumentTypes = function(data) {
  if (data !== null) {
    window.docTypes = data;
    var aaData = [];
    for (var j = 0; j < data.length; j++) {
      var rowData = data[j];
      var a = [];
      a.push(rowData['id']); //1
      a.push(rowData['publicId']); //2
      a.push(rowData['name']); //3
      a.push(rowData['description']); //4
      a.push(rowData['category']); //5
      a.push(rowData['subCategory']); //6
      a.push(rowData['tags']); //7
      a.push("edit"); //8
      aaData.push(a);
    }

    oTable = $('#document_types_table')
      .dataTable({
        "aaData": aaData,
        "bProcessing": true,
        "bDestroy": true,
        "bAutoWidth": false,
        "iDisplayLength": 25,
        "aoColumns": [{
            "fnRender": function(oObj) {
              return oObj.aData[0];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[1];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[2];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[3];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[4];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[5];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[6];
            }
          },
         {
           "fnRender": function(oObj) {
             return "<input type=\"button\" value=\"edit\" onclick=\"editDocType(" + oObj.aData[0] + ")\" class=\"EditDocType\"/>"

           }
         }
        ]
      });
  }
};

get_original_data_for_id = function (id) {
    if(window.docTypes) {
        for (var i in window.docTypes) {
            var d = window.docTypes[i];
            if(d.id === id) {
                return d;
            }
        }
    }
    return null;
};
function editDocType(id) {
    var form = document.getElementById('add_document_type');
    form.reset();
    var obj = get_original_data_for_id(id)
    form.docId.value = obj['id'];
    form.publicId.value = obj['publicId'];
    form.docName.value = obj['name'];
    form.docDescription.value = obj['description'];
    $('select[id="docCategory"]').find('option:contains(' + obj['category'] + ')').attr("selected",true);
    $('select[id="docSubCategory"]').find('option:contains(' +  obj['subCategory'] + ')').attr("selected",true);
    for (var tag in obj['tags']) {
        $('#'+obj['tags'][tag]).prop("checked",true);
    }
     $('#AddDocumentType').modal('show');
}