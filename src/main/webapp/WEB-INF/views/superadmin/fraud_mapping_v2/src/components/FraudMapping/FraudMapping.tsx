import { FC, useState, useCallback, useContext } from "react";
import { <PERSON><PERSON><PERSON>, Button, useToast, Typography } from "@socure/components";
import MenuItem from "@mui/material/MenuItem";
import { Autocomplete, Box, TextareaAutosize, Popper } from "@mui/material";
import { addMapping } from "../../services";
import { AccountAutoComplete, FraudModelAutoComplete } from "../../types";
import { ASSOCIATION_TYPE, ENVIRONMENT } from "../../utils/constants";
import { FraudModelMappingContext } from "../context/FraudModelMappingContext";
import { getModelAndSetDescription } from "../../utils/helper";
import { styled } from "@mui/material/styles";
import "./FraudMapping.scss";

const FullWidthPopper = styled(Popper)({
  width: "max-content !important",
  minWidth: "305px !important",
  maxWidth: "70vw !important",
  zIndex: 1300,
});

const FraudMapping: FC = () => {
  const { showToast } = useToast();
  const {
    activeAccount,
    accounts,
    models,
    modelsAndDescriptions,
    setActiveAccount,
    getAndUpdateMappingsForAccount,
    setModelsAndDescriptions,
    setMapping,
  } = useContext(FraudModelMappingContext);
  const [activeModel, setActiveModel] = useState<FraudModelAutoComplete>(null);
  const [associationType, setAssociationType] = useState<number>(3);
  const [environment, setEnvironment] = useState<string>("Production");
  const [filterCriteria, setFilterCriteria] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const handleAccountClick = useCallback(
    (_, value: AccountAutoComplete) => {
      setActiveAccount(value);
      if (value) {
        getAndUpdateMappingsForAccount(value.id, true);
      } else {
        setMapping(null);
      }
    },
    [getAndUpdateMappingsForAccount, setActiveAccount, setMapping]
  );

  const handleModelClick = useCallback(
    (_, value: FraudModelAutoComplete) => {
      setActiveModel(value);
      if (value) {
        getModelAndSetDescription(
          value,
          modelsAndDescriptions,
          setDescription,
          setModelsAndDescriptions
        );
      } else {
        setDescription("");
      }
    },
    [modelsAndDescriptions, setModelsAndDescriptions]
  );

  const handleAddMapping = useCallback(() => {
    function validateJson(input: string) {
      if (input.trim() === "") {
        return false;
      }
      try {
        JSON.parse(input);
        return false;
      } catch (error) {
        return true;
      }
    }

    const selectedAccount = activeAccount?.id;
    const selectedmodel = activeModel?.publicId;

    if (!selectedAccount || isNaN(selectedAccount)) {
      showToast({
        title: "Error",
        description: "Please select Account from dropdown",
        type: "error",
      });
      return;
    } else if (!selectedmodel || selectedmodel == "default") {
      showToast({
        title: "Error",
        description: "Please select Fraud Model",
        type: "error",
      });
      return;
    } else if (validateJson(filterCriteria)) {
      showToast({
        title: "Error",
        description: "Please enter valid json for filter criteria",
        type: "error",
      });
      return;
    }
    setIsLoading(true);
    const data = {
      userId: selectedAccount,
      fraudModelId: selectedmodel,
      associationType: associationType,
      environment: environment,
      filterCriteria: filterCriteria,
    };
    const url = app.modelManagement
      ? "/api/1/fraudmodel/map"
      : "/api/1/fraudmodel/fraudassociate.jsonp";
    addMapping(url, data)
      .then(() => {
        showToast({
          title: "Success",
          description: "Mapping added successfully",
          type: "success",
        });
        getAndUpdateMappingsForAccount(selectedAccount);
      })
      .catch((errorMessage) => {
        showToast({
          title: "Error",
          description: errorMessage
            ? errorMessage
            : "Error while adding mapping",
          type: "error",
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [
    activeAccount,
    activeModel,
    associationType,
    environment,
    filterCriteria,
    getAndUpdateMappingsForAccount,
    showToast,
  ]);

  return (
    <div className="fraud_mapping_container">
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <label className="fraud_mapping_label">Account</label>
        <Autocomplete
          options={accounts}
          getOptionLabel={(option) => `${option.id} - ${option.label}`}
          value={activeAccount}
          onChange={handleAccountClick}
          renderInput={(params) => (
            <TextField
              {...params}
              id="outlined-account"
              label="Select"
              placeholder="Choose accounts"
            />
          )}
          sx={{ flex: "1 1 50%" }}
          loading={accounts.length === 0}
        />
      </Box>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <label className="fraud_mapping_label">Model</label>
        <Autocomplete
          options={models}
          getOptionLabel={(option) => option.label}
          value={activeModel}
          onChange={handleModelClick}
          renderInput={(params) => (
            <TextField
              {...params}
              id="outlined-model"
              label="Select"
              placeholder="Select Fraud Model"
            />
          )}
          sx={{ flex: "1 1 50%" }}
          loading={models.length === 0}
          PopperComponent={FullWidthPopper}
        />
      </Box>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <Typography className="fraud_mapping_label" variant="label">
          Model Description
        </Typography>
        <TextField
          hiddenLabel
          disabled
          value={description}
          sx={{ flex: "1 1 50%" }}
        />
      </Box>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <label className="fraud_mapping_label">Association Type</label>
        <TextField
          id="outlined-association-type"
          select
          label="Select"
          defaultValue="3"
          sx={{ flex: "1 1 50%" }}
          onChange={(event) => setAssociationType(Number(event.target.value))}
        >
          {ASSOCIATION_TYPE.map((option) => (
            <MenuItem
              key={option.value}
              value={option.value}
              selected={option.value === associationType}
            >
              {option.label}
            </MenuItem>
          ))}
        </TextField>
      </Box>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <label className="fraud_mapping_label">Environment</label>
        <TextField
          id="outlined-environment"
          select
          label="Select"
          defaultValue="Production"
          sx={{ flex: "1 1 50%" }}
          onChange={(event) => setEnvironment(event.target.value)}
        >
          {ENVIRONMENT.map((option) => (
            <MenuItem
              key={option.value}
              value={option.value}
              selected={option.value === environment}
            >
              {option.label}
            </MenuItem>
          ))}
        </TextField>
      </Box>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <label className="fraud_mapping_label">Filter Criteria</label>
        <TextareaAutosize
          id="outlined-filter-criteria"
          placeholder="Enter Filter Criteria"
          value={filterCriteria}
          onChange={(event) => setFilterCriteria(event.target.value)}
          minRows={4}
        />
      </Box>
      <Button
        onClick={handleAddMapping}
        type="submit"
        sx={{ alignSelf: "flex-start" }}
        loading={isLoading}
      >
        Map
      </Button>
    </div>
  );
};

export default FraudMapping;
