$(document).ajaxStart(function() {
    $("#Loading").show();
});

$(document).ajaxStop(function() {
    $("#Loading").hide();
});

$(document).ready(function() {
    getConfigurations();
});

var configuration;
var arnToResponse;

function getConfigurations() {
    $('#configTable').children().remove();
    var html = ""
    $.ajax({
        url: contexturl + '/superadmin/sandbox/configuration',
        type: 'GET',
        async: false,
        error: function(e) {
            alert('Unable to fetch sandbox configurations. Please refresh the Page!');
        },
        success: function(data) {
            $.each(data.data, function(i, item) {
                html += "<tr><td>" + item.revision + "</td><td>" + item.configName + "</td><td>" + item.configVersion + "</td><td>"
                + item.live + "</td><td>" + item.createdBy + "</td><td>" + item.markedLiveBy + "</td><td>" + item.markedLiveAt
                + "</td><td>  <input type=\"button\" value=\"mark-as-live\" class=\"pad\" onClick=\"markAsLive(\'" + item.revision + "\')\" />  " +
                "  <input type=\"button\" value=\"Download\" class=\"pad\" onClick=\"downloadConfig(\'" + item.revision + "\')\" />  "
                +"</td></tr>";
            });
            $('#configTable').append(html);
        }
    });
}

function addConfig() {
    document.getElementById('sandbox_configuration').reset();
    document.getElementById('create_config_container').style.display = 'block';
}

function closeCreateConfigForm() {
    document.getElementById('create_config_container').style.display = 'none';
}

function getFileExtension(form) {
    var fileName = $(form.configFile).get(0).files[0].name
    return (/[.]/.exec(fileName)) ? /[^.]+$/.exec(fileName)[0] : undefined;
}

function destroyClickedElement(event) {
    document.body.removeChild(event.target);
}

function downloadConfig(revision) {
    $.ajax({
        url: contexturl + '/superadmin/sandbox/configuration/' + revision,
        type: 'GET',
        cache: false,
        contentType: false,
        processData: false,
        xhrFields: {
            responseType: 'blob'
        },
        error: function(e) {
            alert('Unable to download the sandbox configuration. Please refresh the Page!');
        },
        success: function(myBlob) {
            if (myBlob["size"]) {
                var textToSaveAsURL = window.URL.createObjectURL(myBlob);
                var fileNameToSaveAs = "sandbox_configuration.csv";
                var downloadLink = document.createElement("a");
                downloadLink.download = fileNameToSaveAs;
                downloadLink.innerHTML = "Download File";
                downloadLink.href = textToSaveAsURL;
                downloadLink.onclick = destroyClickedElement;
                downloadLink.style.display = "none";
                document.body.appendChild(downloadLink);
                downloadLink.click();
            } else {
                alert('Unable to download the sandbox configuration. Please refresh the Page!');
            }
        }
    });
}

function markAsLive(revision) {
    $.ajax({
        url: contexturl + '/superadmin/sandbox/configuration/' + revision + '/mark-as-live',
        type: 'GET',
        async: false,
        error: function(e) {
            alert('Unable to mark sandbox configuration as live. Please refresh the Page!');
        },
        success: function(data) {
            alert('Selected configuration is marked as live!');
            getConfigurations();
        }
    });
}

function createConfig(form) {
    var sandboxConfig = new FormData();
    form = document.getElementById('sandbox_configuration')
    var isLive = document.getElementById('isConfigLive').checked

    if (form.configName.value === "") {
        alert('Configuration Name cannot be empty!');
        return
    }
    if (form.configVersion.value === "") {
        alert('Configuration Version cannot be empty!');
        return
    }
    if($.trim($(form.configFile).val()).length != 0 && $(form.configFile).get(0).files[0] != undefined && getFileExtension(form) === "csv"){
        var file = $(form.configFile).get(0).files[0]
        sandboxConfig.append('configFile', file)
    } else {
        alert('Please choose a valid .csv file!')
        return
    }
    sandboxConfig.append('configName', form.configName.value)
    sandboxConfig.append('configVersion', form.configVersion.value)
    sandboxConfig.append('isLive', isLive)

    $.ajax(contexturl + '/superadmin/sandbox/configuration', {
        type: 'POST',
        data: sandboxConfig,
        cache: false,
        contentType: false,
        processData: false,
        error: function(e) {
            closeCreateConfigForm();
            form.reset();
            alert('Unable to add revision: ' + e.responseJSON.msg);
        },
        success: function(data) {
            closeCreateConfigForm();
            getConfigurations();
            alert('New Revision is added successfully!');
        }
    });
}