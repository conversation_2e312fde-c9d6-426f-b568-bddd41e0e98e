var passive, active;
var selectedVendor;
var totalTestJobPages;
var selectedTestPage;

var rulecodeNameBox, dependenciesBox, operatorBox, inputsBox, rulecodeTable;

$(document).ready(function() {


    /*-------------------------------------Test setup------------------------*/
        $('#testTab').click(function(event) {
            $('#Test').load("studiotest", function() {
                openTab(event, 'Test');
                getTestVendors();
                $('#testPage').hide();

                if(ishigherEnv()){//DS or prod
                    $("#syncVendorsTestTab").show();
                    $("#revertButton").hide();
                }
                else { //stage or localhost
                    $("#syncVendorsTestTab").hide();
                    $("#revertButton").show();
                }
            });
        });

    /*-------------------------------------Studio setup------------------------*/
    $('#studioTab').click(function(event) {
        $('#Studio').load("studio", function() {
            openTab(event, 'Studio');
            getVendors();
            $('#studioPage').hide();
            $('#remove').hide();
            $('#syncVendors').hide();
            $('#studioHomePage').hide();

        /*-------------------------------------Studio Home Setup------------------------*/
        $('#studioHomeTab').click(function(event) {
            $('#studioHome').load("studiohome", function() {

                openInnerTab(event, 'studioHome');

                if(!ishigherEnv()){
                    getOperators();
                    getVendorConfigs();
                }

                loadTable(selectedVendor);

                 $('#rulecodes').on('click', 'input[type="button"]', function() {
                    var $row = $(this).closest('tr');
                    var data = table.fnGetData($row[0]);
                    var rowId = data[0];
                 });

                 if(ishigherEnv()){ //DS or prod
                     $("#syncVendors").show();
                     $("#plus").hide();
                     $("#remove").hide();
                     $("#studioPage :input").attr("disabled", true);
                 }
                 else { //stage or localhost
                      $("#pullConfigButton").hide();
                      $("#plus").show();
                      $("#syncVendors").hide();
                  }

            });
        });

        /*-------------------------------------Dependencies Management ------------------------*/
                $('#manageDependenciesTab').click(function(event) {
                    $('#manageDependencies').load("studio_manage_dependency", function() {
                        openInnerTab(event, 'manageDependencies');
                        $('#dependencyManagementPage').hide();
                        if(!ishigherEnv()){//stage or localhost
                             $("#dependencyManagementPage").show();
                             getListOfConfiguredTables().then((data) => populateDependencyTableDropDown(data), () => alert('Unable to fetch dependencies. Please refresh the Page!'));
                             getListOfSupportedTables().then((data) => populateSupportedDependencyDropDown(data), () => alert('Unable to fetch supported dependencies. Please refresh the Page!'));
                             loadDependencyDefinition();
                        }
                    });
                });

         /*-------------------------------------Dependencies Association ------------------------*/
            $('#dependenciesAssociationsTab').click(function(event) {
                $('#dependenciesAssociations').load("studio_dependencies_association", function() {
                    openInnerTab(event, 'dependenciesAssociations');
                    $('#dependenciesAssociationPage').hide();
                    if(!ishigherEnv()){//stage or localhost
                        $("#dependenciesAssociationPage").show();
                        getListOfConfiguredTables().then((data) => populateDependencyTableDropDown(data), () => alert('Unable to fetch dependencies. Please refresh the Page!'));
                        getListOfSupportedTables().then((data) => populateSupportedDependencyDropDown(data), () => alert('Unable to fetch supported dependencies. Please refresh the Page!'));
                        getPrefix(selectedVendor).then((data) => getPrefixSuccessCallback(data), () => alert('Unable to fetch prefix. Please refresh the Page!'));
                    }
                    $('#prefixSelection').hide();
                });
            });

        /*-------------------------------------Add Vendor config ------------------------*/
                    $('#addVendorConfigTab').click(function(event) {
                        $('#addVendorConfig').load("studio_add_vendor_config", function() {
                            openInnerTab(event, 'addVendorConfig');

                            if(!ishigherEnv()){//stage or localhost
                                $("#dependenciesAssociationPage").show();
                                getListOfConfiguredTables().then((data) => populateDependencyTableDropDown(data), () => alert('Unable to fetch dependencies. Please refresh the Page!'));
                                getListOfSupportedTables().then((data) => populateSupportedDependencyDropDown(data), () => alert('Unable to fetch supported dependencies. Please refresh the Page!'));
                                getVendorPrefix(selectedVendor).then((data) => getVendorPrefixSuccessCallback(data), () => alert('Unable to fetch prefix. Please refresh the Page!'));
                            }
                            $('#vendorPrefixSelection').hide();
                        });
                    });


        });
    });

    /*-------------------------------------Studio v2 setup------------------------*/
    $("#studioV2Tab").click((event) => {
        $('#StudioV2').load("studio_v2", () => {
            openTab(event, 'StudioV2');
            getStudioV2Vendors();
            $('#studioV2Page').hide();
            $('#studio_v2_remove').hide();
            $('#studio_v2_syncVendors').hide();
            $('#studioHomePageV2').hide();
            
            /*-------------------------------------Studio v2 Home Setup------------------------*/
            $('#studioHomeTabV2').click(function(event) {
                $('#studioV2Home').load("studio_v2_home", function() {
                    openInnerTab(event, 'studioV2Home');
                    loadTableV2(selectedVendor);

                    if(ishigherEnv()){ //DS or prod
                        $("#studio_v2_syncVendors").show();
                        $("#studio_v2_plus").hide();
                        $("#studio_v2_remove").hide();
                        $("#studioV2Page :input").attr("disabled", true);
                    }
                    else { //stage or localhost
                        $("#pullConfigButtonV2").hide();
                        $("#studio_v2_plus").show();
                        $("#studio_v2_syncVendors").hide();
                    }
                });
            });

            /*-------------------------------------Dependencies Management v2------------------------*/
            $('#manageDependenciesTabV2').click(function(event) {
                $('#manageDependenciesV2').load("studio_manage_dependency_v2", function() {
                    openInnerTab(event, 'manageDependenciesV2');
                    $('#dependencyManagementPageV2').hide();
                    if(!ishigherEnv()){//stage or localhost
                        $("#dependencyManagementPageV2").show();
                        getListOfConfiguredTables().then((data) => populateDependencyTableDropDownV2(data), () => alert('Unable to fetch dependencies. Please refresh the Page!'));
                        getListOfSupportedTables().then((data) => populateSupportedDependencyDropDownV2(data), () => alert('Unable to fetch supported dependencies. Please refresh the Page!'));
                        loadDependencyDefinitionV2();
                    }
                });
            });

            /*-------------------------------------Dependencies Association v2------------------------*/
            $('#dependenciesAssociationsTabV2').click((event) => {
                $('#dependenciesAssociationsV2').load("studio_dependencies_association_v2", function() {
                    openInnerTab(event, 'dependenciesAssociationsV2');
                    $('#dependenciesAssociationPageV2').hide();
                    if(!ishigherEnv()) { //stage or localhost
                        $("#dependenciesAssociationPageV2").show();
                        getListOfConfiguredTables().then((data) => populateDependencyTableDropDownV2(data), () => alert('Unable to fetch dependencies. Please refresh the Page!'));
                        getListOfSupportedTables().then((data) => populateSupportedDependencyDropDownV2(data), () => alert('Unable to fetch supported dependencies. Please refresh the Page!'));
                        getPrefix(selectedVendor, "v2").then((data) => getPrefixSuccessCallbackV2(data), () => alert('Unable to fetch prefix. Please refresh the Page!'));
                    }
                    $('#prefixSelectionV2').hide();
                });
            });

            /*-------------------------------------Add Vendor config  v2------------------------*/
            $('#addVendorConfigTabV2').click(function(event) {
                $('#addVendorConfigV2').load("studio_add_vendor_config_v2", function() {
                    openInnerTab(event, 'addVendorConfigV2');
                    if(!ishigherEnv()){//stage or localhost
                        $("#dependenciesAssociationPageV2").show();
                        getListOfConfiguredTables().then((data) => populateDependencyTableDropDownV2(data), () => alert('Unable to fetch dependencies. Please refresh the Page!'));
                        getListOfSupportedTables().then((data) => populateSupportedDependencyDropDownV2(data), () => alert('Unable to fetch supported dependencies. Please refresh the Page!'));
                        getVendorPrefix(selectedVendor).then((data) => getVendorPrefixSuccessCallbackV2(data), () => alert('Unable to fetch prefix. Please refresh the Page!'));
                    }
                    $('#vendorPrefixSelectionV2').hide();
                });
            });
       });
    });

    /*-------------------------------------Ingestion  setup------------------------*/
    $('#ingestionTab').click(function(event) {
        openTab(event, 'Ingestion');
        $('#Ingestion').load("ingestion", function() {
            getDBInfo();
            clearPage();
            $("#hideHistory").click(function() {
                $('#result').hide();
                enableButton1();
                showShowHistory();
                hidePagination();
            });
            var ingestionPayload = "";

            //upgrade backup1
            $('#backup1').click(function() {
                var path = getCurFilePath();
                if (validatePath(path)) {
                    disableAllButtons();

                    ingestionPayload = {
                        filePath: path,
                        action: 'UPDATE_BACKUP1'
                    };
                    callUpdateBackup(ingestionPayload, this.id);
                }
            });

            //Switch button
            $('#switch').click(function() {
                disableAllButtons();
                var path = getCurFilePath();
                ingestionPayload = {
                    filePath: path
                };
                $.ajax({
                    url: contexturl + "/superadmin/rulecode-service/v1/switch",
                    data: ingestionPayload,
                    type: 'POST',
                    beforeSend: function() {
                        $('#Loading').show();
                    },
                    complete: function() {
                        showShowHistory();
                        hidePagination();
                        $('#Loading').hide();
                    },
                    error: function(e) {
                        handleLogs(e.responseJSON.logs, false);
                        alert("Request failed! " + e.responseJSON.errorMessage);
                    },
                    success: function(data) {
                        handleLogs(data.logs, false);
                        alert("Switched Db instances Successfully!");
                    }
                });
            });

            
            //update config button click handle
            $('#updateConfig').click(function() {
                showConfigHandler()
            })

            //Switch v2 button
            $('#swapTable').click(function() {
                $.ajax({
                    url: contexturl + "/superadmin/rulecode-service/v1/swap",
                    type: 'POST',
                    beforeSend: function() {
                        $('#Loading').show();
                    },
                    complete: function() {
                        $('#Loading').hide();
                    },
                    error: function(e) {
                        alert("Request failed! " + e.responseJSON.errorMessage);
                    },
                    success: function(data) {
                        getDBInfoV2()
                        alert("Switched Dynamo Tables Successfully!");
                    }
                });
            });

            $('#rollbackTable').click(function() {
                $.ajax({
                    url: contexturl + "/superadmin/rulecode-service/v1/rollback",
                    type: 'POST',
                    beforeSend: function() {
                        $('#Loading').show();
                    },
                    complete: function() {
                        $('#Loading').hide();
                    },
                    error: function(e) {
                        alert("Request failed! " + e.responseJSON.errorMessage);
                    },
                    success: function(data) {
                        getDBInfoV2()
                        alert("Rollback Dynamo Tables Successful!");
                    }
                });
            });

            $('#backup2').click(function() {
                var path = getCurFilePath();
                if (validatePath(path)) {
                    disableAllButtons();

                    ingestionPayload = {
                        filePath: path,
                        action: 'UPDATE_BACKUP2'
                    };
                    callUpdateBackup(ingestionPayload);
                }
            });

            $('#next').click(function() {
                next();
            });

            $('#back').click(function() {
                back();
            });

            $('#history').click(function() {
                getHistory(1);
                showHideHistory();
                showPagination();
            });

            $('#batch-job-input').on('change', function(file) {
                $('#create-job-btn').prop('disabled', false);
            });

            $('#nav-rds-tab').click(function(event) {
                $('#ingestionTab').click()
            })
            
            $('#nav-dynamo-tab').click(function(event) {
                getDBInfoV2()
                getAwsImportInfo()
                getBatchJobList()
            })
        });

    });

    /*------------------------------------------------------------*/
    //default tab
    $('#studioTab').click();

    //If it is prod disable prod env
    if((appenv!=='dev') && ((appenv==='eks-prod') || isGivenEnv('prod') || hasQueryParam('prod')))
        $("#testTab").hide();

});

function updateConfigHandler() {
    const updatedConfig = $("#updatedConfig").val()
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/update",
        type: 'POST',
        dataType: 'json',
        data: updatedConfig,
        contentType: 'application/json',
        async: false,
        beforeSend: function() {
            $('#Loading').show();
        },
        complete: function() {
            $('#Loading').hide();
        },
        error: function(e) {
            $('#Loading').hide();
            closeUpdateConfigPopup()
            alert("Request failed! " + e.responseText);
        },
        success: function(data) {
            closeUpdateConfigPopup()
            alert("Updated Config Successfully!");
        }
    })
}

function showConfigHandler() {
    $('#updateConfigModal').show();
}

function closeUpdateConfigPopup() {
    $('#updateConfigModal').hide();
}

function ishigherEnv(){ //everything except stage / localhost
     return (hasQueryParam('prod') || hasQueryParam('ds') || (appenv === 'eks-prod') || (appenv === 'govcloud-prod') || !(isGivenEnv('stage') || isGivenEnv('localhost') || (appenv === 'dev') || (appenv === 'govcloud-stage')))//DS or prod
}
function isGivenEnv(env){
     var isPresent = window.location.hostname.indexOf(env);
     return (isPresent!=-1)
}

function hasQueryParam(str){
     var queryString = window.location.search;
     var urlParams = new URLSearchParams(queryString);
     return urlParams.has(str)
}

function openTab(evt, tabName) {
    // Declare all variables
    var i, tabcontent, tablinks;

    // Get all elements with class="tabcontent" and hide them
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }

    // Get all elements with class="tablinks" and remove the class "active"
    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Show the current tab, and add an "active" class to the button that opened the tab
    document.getElementById(tabName).style.display = "block";
    evt.target.className += " active";
}

function openInnerTab(evt, innerTabName) {
    // Declare all variables
    var i, tabcontent, tablinks;

    // Get all elements with class="tabcontent" and hide them
    tabcontent = document.getElementsByClassName("innertabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }

    // Get all elements with class="tablinks" and remove the class "active"
    tablinks = document.getElementsByClassName("innertablinks");
    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Show the current tab, and add an "active" class to the button that opened the tab
    document.getElementById(innerTabName).style.display = "block";
    evt.target.className += " active";
}
