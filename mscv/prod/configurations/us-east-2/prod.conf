#Spring social configuration
application.secureUrl="https://service-dr.socure.com"
facebook.longlived.accesstoken.url="https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token"
facebook.appid.url="https://graph.facebook.com/app?access_token="

#Mysql db configuration

db.driver="com.mysql.cj.jdbc.Driver"


db.url="*********************************************************************************************************************************************************************************************************************************************************************************************************"
db.username="socure-2018"
db.password="""ENC(02eDfk7M35lPjy2etx1caH+6E8l9yM4mL2eR3Ksr1uwVXmP9V+SOgDAyYI7V5yJdDuijhg==)"""
db.defaultAutoCommit="true"

# Model Management DB config
mmdb.driver="com.amazonaws.secretsmanager.sql.AWSSecretsManagerMySQLDriver"
mmdb.jdbcUrl="jdbc-secretsmanager:mysql://prod-model-management-2024-global-cluster.global-gqwjcey803sb.global.rds.amazonaws.com:3306/model_management"
mmdb.dataSourceName=model-management-db
mmdb.user="rds-pltv2-ue2-model-management-prod-7c426e-app"
mmdb.maxIdleTime=900
mmdb.maxConnectionAge=3600
mmdb.forceUseNamedDriverClass="true"

mmdb.maxPoolSize=50
mmdb.minPoolSize=3
mmdb.initialPoolSize=3
mmdb.testConnectionOnCheckIn="true"
mmdb.testConnectionOnCheckOut="false"
mmdb.idleConnectionTestPeriod=20

mmdb.useNewDB="true"
mmdb.modelIdSize=10

#Hibernate pool
hibernate.c3p0.initialPoolSize=10
hibernate.c3p0.min_size=10
hibernate.c3p0.max_size=25

hibernate.connection.provider_class="org.hibernate.connection.C3P0ConnectionProvider"
hibernate.c3p0.timeout=100
#you need to consider the number of frequently used PreparedStatements in your application, and multiply that by the number of Connections you expect in the pool (maxPoolSize in a busy application)
hibernate.c3p0.max_statements=500
hibernate.c3p0.idle_test_period=90

#Newly added attribute
hibernate.c3p0.unreturnedConnectionTimeout=10
hibernate.c3p0.debugUnreturnedConnectionStackTraces="true"
hibernate.c3p0.acquireincrement=8
hibernate.c3p0.maxconnectionage=3600
hibernate.c3p0.validationQuery="SELECT 1"
hibernate.c3p0.testOnBorrow="true"
hibernate.c3p0.numHelperThreads=200
hibernate.c3p0.automaticTestTable="tbl_hibernate_c3p0TestTable"
hibernate.c3p0.testOnCheckin=true

#Hibernate JConsole
hibernate.generate_statistics="false"

#Memcache Servers
memcache.servers="pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com:11211"
opt.timeout=500

#Static resources like js, css, images url from s3 bucket
static.resource.url="/resources"

#Debug Settings
debugMode="false"

#Scoring Cache Properties
#1 day(in seconds)
authscore.memcached.timout=86400
#1 day (in seconds)
authscore.low_confidence.memcached.timeout=86400
#7 days(in seconds)
authscore.high_confidence.memcached.timeout=604800
# The timeout(in days) for the scoring of a user. This is used for the second level cache.
authscore.last_scanned_days.cache.timeout=2


#Facebook throttling mail ID
fb.throttling.mail.to="Engineering <<EMAIL>>"


#Factual Credentials
factual.oauth.key="g1tCPVR41NlU96STdd5kw1LnYzHkCd5aAGoBfY3w"
factual.oauth.secret="GMHuY7NpNcNjnioQCepzhOSqKjynMaSZhLAXrot3"

#Scoring Component Memcache
component.email.memcached.timeout=15552000
component.form.memcached.timeout=15552000
component.fb.memcached.timeout=15552000
component.pipl.email.memcached.timeout=15552000
component.pipl.address.memcached.timeout=15552000
component.gplus.memcached.timeout=15552000
component.twitter.memcached.timeout=15552000
component.fullcontact.memcached.timeout=15552000
component.yahoo.memcached.timeout=15552000

#Scoring Component Database
db.component.email.timeout=15552000
db.component.form.timeout=15552000
db.component.fb.timeout=15552000
db.component.pipl.email.timeout=15552000
db.component.pipl.address.timeout=15552000
db.component.gplus.timeout=15552000
db.component.twitter.timeout=15552000
db.component.fullcontact.timeout=15552000
db.yahoo.memcached.timeout=15552000

#===============Mapquest Credentials===============
mapquest.oauth.key="""ENC(7exQGUlGsW+Uy3+/vPih6cpB8zTnOsa35SfIP6IGpjNnn78dM3bbfq//ib9p+3GOSMs4svoVzydDYE1D/Qb8zzSi)"""

#=============TowerData Integration=================
towerdata.apikey="""ENC(E/Mc9vaxntqLLMMk2Qdcev32S2WpukG0X1zj1m4ud4iCayKgNU18LcHh)"""

#==============FullContact Integration===============
fullcontact.apikey="""ENC(Of5zv2evRkyyzPG+6YzIwoaJ0gJzSQc8SkPb5DLq2y+NSyDrw4AEUVWkHIMTSKef)"""
fullcontact.webhook.base.url="https://stage.socure.com/"

#AWS Profile Image Bucket

#============== Admin Specific Properties =================

application.authScore.baseUrl="https://service-dr.socure.com" 

marketing.url="http://www.socure.com/"

application.logoutUrl="http://admin.socure.com.s3-website-us-east-2.amazonaws.com"

application.activation.url="https://dashboard-dr.socure.com/#/activate"
application.forget.pass.url="https://dashboard-dr.socure.com/reset_password/"

application.set.pass.url="https://dashboard-dr.socure.com/#!/reset_password_token"

#static resources


#============== Admin Specific Properties =================

#============== Empire properties ================
#annotation.index = annotation.config

#0.name = activity
#0.factory = sesame

# You can obtain this file at http://inkdroid.org/journal/2009/12/22/hacking-oreilly-rdfa/
#0.files = catalog.rdf

#0.name = Empire-JPA-Repo
#0.factory = sesame
#0.repo=activity
#0.url=http://ec2-107-22-72-245.compute-1.amazonaws.com:8080/openrdf-sesame/
#============== Empire properties ================

#============= Entity Image ==============
#In Seconds
cache.image.timeout=86400
#============= Entity Image ==============

#============= Hibernate Search ==============
hibernate.search.fuzzy.threshold=15
#============= Hibernate Search ==============


#===Component Timeout Config=================
component.default.timeout=2
fmval.timeout=2
fcval.timeout=2
yoval.timeout=2
pival.timeout=2
paval.timeout=2
boval.tiemout=2
lival.timeout=2
gpval.timeout=2
fbval.timeout=3
twval.timeout=2
kycofacval.timeout=2

api.v2.scheduler.timeout ="2400"
api.v2.overall.timeout ="3000"
api.system.overall.timeout=3200


#=============== Thread pool configuration ==================
thread.pool.executor.pool.size=400
thread.pool.executor.max.pool.size=700
thread.pool.executor.queue.capacity=0
thread.pool.executor.keep.alive.seconds=1

#=============== Thread pool configuration ==================

future.util.wait.duration=50

trace.authscore.factory="false"

#configure search for depth
#SAEARCH_LEVEL_DEPTH=1
#============= SLA ROLE =====================

component.default.timeout=4

default.sla.fmval.timeout=4
default.sla.fcval.timeout=4
default.sla.yoval.timeout=4
default.sla.pival.timeout=4
default.sla.paval.timeout=4
default.sla.boval.tiemout=4
default.sla.lival.timeout=4
default.sla.gpval.timeout=4
default.sla.fbval.timeout=4
default.sla.twval.timeout=4
default.sla.kycofacval.timeout=4

default.sla.api.v2.scheduler.timeout ="5000"
default.sla.api.v2.overall.timeout ="5400"
default.sla.api.system.overall.timeout=6000

#=======================================

highperformace.sla.fmval.timeout=2
highperformace.sla.fcval.timeout=2
highperformace.sla.yoval.timeout=2
highperformace.sla.pival.timeout=2
highperformace.sla.paval.timeout=2
highperformace.sla.boval.tiemout=2
highperformace.sla.lival.timeout=2
highperformace.sla.gpval.timeout=2
highperformace.sla.fbval.timeout=2
highperformace.sla.twval.timeout=2
highperformace.sla.kycofacval.timeout=2

highperformace.sla.api.v2.scheduler.timeout ="2400"
highperformace.sla.api.v2.overall.timeout ="2800"
highperformace.sla.api.system.overall.timeout=2900

#====================================================

#=============== PIPL Credentials ============
pipl.key="mau3erfkn3ek2qw5j5j4bd89"
pipl.premium.key="""ENC(iBxKkeW16i/y+8Ik0lg2ppZunwPyh5FW5dIXCgMA1J4P4yl00RWzBzoi+/JNKsROQUdk3f5oiwU=)"""
pipl.match.criteria="matching"
pipl.partial.threshold=5
#=============== PIPL Credentials ============

#=============== DSTK ============
data.science.toolkit.url="http://internal-prod-dstk-vpc-*********.us-east-1.elb.amazonaws.com/"
data.science.toolkit.fetch.timeout=1500
data.science.toolkit.fetch.timeunit="MILLISECONDS"
data.science.toolkit.sleep.duration.ms=20
#=============== DSTK ============


#============= Entity Resolution Filter Config =============#
default.er.component.filter.name=0.5
default.er.component.filter.email=0.5
default.er.component.filter.address=0.5
default.er.component.filter.geocode=0.5
default.er.component.filter.companyname=0.5
default.er.component.filter.nationalid=0.5
default.er.component.filter.dob=0.5
default.er.component.filter.mobilenumber=0.5
default.er.component.filter.username=0.5
default.er.component.filter.gender=0.5
default.er.component.filter.image=0.5

#============= Entity Resolution Filter Config =============#

#======App Environment==========#
appEnv="eks-prod"
#======App Environment==========#

#======TPAudit Stats========================#
aws.tp.stats.bucket="thirdparty-stats-prod-************-us-east-2"

#=======TPAudit Stats=======================#

#============= Super Admin Specific Config =============
#static resources
staticurl.super.admin="/resources"
isexternal.app="false"
#=====================================================

#================= Mail Config =================#
socure.support.email="<EMAIL>"
socure.sales.email="<EMAIL>"
mailgun.apikey="""ENC(/HkmYtyOQNeWEuPiouMrG3elgy6jvPVR8AwZn21qMdQP9/18LHq5G4E3t7belt73N0JWw4M3I4Jri/rny69s5d5xmxY=)"""
mailgun.domain="socure.com"
mailgun.from="Socure Support <<EMAIL>>"
#================= Mail Config =================#

## account service
account.service.url="http://account-service/"

#============= Rate Limit Service Configuration - Not Used(06/12/2023) ============= 
rate.limit.service.url="https://socure-rate-limiting-prod.us-east-1.elasticbeanstalk.com"
rate.limit.service.disabled="true"
rate.limit.service.apis.deprecated=false

#============= Mailgun config =============#
mailgun.endpoint="https://api.mailgun.net/v2/socure.com/messages"
mailgun.key="""ENC(HMITa0/agtJn6b7C0yBJg1ws9wF04fc3Axdy0rNN2vTPLRW2126aTeLAiYjJ7t0RI7KO2zyyUC+m2IeeWBA4CAOpQVA=)"""
mailgun.domain_name="socure.com"
#============= Datadog config =============#
#============= Model monitoring error reporting =============#
error.reporter.model.monitoring.subject="Model Monitoring Metrics Reporting Failure - Prod"
error.reporter.model.monitoring.from="<EMAIL>"
error.reporter.model.monitoring.to="<EMAIL>"
error.reporter.model.monitoring.cc=""
error.reporter.model.monitoring.bcc=""
#============= Model monitoring error reporting =============#

#============= Model Mapping =============#
h2opredictor.service.url="http://h2o-predictor-service"
h2opredictor.service.endpoint2="http://h2o-predictor-service"
h2omlpredictor.service.url="http://h2o-ml-predictor"
h2omlpredictor.service.endpoint2 = "http://h2o-ml-predictor"
modelmanagement {
  endpoint="http://model-management"
  endpoint2="http://model-management"
  hmac {
    realm = "Socure"
    version = "1.0"
    strength = 512
    secret.refresh.interval = 5000
    aws.secrets.manager.id="model-management/prod/hmac-8150e3"
  }
  enableModelManagement = "true"
}

#============= Audit Actioning =============#
sqsEndpoint="https://sqs.us-east-2.amazonaws.com/"
actionAuditS3BucketName=pltv2-ue2-action-auditing-************-us-east-2
actionAuditQueueName=pltv2-ue2-action-auditing-prod
region="us-east-2"
# batchrun-prod
kmsKey="ENC(QnJ0kB3Xc9u5oSEJNEkUXNGHyTaHrYI0wGR2A3bRYx/r0JsvU4qA66tHyb4TBrPC8Riz7Jjs1AtTqx1hKGkTQ1844DBU4rpzWoIfB7C/vcULPdKtlxH7wW236B+GBhXc2vcYcypceA5DJqw9C9ytalfCsMhvCK/IoAlPjhpNvC3S0mNijq+o03tmY+kgmtMOjONoNMJF2sUx3B3c7bSo3g==)"

#============= Audit Actioning =============#

s3.transaction_error_bucket="prod-audit-errors"

#============= Client specific encryption =============#
# prod-kms-client
client.specific.encryption.aws.access.key="""ENC(3neNdEq/TkHYcFoq64fEzR/9lWfJmVgocyUgvp/mxvBmfYPv5wtRqBGp2/3cXLG24hV5Hg==)"""
client.specific.encryption.aws.secret.key="""ENC(bfb3fGkYBBPkqAoR0QQw8TRO1EPUtJQFlfWEgOuHuhtbuz/+emEUHs5TwirTDdSK699NtMxGXzB190VYcZsecqlujs6DHYQ=)"""
client.specific.encryption.encryption.context.account_id.key="socure_account_id"
client.specific.encryption.operation.timeout.ms=20000
client.specific.encryption.kms.ids.us-east-1="arn:aws:kms:us-east-2:************:alias/socure/client-specific-encryption-prod"
client.specific.encryption.kms.ids.us-west-1="arn:aws:kms:us-west-1:************:alias/client-specific-encryption-prod"
#============= Client specific encryption =============#

#============= Image auditing bucket configuration =============#
idplus.audit.image.bucket="vendor-call-audit-errors-prod-************-us-east-2"
idplus.audit.image.encryption.mode="aws:kms"
idplus.audit.image.kms.id="arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
#============= Image auditing bucket configuration =============#

#Recaptcha Configuration
recaptcha.privatekey = "6LfGEwgTAAAAAFNaWC9Wwumw-HBqioJceicBR5s_"
recaptcha.publickey = "6LfGEwgTAAAAAPithJiufWOSfCifzwKN28Rgb_Yq"

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {
    maxRetries = 10

    primary {
      sqs {
        region=us-west-1
        transaction {
          queueName=transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-prod-************-us-east-2"
      }
      third-party {
        region=us-east-2
        bucket="thirdparty-stats-prod-************-us-east-2"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

#================ SP metadata endpoint ================#
saml2.sp.metadata {
  endpoint = "http://authentication-service"
  path = """ENC(+FJme/9DUEv0h09bV87VuCEzO1n39T1MjDvKAPHZg71Vqq6TAIigNXtbpZZkiPk+5+cQ/5QzxXPfeqNdUelM8E7g8XEKRoEPLbAX6TSNolN7tQ==)"""
  token = """ENC(4oBCFyj+XetHRU8VHXNO43xk+VSte+0NDEecKIENbb/KcSo9kl44Jlnba/xWpEpbReHa8hr+v5dL7d9QVYWbJHPXzvurE1qXsDZ9NjT2MAoAfg01P29w10NSb+CzoboWKdRCH9E8cbUdEDMnV5zs2lowqC5CON1awSULgFfAAmzpSweO)"""
}
#================ SP metadata endpoint ================#

transaction.auditing {
  endpoint = "http://transaction-auditing-service"
  endpoint2 = "http://transaction-auditing-service"
  hmac {
    secret.key="""ENC(qkBSLhaiZPAgJMytRmXsQHhznOr2R6uIBSpnTW87Xzph/9saoPwMXdeKKclSWHK/h6hHNDIIsw==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
}

#================ File storage download config ================#
file.storage.download {
  endpoint = "http://file-storage-download"

  dynamic.control.center{
    s3 {
      bucketName="pltv2-ue2-globalconfig-************-us-east-2"
      kms.key = "arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
    }
    memcached {
      host="pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    secret.key = """ENC(U+dSKP2In8bX9LCItNiDcmWUJd50c1ukKc2erN9ydpEvCKRDRDALhp4zNNdnKLmbHkL/oFu20Lz0qTPhkqp9fQ==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
}
#================ File storage download config ================#

#================ AlertList Maintainer =================#
alertlist.maintainer {
  endpoint = "http://alertlist-maintainer/"
  hmac {
    secret.key = """ENC(8mnNn5XZIe0D7bpMQKVICq0doOFy3F1LVjfawAoFQOlve5IRHcsE93ayPSl5FMInDDdRQJC77/UDVA==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ AlertList Maintainer =================#

#================ SAML ================#
saml {

  assertionConsumerServiceUrl = "https://superadmin.webapps.us-east-2.prod.socure.link/saml2/SSO"
  maxAuthenticationAge = 43200 # 12 hours
}

#================ SAML ================#

authentication.service {
    endpoint = "http://authentication-service"
    endpoint2="http://authentication-service"
    groupName="UXServicesRamp"
    flagName="AuthenticationService_Ramp"

    hmac {
      secret.key="""ENC(eLJdg/EXhQNfoNmfGMnRL/eVYxOIAOEQuX+JUSVDv26knUgZesr2lGqTG+xbHrN43s8I8TDbX+ynFAWn858Wrw==)"""
      strength=512
      realm="Socure"
      version = "1.0"
    }
}

#================ Account Service config ================#
account.service {
  endpoint="http://account-service"
  endpoint2="http://account-service"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"

  hmac {
    secret.key="""ENC(eMMykO4EF/wG2XxozoWlPoIMungWFc2GTTDZy0DhnU2vSFNC6jX05VuI55wzUG8/NZNQ5R6K6wOGywXbZ9NIow==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ Account Service config ================#

#================ Reasoncode Service config ================#
reasoncode.service {
  endpoint="http://reasoncode-service"
  hmac {
    secret.key="""ENC(heIvC0MhkAnR/vU9ama9D5pemvo43Y+iXly7d4LVhZ41Ps8H9RODZWYbGJuihOvJdUnY7TZO5umAS3ab1qY/OA==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ Reasoncode Service config ================#

#================ Event Auditing Service =================#
event.auditing{
  security{
    endpoint = "http://event-auditing-service/api"
    hmac {
      realm="Socure"
      version=1.0
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="event-auditing/prod/hmac-50c470"
      secret.refresh.interval=5000
    }
  }
}

#================ Event Auditing Service =================#

#================ Batch Job Service ================#

batch.job {
  endpoint="http://batch-job-service"
  endpoint2="http://batch-job-service"
  groupName="UXServicesRamp"
  flagName="BatchJobService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-prod"
      kms.key = "arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
    }
    memcached {
      host="pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "batchjob-service/prod/hmac"
    secret.refresh.interval = 5000
    realm="Socure"
    version = "1.0"
  }
}

#================ Batch Job Service ================#

#================ Rulecode Service config ================#
rulecode.service {
  endpoint = "http://rulecode-service"
  endpoint2 = "http://rulecode-service"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="rulecode-service/prod/hmac-e6b65a"
    secret.refresh.interval=5000
  }
}
#================ Rulecode Service config ================#
#=================Rulecode Ingestion config =============#
rulecode.ingestion{
    aws{
        s3{
            bucketname="rulecode-audit-prod-************-us-east-2"
            kmsKey="arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
        }
    }
}
#=================Rulecode Ingestion config =============#
#=================Rulecode Studio config =============#

rulecode.studio {
    git{
        projectId = "235"
        access_token = """ENC(H6hQfZ+VfMMIX8KYglWrB8jhJsr5a/GR7khljnZl/uOuh3nNYH+E/XcPrgiA82rwxKoZJw==)"""
        rulecodePath = "rulecode_definitions/"
        rulecodePathV2 = "rulecode_definitions_v2/"
        dependencyPath = "table_definitions/"
        branchName = "master"
        baseUrl = "https://gitlab-ee.us-east-vpc.socure.be/api/v4/projects/"
    }
   s3{
        audit{
            bucketname="rulecode-audit-prod-************-us-east-2"
            kmsKey="arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
            rulecodePath = "studio/rulecode_definitions/"
            rulecodePathV2 = "studio/rulecode_definitions_v2/"
            dependencyPath = "studio/table_definitions/"
        }
        config{
            bucketname="rulecode-audit-prod-************-us-east-2"
            rulecodePath = "rulecode_definitions/"
            rulecodePathV2 = "rulecode_definitions_v2/"
            dependencyPath = "table_definitions/"
        }
        data{
            bucketname="rulecode-audit-prod-************-us-east-2"
            bulkFilePath = "rulecode_create_data/"
        }
   }
   test{
        envUrl = "https://service-dr.socure.com"
        inputBucketname="rulecode-data-prod-************-us-east-2"
        outputBucketname="batch-job-storage-prod"
        prefix = "studio-test/"
   }
}
#=================Rulecode Studio config =============#

#=================Document Manager config =============#
document.manager {
  document.manager {
    endpoint="http://document-manager"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager/prod/hmac-8a3e2a"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host=pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com
      port=11211
    }
  }

  dynamic.control.center {
    s3 {
      bucketName="pltv2-ue2-globalconfig-************-us-east-2"
      kms.key = "arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
    }
    memcached {
      host="pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }
}
#=================Document Manager config =============#
#=================Decision Service Config===============#
decision.service {
  endpoint = "http://decision-service"
  endpoint2="http://decision-service"
  groupName="UXServicesRamp"
  flagName="DecisionService_Ramp"
}
#=================Decision Service Config===============#
#=================Sandbox Service Config===============#
sandbox.service {
  endpoint = "http://idplus-sandbox"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="idplus-sandbox/prod/hmac"
    secret.refresh.interval=5000
  }
}
#=================Sandbox Service Config===============#

#=================StepUp Service=====================#
stepUp.service {
  endpoint="https://stepup.socure.com/"
  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "step-up-service/prod/hmac-607964"
    secret.refresh.interval = 5000
    realm = "Socure"
    version = "1.0"
  }
  metrics.enabled = false
}
#=================StepUp Service=====================#
#=================Docv Orchestra Service=====================#
 docvOrchestra {
     endpoint = "http://docv-orchestra-service/"
     hmac {
        realm="Socure"
        version = "1.0"
        strength=512
        aws.secrets.manager.id="docv-orchestra/prod/hmac"
      }
      metrics.enabled = false
  }
#=================Docv Orchestra Service=====================#

#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "pltv2-ue2-idplus-global-settings-************-us-east-2"
    kms.key = "arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
  }
  memcached {
    host=pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com
    port=11211
    ttl="24 hours"
  }
}

#===================Control Center==========================#

#===================batchjob==========================#
batch.job {
  aws {
    s3 {
      bucket.name = "batch-job-storage-prod"
      kms.key = """ENC(6aqiBAKHBhPG5lmQOpaNp24EVACFjMNs9nmOVUEOQoXB+WFflLiOcEy3IYPc1L5+UBMp9xSIwYWhz1d3pFIVhKW49B+3k+S4E5SIXeSyCv3yaqnV4l/JWm/zO/NSY6i2IMzp3Bdfp5hdVzM=)"""
    }
  }
}

#===================batchjob==========================#

#===================Dynamic Control Center==========================#

dynamic.control.center {
  s3 {
    bucketName = "pltv2-ue2-globalconfig-************-us-east-2"
    kms.key = "arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
  }
  memcached {
    host=pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

#================ ThirdParty Reader Service config ================#
thirdparty.reader.service {
  endpoint = "http://thirdparty-reader-service"

  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="thirdparty-reader-service/prod/hmac-12ae651b"
    secret.refresh.interval=5000
  }
  metrics.enabled = false
}
#================ ThirdParty Reader Service config ================#

#===================sftp==========================#
sftp {
 role="arn:aws:iam::************:role/socure-transfer-deliveries-users"
 homeDirectoryType="LOGICAL"
 region="us-east-1"
 serverId="s-1edb1c1a1fc442da8"
 target="/client-datafile-prod/"
 entry="/"
}
#===================sftp==========================#

txn.case.workflow.service {
  endpoint = "http://txn-case-workflow-service"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "txn-case-workflow-service/prod/hmac"
    secret.refresh.interval = 5000
  }
}

globalwatchlist.service {
  endpoint = "http://watchlist-private-service/api"
  endpoint2="http://watchlist-private-service/api"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="watchlist-private-service/prod/hmac"
    secret.refresh.interval=5000
  }
}

entity.feedback.reader {
  endpoint = "http://entity-feedback-service"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "entity-feedback-service/prod/hmac"
    secret.refresh.interval = 5000
  }
}

superadmin.hmac="superadmin-backend/prod/hmac"

#===================docv-berbix==========================#
berbix.v1 {
   	endpoint="http://docv-bb-backend"
    hmac {
        secret.key="""ENC(gO8DB68gU4Kfl5oqaLn3QFg/w/IZ/sXOdab8aRnCvFSZdWsAuwsQy8+6VKi6p+om6E+SGgSEa94BwV3ERklkCFmvjMWFU5LkX+tUBQ==)"""
        strength=512
        realm="Socure"
        version="1.0"
    }
}

cors{
    alloweddomains = ["https://superadmin.webapps.us-east-2.prod.socure.link/"]
}

launchdarkly {
  sdk.key = "ENC(Sa2DsunYtlXFkjKPzL3yrLWzpFVCwbWUk6BMhltiCMt/mqibfYAYtyW53DS/v6Z2tSLRTzY673SRBy3gZl4utTaYEgBT1Q1C)"
  use.fedramp.version = false
}

kyc {
  endpoint = "http://kyc-search-service"
  recordcorrection.endpoint = "http://transaction-resolved-entity-worker/es/record-correction"
}

