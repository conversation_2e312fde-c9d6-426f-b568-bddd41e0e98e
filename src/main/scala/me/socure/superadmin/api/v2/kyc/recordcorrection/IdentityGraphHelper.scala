package me.socure.superadmin.api.v2.kyc.recordcorrection

import com.fasterxml.jackson.databind.JsonNode
import com.typesafe.config.Config
import me.socure.thirdparty.reader.service.ThirdPartyReaderServiceClient
import org.json4s.native.JsonMethods.parse
import org.json4s.{DefaultFormats, Formats}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component
import org.json4s.jackson.JsonMethods.asJsonNode
import dispatch.url
import me.socure.common.http.NonSecuredHttpFactory
import org.json4s.jackson.Serialization

import java.nio.charset.Charset
import scala.collection.JavaConverters._
import scala.concurrent.{Await, ExecutionContext}
import scala.concurrent.duration.Duration

@Component
@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
class IdentityGraphHelper {

  private implicit val formats: Formats = DefaultFormats

  private val httpClient = new NonSecuredHttpFactory().getHttpClient()

  @Autowired
  implicit var executionContext: ExecutionContext = _


  @Autowired
  var thirdPartyReaderServiceClient: ThirdPartyReaderServiceClient = _

  @Autowired
  var config: Config = _


  def getDetailsFromTransaction(transactionId: String): (String, JsonNode, java.util.List[String]) = {
    val tpAudit = thirdPartyReaderServiceClient.fetchThirdPartyAuditData(transactionId, scala.Option.empty, scala.Option.empty)
    val kycResponseBody = if(tpAudit.containsKey("KYC_SEARCH_SERVICE_RESPONSE")) {
      parse(tpAudit.get("KYC_SEARCH_SERVICE_RESPONSE").values().iterator().next().get("Response"))
    } else {
      parse(tpAudit.get("EQUIFAX").values().iterator().next().get("Response")) \ "data"
    }

    val socureID = (kycResponseBody \ "uniqueEntityId").extract[String]
    val mergedEntity = asJsonNode(kycResponseBody \ "mergedEntity")
    val entityIDs = (kycResponseBody \ "kyc" \ "cid").extract[List[String]].flatMap(_.split(",").toList).map(_.trim).filter(_.nonEmpty).asJava
    (socureID, mergedEntity, entityIDs)
  }

  def getAdditionalEntitiesBasedOnPII(piiField: String, value: String): JsonNode = {
    val httpRequestBody = Serialization.write(
      Map.apply(
        ("entityType", piiField),
        (piiField, value)
      )
    )
    val httpRequest = url(config.getString("kyc.recordcorrection.endpoint") + "/fetchEntityDetailsForCG")
      .setHeader("Accept", "application/json")
      .setHeader("Content-Type", "application/json")
      .setBodyEncoding(Charset.forName("UTF-8")) << httpRequestBody
    Await.result(
      httpClient(httpRequest).map(response => {
        if (response.getStatusCode == 200) {
          asJsonNode(parse(response.getResponseBody))
        } else {
          throw new RuntimeException("An unknown error occurred while retrieving additional entities.")
        }
      }),
      Duration.Inf
    )
  }

}
