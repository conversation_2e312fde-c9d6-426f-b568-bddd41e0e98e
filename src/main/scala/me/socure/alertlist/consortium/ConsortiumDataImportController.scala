package me.socure.alertlist.consortium

import com.fasterxml.jackson.databind.ObjectMapper
import me.socure.alertlist.maintainer.client.ConsortiumImporterClient
import me.socure.alertlist.matcher.common.{DatasetVersionResponse, GenericErrorResponse, ValidationFailureResponse}
import me.socure.common.json.bean.APIOutput
import me.socure.service.constants.IConstants
import me.socure.superadmin.factory.ObjectMapperFactory
import org.apache.commons.io.IOUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.{HttpHeaders, HttpStatus, ResponseEntity}
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{RequestMapping, RequestMethod, RequestParam}
import org.springframework.web.multipart.MultipartFile

import scala.concurrent.Await
import scala.concurrent.duration._
import scala.util.{Failure, Success, Try}

@Controller
@RequestMapping(Array("/superadmin/1/consortium/*"))
class ConsortiumDataImportController() {

  @Autowired
  private var consortiumImporterClient: ConsortiumImporterClient = _

  import ConsortiumDataImportController._

  @RequestMapping(value = Array("/import"), method = Array(RequestMethod.POST))
  def importConsortiumData(@RequestParam("file") multipartFile: MultipartFile): ResponseEntity[Object] = {
    val result = Try(Await.result(consortiumImporterClient.importData(data = multipartFile.getInputStream), 10.minutes))
    val responseEntity = result match {
      case Success(DatasetVersionResponse(datasetVersion)) =>
        logger.info(s"Successfully imported consortium data to version : $datasetVersion")
        jsonRes(
          apiStatus = IConstants.API_OUTPUT_STATUS_OK,
          data = Some(int2Integer(datasetVersion.value)),
          msg = s"Successfully imported data to version [${datasetVersion.value}]",
          status = HttpStatus.OK
        )
      case Success(GenericErrorResponse(errorResponse)) =>
        logger.error(s"Error while importing consortium data due to : $errorResponse")
        jsonRes(
          apiStatus = IConstants.API_OUTPUT_STATUS_ERROR,
          data = Some(int2Integer(errorResponse.code)),
          msg = errorResponse.message,
          status = HttpStatus.INTERNAL_SERVER_ERROR
        )
      case Success(ValidationFailureResponse(validationFailureStream)) =>
        logger.error(s"Validation error while importing consortium data")
        val resource = new ByteArrayResource(IOUtils.toByteArray(validationFailureStream))
        new ResponseEntity[Object](resource, HttpStatus.UNPROCESSABLE_ENTITY)
      case Failure(exception) =>
        logger.error("Error while importing consortium data", exception)
        jsonRes(
          apiStatus = IConstants.API_OUTPUT_STATUS_ERROR,
          data = None,
          msg = exception.getMessage,
          status = HttpStatus.INTERNAL_SERVER_ERROR
        )
    }
    responseEntity
  }
}

object ConsortiumDataImportController {
  private val logger = LoggerFactory.getLogger(getClass)
  private val objectMapper = new ObjectMapper()

  private def jsonRes(apiStatus: String, data: Option[Object], msg: String, status: HttpStatus): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    apiOutput.setStatus(apiStatus)
    apiOutput.setData(data.orNull)
    apiOutput.setMsg(msg)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, status)
  }
}
