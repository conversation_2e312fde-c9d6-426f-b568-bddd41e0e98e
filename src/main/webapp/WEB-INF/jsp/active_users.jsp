<html>
  <head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>DataTables live example</title>
	<script type="text/javascript" charset="utf-8" language="javascript" src="jquery.dataTables.js"></script>
  </head>
 <body>
		<div class="container" style="margin-top: 10px">
			
<table cellpadding="0" cellspacing="0" border="0" class="table table-striped table-bordered" id="example">
	<thead>
		<tr>
			<th>Rendering engine</th>
			<th>Browser</th>
			<th>Platform(s)</th>
			<th>Engine version</th>
			<th>CSS grade</th>
		</tr>
	</thead>
	<tbody>
		<tr class="odd gradeX">
			<td>Trident</td>
			<td>Internet
				 Explorer 4.0</td>
			<td>Win 95+</td>
			<td class="center"> 4</td>
			<td class="center">X</td>
		</tr>
		<tr class="even gradeC">
			<td>Trident</td>
			<td>Internet
				 Explorer 5.0</td>
			<td>Win 95+</td>
			<td class="center">5</td>
			<td class="center">C</td>
		</tr>
	</tbody>
</table>
			
		</div>
<script>
		$(document).ready(function() {
			$('#example').dataTable();
		});
	</script>
	</body>
</html>
