import React from 'react';
import { Alert } from '@socure/components';
import { toast } from 'react-toastify';

const ALERT_TIMER = 3000;

export const TOAST_PROPS_DEFAULT = {
  position: toast.POSITION.BOTTOM_CENTER,
  hideProgressBar: true,
  autoClose: ALERT_TIMER,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: false,
  progress: undefined,
  closeButton: false
}


const showError = (message: string): void => {
  toast(<Alert
    variant='error'
    type='snackbar'
    message={message} />,
    TOAST_PROPS_DEFAULT
  );
}
const showBasicErrorToastWithoutTimer = (message: string): void => {
  toast(<Alert
    variant='error'
    message={message}
    primaryButtonLabel='dismiss' />,
    { ...TOAST_PROPS_DEFAULT, autoClose: false }
  );
}

export const showErrorToast = (error: any): void => { //eslint-disable-line @typescript-eslint/no-explicit-any
  if (typeof error === 'string') {
    showError(error);
  } else {
    const message = error?.response?.data?.data?.message || error?.data?.data?.message || error?.data?.message || 'Unknown error!';
    showError(message);
  }
}

export const showErrorToastWithoutTimer = (error: any): void => {
  if (typeof error === 'string') {
    showBasicErrorToastWithoutTimer(error);
  } else {
    const message = error?.response?.data?.data?.message || error?.data?.data?.message || error?.data?.message || 'Unknown error!';
    showBasicErrorToastWithoutTimer(message);
  }
}


const showBasicToast = (message: string, variant: "success" | "error" | "information" | "timeout"): void => {
  toast(<Alert
    variant={variant}
    message={message}
    type='snackbar'
    primaryButtonLabel='dismiss' />,
    TOAST_PROPS_DEFAULT
  );
}

export const showSuccessToast = (message: string): void => {
  showBasicToast(message, 'success');
}

export const showInfoToast = (message: string): void => {
  showBasicToast(message, 'information');
}

export const showSuccessToastWithoutTimer = (message: string): void => {
  toast(<Alert
    variant='success'
    message={message}
    primaryButtonLabel='dismiss' />,
    { ...TOAST_PROPS_DEFAULT, autoClose: false }
  );
}