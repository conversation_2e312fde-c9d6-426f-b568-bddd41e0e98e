<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=1200" />
    <title>SFTP Users</title>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <link rel="shortcut icon" type="image/ico"
          href="http://www.datatables.net/favicon.ico" />
    <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
    <link rel="stylesheet"
          href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

    <link rel="stylesheet"
          href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

    <link rel="stylesheet" type="text/css"
          href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
    <link rel="stylesheet" type="text/css"
          href="#{bucket.resourceURL}/styles/pagination.css" />
    <link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
          media="print" />
    <link rel="stylesheet" href="#{bucket.resourceURL}/styles/chosen.min.css"/>
    <style>
		.chosen-search input{
			margin-left:0 !important;
		}
		.modal-body{
			overflow-y: inherit;
		}
	</style>

    <script>
        var bucketurl = "#{bucket.resourceURL}";
        var contexturl = "#{request.contextPath}";
        var marketingurl = "#{bucket.marketingSite}";
        loggedinUsername = "#{loggedinUsername}";
        appenv = "#{bucket.appEnv}";
	</script>
    <script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>

<h:body>
    <div align="right" style="padding-top: 15px;">
        <h:outputLink value="leaderboard">Leaderboard</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
        <h:outputLink value="industry">Manage Industry</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
        <h:outputLink value="active_users">Active Users</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
        <h:outputLink value="inactive_users">Inactive Users</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
        <h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
        &nbsp;&nbsp; | &nbsp; &nbsp;
        <h:outputLink value="logout">Logout</h:outputLink>
    </div>
    <br/>
    <br/>
    <center><h3>SFTP Users</h3></center>
    <hr/>


    <section id="Loading" class="overlay show">
        <p>Loading ...</p>
        <div class="loader">
            <img src="#{bucket.resourceURL}/assets/images/socure-loader.svg" />
            <span class="dot"></span>
        </div>
    </section>

    <div align="center">
        <font color='blue' id='msg_font'><label id="super_admin_msg"></label>
        </font>
    </div>
    <div class="container">
        <div class="col-8 row col-form-label">
            Note: SFTP user account can only be generated if the account has PGP key.
        </div>
        <div class="form-group row align-items-center">
            <label  class="col-2 col-form-label">Select Account: </label>

            <select name="accountList" id="accountList" class="chosen" onchange="handleAccountSelect(this)">
                <option value="none">--Select Account--</option>
            </select>
        </div>
        <div class="form-group row align-items-center">
            <label  class="col-2 col-form-label">SFTP Username: </label>
            <input type="text" name="sftpUsername" id="sftpUsername" required="true" />
        </div>
        <div class="form-group row align-items-center">
            <label  class="col-2 col-form-label">SSH Key: </label>
            <input type="text" name="sshKey" id="sshKey" required="true" />
        </div>
        <div class="form-group row align-items-center">
            <input type="button" class="btn btn-primary" value="Create" onclick="createSFTPUser()" />
        </div>
    </div>
    <div class="accordion" id="accordion2">
        <div class='container' style='margin-top: 10px'>
            <table cellpadding='0' cellspacing='0' border='0'
                   class='table table-striped table-bordered datatable' id='sftp_users_list'>
                <thead>
                <tr>
                    <th>Account ID</th>
                    <th>Account Name</th>
                    <th>SFTP Username</th>
                    <th>Created At</th>
                    <th>View</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>


    <script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
    <script src="#{bucket.resourceURL}/scripts/chosen.jquery.min.js"></script>
    <script src="#{bucket.resourceURL}/scripts/lib.js"></script>
    <script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>

    <script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
    <script src="#{bucket.resourceURL}/scripts/jquery-dateFormat.min.js"></script>

    <script type="text/javascript"
            src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/sftp_users.js"></script>
</h:body>
</html>