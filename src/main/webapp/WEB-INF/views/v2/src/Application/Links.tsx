import React, { useMemo } from 'react';
import { G<PERSON>, <PERSON>, Typography } from '@mui/material';

const linksList = [
    {
        label: "Active Users",
        target: "/active_users"
    },
    {
        label: "Troubleshooting",
        target: "/troubleshooting"
    },
    {
        label: "Record Correction",
        target: "/v2/record_correction"
    }
];

const Links: React.FC = function (props) {
    const rendering = useMemo(() => (
        <React.Fragment>
            <Grid direction="row" container spacing={2} key="Links">
                {linksList.map((_: any, i) => {
                    return <Grid item key={i}>
                        <Link href={_.target}>
                            <Typography>
                                {_.label}
                            </Typography>
                        </Link>
                    </Grid>
                })}
            </Grid>
        </React.Fragment>
    ),
        []
    );

    return rendering;
}

export default Links;
