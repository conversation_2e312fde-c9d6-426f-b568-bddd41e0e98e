import { getFraudModelList, getFraudModels } from "../services";
import { FraudModelAutoComplete, ModelInfo } from "../types";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const handleError = (error: any) => {
  if (typeof error === "string" || !error) {
    return error;
  }
  return error.response?.data?.data?.message || error.data?.data?.message;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getSuccessMessage = (response: any): string => {
  return (
    response.data?.data?.message ||
    response.data?.message ||
    response.data?.msg ||
    ""
  );
};

export const getModelDescriptionByModelName = (
  modelName: string,
  models: ModelInfo[]
): string => {
  const modelObj = models.find((model) => model.name === modelName);

  if (modelObj == undefined) {
    return "Current model is deprecated.";
  } else if (modelObj.description != undefined && modelObj.description != "") {
    return modelObj.description;
  } else {
    return "No description.";
  }
};

export const getModelAndSetDescription = (
  activeModel: FraudModelAutoComplete,
  modelsAndDescriptions: ModelInfo[],
  setDescription: (value: string) => void,
  setModelsAndDescriptions: (value: ModelInfo[]) => void
) => {
  if (modelsAndDescriptions?.length > 0) {
    setDescription(
      getModelDescriptionByModelName(activeModel.label, modelsAndDescriptions)
    );
    return;
  }
  if (app.modelManagement) {
    getFraudModels().then((data) => {
      setModelsAndDescriptions(data);
      setDescription(getModelDescriptionByModelName(activeModel.label, data));
    });
  } else {
    getFraudModelList().then((data) => {
      setModelsAndDescriptions(data);
      setDescription(getModelDescriptionByModelName(activeModel.label, data));
    });
  }
};

export const getAssociationNameByType = (associationType: number): string => {
  if (associationType === 1) {
    return "Primary";
  } else if (associationType === 2) {
    return "Sigma";
  } else if (associationType === 3) {
    return "Custom";
  }
  return "Shadow";
};
