package me.socure.account.provisioning

import com.fasterxml.jackson.datatype.joda.JodaModule
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.google.common.base.Strings
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.resolver.BatchJobClientResolver
import me.socure.common.clock.RealClock
import me.socure.common.json.bean.APIOutput
import me.socure.docv.AuthenticIdResolver
import me.socure.docv.common.dto.AccountStrategyDto
import me.socure.model.account.automation
import me.socure.model.account.automation.UpdateAccountProvisioningDetails
import me.socure.model.account.data.retention
import me.socure.model.sai.SAIPreferences
import me.socure.model.{AccountProducts, ErrorResponse, UpdateProduct}
import me.socure.service.accountservice.AccountInfoResolver
import me.socure.service.constants.IConstants
import me.socure.superadmin.authentication.UserDetailsWithoutCredentials
import me.socure.superadmin.factory.ObjectMapperFactory
import me.socure.util.{AccountUtil, SecureUtil}
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.{HttpHeaders, HttpStatus, MediaType, ResponseEntity}
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{PathVariable, RequestBody, RequestMapping, RequestMethod}

import scala.collection.JavaConversions.asScalaBuffer
import scala.concurrent.ExecutionContext

@Controller
@RequestMapping(Array("/automation"))
class AccountAutomationController {
  private val logger = LoggerFactory.getLogger(getClass)
  private val clock = new RealClock
  @Autowired
  var batchJobClientResolver: BatchJobClientResolver = _

  @Autowired
  var bundleManagerResolver: BundleManagerResolver = _

  @Autowired
  var accountAutomationResolver: AccountAutomationResolver = _

  @Autowired
  var authenticIdResolver : AuthenticIdResolver = _

  @Autowired
  private var accountInfoResolver: AccountInfoResolver = _

  @Autowired
  var accountUtil: AccountUtil = _
  @Autowired
  var executionContext: ExecutionContext = _
  private var WATCHLIST_MONITORING_PRODUCT_ID = 8;
  @RequestMapping(value = Array("/bundles"), method = Array(RequestMethod.GET))
  def getBundleInformation: ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
    apiOutput.setData(bundleManagerResolver.getBundleInformation)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    val mapper = ObjectMapperFactory.get()
    mapper.registerModule(DefaultScalaModule)
    new ResponseEntity[Object](mapper.writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  @RequestMapping(value = Array("/account/{accountId}"), method = Array(RequestMethod.GET))
  def getAccountProvisioningInformation(@PathVariable("accountId") accountId: Long): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val headers = new HttpHeaders()
    val mapper = ObjectMapperFactory.get()
    headers.add("Content-Type", "application/json")
    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
    accountAutomationResolver.getAccountProvisioningDetails(accountId) match {
      case Right(response) =>
        logger.info("getAccountProvisioningDetails - response:", response)
        val productsAsJava = new java.util.ArrayList[DtoAccountProducts]()
        response.products.foreach(product => productsAsJava.add(getAccountProductsAsJava(product)))
        val ap = new AccountProvisioning()
        val pc = new ProductConfiguration()
        if(response.productConfiguration.ein.isDefined) pc.setEin(response.productConfiguration.ein.get)
        if(response.productConfiguration.saiPreferences.isDefined) pc.setSaiPreferencesFromAccService(response.productConfiguration.saiPreferences.get)
        if(response.productConfiguration.retentionSchedule.isDefined) {
          pc.setRetentionSchedule(response.productConfiguration.retentionSchedule.get.cadence, response.productConfiguration.retentionSchedule.get.routine)
        }
        if(response.productConfiguration.lookupApiKey.isDefined) pc.setLookupApiKey(response.productConfiguration.lookupApiKey.get)
        if(response.productConfiguration.serviceId.isDefined) pc.setServiceId(response.productConfiguration.serviceId.get)

        val accountPublicId = accountInfoResolver.getAccountNameAndPublicId(accountId).getId
        val docVStrategy = getAccountDocVStrategy(accountPublicId)
        docVStrategy match {
          case Right(strategy) =>
            pc.setDocVStrategy(strategy)
          case Left(error) =>
            logger.error(" docVStrategy error :", error)
        }

        ap.setBundleReference(response.bundleReference.getOrElse(""))
        ap.setProducts(productsAsJava)
        ap.setProductConfiguration(pc)
        apiOutput.setData(ap)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        mapper.registerModule(DefaultScalaModule)
        mapper.registerModule(new JodaModule())
      case Left(errorResponse) =>
        logger.error("errorResponse :", errorResponse)
        apiOutput.setMsg(errorResponse)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
    }
    new ResponseEntity[Object](mapper.writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  private def getAccountDocVStrategy(accountPublicId: String): Either[String, String] = {
    val result = try {
      authenticIdResolver.fetchAssociatedAuthenticIdStrategy(accountPublicId)
    } catch {
      case exception: Throwable =>
        logger.info(exception.getMessage)
        Left(ErrorResponseFactory.get(100, exception.getMessage))
    }
    result match {
      case Right(strategy) if strategy.strategyId.isDefined => Right(strategy.strategyId.get)
      case Right(_) => Right(StringUtils.EMPTY)
      case Left(error) =>
        val failureMessage = s"Failed to get DocV Strategy for account $accountPublicId. Error: ${error.message}"
        logger.error(failureMessage)
        Left(failureMessage)
    }
  }

  @RequestMapping(value = Array("/account/{accountId}"), method = Array(RequestMethod.POST), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def updateAccountProvisioningInformation(@PathVariable("accountId") accountId: Long,
                              @RequestBody updateAccountProvisioningRequest: UpdateAccountProvisioningRequest): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val headers = new HttpHeaders()
    val mapper = ObjectMapperFactory.get()
    headers.add("Content-Type", "application/json")
    val userDetails = SecurityContextHolder.getContext.getAuthentication.getPrincipal.asInstanceOf[UserDetailsWithoutCredentials]
    val initiatedBy = userDetails.getUsername

    val bundle = updateAccountProvisioningRequest.getBundleReference.trim
    if (bundleManagerResolver.bundles.exists(_.name.equals(bundle)) || bundle.isEmpty) {
      var httpStatus = HttpStatus.OK
      apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
      val products = updateAccountProvisioningRequest.getProducts.map(p => UpdateProduct(p.getId, p.getProvisioned, p.getEnabled))
      accountAutomationResolver.saveAccountProvisioningDetails(accountId,
        getUpdateAccountProvisioningDetails(bundle,
          products =  products,
          updateAccountProvisioningRequest.getProductConfiguration,
          initiatedBy = initiatedBy)) match {
        case Right(_) =>
          val updateDocVStrategyResult = updateDocVStrategy(accountId, updateAccountProvisioningRequest.getProductConfiguration.getDocVStrategy)
          updateDocVStrategyResult match {
            case Right(_) =>
              if(updateAccountProvisioningRequest.getUnEnrollWatchListMonitors){
                val unEnrollWLMonitorResult = triggerUnEnrollWLMonitorJob(accountId) //Trigger batch job to un-enroll watchlist monitors associated with the account
                unEnrollWLMonitorResult match {
                  case Right(_) =>
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
                  case Left(error) =>
                    val failureMessage = s"Failed to trigger disable monitor job for account $accountId. Error: ${error.message}"
                    logger.error(failureMessage)
                    apiOutput.setMsg(failureMessage)
                    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
                }
              } else {
                apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
              }
            case Left(error) =>
              val failureMessage = s"Failed to update DocV Strategy for account $accountId. Error: ${error.message}"
              logger.error(failureMessage)
              apiOutput.setMsg(failureMessage)
              apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
          }

          accountUtil.invalidateSession(accountId.toString)
          mapper.registerModule(DefaultScalaModule)
          mapper.registerModule(new JodaModule())
        case Left(errorResponse) =>
          apiOutput.setMsg(errorResponse)
          apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
          httpStatus = HttpStatus.INTERNAL_SERVER_ERROR
      }
      new ResponseEntity[Object](mapper.writeValueAsString(apiOutput), headers, httpStatus)
    } else {
      apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
      apiOutput.setMsg("Invalid Bundle Reference")
      new ResponseEntity[Object](mapper.writeValueAsString(apiOutput), headers, HttpStatus.BAD_REQUEST)
    }
  }

  private def getUpdateAccountProvisioningDetails(bundle: String, products: Seq[UpdateProduct], pc: ProductConfiguration, initiatedBy: String) = {
    val retentionSchedule =
      if(pc.getRetentionSchedule != null)
        Some(
          retention.RetentionSchedule(
            pc.getRetentionSchedule.getCadence,
            pc.getRetentionSchedule.getRoutine
            ))
      else None

    val lookupApiKey = (null != pc.lookupApiKey && !"".equalsIgnoreCase(pc.lookupApiKey.trim)) match {
      case true=>Some(pc.lookupApiKey)
      case false=>None
    }
    val serviceId = (null != pc.serviceId && !"".equalsIgnoreCase(pc.serviceId.trim)) match {
      case true => Some(pc.serviceId)
      case false => None
    }
    val saiPreferences =
      if(pc.getSaiPreferences != null)
        Some(
          SAIPreferences(
            memo = Some(pc.saiPreferences.getMemo),
            triceUltimateSendingPartyId = None,
            depositorName = Some(pc.saiPreferences.getDepositorName),
            physicalAddress = Some(pc.saiPreferences.getPhysicalAddress),
            city = Some(pc.saiPreferences.getCity),
            state = Some(pc.saiPreferences.getState),
            zip = Some(pc.saiPreferences.getZip),
            country = Some(pc.saiPreferences.getCountry)
            ))
      else None
    UpdateAccountProvisioningDetails(bundleReference = bundle,
                                     products = products,
                                     productConfiguration =
                                       automation.ProductConfiguration(
                                         ein = Option(pc.getEin),
                                         saiPreferences = saiPreferences,
                                         retentionSchedule = retentionSchedule,
                                         lookupApiKey = lookupApiKey,
                                         serviceId = serviceId
                                         ),
                                     initiatedBy = initiatedBy)
  }

  private def getAccountProductsAsJava(accountProducts: AccountProducts): DtoAccountProducts = {
    val dtoAccountProducts = new DtoAccountProducts()
    dtoAccountProducts.setId(accountProducts.id)
    dtoAccountProducts.setBusinessUserRoleId(accountProducts.businessUserRoleId)
    dtoAccountProducts.setProvisioningType(accountProducts.provisioningType.toString)
    dtoAccountProducts.setName(accountProducts.name)
    dtoAccountProducts.setOrder(accountProducts.order)
    dtoAccountProducts.setProvisioned(accountProducts.provisioned)
    dtoAccountProducts.setEnabled(accountProducts.enabled)
    dtoAccountProducts.setDefaultState(accountProducts.defaultState)
    if(accountProducts.parentId.isDefined)
      dtoAccountProducts.setParentId(accountProducts.parentId.get)
    if(accountProducts.createdBy.isDefined)
      dtoAccountProducts.setCreatedBy(accountProducts.createdBy.get)
    if(accountProducts.createdAt.isDefined)
      dtoAccountProducts.setCreatedAt(accountProducts.createdAt.get)
    if(accountProducts.updatedBy.isDefined)
      dtoAccountProducts.setUpdatedBy(accountProducts.updatedBy.get)
    if(accountProducts.updatedAt.isDefined)
      dtoAccountProducts.setUpdatedAt(accountProducts.updatedAt.get)
    if(accountProducts.allowEditing.isDefined)
      dtoAccountProducts.setAllowEditing(accountProducts.allowEditing.get)

    dtoAccountProducts
  }

  private def updateDocVStrategy(accountId: Long, strategyId: String): Either[ErrorResponse, Boolean] = {
    if(Strings.isNullOrEmpty(strategyId)) {
      Right(true)
    } else {
      val accountPublicId = accountInfoResolver.getAccountNameAndPublicId(accountId).getId
      val accountStrategyDto = AccountStrategyDto(accountPublicId, strategyId)
      authenticIdResolver.associateAuthenticIdStrategy(accountStrategyDto)
    }
  }
  private def triggerUnEnrollWLMonitorJob(accountId: Long): Either[ErrorResponse, Boolean] = {
    if (accountId != null) {
      val params: String = "{\"accountId\": \"" + accountId + "\", \"fullyDisable\": true}";
      batchJobClientResolver.createJobWithPayload(130, params, SecureUtil.getUsername(), clock, executionContext) match {
        case Some(jobId) =>
          logger.info(s"Un-enroll watchlist monitor job submitted with id: $jobId")
          Right(true)

        case _ =>
          logger.error("triggerUnEnrollWLMonitorJob: batchJobClientResolver.createJobWithPayload returned None")
          Left(ErrorResponse(100, "Failed to trigger disable monitor job"))
      }
    } else {
      Right(false)
    }
  }
}
