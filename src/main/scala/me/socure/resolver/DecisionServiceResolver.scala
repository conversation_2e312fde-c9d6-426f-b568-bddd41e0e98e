package me.socure.resolver

import java.io.InputStream
import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.decision.service.client.{DecisionServiceClient, DecisionServiceClientV2}
import me.socure.decision.service.common._
import me.socure.decision.service.common.model.DecisionSimulateRequest
import me.socure.decision.service.common.models.v2.{LogicDetailsWithMappingsResponse, AddLogicRequest => _, AddLogicResponse => _, _}
import me.socure.model.ErrorResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import scala.concurrent.Await
import scala.concurrent.duration._

@Component
class DecisionServiceResolver {

  @Autowired
  var decisionServiceClient: DecisionServiceClient = _

  @Autowired
  var decisionServiceClientV2: DecisionServiceClientV2 = _

  def simulate(simulateRequest: DecisionSimulateRequest): Either[ErrorResponse, InputStream] = {
    val response = decisionServiceClient.simulateDecisionLogic(simulateRequest)
    Await.result(response, Duration.Inf)
  }

  def listLogic(accountId: String): Either[ErrorResponse, List[ListLogicResponse]] = {
    val response = decisionServiceClient.listLogic(accountId)
    Await.result(response, Duration.Inf)
  }

  def addLogic(addLogicRequest: AddLogicRequest): Either[ErrorResponse, AddLogicResponse] = {
    val response = decisionServiceClient.addLogic(addLogicRequest)
    Await.result(response, Duration.Inf)
  }

  def markAsLive(markAsLiveLogicRequest: MarkAsLiveLogicRequest): Either[ErrorResponse, MarkAsLiveLogicResponse] = {
    val response = decisionServiceClient.markAsLive(markAsLiveLogicRequest)
    Await.result(response, Duration.Inf)
  }

  def listLogicByAccountV2(accountId: String, envId: Int): Either[ErrorResponse, LogicDetailsWithMappingsResponse] = {
    val decisionLogicSortRequest = DecisionLogicSortRequest(Some(Seq(Sort(DecisionLogicColumns.LAST_UPDATED_AT, ascending = false))))
    val decisionLogicFilterRequest = DecisionLogicFilterRequest(accountId.toString, envId.toString, Some(Set(DecisionLogicStates.ACTIVE.id, DecisionLogicStates.INACTIVE.id, DecisionLogicStates.DRAFT.id, DecisionLogicStates.MIGRATED.id)), Some(Pagination(1, 50)))
    val listLogicRequest = ListLogicRequest(decisionLogicFilterRequest, decisionLogicSortRequest)
    val response = decisionServiceClientV2.listLogicGroupedByPublicId(listLogicRequest)
    Await.result(response, Duration.Inf)
  }

  def getLogicV2(logicId: Long, accountId: Long, environmentTypeId: Int): Either[ErrorResponse, String] = {
    val response = decisionServiceClientV2.getLogic(logicId.toString)
    val decisionLogicOpt = Await.result(response, Duration.Inf)
    decisionLogicOpt match {
      case Right(response) if response.isDefined && response.get.accountId == accountId && response.get.environmentTypeId == environmentTypeId => Right(response.get.logic)
      case _ =>
        val response = decisionServiceClientV2.listRecommendedLogic(ListRecommendedLogicRequest(RecommendedLogicFilterRequest(None), RecommendedLogicSortRequest(None)))
        val recommendedLogics = Await.result(response, Duration.Inf)
        recommendedLogics match {
          case Right(response) if response.total > 0 && response.logics.exists(logic => logic.id.isDefined && logic.id.get == logicId) =>
            Right(response.logics.filter(logic => logic.id.isDefined && logic.id.get == logicId).head.logic)
          case _ => Left(ErrorResponse(400, "Logic not found"))
        }
    }
  }

  def getLogicTemplateV2(logicId: Long, accountId: Long, environmentTypeId: Int): Either[ErrorResponse, String] = {
    val response = decisionServiceClientV2.getLogicTemplate(logicId, accountId, environmentTypeId)
    val logicTemplate = Await.result(response, Duration.Inf)
    logicTemplate match {
      case Right(response) => Right(response.template)
      case _ =>
        val response = decisionServiceClientV2.listRecommendedLogicTemplates(ListRecommendedLogicRequest(RecommendedLogicFilterRequest(None), RecommendedLogicSortRequest(None)))
        val recommendedLogicTemplats = Await.result(response, Duration.Inf)
        recommendedLogicTemplats match {
          case Right(response) if response.total > 0 && response.logics.exists(logic => logic.id == logicId) =>
            Right(response.logics.filter(logic => logic.id == logicId).head.template)
          case _ => Left(ErrorResponse(400, "Logic template not found"))
        }
    }
  }

  def addBulkLogic(bulkAddLogicRequest: BulkAddLogicRequest): Either[ErrorResponse, Int] = {
    val response = decisionServiceClientV2.addBulkLogic(bulkAddLogicRequest)
    Await.result(response, Duration.Inf)
  }

  def activateLogic(logicId: Long, createdBy: String): Either[ErrorResponse, Unit] = {
    val response = decisionServiceClientV2.activateLogic(logicId.toString, ActivationLogicRequest(createdBy, Some("From SuperAdmin"), "Other")) // give default comment and reason for deployment for now
    Await.result(response, Duration.Inf)
  }
}
