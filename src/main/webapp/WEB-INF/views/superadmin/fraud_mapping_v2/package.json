{"name": "fraud_mapping_v2", "version": "1.0.0", "private": true, "description": "Fraud Mapping v2", "scripts": {"start": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=4096 ts-node -O '{\"module\":\"commonjs\"}' ./node_modules/.bin/webpack serve", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 ts-node -O '{\"module\":\"commonjs\"}' ./node_modules/.bin/webpack && yarn run build-mv", "build-mv": "rm -rf ../../../../resources/fraud_mapping && mv -f public/dist '../../../../resources/fraud_mapping'", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx}\""}, "dependencies": {"@mui/material": "^6.4.5", "@socure/components": "git+ssh://**************************************:product/ux/socure-ui-components.git", "axios": "^1.8.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-toastify": "^11.0.3"}, "devDependencies": {"@emotion/react": "^11.14.0", "@svgr/webpack": "^8.1.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.9.2", "prettier": "^2.8.7", "sass": "^1.85.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.11", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.7.3", "url-loader": "^4.1.1", "webpack": "^5.98.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0"}}