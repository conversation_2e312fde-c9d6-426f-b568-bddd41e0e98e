package me.socure.resolver

import me.socure.account.client.ManageAccountsV2Client
import me.socure.model.{AccountConsentReasons, ErrorResponse}
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component

import scala.concurrent.Await
import scala.concurrent.duration._

@Component
class AccountDetailsResolver {

  private val logger = LoggerFactory.getLogger(getClass)

  @Autowired
  @Qualifier("manageAccountsV2Client")
  var manageAccountsV2Client: ManageAccountsV2Client = _

  def updateConsentReason(accountId: Long, consentId: Int): Int = {
    val response = manageAccountsV2Client.updateConsentId(accountId, consentId)
    val result = Await.result(response, 35 seconds)
    result match {
      case Right(x) => x
      case Left(errorMsg) =>
        logger.error(s"Update consentReason failed :$errorMsg")
        0
    }
  }

  def getConsentReason(accountId: Long): Int = {
    val response = manageAccountsV2Client.getConsentReasonId(accountId)
    val result = Await.result(response, 35 seconds)
    result match {
      case Right(x) => x
      case Left(errorMsg) =>
        logger.error(s"get consentReason failed :$errorMsg")
        AccountConsentReasons.OPEN_BANK_ACCOUNT.id
    }
  }

  def resetPassword(email: String):  Int = {
    val response = manageAccountsV2Client.resetPassword(email)
    val result = Await.result(response, 35 seconds)
    result match {
      case Right(x) => 1
      case Left(errorMsg) =>
        logger.error(s"Update password failed :$errorMsg")
        0
    }
  }

}


