<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>

    <style>

        .pad {
          margin-top: 10px;
          margin-bottom: 10px;
        }

        .button-align {
          margin-left: 40px;
        }

        th, td {
            padding: 10px;
        }

          .table-alignment{
            margin-left : 10%;
        }

        .align-right{
            margin-left : 80%;
        }

        .button-alignment{
            margin-left : 20%;
        }

        table.width {
          table-layout: auto;
          width: 300px;
        }

        input[type="radio"]{
          margin: 0 10px 0 10px;
        }

        .accordion { list-style-type: none; padding: 0; margin: 0 0 30px; border: 1px solid #17a; border-top: none; border-left: none; }
        .accordion ul { padding: 0; margin: 0; float: left; display: block; width: 100%; }
        .accordion li { cursor: pointer; list-style-type: none; padding: 0; margin: 0; float: left; display: block; width: 100%;}

        .accordion li div { padding: 20px;  display: block; clear: both; float: left;}
        .accordion a { text-decoration: none; border-bottom: 1px solid #4df; font: bold 1.1em/2em Arial, sans-serif;  padding: 0 10px; display: block; cursor: pointer;}

        /*  .active{background:red!important}*/
        /* Level 2 */
        .accordion li ul li { padding: 1px 20px; font-size: 0.9em; }

        .tab {
          overflow: hidden;
          border: 1px solid #ccc;
          background-color: #f1f1f1;
        }

        /* Style the buttons that are used to open the tab content */
        .tab button {
          background-color: inherit;
          float: left;
          border: none;
          outline: none;
          cursor: pointer;
          padding: 14px 16px;
          transition: 0.3s;
        }

        /* Change background color of buttons on hover */
        .tab button:hover {
          background-color: #ddd;
        }

        /* Create an active/current tablink class */
        .tab button.active {
          background-color: #ccc;
        }

        /* Style the tab content */
        .tabcontent {
          display: none;
          padding: 6px 12px;
          border: 1px solid #ccc;
          border-top: none;
        }

        body {font-family: Arial, Helvetica, sans-serif;}
        * {box-sizing: border-box;}

        .row {
          margin-left: 0
        }

        .results-table {
            border-collapse: collapse;
            border-spacing: 0;
            color: #49424f;
            width: 100%;
        }

        .table-header {
            background: #635B69;
            color: #fff;
        }

        .results-table .data-row {
            display: table-row;
            vertical-align: inherit;
            border-color: inherit;
            border-collapse: collapse;
            border-spacing: 0;
            color: #49424f;
        }

        .results-table th, .results-table td {
            border: 1px solid #d2d3d6;
            font-size: 13px;
            font-weight: 400;
            padding: 20px 10px;
            display: table-cell;
            vertical-align: inherit;
            text-align: center;
        }

        .grey-row {
            background: rgba(209,199,230,.4);
        }

        .heading {
          margin-top: 10px;
          font-weight: bold;
        }

        .link-dark {
          margin-left: 20px;
          color: #ee8e3c;
          cursor: pointer;
          font-weight: normal;
          text-decoration: underline;
        }

        .accordion-heading {
          border: none;
          background: transparent;
        }


    </style>

</h:head>
<h:body>

  <div class="modal" tabindex="-1" role="dialog" id="updateConfigModal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Update Config</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeUpdateConfigPopup()">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div>
						<label for="updatedConfig" style="width: 60px;">Config</label>
						<textarea type="text" id="updatedConfig" value="" required="false"/>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="updateConfigHandler()">Update</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeUpdateConfigPopup()">Cancel</button>
				</div>
			</div>
		</div>
	</div>

        <div id="spin_container">
          <div class="nav nav-tabs" id="nav-tab" role="tablist">
            <a class="nav-item nav-link active"
              id="nav-rds-tab" data-toggle="tab"
              href="#tab_rds" role="tab" aria-controls="nav-rds"
              aria-selected="true">RDS
            </a>
            <a class="nav-item nav-link"
              id="nav-dynamo-tab" data-toggle="tab"
              href="#tab_dynamo" role="tab" aria-controls="nav-dynamo"
              aria-selected="true">Dynamo
            </a>
          </div>
          <div class="tab-content">
            <div class="tab-pane active" id="tab_rds">
              <div class="button-alignment">
                Enter  file Path :   <input id="path" type="text"/>
                <input style="margin-left:30em" id="history" type="button" value="Show History" class="pad" />
                <input style="margin-left:30em" id="hideHistory" type="button" value="Hide History" class="pad"/>
              </div>

              <div class="button-alignment">
                <input id="backup1" type="button" value="Update Backup 1" class="pad"/>
                <input id="switch" type="button" value="Switch DB" class="pad" />
                <input id="backup2" type="button" value="Update Backup 2" class="pad" />
              </div>
              <br/><br/>


              <div class="table-alignment" id="result">

                <table id="log" class="width" border="2">
                  <thead>
                  <tr>
                    <th>Job Id</th>
                    <th>File</th>
                    <th>Description</th>
                    <th>Progress</th>
                    <th>Job Status</th>
                    <th>Transaction status</th>
                    <th>Trigged By</th>
                    <th>Triggered At</th>
                    <th></th>
                  </tr>
                  </thead>
                  <tbody id="resultBody">

                  </tbody>

                </table>
                <input id="number" type="hidden" value="1" disabled="disabled" />
                <br/>
              </div>
              <div class="align-right">
                <a id="back" href="#" >prev</a> &nbsp; &nbsp;
                <a id="next" href="#" >next</a>
              </div>
            </div>
            <div class="tab-pane" id="tab_dynamo">
              <div>
                <input id="updateConfig" type="button" value="Update Config" class="pad" />
                <input id="swapTable" type="button" value="Swap Table" class="pad" />
                <input id="rollbackTable" type="button" value="Rollback" class="pad" />
              </div>
              <div>
                <input id="batch-job-input" type="file" accept=".json" style="line-height: normal;" name="Upload" />
                <button disabled="true" class="btn-orange pad" id="create-job-btn" onclick="handleCreateJob()">Trigger BatchJob</button>
              </div>
              <div id="dynamoTables" class="row"></div>
              <div id="awsImports">
                <div class="heading">
                  <span>AWS Dynamo Imports</span>
                  <span class="link-dark" onclick="getAwsImportInfo()">next</span>
                </div>
                <table class='width results-table' border='2'>
                  <thead class="table-header">
                    <tr>
                      <th>ImportArn</th>
                      <th>TableArn</th>
                      <th>Status</th>
                      <th>Start Time</th>
                      <th>End Time</th>
                    </tr>
                  </thead>
                  <tbody id="awsImportsResult"></tbody>
                </table>
              </div>
              <div id="batchTable">
                <div class="heading">
                  <span>Dynamo Batch Job</span>
                  <span id="prev-batch-btn" class="link-dark" onclick="getPreviousBatchJobList()">prev</span>
                  <span id="next-batch-btn" class="link-dark" onclick="nextPreviousBatchJobList()">next</span>
                </div>
                <table class='width results-table' border='2'>
                  <thead class="table-header">
                    <tr>
                      <th>Job Id</th>
                      <th>Created by</th>
                      <th>Created at</th>
                      <th>Status</th>
                      <th>Payload</th>
                    </tr>
                  </thead>
                  <tbody id="batchResult"></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>


</h:body>
</html>
