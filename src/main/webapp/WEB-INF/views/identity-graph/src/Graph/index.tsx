import React, { useCallback, useEffect, useMemo, useState, Fragment, ReactNode } from "react";
import { ContextMenuEvent, GraphCanvas, RadialMenu } from 'reagraph';

import { buildGraph, buildGraphRawData, deepClone, sharedPiiFields } from "./utilities"

const ownedPiiFields = new Set([
  "firstName",
  "middleName",
  "surName",
  "suffixName",
  "dob",
  "address"
]);

const Graph: React.FC<{ transactionID: string}> = (props: any) => {
  const { transactionID } = props;

  const [primarySocureID, setPrimarySocureID] = useState<string | undefined>(undefined);
  const [apiData, setApiData] = useState<any>(undefined);
  const [baseIDSet, setBaseIDSet] = useState<Set<string>|undefined>(undefined);

  const retrieveTransactionData = useCallback(async (): Promise<void> => {
    const result = await fetch("/api/v2/kyc/identity_graph/get-merged-id", {
      method: "POST",
      headers: {
        "Content-Type":"application/json"
      },
      body: JSON.stringify({ transactionID })
    });
    if(result.status === 200) {
      const data:any = await result.json()
      setPrimarySocureID(data.socureId);
      setApiData({
        [data.entityId]: data
      })
      setBaseIDSet(new Set<string>(data.underlyingRecordIDs))
    } else {
      alert("An error occured when fetching the transaction entity.")
    }
  }, [setPrimarySocureID, setApiData, transactionID, setBaseIDSet]);

  const retrieveExpandingData = useCallback(async (requestData:{field: string, value: string}): Promise<void> => {
    const { field, value} = requestData;
    const result = await fetch("/api/v2/kyc/identity_graph/get-extended-entities", {
      method: "POST",
      headers: {
        "Content-Type":"application/json"
      },
      body: JSON.stringify({ 
        entityType: field,
        value
       })
    });
    if(result.status === 200) {
      const extendedData = await result.json()
      const filteredData = extendedData.filter((_: any) => (
        apiData[_.entityId] === undefined &&
        !(baseIDSet?.has(_.entityId))
      ));
      if (filteredData.length >= 0) {
        filteredData.forEach((d: any) => {
          apiData[d.entityId] = d;
        });
        setApiData(deepClone(apiData));
      } else {
        alert("No Valid Data Added.");
      }
    } else {
      alert("An error occured while fetching extended entities.")
    }
  }, [setApiData, apiData, primarySocureID, baseIDSet]);

  useEffect((): void => {
    retrieveTransactionData();
  }, []);

  const renderRadialMenu = useCallback((event: ContextMenuEvent): ReactNode => {
    const { data: { data }, onClose } = event;
    if (data.type === "edge" || data.nodeType === "root") {
      return null;
    } else if(data.nodeType === "field" || (ownedPiiFields.has(data.nodeType))) {
      return <RadialMenu
        className={`${data.color}-radial-menu`}
        onClose={onClose}
        items={[
          {
            label: 'Info',
            onClick: () => {
              alert('Coming soon');
              onClose();
            }
          }
        ]}
      />
    } else {
      if(sharedPiiFields.has(data.nodeType)) {
        return <RadialMenu
        className={`${data.color}-radial-menu`}
        onClose={onClose}
        items={[
          {
            label: 'Info',
            onClick: () => {
              alert('Coming soon');
              onClose();
            }
          },
          {
            label: 'Expand',
            onClick: () => retrieveExpandingData({ 
              field: data.nodeType,
              value: data.value
            })
          }
        ]}
      />
      }
    }
    return null;
  }, [retrieveExpandingData]);

  const result = useMemo(() => {
    if (primarySocureID !== undefined && apiData !== undefined && baseIDSet !== undefined) {
      const graphRawData = buildGraphRawData(Object.values(apiData));
      const { nodes, edges } = buildGraph(primarySocureID, graphRawData);
      return <Fragment>
        <div>
          <GraphCanvas
            nodes={nodes}
            edges={edges}
            contextMenu={renderRadialMenu}
          />
        </div>
      </Fragment>;
    }
    return <>{"Loading....."}</>
  }, [primarySocureID, apiData, retrieveExpandingData, renderRadialMenu, baseIDSet]);

  return result;

}

export default Graph;