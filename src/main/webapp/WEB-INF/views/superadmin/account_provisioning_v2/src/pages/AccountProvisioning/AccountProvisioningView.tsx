import React, { FC } from 'react';
import styled from 'styled-components';
import { Content, Collapsible, TreeGrid, Dropdown, DropdownItem, Button, ModalDialog } from '@socure/components';
import { CellActionProps, HeaderProps } from '@socure/components/src/components/TreeGrid/TreeGrid.type';
import { TreeGridPayload, ModifiedModules, TreeFormattedModule, BundleDetails } from './AccountProvisioning.type';
import UpdateSummary from './UpdateSummary';
import './accountProvisioning.scss';
import UpdateConfirmationDialog from './UpdateConfirmationDialog';
import './accountProvisioning.scss';
import BundleUpdateWarning from './BundleUpdateWarning';

export const LabelText = styled.span`
  font-size: var(--socure-global--FontSize--2xs);
  font-weight: 700;
  color: #858585;
  display: flex;
`;

const InfoText = styled.span`
  text-align: center;
  font-size: var(--socure-global--FontSize--13xs);
  font-weight: 700;
  color: var(--socure-r-grey-19);
`;

const Wrapper = styled.div`
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  width: 100%;
  font-family: 'Open Sans';
  font-style: normal;
  background-color: #F7F7F7;
`

const DropdownWrapper = styled.div`
  width: 180px;
`

const Container = styled.div`
    display: flex;
    flex-direction: column;
    padding: 16px 0;
`

const ButtonStyle = styled.div`
  display: flex;
  justify-content: space-between;

  .socure-c-button {
      margin: 0 12px;
  }
`

const ModalWrapper = styled.div`
display: flex;
flex-direction: column;
algn-items: center;
padding-bottom: 40px;
min-width: 616px;
`

const Actions = styled.div`
    width: 100%;
    display: flex;
    justify-content: flex-end;
`

const AccordionHeader = styled.div`
    display: 'flex';
    alignItems: 'center';
    padding: 24px;
`

const AccordionTitle = styled.span`
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 27px;
    align-items: center;
    color: #22272F;
`

const AccordionSubTitle = styled.span`
    font-size: 14px;
    font-weight: 400;
    padding-left: 4px;
    line-height: 19px;
    color: #22272F;
`

interface iAccountProvisioningView {
    headerConfig: HeaderProps[];
    getTreeGridPayload: (forBundle: boolean) => TreeGridPayload;
    bundlesList: string[];
    updateBundle: (selectedBundle: string) => void;
    handleCellAction: (cellActionProps: CellActionProps) => void;
    selectedBundle: string;
    modifiedProducts: ModifiedModules[];
    modifiedFeatureFlags: ModifiedModules[];
    showUpdateConfirmationHandler: () => void;
    closeUpdateConfirmationHandler: () => void;
    showModalDialog: boolean;
    updateHandler: () => void;
    closeWatchListMonitorDialog: () => void;
    showWatchListMonitorDialog: boolean;
    confirmWatchListMonitorUnEnrollment: () => void;
    propChanged: boolean;
    existingBundle: string;
    resetProvisioning: () => void;
    isReadOnly: boolean;
    selectedBundleDetail: BundleDetails;
    payload: TreeFormattedModule[];
    isSubaccount: boolean;
    showToggleFlagDialog: boolean;
    closeToggleFlagHandler: () => void;
    proceedToggleHandler: () => void;
    decisionWaningText: string;

}


const AccountProvisioningView: FC<iAccountProvisioningView> = ({ headerConfig, getTreeGridPayload, bundlesList, updateBundle, handleCellAction, selectedBundle, modifiedProducts,
    modifiedFeatureFlags, showUpdateConfirmationHandler, closeUpdateConfirmationHandler, showModalDialog, updateHandler, propChanged,
    existingBundle, resetProvisioning, isReadOnly, selectedBundleDetail, payload, isSubaccount, showToggleFlagDialog, decisionWaningText, closeToggleFlagHandler, proceedToggleHandler, showWatchListMonitorDialog, closeWatchListMonitorDialog, confirmWatchListMonitorUnEnrollment }) => {
    const bundleProductsTreePayload = getTreeGridPayload(true);
    const otherProductsTreePayload = getTreeGridPayload(false);

    return <div><Content>
        <Content.ContentArea>
            <Wrapper>
                <DropdownWrapper>
                    <LabelText>BUNDLES</LabelText>
                    <Dropdown
                        id='bundle'
                        selected={selectedBundle}
                        onChange={(selected) => updateBundle(selected as string)}
                        placeholder="Select a bundle"
                        label=" "
                        isDisabled={isReadOnly || isSubaccount}
                    >
                        {bundlesList && bundlesList.map(bundleItem => <DropdownItem value={bundleItem}>{bundleItem}</DropdownItem>)}
                    </Dropdown>
                </DropdownWrapper>
                {isSubaccount && <InfoText>Unchecked Disabled Modules/Feature Flags are not provisioned on the parent account</InfoText>}
                <BundleUpdateWarning currentBundle={selectedBundle} existingBundle={existingBundle} />
                {selectedBundle && <Container>
                    <UpdateSummary selectedBundle={selectedBundle} modifiedProducts={modifiedProducts} />
                    <Collapsible className='accordion' title={
                        <AccordionHeader>
                            <AccordionTitle>{selectedBundle} Modules  </AccordionTitle>
                            <AccordionSubTitle>({bundleProductsTreePayload.chosenModules} Modules, {bundleProductsTreePayload.chosenFFs} Feature flags)</AccordionSubTitle>
                        </AccordionHeader>
                    }>
                        <TreeGrid headers={headerConfig} results={bundleProductsTreePayload.accordionPayload} onCellAction={handleCellAction} rowBorderBottom={"false"} />
                    </Collapsible>
                    <Collapsible className='accordion' title={
                        <AccordionHeader>
                            <AccordionTitle>Other Modules  </AccordionTitle>
                            <AccordionSubTitle>({otherProductsTreePayload.chosenModules} Modules, {otherProductsTreePayload.chosenFFs} Feature flags)</AccordionSubTitle>
                        </AccordionHeader>
                    }>
                        <TreeGrid headers={headerConfig} results={otherProductsTreePayload.accordionPayload} onCellAction={handleCellAction} rowBorderBottom={"false"} />
                    </Collapsible>
                </Container>}
                {!selectedBundle && <Container>
                    <UpdateSummary selectedBundle={selectedBundle} modifiedProducts={modifiedProducts} />
                    <Collapsible className='accordion' title={
                        <AccordionHeader>
                            <AccordionTitle>Modules  </AccordionTitle>
                            <AccordionSubTitle>({otherProductsTreePayload.chosenModules} Modules, {otherProductsTreePayload.chosenFFs} Feature flags)</AccordionSubTitle>
                        </AccordionHeader>
                    }>
                        <TreeGrid headers={headerConfig} results={bundleProductsTreePayload.accordionPayload} onCellAction={handleCellAction} rowBorderBottom={"false"} />
                    </Collapsible>
                </Container>}
            </Wrapper>
        </Content.ContentArea>
        {!isReadOnly && <Content.Footer footerBackgroundColor='#F7F7F7' borderTop={false}>
            <Actions>
                <ButtonStyle>
                    <Button buttonText='Revert' buttonType='secondary' onClick={resetProvisioning} />
                    <Button buttonText='Update' backgroundColor='orange' onClick={() => showUpdateConfirmationHandler()} isDisabled={!propChanged} />
                </ButtonStyle>
            </Actions>
        </Content.Footer>}
    </Content>
        <ModalDialog
            show={showModalDialog}
            title={"Confirm Updates!!"}
            primaryButtonText={"Confirm"}
            secondaryButtonText={"Cancel"}
            handleClose={() => closeUpdateConfirmationHandler()}
            onSecondaryButtonClick={() => closeUpdateConfirmationHandler()}
            onPrimaryButtonClick={() => updateHandler()}
            primaryButtonBackgroundColor="orange"

            fullScreen
        >
            <ModalWrapper>
                <UpdateConfirmationDialog
                    payload={payload}
                    modifiedFeatureFlags={modifiedFeatureFlags}
                    selectedBundleDetail={selectedBundleDetail}
                    existingBundle={existingBundle}
                    modifiedProducts={modifiedProducts}
                />

            </ModalWrapper>
        </ModalDialog>

         <ModalDialog
                    show={showWatchListMonitorDialog}
                    title={"Remove watchlist monitoring"}
                    primaryButtonText={"Confirm"}
                    secondaryButtonText={"Cancel"}
                    handleClose={() => closeWatchListMonitorDialog()}
                    onSecondaryButtonClick={() => closeWatchListMonitorDialog()}
                    onPrimaryButtonClick={() => confirmWatchListMonitorUnEnrollment()}
                    primaryButtonBackgroundColor="orange"
                >
                    <ModalWrapper>
                        <div>
                            Upon removing watchlist monitoring all watchlist monitors associated with this account will be un-enrolled.
                        </div>
                    </ModalWrapper>
                </ModalDialog>

        <ModalDialog
            show={showToggleFlagDialog}
            title={"Warning!"}
            primaryButtonText={"Proceed"}
            secondaryButtonText={"Cancel"}
            handleClose={() => closeToggleFlagHandler()}
            onSecondaryButtonClick={() => closeToggleFlagHandler()}
            onPrimaryButtonClick={() => proceedToggleHandler()}
            primaryButtonBackgroundColor="orange"
        >
            <ModalWrapper>
               <div>{decisionWaningText}</div>
            </ModalWrapper>
        </ModalDialog>
    </div>
}

export default AccountProvisioningView;