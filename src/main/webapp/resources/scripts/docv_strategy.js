saveStrategy = function () {
    var obj = {};
    var url = contexturl + '/superadmin/1/docv/authenticId';
    var form = document.getElementById('add_strategy');;
    var formData = new FormData();
    obj['id'] = form.docvId.value;
     if (form.strategyId.value.trim() === "") {
         url = url + "/add"
    } else {
        url = url + "/update"
    }
    obj['strategyId']=form.strategyId.value;
    if (form.strategyName.value.trim() === "") {
        alert("Please fill the name of the Strategy");
        return;
    }
    obj['name']= form.strategyName.value;
    obj['keyType']= form.keyType.value;
    if (form.llc.value.trim() === "") {
            alert("Please fill the Lenient code of the Strategy");
            return;
        }
    obj['lenientLocationCode']= form.llc.value;
    if (form.slc.value.trim() === "") {
            alert("Please fill the Strict code of the Strategy");
            return;
        }
    obj['strictLocationCode']= form.slc.value;
    var json = JSON.stringify(obj);
    $.ajax(
            url,
            {
                type: 'POST',
                data: json,
                contentType: "application/json",

                success: function (data) {
                    if (data['status'].toLowerCase() === 'ok') {
                        $('#message').html('Updated Successfully!!!');
                        $("#AddStrategy").modal("hide");
                        form.reset();
                        getAllStrategies();
                    } else {
                        $("#AddStrategy").modal("show");
                        $('#message').html(data['msg']);
                    }
                }
    });
};

show_strategy_add_form = function () {
    var form = document.getElementById('add_strategy');
    form.reset()
    $('#AddStrategy').modal('show');
};

getAllStrategies = function() {
  var url = 'superadmin/1/docv/authenticId/fetch/all';
  $.ajax(url, {
    type: 'GET',
    success: function(result) {
      if (result['status'].toLowerCase() === 'ok') {
        showStrategies(result['data'])
      } else {
        alert("Problem in fetching strategies. Please try again later")
      }
    }
  });
};

showStrategies = function(data) {
  if (data !== null) {
    window.strategies = data;
    var aaData = [];
    for (var j = 0; j < data.length; j++) {
      var rowData = data[j];
      var a = [];
      a.push(rowData['id']); //1
      a.push(rowData['strategyId']); //2
      a.push(rowData['name']); //3
      a.push(rowData['keyType']); //4
      a.push(rowData['lenientLocationCode']); //5
      a.push(rowData['strictLocationCode']); //6
      a.push("edit"); //7
      aaData.push(a);
    }

    oTable = $('#docv_strategy_table')
      .dataTable({
        "aaData": aaData,
        "bProcessing": true,
        "bDestroy": true,
        "bAutoWidth": false,
        "iDisplayLength": 25,
        "aoColumns": [{
            "fnRender": function(oObj) {
              return oObj.aData[0];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[1];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[2];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[3];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[4];
            }
          },
          {
            "fnRender": function(oObj) {
              return oObj.aData[5];
            }
          },
         {
           "fnRender": function(oObj) {
             return "<input type=\"button\" value=\"edit\" onclick=\"editDocType(" + oObj.aData[0] + ")\" class=\"EditDocType\"/>"

           }
         }
        ]
      });
  }
};

get_original_data_for_id = function (id) {
    if(window.strategies) {
        for (var i in window.strategies) {
            var d = window.strategies[i];
            if(d.id === id) {
                return d;
            }
        }
    }
    return null;
};
function editDocType(id) {
    var form = document.getElementById('add_strategy');
    form.reset();
    var obj = get_original_data_for_id(id)
    form.docvId.value = obj['id'];
    form.strategyId.value = obj['strategyId'];
    form.strategyName.value = obj['name'];
    $('select[id="keyType"]').find('option:contains(' + obj['keyType'] + ')').attr("selected",true);
    form.llc.value = obj['lenientLocationCode'];
    form.slc.value = obj['strictLocationCode'];
     $('#AddStrategy').modal('show');
}