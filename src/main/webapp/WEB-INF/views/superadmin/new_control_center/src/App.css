.App {
  text-align: center;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.mt-16 {
  margin-top: 16px;
}

.mt-32 {
  margin-top: 32px;
}

.ml-16 {
  margin-left: 16px;
}

.ml-32 {
  margin-left: 32px;
}

.p-24 {
  padding: 24px;
}

.align-center {
  align-items: center!important;
}

.align-start {
  align-items: flex-start;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center!important;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.topbar-container {
  margin-top: 64px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.page-heading {
  font-size: 24px;
  font-weight: bold;
  line-height: 24px;
  text-align: center;
  margin-top: 24px;
}

.page-container {
  margin-top: 32px;
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

.left-bar {
  display: flex;
  width: 240px;
  flex-direction: column;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 32px 64px rgb(44 9 12 / 10%);
  padding: 24px;
}

.config-editor {
  display: flex;
  flex-direction: column;
  flex: 1 1;
  align-items: flex-start;
  padding: 24px;
  overflow: auto;
}

.flag-editor {
  border: solid 1px rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
}

.generic-label {
  font-size: 16px;
  line-height: 24px;
  padding: 0 8px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.config-label {
  min-width: 240px;
  text-align: left;
}


.socure-c-text-box, .scoure-c-dropdown, .socure-c-text-area {
  position: relative;
  display: inline-block;
  width: 240px;
  padding: 12px;
  font-size: 16px;
  line-height: 24px;
  background-color: transparent !important;
  border: 1px solid #454d5f;
  border-radius: 4px;
}

.socure-c-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  border: none;
  cursor: pointer;
}

.socure-c-button.disabled {
  cursor: not-allowed;
  opacity: 0.2;
}

.socure-c-button-primary {
  color: #ffffff;
  border-radius: 8px;
  transition-duration: .2s;
  min-width: 79px;
  height: 36px;
  padding: 6px 16px;
  background-color: #FF6900;
  border: solid 1px rgba(0, 0, 0, 0.5);
}

.socure-c-button-link {
  color: #0000EE;
}

.socure-c-button-secondary {
  color: #333;
  border-radius: 8px;
  transition-duration: .2s;
  min-width: 79px;
  height: 36px;
  padding: 6px 16px;
  background-color: transparent;
  border: solid 1px rgba(0, 0, 0, 0.5);
}

.socure-c-radio-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
}

.socure-c-radio-circle {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: solid 1px rgba(0, 0, 0, 0.5);
}

.socure-c-radio-circle.socure-c-radio-selected {
  border: solid 1px #FF6900;
  background-color: #FF6900;
}

.socure-c-radio-vertical {
  display: flex;
  flex-direction: column;
}

.socure-c-radio-horizontal {
  display: flex;
  flex-direction: row;
}

.socure-c-topbar-container {
  display: flex;
  flex-direction: row;
}

.socure-c-each-menu {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.socure-c-topbar-menu {
  color: #FF6900;
  font-size: 16px;
  line-height: 24px;
  padding: 0 8px;
  cursor: pointer;
}

.socure-c-menu-separator {
  height: 16px;
  border: solid 1px rgba(0, 0, 0, 0.5);
}

.popup-container {
  background-color: #fff;
  box-shadow: 0 32px 64px rgb(44 9 12 / 10%);
  max-height: 75%;
  width: 50%;
  display: flex;
  align-items: flex-start;
  text-align: left;
  overflow: auto;
}

.socure-config-item {
  text-align: left;
  padding: 16px;
  box-shadow: 0 16px 16px rgb(44 9 12 / 10%);
  margin: 8px 0;
  cursor: pointer;
  transition: all linear 0.2s;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 16px;
  line-height: 24px;
  font-weight: bold;
  border: solid 1px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.socure-config-item:hover {
  background-color: rgba(0, 0, 0, 0.1);
  box-shadow: 0 16px 32px rgb(44 9 12 / 10%);
}

.w-100 {
  width: 100%;
}

.flags-list th, .flags-list td {
  padding: 16px;
  border: solid 1px rgba(0, 0, 0, 0.1);
}

.flags-list td {
  text-align: left;
}

.each-prop-container {
  border-bottom: solid 1px rgba(0, 0, 0, 0.5);
  padding-bottom: 32px;
}

.socure-c-switch {
  position: relative;
  display: inline-block;
  height: 21px;
  font-size: 14px;
  line-height: 1.5;
  vertical-align: middle;
  cursor: pointer;
}

.socure-c-switch__input {
  position: absolute;
  cursor: pointer;
  opacity: 0;
}

.socure-c-switch__toggle {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
  background-color: red;
  border-radius: 30px;
}

.socure-c-switch__toggle::before {
  position: absolute;
  top: 50%;
  margin-top: -11px;
  left: 1px;
  display: block;
  width: 22px;
  height: 22px;
  content: "";
  background-color: #fff;
  border-radius: 50%;
  transition: transform .25s ease 0s;
}

.socure-c-switch__input:disabled + .socure-c-switch__toggle {
  opacity: 0.2;
}

.socure-c-switch__input:checked + .socure-c-switch__toggle {
  background-color: green;
}

.socure-c-switch__input:checked + .socure-c-switch__toggle::before {
  transform: translateX(16px);
}
  