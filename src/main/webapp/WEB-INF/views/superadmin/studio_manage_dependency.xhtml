<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>

    <script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
        appenv = "#{bucket.appEnv}";
    </script>

    <meta charset="UTF-8"/>

    <style>

        .pad {
          margin-top: 10px;
          margin-bottom: 10px;
        }

    </style>
</h:head>
<h:body>
    <div id="dependencyManagementPage">

        <h5>Note: Dependency definitions are not tied to vendor</h5>

        <div>
            <table cellpadding='0' cellspacing='0' border='0'
                   class='table table-striped table-bordered' id='rulecodeDependency'>
                <thead>
                <tr>
                    <th>Name</th>
                    <th>Keys</th>
                    <th>Json field</th>
                </tr>
                </thead>
            </table>
        </div>

        <div style="padding-left: 50px;">
            <a href="javascript:openDependencyCreationForm();">Add New</a>
        </div>

        <div class="form-popup-dependency" id="dependencyCreationForm">
            <form id="dependencyCreationFormFields" action="" class="form-container">
                <label><b>Table Name</b></label>
                <select id="supportedDependencies">
                    <option value="default" selected="selected">Choose table</option>
                </select>

                <label>Please ensure that combination of keys is unique.</label>
                <label><b>Keys</b></label>
                <div id="dependencyKeys">
                    <label>Name</label><input type="text" />
                    <label>Value</label><input type="text" />
                </div>
                <button type="button" onclick="addKey()">+</button>
                <br /><br />
                <label><b>Json Field</b></label>
                <label id="jsonField">data</label><br /><br />

                <button type="button" class="btn" onclick="createDependencyTable()">Submit</button>
                <a href="#" class="btn" onclick="closeDependencyCreationForm()">Close</a>
            </form>
        </div>

    </div>
</h:body>
</html>
