<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Account Admins</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>DataTables live example</title>
	<link rel="shortcut icon" type="image/ico"
		href="http://www.datatables.net/favicon.ico" />
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/socure-theme.css" />

	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		media="print" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

	<script>
		var bucketurl = "#{bucket.resourceURL}";
		var contexturl = "#{request.contextPath}";
		var marketingurl = "#{bucket.marketingSite}";
		var appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px;">
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="left" style="padding-left: 215px;">
		Account Admin <select name="menu" id="admin_list" />
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>
	<div id="MessageModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="delete_msg"></label>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="delete_delegate_admin(true);">Delete</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Promote Dialog -->
	<div id="MessageModalPromote" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="promote_msg"></label>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="promote_delegate_admin(true);">Promote</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div class="accordion" id="accordion2">
		<div class='container' style='margin-top: 10px'>
			<table cellpadding='0' cellspacing='0' border='0'
				class='table table-striped table-bordered' id='example'>
				<thead>
					<tr>
						<th>Image</th>
						<th>First Name</th>
						<th>Last Name</th>
						<th>Company Name</th>
						<th>Contact Number</th>
						<th>Email</th>
						<th>Address</th>
						<th>ID</th>
						<th>Roles</th>
						<th><input type="checkbox" id="delegatedAdminAll"
							name="delegatedAdminAll" value="select All" /></th>
					</tr>
				</thead>
				<!-- <tbody>
					<tr class='odd gradeX'>
						<td>Kumar</td>
						<td>Yesu</td>
						<td>Socure</td>
						<td>9698290324</td>
						<td><EMAIL></td>
						<td>-</td>
					</tr>
				</tbody> -->
			</table>
		</div>
	</div>
	<div style="padding-left: 215px;">
		<a href="javascript:show_add_form();">Add New</a>&nbsp;&nbsp; | &nbsp;
		&nbsp; <a href="javascript:show_delete_form();">Delete</a>&nbsp;&nbsp; | &nbsp;
		&nbsp; <a href="javascript:show_promote_form();">Promote</a>
	</div>

	<div id="AddDelegatedAdminModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<input id="companyName" type="hidden" value="company" />
					<form>
						<div class="form-group row">
							<label for="account_admin_list" class="col-sm-2 col-form-label">Account Admin</label>
							<div class="col-sm-10">
								<select name="menu" id="account_admin_list" />
							</div>
						</div>
						<div class="form-group row">
							<label for="firstName" class="col-sm-2 col-form-label">First Name</label>
							<div class="col-sm-10">
								<input type="text" id="firstName" />
							</div>
						</div>
						<div class="form-group row">
							<label for="lastName" class="col-sm-2 col-form-label">Last Name</label>
							<div class="col-sm-10">
								<input type="text" id="lastName" />
							</div>
						</div>
						<div class="form-group row">
							<label for="contactNumber" class="col-sm-2 col-form-label">Contact Number</label>
							<div class="col-sm-10">
								<input type="text" id="contactNumber" />
							</div>
						</div>
						<div class="form-group row">
							<label for="email" class="col-sm-2 col-form-label">Email</label>
							<div class="col-sm-10">
								<input type="text" id="email" />
							</div>
						</div>
						<div class="form-group row">
							<label for="password" class="col-sm-2 col-form-label">Password</label>
							<div class="col-sm-10">
								<input type="text" id="password" />
							</div>
						</div>
						<fieldset class="form-group">
							<div class="row">
								<legend class="col-form-label col-sm-2 pt-0">Role(s)</legend>
								<div class="col-sm-10">
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="gridRadios" id="gridRadios1" value="reports" />
										<label class="form-check-label" for="gridRadios1">
											Reports
										</label>
									</div>
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="gridRadios" id="gridRadios2" value="settings" />
										<label class="form-check-label" for="gridRadios2">
											Settings
										</label>
									</div>
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="gridRadios" id="gridRadios3" value="developer" />
										<label class="form-check-label" for="gridRadios3">
											Developer
										</label>
									</div>
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="gridRadios" id="gridRadios4" value="feedback" />
										<label class="form-check-label" for="gridRadios4">
											Feedback
										</label>
									</div>
								</div>
							</div>
						</fieldset>

					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="save_delegate_admin(null,true);">Save</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="resources/scripts/bootstrap.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/lib.js"></script>

	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
	<script type="text/javascript"
		src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script>
		$(document).ready(function() {
			get_account_admin('admin_list');
		});
	</script>
</h:body>
</html>