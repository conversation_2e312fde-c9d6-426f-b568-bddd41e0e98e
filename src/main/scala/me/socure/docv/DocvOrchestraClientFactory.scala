package me.socure.docv

import com.typesafe.config.{Config, ConfigFactory}
import me.socure.docv.client.{DocvOrchestraClient, DocvOrchestraClientFactory}
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor

import scala.concurrent.ExecutionContext

class DocvOrchestraClientFactory extends FactoryBean[DocvOrchestraClient] {

  @Autowired
  var envUtl: EnvUtil = _

  @Autowired
  @Qualifier("taskExecutor")
  var threadPoolTaskExecutor: ThreadPoolTaskExecutor = _

  override def getObjectType: Class[_] = classOf[DocvOrchestraClient]

  override def getObject: DocvOrchestraClient = {

    implicit val ec: ExecutionContext = ExecutionContext.fromExecutor(threadPoolTaskExecutor)
    val properties = envUtl.getPropertiesPlain("docvOrchestra")
    if (properties.size() != 0) {
      val config: Config = ConfigFactory.parseProperties(envUtl.getPropertiesPlain("docvOrchestra"))
      val endpoint = config.getString("docvOrchestra.endpoint")
      DocvOrchestraClientFactory.create(endpoint, config.getConfig("docvOrchestra.hmac"))(ec)
    } else {
      null
    }
  }

  override def isSingleton: Boolean = true

}
