"use strict";(self.webpackChunkfraud_mapping_v2=self.webpackChunkfraud_mapping_v2||[]).push([[792],{318:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=n(a(679));t.default=l.default},589:function(e,t,a){var n=this&&this.__awaiter||function(e,t,a,n){return new(a||(a=Promise))((function(l,i){function o(e){try{d(n.next(e))}catch(e){i(e)}}function r(e){try{d(n.throw(e))}catch(e){i(e)}}function d(e){var t;e.done?l(e.value):(t=e.value,t instanceof a?t:new a((function(e){e(t)}))).then(o,r)}d((n=n.apply(e,t||[])).next())}))},l=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.updateModelMapping=t.unAssociate=t.unmapModel=t.addMapping=t.getFraudModelList=t.getFraudModels=t.getMappingsForAccount=t.getAccountsAndFraudModels=void 0;const i=l(a(6425)),o=a(4171);t.getAccountsAndFraudModels=()=>i.default.get("/fraudmapping/v2/get_accounts_and_models").then((e=>e.data.data)).catch((e=>{throw(0,o.handleError)(e)}));t.getMappingsForAccount=e=>i.default.get(`/fraudmapping/v2/account/${e}`).then((e=>e.data.data)).catch((e=>{throw(0,o.handleError)(e)}));t.getFraudModels=()=>i.default.get("/api/1/fraudmodel/models").then((e=>e.data.data)).catch((e=>{throw(0,o.handleError)(e)}));t.getFraudModelList=()=>i.default.get("api/1/fraudmodel/list").then((e=>e.data.data)).catch((e=>{throw(0,o.handleError)(e)}));t.addMapping=(e,t)=>i.default.get(e,{params:t}).then((e=>e)).catch((e=>{throw(0,o.handleError)(e)}));t.unmapModel=(e,t,a,l)=>n(void 0,void 0,void 0,(function*(){const n=new URLSearchParams({accountId:e.toString(),modelId:t,environment:a,associationType:l.toString()});try{const e=yield fetch(`/api/1/fraudmodel/unmap?${n}`,{method:"GET",redirect:"manual",headers:{"Content-Type":"application/json"}});if("opaqueredirect"===e.type)return"";if(!e.ok)throw yield e.json();const t=yield e.json();return(0,o.getSuccessMessage)(t)}catch(e){throw(0,o.handleError)(e)}}));t.unAssociate=(e,t,a,l)=>n(void 0,void 0,void 0,(function*(){const n=new URLSearchParams({accountId:e.toString(),modelId:t,environment:a,associationType:l.toString()});try{const e=yield fetch(`/api/1/fraudmodel/unassociate?${n}`,{method:"GET",redirect:"manual",headers:{"Content-Type":"application/json"}});if("opaqueredirect"===e.type)return"";if(!e.ok)throw yield e.json();const t=yield e.json();return(0,o.getSuccessMessage)(t)}catch(e){throw(0,o.handleError)(e)}}));t.updateModelMapping=(e,t,a,n)=>{const l=new FormData;return l.append("newFraudModelId",e),l.append("oldFraudModelId",t),l.append("userId",a.toString()),l.append("associationType",n),i.default.post("/api/1/fraudmodel/update_mapping",l).then((e=>(0,o.getSuccessMessage)(e))).catch((e=>{throw(0,o.handleError)(e)}))}},679:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=a(4848),i=a(6540),o=a(444),r=n(a(5678)),d=n(a(8275)),s=a(5314),c=n(a(5229)),u=a(589),p=n(a(4080)),m=a(3503);a(9553);t.default=()=>{const{showToast:e}=(0,m.useToast)(),[t,a]=(0,i.useState)(null),[n,f]=(0,i.useState)(!1),[h,g]=(0,i.useState)([]),[M,v]=(0,i.useState)([]),[x,j]=(0,i.useState)(null),[y,b]=(0,i.useState)([]),[_,w]=(0,i.useState)(!1);(0,i.useEffect)((()=>{(0,u.getAccountsAndFraudModels)().then((e=>{g(e.accounts.map((e=>({label:e.name,id:e.id})))),v(e.fraudmodels.map((e=>({label:e.name,publicId:e.publicId}))))})).catch((t=>{e({title:"Error",description:t||"Error while fetching accounts and fraud models",type:"error"})}))}),[g,v,e]);const N=(0,i.useCallback)((()=>{f((e=>!e))}),[]),S=(0,i.useCallback)(((t,a=!1)=>{t&&(a&&w(!0),(0,u.getMappingsForAccount)(t.toString()).then((e=>{if((null==e?void 0:e.length)>0){const t=e[0];t.models=t.models.map(((e,t)=>Object.assign(Object.assign({},e),{rowId:`${e.model.publicId}_${e.model.environment}_${e.associationType}_${t}`}))),j(t)}})).catch((t=>{e({title:"Error",description:t||"Error while fetching mappings for account",type:"error"})})).finally((()=>a&&w(!1))))}),[j,e]);return(0,l.jsxs)(s.FraudModelMappingContext.Provider,{value:{activeAccount:t,accounts:h,models:M,mapping:x,modelsAndDescriptions:y,setActiveAccount:a,setAccounts:g,setModels:v,setMapping:j,setModelsAndDescriptions:b,getAndUpdateMappingsForAccount:S,setShowLoader:w},children:[(0,l.jsx)("h1",{className:"fraud_mapping_heading1",children:"Fraud Model Mapping"}),(0,l.jsx)(o.Divider,{}),(0,l.jsxs)("div",{className:"fraud_mapping_model-container",children:[(0,l.jsx)("a",{className:"fraud_mapping_link",onClick:N,children:"Fraud Model Service"}),(0,l.jsx)(r.default,{}),n&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(d.default,{}),x&&(0,l.jsx)(c.default,{})]})]}),_&&(0,l.jsx)(p.default,{})]})}},2465:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=a(4848),i=a(6540),o=a(3503),r=n(a(361)),d=a(444),s=a(589),c=a(4126),u=a(5314),p=a(4171),m=a(885);a(7235);const f=(0,m.styled)(d.Popper)({width:"max-content !important",minWidth:"305px !important",maxWidth:"70vw !important",zIndex:1300});t.default=()=>{const{showToast:e}=(0,o.useToast)(),{activeAccount:t,accounts:a,models:n,modelsAndDescriptions:m,setActiveAccount:h,getAndUpdateMappingsForAccount:g,setModelsAndDescriptions:M,setMapping:v}=(0,i.useContext)(u.FraudModelMappingContext),[x,j]=(0,i.useState)(null),[y,b]=(0,i.useState)(3),[_,w]=(0,i.useState)("Production"),[N,S]=(0,i.useState)(""),[C,T]=(0,i.useState)(""),[I,D]=(0,i.useState)(!1),A=(0,i.useCallback)(((e,t)=>{h(t),t?g(t.id,!0):v(null)}),[g,h,v]),L=(0,i.useCallback)(((e,t)=>{j(t),t?(0,p.getModelAndSetDescription)(t,m,T,M):T("")}),[m,M]),O=(0,i.useCallback)((()=>{const a=null==t?void 0:t.id,n=null==x?void 0:x.publicId;if(!a||isNaN(a))return void e({title:"Error",description:"Please select Account from dropdown",type:"error"});if(!n||"default"==n)return void e({title:"Error",description:"Please select Fraud Model",type:"error"});if(function(e){if(""===e.trim())return!1;try{return JSON.parse(e),!1}catch(e){return!0}}(N))return void e({title:"Error",description:"Please enter valid json for filter criteria",type:"error"});D(!0);const l={userId:a,fraudModelId:n,associationType:y,environment:_,filterCriteria:N},i=app.modelManagement?"/api/1/fraudmodel/map":"/api/1/fraudmodel/fraudassociate.jsonp";(0,s.addMapping)(i,l).then((()=>{e({title:"Success",description:"Mapping added successfully",type:"success"}),g(a)})).catch((t=>{e({title:"Error",description:t||"Error while adding mapping",type:"error"})})).finally((()=>{D(!1)}))}),[t,x,y,_,N,g,e]);return(0,l.jsxs)("div",{className:"fraud_mapping_container",children:[(0,l.jsxs)(d.Box,{sx:{display:"flex",alignItems:"center"},children:[(0,l.jsx)("label",{className:"fraud_mapping_label",children:"Account"}),(0,l.jsx)(d.Autocomplete,{options:a,getOptionLabel:e=>`${e.id} - ${e.label}`,value:t,onChange:A,renderInput:e=>(0,l.jsx)(o.TextField,Object.assign({},e,{id:"outlined-account",label:"Select",placeholder:"Choose accounts"})),sx:{flex:"1 1 50%"},loading:0===a.length})]}),(0,l.jsxs)(d.Box,{sx:{display:"flex",alignItems:"center"},children:[(0,l.jsx)("label",{className:"fraud_mapping_label",children:"Model"}),(0,l.jsx)(d.Autocomplete,{options:n,getOptionLabel:e=>e.label,value:x,onChange:L,renderInput:e=>(0,l.jsx)(o.TextField,Object.assign({},e,{id:"outlined-model",label:"Select",placeholder:"Select Fraud Model"})),sx:{flex:"1 1 50%"},loading:0===n.length,PopperComponent:f})]}),(0,l.jsxs)(d.Box,{sx:{display:"flex",alignItems:"center"},children:[(0,l.jsx)(o.Typography,{className:"fraud_mapping_label",variant:"label",children:"Model Description"}),(0,l.jsx)(o.TextField,{hiddenLabel:!0,disabled:!0,value:C,sx:{flex:"1 1 50%"}})]}),(0,l.jsxs)(d.Box,{sx:{display:"flex",alignItems:"center"},children:[(0,l.jsx)("label",{className:"fraud_mapping_label",children:"Association Type"}),(0,l.jsx)(o.TextField,{id:"outlined-association-type",select:!0,label:"Select",defaultValue:"3",sx:{flex:"1 1 50%"},onChange:e=>b(Number(e.target.value)),children:c.ASSOCIATION_TYPE.map((e=>(0,l.jsx)(r.default,{value:e.value,selected:e.value===y,children:e.label},e.value)))})]}),(0,l.jsxs)(d.Box,{sx:{display:"flex",alignItems:"center"},children:[(0,l.jsx)("label",{className:"fraud_mapping_label",children:"Environment"}),(0,l.jsx)(o.TextField,{id:"outlined-environment",select:!0,label:"Select",defaultValue:"Production",sx:{flex:"1 1 50%"},onChange:e=>w(e.target.value),children:c.ENVIRONMENT.map((e=>(0,l.jsx)(r.default,{value:e.value,selected:e.value===_,children:e.label},e.value)))})]}),(0,l.jsxs)(d.Box,{sx:{display:"flex",alignItems:"center"},children:[(0,l.jsx)("label",{className:"fraud_mapping_label",children:"Filter Criteria"}),(0,l.jsx)(d.TextareaAutosize,{id:"outlined-filter-criteria",placeholder:"Enter Filter Criteria",value:N,onChange:e=>S(e.target.value),minRows:4})]}),(0,l.jsx)(o.Button,{onClick:O,type:"submit",sx:{alignSelf:"flex-start"},loading:I,children:"Map"})]})}},2967:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});const n=a(4848);t.default=()=>(0,n.jsxs)("ul",{children:[(0,n.jsx)("li",{children:"While mapping a model to an account, you can select between 'Primary', 'Sigma', 'Custom' & 'Shadow'."}),(0,n.jsx)("li",{children:"Only one primary and one sigma models can be mapped to an account"}),(0,n.jsx)("li",{children:"Sigma model is returned only on 3.0 calls"}),(0,n.jsx)("li",{children:"Sigma models always have the name 'sigma' in the response, like generic models have the name 'generic' (not in debug node)"}),(0,n.jsx)("li",{children:"3.0 require minimum one model mapped in the account. It can be any of the 3 model types mentioned above."}),(0,n.jsx)("li",{children:"3.0 calls with 'Legacy Associations 3.0' calls use 'generic' model from config if not explicitly mapped through super-admin as primary model (not a new change, but worth mentioning)"}),(0,n.jsx)("li",{children:"'Sigma' models can only be mapped through super-admin. They cannot be configured in microservice-configurations."}),(0,n.jsx)("li",{children:"Shadow model scores will only appear in the debug node and not in the response."}),(0,n.jsxs)("li",{children:["Filter Criteria can be filled with a configuration following the syntax in"," ",(0,n.jsx)("a",{href:"https://www.notion.so/Model-Filter-Criteria-1431e4e9397380609dacd297f339428c",target:"_blank",rel:"noreferrer",children:"here"})," ","to use different models under different input conditions. This only works for Sigma Identity models and Sigma Synthetic models."]})]})},3566:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=a(4848),i=n(a(318)),o=(n(a(6540)),a(885)),r=a(7437),d=a(3503),s=(0,o.createTheme)();t.default=()=>(0,l.jsxs)(o.StyledEngineProvider,{injectFirst:!0,children:[(0,l.jsx)(d.BaseStyles,{}),(0,l.jsx)(o.ThemeProvider,{theme:s,children:(0,l.jsxs)(r.ThemeProvider,{theme:d.EffectivTheme,children:[(0,l.jsx)(d.ToastContainer,{position:"bottom-left",newestOnTop:!1,closeOnClick:!1,rtl:!1,pauseOnFocusLoss:!0,draggable:!1,hideProgressBar:!0,toastClassName:"effectiv-toast"}),(0,l.jsx)(i.default,{})]})})]})},3627:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=a(4848),i=(n(a(6540)),n(a(9541)));t.default=()=>(0,l.jsxs)("section",{id:"Loading",className:"overlay show",children:[(0,l.jsx)("p",{children:"Loading ..."}),(0,l.jsxs)("div",{className:"loader",children:[(0,l.jsx)("img",{src:i.default}),(0,l.jsx)("span",{className:"dot"})]})]})},4080:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=n(a(3627));t.default=l.default},4126:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ENVIRONMENT=t.ASSOCIATION_TYPE=void 0,t.ASSOCIATION_TYPE=[{label:"Primary",value:1},{label:"Sigma",value:2},{label:"Custom",value:3},{label:"Shadow",value:4}],t.ENVIRONMENT=[{label:"Production",value:"Production"},{label:"Sandbox",value:"Sandbox"},{label:"Development",value:"Development"}]},4171:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getAssociationNameByType=t.getModelAndSetDescription=t.getModelDescriptionByModelName=t.getSuccessMessage=t.handleError=void 0;const n=a(589);t.handleError=e=>{var t,a,n,l,i;return"string"!=typeof e&&e?(null===(n=null===(a=null===(t=e.response)||void 0===t?void 0:t.data)||void 0===a?void 0:a.data)||void 0===n?void 0:n.message)||(null===(i=null===(l=e.data)||void 0===l?void 0:l.data)||void 0===i?void 0:i.message):e};t.getSuccessMessage=e=>{var t,a,n,l;return(null===(a=null===(t=e.data)||void 0===t?void 0:t.data)||void 0===a?void 0:a.message)||(null===(n=e.data)||void 0===n?void 0:n.message)||(null===(l=e.data)||void 0===l?void 0:l.msg)||""};t.getModelDescriptionByModelName=(e,t)=>{const a=t.find((t=>t.name===e));return null==a?"Current model is deprecated.":null!=a.description&&""!=a.description?a.description:"No description."};t.getModelAndSetDescription=(e,a,l,i)=>{(null==a?void 0:a.length)>0?l((0,t.getModelDescriptionByModelName)(e.label,a)):app.modelManagement?(0,n.getFraudModels)().then((a=>{i(a),l((0,t.getModelDescriptionByModelName)(e.label,a))})):(0,n.getFraudModelList)().then((a=>{i(a),l((0,t.getModelDescriptionByModelName)(e.label,a))}))};t.getAssociationNameByType=e=>1===e?"Primary":2===e?"Sigma":3===e?"Custom":"Shadow"},4879:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=a(4848),i=n(a(6540)),o=a(5338),r=n(a(3566)),d=document.getElementById("fraud_mapping_v2_root");if(!d)throw new Error("Failed to find the root element");(0,o.createRoot)(d).render((0,l.jsx)(i.default.StrictMode,{children:(0,l.jsx)(r.default,{})}))},5229:function(e,t,a){var n,l=this&&this.__createBinding||(Object.create?function(e,t,a,n){void 0===n&&(n=a);var l=Object.getOwnPropertyDescriptor(t,a);l&&!("get"in l?!t.__esModule:l.writable||l.configurable)||(l={enumerable:!0,get:function(){return t[a]}}),Object.defineProperty(e,n,l)}:function(e,t,a,n){void 0===n&&(n=a),e[n]=t[a]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(n=function(e){return n=Object.getOwnPropertyNames||function(e){var t=[];for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[t.length]=a);return t},n(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a=n(e),o=0;o<a.length;o++)"default"!==a[o]&&l(t,e,a[o]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0});const r=a(6540),d=a(4848),s=o(a(6540)),c=a(444),u=a(5314),p=a(3503),m=a(589),f=a(4171);a(8825);t.default=()=>{const{showToast:e}=(0,p.useToast)(),{activeAccount:t,mapping:a,models:n,modelsAndDescriptions:l,setModelsAndDescriptions:i,getAndUpdateMappingsForAccount:o,setShowLoader:h}=(0,s.useContext)(u.FraudModelMappingContext),[g,M]=(0,s.useState)(!1),[v,x]=(0,s.useState)(null),[j,y]=(0,s.useState)(null),[b,_]=(0,s.useState)(""),[w,N]=(0,s.useState)(!1),S=(0,s.useCallback)((t=>{const n=t.model.name,o=t.associationType,r=a.accountId;if(!n||!o||!r)return void e({type:"error",title:"Error",description:"Please select the model mapping to update."});M(!0),x(t);const d={label:n,publicId:t.model.publicId};y(d),(0,f.getModelAndSetDescription)(d,l,_,i)}),[a,l,i,e]),C=(0,s.useCallback)((n=>{h(!0),app.modelManagement?(0,m.unmapModel)(a.accountId,n.model.publicId,n.model.environment,n.associationType).then((a=>{o(null==t?void 0:t.id),e({type:"success",title:"Success",description:a||"Model unmapped successfully."})})).catch((t=>{e({type:"error",title:"Error",description:t})})).finally((()=>h(!1))):(0,m.unAssociate)(a.accountId,n.model.publicId,n.model.environment,n.associationType).then((()=>{o(null==t?void 0:t.id),e({type:"success",title:"Success",description:"Model unmapped successfully."})})).catch((t=>{e({type:"error",title:"Error",description:t})})).finally((()=>h(!1)))}),[t,o,a.accountId,e,h]),T=(0,s.useMemo)((()=>[{field:"edit",headerName:"Edit",flex:1,sortable:!1,filterable:!1,disableColumnMenu:!0,renderCell:({row:e})=>(0,d.jsx)(p.Button,{onClick:()=>S(e),children:"Edit"})},{field:"name",headerName:"Name",flex:1,valueGetter:(e,t)=>t.model.name},{field:"identifier",headerName:"Identifier",flex:1,valueGetter:(e,t)=>t.model.identifier},{field:"version",headerName:"Version",flex:1,valueGetter:(e,t)=>t.model.version},{field:"scoreName",headerName:"Score Name",flex:1,valueGetter:(e,t)=>t.model.scoreName},{field:"associationType",headerName:"Association Type",flex:1,valueGetter:(e,t)=>(0,f.getAssociationNameByType)(t.associationType)},{field:"normalized",headerName:"Normalized",flex:1,valueGetter:(e,t)=>t.model.normalized},{field:"environment",headerName:"Environment",flex:1,valueGetter:(e,t)=>t.model.environment},{field:"filterCriteria",headerName:"Filter Criteria",flex:1,valueGetter:(e,t)=>t.filterCriteria},{field:"delete",headerName:"Delete",flex:1,sortable:!1,filterable:!1,disableColumnMenu:!0,renderCell:({row:e})=>(0,d.jsx)(p.Button,{onClick:()=>C(e),children:"Delete"})}]),[C,S]),I=(0,s.useCallback)((()=>{M(!1)}),[]),D=(0,s.useCallback)((()=>{if(!j)return void e({type:"error",title:"Error",description:"Please select a valid model."});const n=v.model.publicId,l=j.publicId,i=(0,f.getAssociationNameByType)(v.associationType).toLowerCase();N(!0),(0,m.updateModelMapping)(l,n,a.accountId,i).then((a=>{o(null==t?void 0:t.id),e({type:"success",title:"Success",description:a||"Successfully updated model mapping."}),I()})).catch((t=>{e({type:"error",title:"Error",description:t||"Unexpected Error"})})).finally((()=>{N(!1)}))}),[t,v,j,a,I,o,e]),A=(0,s.useCallback)(((e,t)=>{y(t),t?(0,f.getModelAndSetDescription)(t,l,_,i):_("")}),[l,i]);return(0,d.jsxs)("div",{className:"fraud_mapping_table-container",children:[(0,d.jsxs)("div",{className:"fraud_mapping_account-id-name-wrapper",children:[(0,d.jsxs)("span",{children:["Account Id: ",a.accountId]}),(0,d.jsxs)("span",{children:["Account Name: ",a.accountName]})]}),(0,d.jsx)(p.DataGrid,{rows:a.models,columns:T,disableRowSelectionOnClick:!0,rowHeight:52,getRowId:e=>e.rowId}),(0,d.jsxs)(p.Modal,{id:"update-model-mapping-modal",open:g,onClose:I,title:"Update Model Mapping",footer:[{label:"Cancel",onClick:I},{label:"Update",onClick:D,variant:"primary",loading:w}],maxWidth:"xs",children:[(0,d.jsxs)(c.Box,{sx:{display:"flex",flexDirection:"column",rowGap:"0.5rem",marginBottom:"1rem"},children:[(0,d.jsx)(p.Typography,{variant:"label",children:"Model Name"}),(0,d.jsx)(c.Autocomplete,{options:n,renderOption:(e,t)=>(0,r.createElement)("li",Object.assign({},e,{title:t.label,key:`${t.label}-${t.publicId}`}),t.label),value:j,onChange:A,renderInput:e=>(0,d.jsx)(p.TextField,Object.assign({},e,{hiddenLabel:!0,id:"outlined-model",placeholder:"Select Fraud Model"})),sx:{flex:"1 1 50%"},loading:0===n.length})]}),(0,d.jsxs)(c.Box,{sx:{display:"flex",flexDirection:"column",rowGap:"0.5rem",marginBottom:"1rem"},children:[(0,d.jsx)(p.Typography,{variant:"label",children:"Model Description"}),(0,d.jsx)(p.TextField,{hiddenLabel:!0,disabled:!0,value:b})]})]})]})}},5314:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FraudModelMappingContext=void 0;const n=a(6540);t.FraudModelMappingContext=(0,n.createContext)({})},5678:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=n(a(2967));t.default=l.default},7235:(e,t,a)=>{a.r(t)},8275:function(e,t,a){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const l=n(a(2465));t.default=l.default},8825:(e,t,a)=>{a.r(t)},9541:(e,t,a)=>{a.r(t),a.d(t,{ReactComponent:()=>o,default:()=>r});var n,l=a(6540);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},i.apply(null,arguments)}var o=function(e){return l.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",xmlSpace:"preserve",width:25.164,height:25.164},e),n||(n=l.createElement("path",{fill:"#F6921E",d:"M0 12.584C0 5.646 5.645 0 12.582 0c4.195 0 8.1 2.079 10.445 5.563a2.055 2.055 0 0 1-.559 2.856 2.056 2.056 0 0 1-2.855-.559 8.46 8.46 0 0 0-7.031-3.746c-4.668 0-8.467 3.799-8.467 8.469 0 4.668 3.799 8.467 8.467 8.467a8.46 8.46 0 0 0 7.031-3.746 2.058 2.058 0 0 1 3.414 2.297 12.58 12.58 0 0 1-10.445 5.563C5.645 25.166 0 19.521 0 12.584"})))};const r="data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIKICAgICB3aWR0aD0iMjUuMTY0cHgiIGhlaWdodD0iMjUuMTY0cHgiIHZpZXdCb3g9IjAgMCAyNS4xNjQgMjUuMTY0IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCAyNS4xNjQgMjUuMTY0IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8Zz4KCQk8cGF0aCBmaWxsPSIjRjY5MjFFIiBkPSJNMCwxMi41ODRDMCw1LjY0Niw1LjY0NSwwLDEyLjU4MiwwYzQuMTk1LDAsOC4xLDIuMDc5LDEwLjQ0NSw1LjU2M2MwLjYzNiwwLjk0MiwwLjM4NiwyLjIyMi0wLjU1OSwyLjg1NgoJCQljLTAuOTQyLDAuNjM0LTIuMjIsMC4zODUtMi44NTUtMC41NTljLTEuNTc4LTIuMzQ2LTQuMjA2LTMuNzQ2LTcuMDMxLTMuNzQ2Yy00LjY2OCwwLTguNDY3LDMuNzk5LTguNDY3LDguNDY5CgkJCWMwLDQuNjY4LDMuNzk5LDguNDY3LDguNDY3LDguNDY3YzIuODI1LDAsNS40NTMtMS4zOTksNy4wMzEtMy43NDZjMC42MzYtMC45NDMsMS45MTMtMS4xOTIsMi44NTUtMC41NTkKCQkJYzAuOTQ0LDAuNjM2LDEuMTk0LDEuOTE0LDAuNTU5LDIuODU2Yy0yLjM0NiwzLjQ4My02LjI1LDUuNTYzLTEwLjQ0NSw1LjU2M0M1LjY0NSwyNS4xNjYsMCwxOS41MjEsMCwxMi41ODR6Ii8+Cgk8L2c+CjwvZz4KPC9zdmc+"},9553:(e,t,a)=>{a.r(t)}},e=>{e.O(0,[96],(()=>{return t=4879,e(e.s=t);var t}));e.O()}]);