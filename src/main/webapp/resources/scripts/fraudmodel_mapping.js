show_mapping_update_dialog = function (model, account, associationType) {
    sOut = "<fieldset>"
                + "<div>"
                + "<div style='margin-top:20px;'>"
                + "     <h6>Model Name: </h6>"
                + '     <div><input id="fraudname" class="form-control identifier-width" list="fraudnamelist" />'
                + '        <datalist id="fraudnamelist"></datalist><div class="error-msg" id="nameerrormsg"></div>'
                + "<div style='margin-top:20px;'>"
                + "     <h6>Model Description: </h6>"
                + '     <div id="updateDescription"></div></div>'
                + "</div>"
    if (model != null && account != null) {
        get_model(model, render_description_for_update);
        $('#update_form').html(sOut);
        document.getElementById("fraudname").value = model;
        $('#UpdateConfirmationModal').modal('show');
    } else {
        $('#admin_message').html('Please select the model mapping to update.');
    }
    document.getElementById("update_mapping").addEventListener("click", function(){
        update_mapping(model, account, associationType);
    });
};

get_model_by_name = function (model) {
    for (var i = 0; i < fraudmodel_data.length; i++) {
        if (fraudmodel_data[i].name === model) {
            return fraudmodel_data[i];
        }
    }
};

validateAccountId = function (accountId) {
  const accountIdRegex = /^\d+$/;
  return accountIdRegex.test(accountId);
};

	$(function() {
	     $('#Loading').hide();
	     if(/.*\/account\/\d*/.test($(location).attr('href'))) {
	        $('#showMapping').click();
         }
         let accountId = $(location).attr('href').match(/(\w+)\/fraudmapping\/account\/(\d+)/)[2];
         if (validateAccountId(accountId)) {
                  const regexString = `^${accountId} - \\w+`;
                  const regex = new RegExp(regexString);

                  const accountName = $("#socure_accounts li").filter(function () {
                                          return regex.test($(this).text());
                                      }).text();
                  $('.accounts-dropdown-input').val(accountName)
         }
	});

$(function() {
  $('.accounts-dropdown-input').change(function() {
      var selectedAccount =$('.accounts-dropdown-input').val().trim().split(" - ")[0];
      $('#Loading').show();
      location.replace(`/fraudmapping/account/${selectedAccount}`);
  });
});
window.onload = function() {
  if(DD_RUM) {
    DD_RUM.startView({ name: "/fraudmodel_mapping" });
    DD_RUM.setUser({ id: loggedinUsername });
  }
}

var fraudmodel_data = null;

get_model = function (model, callback) {
    var url = "";
    if (app.modelManagement) {
        url = contexturl + '/api/1/fraudmodel/models';
    } else {
        url = contexturl + '/api/1/fraudmodel/list';
    }
    $.ajax(url, {
        type: 'GET',
        success: function (data) {
            if (data !== null) {
                data = data['data'];
                fraudmodel_data = data;
                callback(data, model);
            }
        }
    });
};

render_description_value = function (fieldName, model) {
    var modelObj = get_model_by_name(model);
    if (modelObj == undefined) {
        document.getElementById(fieldName).innerHTML = "Current model is deprecated.";
    } else if (modelObj.description != undefined && modelObj.description != "") {
        document.getElementById(fieldName).innerHTML = modelObj.description;
    } else {
        document.getElementById(fieldName).innerHTML = "No description.";
    }
}

render_description_for_update = function (data, model) {
    render_model_name_dropdown(data);
    render_description_value("updateDescription", model)
}

render_description_field = function (data, model) {
    render_description_value("descriptionfield", model)
}

render_description_for_mapping = function () {
    var selectedmodel = $('#fraudmodel').find('option:selected').text();
    get_model(selectedmodel, render_description_field);
}

render_model_name_dropdown = function (data) {
    var dropdown = document.getElementById('fraudnamelist');
    dropdown.innerHTML = "";
    for (var i = 0; i < data.length; i++) {
        var option = document.createElement('option');
        option.value = data[i].name;
        dropdown.appendChild(option);
    }
    var namefield = document.getElementById("fraudname");
    namefield.addEventListener("change", function(){
        render_description_value("updateDescription", namefield.value);
    });
}

update_mapping = function (model, account, associationType) {

    var url = contexturl + '/api/1/fraudmodel/update_mapping';

    var formData = new FormData();

    var nameErrorMsg = document.getElementById("nameerrormsg");
    var namefield = document.getElementById("fraudname");
    if (namefield.value) {
        var newModel = get_model_by_name(namefield.value);
        if (newModel != null) {
            formData.append('newFraudModelId', get_model_by_name(namefield.value).publicId);
        } else {
            nameErrorMsg.innerHTML = "Please select a valid model.";
            return;
        }
    } else {
        nameErrorMsg.innerHTML = "Please select a valid model.";
        return;
    }
    formData.append('oldFraudModelId', get_model_by_name(model).publicId);
    formData.append('userId', account);
    var assocType;
    if (associationType === "1"){
        assocType = "primary";
        formData.append('associationType', "primary");
    } else if (associationType === "2"){
        assocType = "sigma";
        formData.append('associationType', "sigma");
    } else if (associationType === "4"){
        assocType = "shadow";
        formData.append('associationType', "shadow");
    } else {
        assocType = "custom";
        formData.append('associationType', "custom");
    }
    $.ajax(
            url,
            {
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,

                success: function (data) {
                    if (data['status'].toLowerCase() === 'ok') {
                        $("#UpdateConfirmationModal").modal("hide");
                        $('#fraudModelResult').text(data['msg']);
                    } else {
                        $("#UpdateConfirmationModal").modal("show");
                        $('#fraudModelResult').text(data['msg']);
                    }
                }
    });
    DD_RUM.track("Update Fraud Model Mapping", { oldFraudModelId: get_model_by_name(model).publicId, newFraudModelId: get_model_by_name(namefield.value).publicId, userId: account, associationType: assocType })
}
