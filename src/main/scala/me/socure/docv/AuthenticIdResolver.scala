package me.socure.docv

import me.socure.docv.client.DocvOrchestraClient
import me.socure.docv.common.dto.{AccountStrategyDto, AuthenticIdStrategyDto}
import me.socure.model.ErrorResponse
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component

import scala.concurrent.Await
import scala.concurrent.duration._

@Component
class AuthenticIdResolver {

  private val logger = LoggerFactory.getLogger(getClass)

  @Autowired
  @Qualifier("docvOrchestraClient")
  var docvOrchestraClient: DocvOrchestraClient = _

  def fetchAllAuthenticIdStrategies() :  Either[ErrorResponse,Seq[AuthenticIdStrategyDto]]= {
    Await.result(docvOrchestraClient.fetchAllAuthenticIdStrategies(), Duration.Inf)
  }

  def fetchAuthenticIdStrategy(strategyId: String): Either[ErrorResponse, AuthenticIdStrategyDto] = {
    Await.result(docvOrchestraClient.fetchAuthenticIdStrategy(strategyId), Duration.Inf)
  }

  def addAuthenticIdStrategy(authStrategy: AuthenticIdStrategyDto) : Either[ErrorResponse, AuthenticIdStrategyDto] = {
    Await.result(docvOrchestraClient.addAuthenticIdStrategy(authStrategy), Duration.Inf)
  }

  def updateAuthenticIdStrategy(auth: AuthenticIdStrategyDto) : Either[ErrorResponse, AuthenticIdStrategyDto] = {
    Await.result(docvOrchestraClient.updateAuthenticIdStrategy(auth), Duration.Inf)
  }


  def associateAuthenticIdStrategy(accountStrategy: AccountStrategyDto): Either[ErrorResponse, Boolean]=  {
    Await.result(docvOrchestraClient.associateAuthenticIdStrategy(accountStrategy), Duration.Inf)
  }

  def fetchAssociatedAuthenticIdStrategy(publicAccountId: String): Either[ErrorResponse, AuthenticIdStrategyDto] = {
    Await.result(docvOrchestraClient.fetchAssociatedAuthenticIdStrategy(publicAccountId), Duration.Inf)
  }

}


