<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">


	<bean id="bulkrunSmileFactory" class="org.codehaus.jackson.smile.SmileFactory"></bean>

	<bean id="smileFactoryObjectMapper" class="org.codehaus.jackson.map.ObjectMapper">
		<constructor-arg ref="bulkrunSmileFactory"></constructor-arg>
	</bean>

	<context:component-scan base-package="me.socure.bulkrun"></context:component-scan>

</beans>
