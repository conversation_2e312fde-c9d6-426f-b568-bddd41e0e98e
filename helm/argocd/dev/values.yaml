image:
  repository: fips-registry.us-east-1.build.socure.link/ux/super-admin
  tag: "OVERRIDE_ME"

serviceAccount:
  # This has to match the trusted entity value configured in the IAM provisioned by terraform
  name: "super-admin-dev"
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/eks-irsa-super-admin-dev20230216084102136700000001"

application:
  env:
    CONFIGURATION_NAME: "super-admin"
    CONFIGURATION_VERSION: "OVERRIDE_MSCV"
    DD_CONSTANT_TAGS: "ddname:socure-admin-superadmin"
    JAVA_TOOL_OPTIONS: "-Dorg.bouncycastle.jca.enable_jks=true -Dorg.bouncycastle.rsa.allow_multi_use=true -javaagent:/opt/socure/dd-java-agent-1.18.1.jar -Dnet.spy.log.LoggerImpl=net.spy.memcached.compat.log.SLF4JLogger"
    CATALINA_OPTS: "-Dorg.bouncycastle.jca.enable_jks=true -Dorg.bouncycastle.rsa.allow_multi_use=true -javaagent:/opt/socure/dd-java-agent-1.18.1.jar -Dnet.spy.log.LoggerImpl=net.spy.memcached.compat.log.SLF4JLogger"

service:
  type: ClusterIP
  annotations: {}
  # -- Service configruation for services that have a HTTP port. When enabled, these values are used for port assignment and healthchecks in the deployment, service, and ingress manifests. It defaults to using `http` as the name for the port to maintain backward compatibility with legacy-tls.
  http:
    enabled: true
    name: "http"
    containerPort: 8080
    svcPort: 80

deployment:
  replicaCount: 1
  healthProbes:
    enabled: true
    type: "http"
    livenessProbe:
      path: "/"
    readinessProbe:
      path: "/"
    startupProbe:
      path: "/"
  securityContext:
    readOnlyRootFilesystem: false
  resources:
    limits:
      cpu: "3"
      memory: "10Gi"
      ephemeral-storage: "20Gi"
    requests:
      cpu: "500m"
      memory: "4Gi"
      ephemeral-storage: "6Gi"
  podVolumes:
    - name: catalina-logs
      emptyDir: {}
    - name: tmp
      emptyDir: {}
    - name: catalina-conf
      emptyDir: {}
    - name: catalina-work
      emptyDir: {}
    - name: catalina-temp
      emptyDir: {}
  podVolumeMounts:
    - mountPath: /usr/local/tomcat/logs
      name: catalina-logs
    - mountPath: /usr/local/tomcat/conf/Catalina/localhost
      name: catalina-conf
    - mountPath: /tmp
      name: tmp
    - mountPath: /usr/local/tomcat/work
      name: catalina-work
    - mountPath: /usr/local/tomcat/temp
      name: catalina-temp

istio:
  enabled: true
  hosts:
    - superadmin.webapps.us-east-1.product-dev.socure.link
  svcPort: 80
  private:
    gateway: private-gw
  authorizationPolicy:
    enabled: true
    pa:
      enabled: true
      serviceAccounts:
        - mock-server-dev
        - idplus-service-dev
    paPrivateGateway:
      enabled: true
