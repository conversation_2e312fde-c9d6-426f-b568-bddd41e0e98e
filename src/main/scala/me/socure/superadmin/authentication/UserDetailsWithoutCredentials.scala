package me.socure.superadmin.authentication

import java.util

import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.userdetails.UserDetails

class UserDetailsWithoutCredentials(
                                     username: String,
                                     authorities: util.Collection[_ <: GrantedAuthority]
                                   ) extends UserDetails {
  override def getAuthorities: util.Collection[_ <: GrantedAuthority] = authorities

  override def getPassword: String = null

  override def getUsername: String = username

  override def isAccountNonExpired: Boolean = true

  override def isAccountNonLocked: Boolean = true

  override def isCredentialsNonExpired: Boolean = true

  override def isEnabled: Boolean = true
}
