import { useCallback, useEffect, useState } from 'react';
import './App.css';
import { Topbar, Container } from './components'
import { getConfigList, downloadConfig, uploadConfig } from './services/controlCenter';
import Popup from './components/Popup';

const Loader = ({ message }) => {
  return <Popup children={<div>{message || 'Loading'} ...</div>} />
}

function App() {
  const [ configList, setConfigList ] = useState([]);
  const [ selectedConfig, setSelectedConfig ] = useState(null);
  const [ loading, setLoading ] = useState('');

  const onMenuClick = useCallback((name) => {
    switch(name) {
      case 'Old Control Center':
        window.location.href = 'control_center';
        break;
      case 'Inactive Users':
        window.location.href = 'inactive_users';
        break;
      case 'Logout':
        window.location.href = 'logout';
        break;
      case 'Active Users':
      default:
        window.location.href = '/active_users';
    }
  }, [])

  useEffect(() => {
    setLoading('Downloading configs');
    getConfigList().then(response => {
      setConfigList(response.data);
    }).catch((err) => {
      console.log(err)
      alert('Error occurred while trying to get configs')
    }).finally(() => setLoading(''));
  }, [])

  const onConfigSelect = useCallback((name) => {
    setLoading('Downloading');
    downloadConfig(name).then(response => {
      const data = response.data;
      setSelectedConfig(JSON.parse(data));
    }).catch((err) => {
      console.log(err)
      alert("Error occurred while downloading config")
    }).finally(() => setLoading(''));
  }, [])

  const onCancel = useCallback(() => {
    setSelectedConfig(null);
  }, [])

  const onSave = useCallback((config) => {
    const data = {
      jsonConfig : JSON.stringify(config),
      configName : config.name
    }
    setLoading('Uploading config');
    uploadConfig(JSON.parse(JSON.stringify(data))).then(response => {
      if(response.status === 'Ok') {
        alert(response.msg);
        setConfigList(list => {
          const newList = !list.find(val => val === config.name)
            && [...list, config.name]
          return newList || list;
        })
        setSelectedConfig(config);
        setSelectedConfig(null);
      }
    }).catch(err => {
      alert(err?.msg || err);
    }).finally(() => setLoading(''));
  }, [])

  return <div className="App">
      <div className='topbar-container'>
        <Topbar menus={[
          'Old Control Center',
          'Active Users',
          'Inactive Users',
          'Logout'
        ]} onClick={onMenuClick} />
      </div>
      <div className='page-heading'>Control Centre</div>
      {loading
        ? <Loader message={loading} />
        : <Container
          list={configList}
          selectedConfig={selectedConfig}
          onSelect={onConfigSelect}
          onCancel={onCancel}
          onSave={onSave} />
      }
  </div>
}

export default App;
