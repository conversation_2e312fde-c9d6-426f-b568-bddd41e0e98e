<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>

    <script>
        bucketurl = "#{bucket.resourceURL}";
        contexturl = "#{request.contextPath}";
        marketingurl = "#{bucket.marketingSite}";
        appenv = "#{bucket.appEnv}";
    </script>


    <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="resources/styles/socure-dashboard.css" />
    <script  type="text/javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
    <script  type="text/javascript" src="#{bucket.resourceURL}/scripts/bootstrap.min.js"></script>
    <script  type="text/javascript" src="#{bucket.resourceURL}/scripts/lib.js"></script>
    <script  src="#{bucket.resourceURL}/scripts/spin.min.js" type="text/javascript"></script>
    <script  src="#{bucket.resourceURL}/scripts/jquery.spin.js" type="text/javascript"></script>
    <script  type="text/javascript"  src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript"
            src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
    <script type="text/javascript" src="resources/scripts/jobdefinition_config.js"></script>

    <meta charset="UTF-8"/>
    <title>Job Definitions</title>

    <style>
        .accordion li ul li { padding: 1px 20px; font-size: 0.9em; }

        .tab {
          overflow: hidden;
          border: 1px solid #ccc;
          background-color: #f1f1f1;
        }

        /* Style the buttons that are used to open the tab content */
        .tab button {
          background-color: inherit;
          float: left;
          border: none;
          outline: none;
          cursor: pointer;
          padding: 14px 16px;
          transition: 0.3s;
        }

        /* Change background color of buttons on hover */
        .tab button:hover {
          background-color: #ddd;
        }

        /* Create an active/current tablink class */
        .tab button.active {
          background-color: #ccc;
        }

        /* Style the tab content */
        .tabcontent {
          display: none;
          padding: 6px 12px;
          border: 1px solid #ccc;
          border-top: none;
        }


    </style>

</h:head>
<h:body>

    <center><h3>Batch Job Central</h3></center>
    <div align="right">
        <h5>
            <h:outputLink value="industry">Manage Industry</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="active_users">Active Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="inactive_users">Inactive Users</h:outputLink>
            &nbsp;&nbsp; | &nbsp; &nbsp;
            <h:outputLink value="logout">Logout</h:outputLink>
            &nbsp;&nbsp;  &nbsp; &nbsp;
        </h5>
    </div>
    <br/>
    <div class="tab">
        <button class="tablinks" id="jobdefinitions" >Job Definitions</button>
<!--        <button class="tablinks" id="eventrules" >Event Rules</button>-->
    </div>

    <div id="JobDefinitions" class="tabcontent"></div>
    <div id="EventRules" class="tabcontent"></div>

    <section id="Loading" class="overlay show">
        <p>Loading ...</p>
        <div class="loader">
            <img src="#{bucket.resourceURL}/assets/images/socure-loader.svg" /> <span class="dot"></span>
        </div>
    </section>

</h:body>
</html>
