import React, { useCallback } from 'react'

const Switch = ( { id, isChecked, onChange, disabled } ) => {
  
  const handleChange = useCallback((event) => {
    onChange && onChange(event.target.checked, event);
  }, [onChange]);

  return (
    <label
        className='socure-c-switch'
        htmlFor={id}
    >
        <input
            id={id}
            className='socure-c-switch__input'
            type="checkbox"
            onChange={handleChange}
            checked={isChecked}
            disabled={disabled}
        />
        <span className='socure-c-switch__toggle' />
    </label>
  );
}

export default Switch;