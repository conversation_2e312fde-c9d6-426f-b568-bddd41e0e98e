package me.socure.bean;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import me.socure.dynamic.control.center.v2.factory.DynamicControlCenterV2Factory;
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate;
import me.socure.util.EnvUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import scala.concurrent.ExecutionContext;

@Configuration
public class ControlCenterBean {

    @Autowired
    @Qualifier("envUtil")
    private EnvUtil envUtil;

    @Autowired
    private ExecutionContext executionContext;

    @Bean
    @Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
    public DynamicControlCenterV2Evaluate getDynamicControlCenterV2Evaluate() {
        final Config controlCenterConfig = ConfigFactory.parseProperties(envUtil.getPropertiesPlain("dynamic.control.center"));
        return DynamicControlCenterV2Factory.getEvaluator(controlCenterConfig, executionContext);
    }
}
