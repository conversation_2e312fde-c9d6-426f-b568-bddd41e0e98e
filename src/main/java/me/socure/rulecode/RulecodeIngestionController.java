package me.socure.rulecode;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import me.socure.exceptions.ValidationException;
import me.socure.json.controllerv1.RuleCodeController;
import me.socure.json.controllerv1.bean.RCIngestionActionEnum;
import me.socure.json.controllerv1.bean.RuleCodeAudit;
import me.socure.json.controllerv1.bean.RuleCodeResponse;
import me.socure.service.BatchJobService;
import me.socure.service.rulecode.RuleCodeIngestionService;
import me.socure.util.SecureUtil;
import me.socure.util.validator.BatchJobValidator;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping("/rulecode/dataload/*")
public class RulecodeIngestionController {
    private static final Logger logger = LoggerFactory.getLogger(RuleCodeController.class);
    public static final String BATCH_IMPORT_LAMBDA = "BATCH_IMPORT_LAMBDA";

    @Autowired
    private RuleCodeIngestionService ruleCodeIngestionService;

    @Autowired
    private BatchJobService batchJobService;

    @Autowired
    private BatchJobValidator validator;

    @RequestMapping(method = RequestMethod.POST)
    public ResponseEntity<RuleCodeResponse> updateBackup(@RequestParam("filePath") String filePath,
                                                         @RequestParam("action")
                                                         RCIngestionActionEnum action, HttpServletRequest request) {
        List<RuleCodeAudit> auditLogs = null;
        try {
            auditLogs = ruleCodeIngestionService.getAuditLogs(filePath);

            if (!ruleCodeIngestionService.isValidAction(action, auditLogs)) {
                throw new ValidationException("Invalid Action!");
            }

            ruleCodeIngestionService.updateBackup(auditLogs, filePath, action, BATCH_IMPORT_LAMBDA);

            return new ResponseEntity<RuleCodeResponse>(ruleCodeIngestionService.getRuleCodeResponse("success", auditLogs), HttpStatus.OK);
        } catch (ValidationException ex) {
            logger.error("Validation Exception", ex);
            return new ResponseEntity<RuleCodeResponse>(ruleCodeIngestionService.getRuleCodeResponse(ex.getMessage(), auditLogs), HttpStatus.BAD_REQUEST);
        } catch (Exception ex) {
            logger.error("Unable to get audit stats", ex);
            return new ResponseEntity<RuleCodeResponse>(ruleCodeIngestionService.getRuleCodeResponse("Internal Error", auditLogs), HttpStatus.BAD_REQUEST);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "/dynamo")
    public ResponseEntity<Object> createRulecodeDynamoIngestionBatchJob(
        @RequestParam(value = "file", required = false) MultipartFile file,
        @RequestParam(value = "input",required = false) String input
    ) {
        try {
            if(StringUtils.isEmpty(input)) {
                if(file == null) throw new ValidationException("Invalid file uploaded. Unable to create job! (/rulecode_dynamo_ingestion/create)");
                String jsonTxt = IOUtils.toString(file.getInputStream(), StandardCharsets.UTF_8);
                return getObjectResponseEntity(jsonTxt);
            }
            return getObjectResponseEntity(input);
        }
        catch (ValidationException ve) {
            logger.error("Unable to create job /rulecode_dynamo_ingestion/create - Validation failed ", ve);
            return new ResponseEntity<>(ve.getMessage(), HttpStatus.BAD_REQUEST);
        }
        catch (IOException ioe) {
            logger.error("Unable to create job /rulecode_dynamo_ingestion/create caused by invalid file", ioe);
            return new ResponseEntity<>(ioe.getMessage(), HttpStatus.BAD_REQUEST);
        }
        catch (Exception e) {
            logger.error("Unable to create job /rulecode_dynamo_ingestion/create", e);
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private ResponseEntity<Object> getObjectResponseEntity(String input) throws Exception {
        validator.isJSONValid(input);
        String jobId = batchJobService.createRulecodeDynamoIngestionBatchJob(BATCH_IMPORT_LAMBDA,
            input);
        return new ResponseEntity<>(jobId, HttpStatus.OK);
    }
}
