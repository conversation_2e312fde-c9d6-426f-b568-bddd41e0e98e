package me.socure.controller

import me.socure.application.ApplicationResolver
import me.socure.common.json.bean.APIOutput
import org.springframework.web.bind.annotation.{GetMapping, PathVariable, RequestMapping, RequestParam}
import me.socure.service.constants.IConstants
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.http.{HttpHeaders, HttpStatus, ResponseEntity}
import org.springframework.stereotype.Controller

@Controller
@RequestMapping(Array("/superadmin/application/*"))
class ApplicationController {

  @Autowired
  @Qualifier("applicationResolver")
  var applicationResolver: ApplicationResolver = _

  @GetMapping(Array("/list"))
  def listApplications(): ResponseEntity[Object] = {

    val apiOutput = new APIOutput

    applicationResolver.fetchList() match {
      case Right(response) =>
        apiOutput.setData(response)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
      case Left(errorResponse) =>
        apiOutput.setMsg(errorResponse.message)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](apiOutput, headers, HttpStatus.OK)
  }

  @GetMapping(Array("/status"))
  def getStatus(@RequestParam("application") application: String): ResponseEntity[Object] = {

    val apiOutput = new APIOutput

    applicationResolver.fetchStatus(application) match {
      case Right(response) =>
        apiOutput.setData(response)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
      case Left(errorResponse) =>
        apiOutput.setMsg(errorResponse.message)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](apiOutput, headers, HttpStatus.OK)
  }

}
