import React from "react";
import { TextInput, InputType } from "@socure/components";
import styled from "styled-components";

const Wrapper = styled.div`
    padding: 10px;
`;
export interface SaiPennyDropValues {
    depositorName: {
        value: string,
        errorMessageOnValidation: string,
    },
    memo: {
        value: string,
        errorMessageOnValidation: string,
    },
    physicalAddress: {
        value: string,
        errorMessageOnValidation: string,
    },
    city: {
        value: string,
        errorMessageOnValidation: string,
    },
    state: {
        value: string,
        errorMessageOnValidation: string,
    },
    zip: {
        value: string,
        errorMessageOnValidation: string,
    },
    country: {
        value: string,
        errorMessageOnValidation: string,
    }
}

interface SaiPennyDropEnablementProps {
    value: SaiPennyDropValues;
    handleChange: (field: string, value: string) => void;
}

class SaiPennyDropEnablement extends React.PureComponent<SaiPennyDropEnablementProps, any> {
    constructor(props: SaiPennyDropEnablementProps) {
        super(props);
        this.state = {};
    }

    render(): React.ReactNode {
        const { value, handleChange } = this.props;
        return <React.Fragment>
            <Wrapper key="depositorName">
                <TextInput
                    label={"Depositor Name"}
                    type={InputType.text}
                    value={value.depositorName.value}
                    doValidate={true}
                    errorMsg={value.depositorName.errorMessageOnValidation || undefined}
                    onChange={(val) => handleChange("depositorName", val)}
                />
            </Wrapper>
            <Wrapper key="memo">
                <TextInput
                    label={"Memo"}
                    type={InputType.text}
                    value={value.memo.value}
                    doValidate={true}
                    errorMsg={value.memo.errorMessageOnValidation || undefined}
                    onChange={(val) => handleChange("memo", val)}
                />
            </Wrapper>
            <Wrapper key="physicalAddress">
                <TextInput
                    label={"Physical Address"}
                    type={InputType.text}
                    value={value.physicalAddress.value}
                    doValidate={true}
                    errorMsg={value.physicalAddress.errorMessageOnValidation || undefined}
                    onChange={(val) => handleChange("physicalAddress", val)}
                />
            </Wrapper>
            <Wrapper key="city">
                <TextInput
                    label={"City"}
                    type={InputType.text}
                    value={value.city.value}
                    doValidate={true}
                    errorMsg={value.city.errorMessageOnValidation || undefined}
                    onChange={(val) => handleChange("city", val)}
                />
            </Wrapper>
            <Wrapper key="state">
                <TextInput
                    label={"State"}
                    type={InputType.text}
                    value={value.state.value}
                    doValidate={true}
                    errorMsg={value.state.errorMessageOnValidation || undefined}
                    onChange={(val) => handleChange("state", val)}
                />
            </Wrapper>
            <Wrapper key="zip">
                <TextInput
                    label={"Zip"}
                    type={InputType.text}
                    value={value.zip.value}
                    doValidate={true}
                    errorMsg={value.zip.errorMessageOnValidation || undefined}
                    onChange={(val) => handleChange("zip", val)}
                />
            </Wrapper>
            <Wrapper key="country">
                <TextInput
                    label={"country"}
                    type={InputType.text}
                    value={value.country.value}
                    doValidate={true}
                    errorMsg={value.country.errorMessageOnValidation || undefined}
                    onChange={(val) => handleChange("country", val)}
                />
            </Wrapper>
        </React.Fragment>
    }
}

export default SaiPennyDropEnablement;
