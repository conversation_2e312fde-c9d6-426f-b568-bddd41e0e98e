import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Button from './Button';
import Dropdown from './Dropdown';
import EachFieldsRow from './EachFieldsRow';
import ExclusionProp from './ExclusonProp';
import Switch from './Switch';
import Textbox from './Textbox';
import { THROTTLE_PERCENT } from '../Constants';

const convertParentToChild = (flag) => {
  return {...flag.allEnv, name: flag.name};
}

const AddNewName = ({ handleAddName, onCancel, addText, placeholder }) => {
  const [ newName, setNewName ] = useState('');

  const onAddFlag = useCallback(() => {
    newName
      && handleAddName
      && handleAddName(newName)
  }, [ newName, handleAddName ])

  return <div className='flex flex-row align-center mt-16'>
    <Textbox inputType='text' value={newName} onChange={setNewName} placeholder={placeholder} />
    <div className='ml-16'>
      <Button text={addText} type='link' onClick={onAddFlag} />
    </div>
    <div className='ml-16'>
      <Button text='Cancel' onClick={onCancel} />
    </div>
  </div>
}

const AddExistingFlag = ({ list, handleAddExistingFlag, onCancel }) => {
  const [flagName, setFlagName] = useState('');

  useEffect(() => {
    list && list.length > 0 && setFlagName(list[0].name);
  }, [ list ])

  const properties = useMemo(() => {
    return list.map(flag => ({
      label : flag.name,
      value: flag.name
    }))
  }, [ list ])

  const onAddFlag = useCallback(() => {
    flagName
      && handleAddExistingFlag
      && handleAddExistingFlag(flagName)
  }, [ flagName, handleAddExistingFlag ])

  return <div className='flex flex-row align-center mt-16'>
    <Dropdown value={flagName} options={properties} onChange={setFlagName} />
    <div className='ml-16'>
      <Button text='+ Add Flag' type='link' onClick={onAddFlag} />
    </div>
    <div className='ml-16'>
      <Button text='Cancel' onClick={onCancel} />
    </div>
  </div>
}

const FlagEditor = ({
  id,
  childFlagId,
  value,
  name,
  parentName,
  onChange,
  onCancel,
  parentFlagsWithoutChild,
  flagList,
  onUnlink
}) => {
  const [ currentFlag, setCurrentFlag ] = useState(null);
  const [ flagName, setFlagName ] = useState('');
  const [ parentFlags, setParentFlags ] = useState([]);
  const [ removedParentFlags, setRemovedParentFlags ] = useState([]);
  const [ showAddNewFlag, setShowAddNewFlag ] = useState(false);
  const [ showAddExitingFlag, setShowAddExitingFlag ] = useState(false);
  const [ showAddConfigName, setShowAddConfigName ] = useState(false);

  useEffect(() => {
    parentFlagsWithoutChild
      && setParentFlags(parentFlagsWithoutChild.filter(flg => flg.name !== name));
  }, [ name, parentFlagsWithoutChild ])

  useEffect(() => {
    setCurrentFlag(value || {
      active : 'false'
    });
  }, [ value ]);

  useEffect(() => {
    setFlagName(name);
  }, [ name ])

  const handleCancel = useCallback(() => {
    onCancel && onCancel();
  }, [ onCancel ]);

  const handleActiveStatusChange = useCallback((val) => {
    setCurrentFlag(flag => ({...flag, active: val.toString()}));
  }, [])

  const handleAddDefaultConfig = useCallback(() => {
    setShowAddConfigName(true);
  }, [] )

  const handleAddConfigName = useCallback((name) => {
    setCurrentFlag(flag => {
      const defaultConfigs = flag.defaultConfigs?.configs || [];
      return {...flag, defaultConfigs : {
        configs: [...defaultConfigs, {
          name,
          value: name === THROTTLE_PERCENT ? 0 : '' 
        }]
      }};
    })
    setShowAddConfigName(false);
  }, [])

  const onConfigAddCancel = useCallback(() => {
    setShowAddConfigName(false);
  }, [])

  const onChildFlagAddCancel = useCallback(() => {
    setShowAddNewFlag(false);
    setShowAddExitingFlag(false);
  }, [])

  const handleAddExistingFlag = useCallback((name) => {
    const addedFlag = parentFlags.find(flg => flg.name === name);
    setParentFlags(flags => flags.filter(flg => flg.name !== name));
    setRemovedParentFlags(flags => [...flags, name])
    setCurrentFlag(flag => {
      const childrenFlags = flag.childrenFlags || [];
      return {...flag, childrenFlags : [...childrenFlags, convertParentToChild(addedFlag)]}
    })
    onChildFlagAddCancel();
  }, [ parentFlags, onChildFlagAddCancel ])

  const defaultConfigs = useMemo(() => {
    return currentFlag?.defaultConfigs?.configs || [];
  }, [ currentFlag ]);

  const exlusionProps = useMemo(() => {
    return currentFlag?.exclusionConfigs?.properties;
  }, [ currentFlag ])

  const inclusionProps = useMemo(() => {
    return currentFlag?.inclusionConfigs?.properties;
  }, [ currentFlag ])

  const childFlags = useMemo(() => {
    return currentFlag?.childrenFlags || [];
  }, [ currentFlag ])

  const isChildFlag = useMemo(() => childFlagId > -1, [ childFlagId ]);

  const isValidChildFlag = useCallback((newFlag) => {
    if(!newFlag) {
      alert("Flag name cannot be empty");
      return false;
    }
    if(newFlag === flagName) {
      alert("Child flag cannot be the same as parent flag!");
      return false;
    }
    if(flagList.find(name => name === newFlag)) {
      alert("Flag name already exists! Please change!");
      return false;
    }
    if(currentFlag.childrenFlags?.find(cf => cf.name === newFlag)) {
      alert("Flag name already exists! Please change!");
      return false;
    }
    return true;
  }, [ flagName, currentFlag, flagList])

  const handleConfigChange = useCallback((name) => (value) => {
    setCurrentFlag(flag => ({...flag, defaultConfigs : {
      configs: flag.defaultConfigs.configs.map(val => val.name === name
        ? {
          name,
          value
        } : val)
    }}))
  }, [])

  const handleAddExlusionProp = useCallback(() => {
    setCurrentFlag(flag => {
      const exclusionProps = flag.exclusionConfigs?.properties || [];
      return {...flag, exclusionConfigs : {
        properties : [...exclusionProps, {
          exclusionAttribute: 'accountId',
          value: []
        }]
      }}
    })
  }, [])

  const handlAddInclusionProp = useCallback(() => {
    setCurrentFlag(flag => {
      const inclusionProps = flag.inclusionConfigs?.properties || [];
      return {...flag, inclusionConfigs : {
        properties : [...inclusionProps, {
          inclusionAttribute: 'accountId',
          value: [],
          configs: []
        }]
      }}
    })
  }, [])

  const handleExclusionChange = useCallback((index, exclusionAttribute, value) => {
    setCurrentFlag(flag => ({...flag, exclusionConfigs : {
      properties : flag.exclusionConfigs.properties.map((val, idx) => idx === index
        ? {
          exclusionAttribute,
          value
        } : val)
    }}))
  }, []);

  const handleInclusionChange = useCallback((index, inclusionAttribute, value, configs) => {
    setCurrentFlag(flag => ({...flag, inclusionConfigs : {
      properties : flag.inclusionConfigs.properties.map((val, idx) => idx === index
        ? {
          inclusionAttribute,
          value,
          configs
        } : val)
    }}))
  }, []);

  const handleAddChildFlag = useCallback((newFlag) => {
    if(isValidChildFlag(newFlag)) {
      setCurrentFlag(flag => {
        const childrenFlags = flag.childrenFlags || [];
        return {...flag, childrenFlags : [...childrenFlags, {
          name : newFlag,
          active: 'false'
        }]}
      })
      onChildFlagAddCancel();
    }
  }, [ isValidChildFlag, onChildFlagAddCancel ])

  const handleChildFlagActiveStatus = useCallback((idx) => (val) => {
    setCurrentFlag(flag => {
      const childrenFlags = [...flag.childrenFlags];
      childrenFlags[idx].active = val.toString();
      return {...flag, childrenFlags};
    })
  }, []);

  const checkProps = useCallback((arr, prop) => {
    for(let i = 0; i < prop.length; i++) {
      const value = prop[i].value;
      for(let j = 0; j < value.length; j++) {
        if(arr.includes(value[j])) {
          return value[j];
        } else {
          arr.push(value[j])
        }
      }
    }
    return 0;
  }, [])

  const findDuplicates = useCallback((flag) => {
    const entitiesCollection = [];
    const exclusionProps = flag?.exclusionConfigs?.properties || [];
    const exclusionMatch = checkProps(entitiesCollection, exclusionProps);
    if(exclusionMatch) {
      return exclusionMatch;
    }
    const inclusionProps = flag?.inclusionConfigs?.properties || [];
    const inclusionMatch = checkProps(entitiesCollection, inclusionProps);
    if(inclusionMatch) {
      return inclusionMatch;
    }
    return -1;
  }, [ checkProps ])

  const isValidFlag = useCallback((duplicateVal) => {
    if(!flagName) {
      alert("Flag name cannot be empty");
      return false;
    }
    if(name !== flagName && flagList.find(name => name === flagName)) {
      alert("Flag name already exists! Please change!");
      return false;
    }
    const configs = currentFlag?.defaultConfigs?.configs;
    if(configs) {
      for(let i = 0; i < configs.length; i++) {
        const conf = configs[i];
        if(conf.name !== THROTTLE_PERCENT && !conf.value) {
          alert(`Value of ${conf.name} config cannot be empty`);
          return false;
        }
      }
    }
    if(duplicateVal !== -1) {
      alert(`You have ${duplicateVal} entity, configured at multiple places! Please fix it!`)
      return false;
    }
    if(currentFlag?.childrenFlags?.find(cf => cf.name === flagName)) {
      alert('Child flag with the same name exists already!');
      return false;
    }
    return true;
  }, [ flagList, name, flagName, currentFlag ])

  const sanitizeFlag = useCallback((flag) => {
    let sanitizedFlag = {...flag}
    if(sanitizedFlag?.exclusionConfigs?.properties) {
      sanitizedFlag = {...sanitizedFlag, exclusionConfigs : {
        properties : sanitizedFlag.exclusionConfigs.properties.map(val => ({
          exclusionAttribute : val.exclusionAttribute,
          value: val.value.filter(entity => entity && entity.trim())
        })).filter(val => val.value.length > 0)
      }}
    }
    if(sanitizedFlag?.inclusionConfigs?.properties) {
      sanitizedFlag = {...sanitizedFlag, inclusionConfigs : {
        properties : sanitizedFlag.inclusionConfigs.properties.map(val => ({
          inclusionAttribute : val.inclusionAttribute,
          value: val.value.filter(entity => entity && entity.trim()),
          configs: val.configs
        })).filter(val => val.value.length > 0)
      }}
    }
    return sanitizedFlag
  }, [])

  const handleRemoveFromParent = useCallback(() => {
    onUnlink && onUnlink(id, childFlagId);
    onCancel && onCancel();
  }, [ id, childFlagId, onUnlink, onCancel ])

  const handleSave = useCallback(() => {
    const sanitizedValue = sanitizeFlag(currentFlag)
    const duplicate = findDuplicates(currentFlag);
    if(isValidFlag(duplicate)) {
      onChange && onChange(id, childFlagId, flagName, sanitizedValue, removedParentFlags);
      onCancel && onCancel();
    }
  }, [
    isValidFlag,
    sanitizeFlag,
    findDuplicates,
    childFlagId,
    flagName,
    onCancel,
    currentFlag,
    onChange,
    id,
    removedParentFlags
  ])

  const canShowAddThrottle = useMemo(() => {
    return !defaultConfigs
      || defaultConfigs.length === 0
      || !defaultConfigs.find(conf => conf.name === THROTTLE_PERCENT)
  }, [ defaultConfigs ]);

  const canShowChildFlagOptions = useMemo(() => !showAddExitingFlag && !showAddNewFlag, [ showAddExitingFlag, showAddNewFlag ]);

  const addExistingFlagDisabled = useMemo(() => !(parentFlags?.length > 0), [ parentFlags ]);

  return <div className='flex flex-col flag-editor popup-container'>
    {isChildFlag && 
      <EachFieldsRow label='Parent Flag' children={parentName} />
    }
    <EachFieldsRow label='Flag Name' children={
      <Textbox value={flagName} onChange={setFlagName} placeholder='Flag Name' />
    } />
    <EachFieldsRow label='Active' children={
      <Switch
        id={`switch_${flagName}`}
        isChecked={currentFlag?.active === 'true'}
        onChange={handleActiveStatusChange} />
    } />
    {!isChildFlag &&
      <div className='flex flex-col'>
        <h3>Child Flags</h3>
        <table className='flags-list w-100'>
          <tbody>
            {childFlags.map((cflag, idx) => 
              <tr key={idx}>
                <td>{cflag.name}</td>
                <td className='text-center'>
                  <Switch
                    id={`switch_cf_${cflag.name}`}
                    isChecked={cflag.active === 'true'}
                    disabled={currentFlag?.active !== 'true'}
                    onChange={handleChildFlagActiveStatus(idx)} />
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {showAddNewFlag && <AddNewName
          addText='+ Add'
          placeholder='Child Flag Name'
          handleAddName={handleAddChildFlag}
          onCancel={onChildFlagAddCancel} />}
        {showAddExitingFlag && <AddExistingFlag list={parentFlags} handleAddExistingFlag={handleAddExistingFlag} onCancel={onChildFlagAddCancel} />}
        {canShowChildFlagOptions &&
          <div className='flex flex-row align-center mt-16'>
            <Button text='+ Add New Flag' type='link' onClick={() => setShowAddNewFlag(true)} />
            <div className='ml-16'>
              <Button
                text='+ Add Existing Flag'
                type='link'
                disabled={addExistingFlagDisabled}
                onClick={() => setShowAddExitingFlag(true)} />
            </div>
          </div>
        }
      </div>
    }
    <div className='flex flex-col align-left mt-32'>
      <h3>Default Config</h3>
      {defaultConfigs && defaultConfigs.map((val, idx) => 
        <EachFieldsRow key={idx} label={val.name} children={
          <Textbox inputType={val.name === THROTTLE_PERCENT ? 'number': 'text'} value={val.value} onChange={handleConfigChange(val.name)} placeholder='Config Value' />
        } />
      )}
      {showAddConfigName && <AddNewName
        addText='+ Add'
        placeholder='Config name'
        handleAddName={handleAddConfigName}
        onCancel={onConfigAddCancel}
        />
      }
      {!showAddConfigName && <div className='flex flex-row align-left mt-32'>
        <Button
          text='+ Add throttle'
          type='link'
          disabled={!canShowAddThrottle}
          onClick={() => handleAddConfigName(THROTTLE_PERCENT)} />
        <div className='ml-16'>
          <Button
            text='+ Add Config'
            type='link'
            onClick={handleAddDefaultConfig} />
        </div>
      </div>}
    </div>
    <div className='flex flex-row align-center mt-32'>
      <h3>Exclusion</h3>
      <div className='ml-16'>
        <Button text='+ Add Property' type='link' onClick={handleAddExlusionProp} />
      </div>
    </div>
    {exlusionProps && exlusionProps.map((val, idx) => 
      <ExclusionProp
        key={idx}
        id={idx}
        propName={val.exclusionAttribute}
        value={val.value}
        onChange={handleExclusionChange} />
    )}
    <div className='flex flex-row align-center mt-32'>
      <h3>Inclusion</h3>
      <div className='ml-16'>
        <Button text='+ Add Property' type='link' onClick={handlAddInclusionProp} />
      </div>
    </div>
    {inclusionProps && inclusionProps.map((val, idx) => 
      <ExclusionProp
        key={idx}
        id={idx}
        propName={val.inclusionAttribute}
        value={val.value}
        configs={val.configs}
        defaultConfigs={defaultConfigs}
        showAddConfig
        onChange={handleInclusionChange} />
    )}
    <div className='mt-32 flex flex-row w-100 justify-center'>
      <Button text='Set' type='primary' onClick={handleSave}/>
      {isChildFlag && <div className='ml-32'>
        <Button text='Remove from Parent' type='primary' onClick={handleRemoveFromParent} />
      </div>}
      <div className='ml-32'>
        <Button text='Cancel' type='secondary' onClick={handleCancel} />
      </div>
    </div>
  </div>
}

export default FlagEditor;