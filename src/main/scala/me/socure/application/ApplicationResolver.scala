package me.socure.application

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.regions.Regions
import com.amazonaws.services.elasticbeanstalk.model._
import com.amazonaws.services.elasticbeanstalk.{AWSElasticBeanstalk, AWSElasticBeanstalkClientBuilder}
import com.amazonaws.services.s3.AmazonS3Client
import com.typesafe.config.{Config, ConfigFactory}
import me.socure.common.aws.ProfileBasedCredentialsProviderChain
import me.socure.common.s3.bucket.BucketName
import me.socure.common.s3.client.AmazonS3ClientFactory
import me.socure.common.s3.files.S3Files
import me.socure.dynamic.control.center.client.DynamicControlCenterServiceClient
import me.socure.dynamic.control.center.v2.factory.DynamicControlCenterV2Factory
import me.socure.dynamic.control.center.v2.response.LookupResponse
import me.socure.model.ErrorResponse
import me.socure.util.EnvUtil
import org.json4s.DefaultFormats
import org.json4s.jackson.JsonMethods.parse
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component

import java.io.IOException
import java.nio.file.{Path, Paths}
import java.util.concurrent.TimeUnit
import javax.annotation.PostConstruct
import scala.collection.JavaConverters._
import scala.concurrent.Await
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration

@Component
class ApplicationResolver {
  private val logger = LoggerFactory.getLogger(getClass)

  @Autowired
  @Qualifier("envUtil")
  var envUtil: EnvUtil = _

  // Temporarily disabled due to network connectivity issues
  // @Autowired(required = false)
  // @Qualifier("dynamicControlCenterServiceClient")
  // var dynamicControlCenterServiceClient: DynamicControlCenterServiceClient = _

  private var controlCenterConfig: Config = _

  def fetchList(): Either[ErrorResponse, ApplicationListResponseMapper] = {
    try{
      val throttledServices = throttledServicesJsonStrToMap(getS3Content(Paths.get("throttled_services.json")))
        .map(service => ApplicationInfo(service.serviceName, ServiceTypes.THROTTLE_SERVICE.name))
      val workerServices = nonThrottledServicesJsonStrToMap(getS3Content(Paths.get("worker_services.json")))
        .map(service => ApplicationInfo(service.serviceName, ServiceTypes.WORKER_SERVICE.name))
      val publicServices = nonThrottledServicesJsonStrToMap(getS3Content(Paths.get("public_services.json")))
        .map(service => ApplicationInfo(service.serviceName, ServiceTypes.PUBLIC_SERVICE.name))

      Right(new ApplicationListResponseMapper((throttledServices ++ workerServices ++ publicServices).asJava))
    } catch {
      case t:Throwable =>
        logger.info("Error while fetching applications list ", t)
        Left(ErrorResponse(900, t.getMessage))
    }
  }

  def fetchStatus(application: String): Either[ErrorResponse, ApplicationStatusResponseMapper] = {
    try{
      val throttledServices = throttledServicesJsonStrToMap(getS3Content(Paths.get("throttled_services.json")))
      val workerServices = nonThrottledServicesJsonStrToMap(getS3Content(Paths.get("worker_services.json")))
      val publicServices = nonThrottledServicesJsonStrToMap(getS3Content(Paths.get("public_services.json")))
      val serviceSeq = application.split(",")

      val throttledSeq = throttledServices.filter(s => serviceSeq.contains(s.serviceName))
      val workerSeq = workerServices.filter(s => serviceSeq.contains(s.serviceName))
      val publicSeq = publicServices.filter(s => serviceSeq.contains(s.serviceName))

      val elasticBeanstalkClientBuilder = AWSElasticBeanstalkClientBuilder
        .standard()
        .withCredentials(new ProfileBasedCredentialsProviderChain)
        .withRegion(Regions.US_EAST_1)
      val elasticBeanstalkClient = elasticBeanstalkClientBuilder.build()

      val throttledServicesResponse = getThrottledServiceResult(throttledSeq, elasticBeanstalkClient)
      val workerServicesResponse = getWorkerServiceResult(workerSeq, elasticBeanstalkClient)
      val publicServicesResponse = getPublicServiceResult(publicSeq, elasticBeanstalkClient)
      Right(new ApplicationStatusResponseMapper((throttledServicesResponse ++ workerServicesResponse ++ publicServicesResponse).asJava))
    } catch {
      case t:Throwable =>
        logger.info("Error while fetching applications status ", t)
        Left(ErrorResponse(900, t.getMessage))
    }
  }

  private def getThrottledServiceResult(services:  Seq[ThrottledServices], elasticBeanstalkClient: AWSElasticBeanstalk): Seq[ApplicationInfoWithEnvironmentMapper] = {

    services.map(service => {
      val throttle = getThrottle(service)
      val describeEnvironmentsRequest = new DescribeEnvironmentsRequest().withApplicationName(service.serviceName)
      val resp = elasticBeanstalkClient.describeEnvironments(describeEnvironmentsRequest)
      val environments = resp.getEnvironments.asScala
      val canDeploy1 = canDeployThrottledService(service.endpoint1, environments, 100 - throttle, elasticBeanstalkClient)
      val canDeploy2 = canDeployThrottledService(service.endpoint2, environments, throttle, elasticBeanstalkClient)
      new ApplicationInfoWithEnvironmentMapper(service.serviceName, ServiceTypes.THROTTLE_SERVICE.name, Seq(ApplicationEnvironment(service.endpoint1, canDeploy1), ApplicationEnvironment(service.endpoint2, canDeploy2)).asJava)
    })
  }

  private def getThrottle(service: ThrottledServices): Int = {
    val dynamicControlCenterV2Evaluate = DynamicControlCenterV2Factory.getEvaluator(controlCenterConfig)
    val response = dynamicControlCenterV2Evaluate.lookup(service.groupName, service.flagName, "throttlePercent", None)
    val result = Await.result(response, Duration.Inf)
    result match {
      case Right(res: LookupResponse) => res.lookupValue.toInt
      case _ => 0
    }
  }

  private def canDeployThrottledService(endpoint: String, environments: Seq[EnvironmentDescription], traffic: Int, elasticBeanstalkClient: AWSElasticBeanstalk): Boolean = {
    environments.find(_.getCNAME == endpoint) match {
      // can deploy if traffic is 0 and live traffic tag is false
      case Some(environment) => traffic == 0 && !isTagPresent(environment, elasticBeanstalkClient)
      case None =>
        logger.error(s"No environment found for $endpoint")
        false
    }
  }

  private def getWorkerServiceResult(services:  Seq[NonThrottledServices], elasticBeanstalkClient: AWSElasticBeanstalk): Seq[ApplicationInfoWithEnvironmentMapper] = {
    services.map(service => {
      val describeEnvironmentsRequest = new DescribeEnvironmentsRequest().withApplicationName(service.serviceName)
      val environments = elasticBeanstalkClient.describeEnvironments(describeEnvironmentsRequest).getEnvironments.asScala
      logger.info(s"environments $environments")
      val canDeploy1 = canDeployWorkerService(service, environments, service.endpoint1, elasticBeanstalkClient)
      val canDeploy2 = canDeployWorkerService(service, environments, service.endpoint2, elasticBeanstalkClient)
      new ApplicationInfoWithEnvironmentMapper(service.serviceName, ServiceTypes.WORKER_SERVICE.name, Seq(ApplicationEnvironment(service.endpoint1, canDeploy1), ApplicationEnvironment(service.endpoint2, canDeploy2)).asJava)
    })
  }

  private def canDeployWorkerService(service: NonThrottledServices, environments: Seq[EnvironmentDescription], endpoint: String, elasticBeanstalkClient: AWSElasticBeanstalk): Boolean = {
    val environmentOpt =  environments.find(_.getCNAME == endpoint)
    environmentOpt match {
      case Some(env) =>
        val isNodeAvailableOpt = isNodeAvailable(elasticBeanstalkClient, service.serviceName, env.getEnvironmentName)
        isNodeAvailableOpt match {
          // can deploy true if is live traffic tag is false and configured node count = 0
          // or is live traffic tag is true and configured node count > 0
          case Some(node) =>
            val isLivePresent = isTagPresent(env, elasticBeanstalkClient)
            (!node && !isLivePresent) || (node && isLivePresent)
          case None => false
        }
      case None =>
        logger.error(s"No environment found for $endpoint")
        false
    }
  }

  private def getPublicServiceResult(services:  Seq[NonThrottledServices], elasticBeanstalkClient: AWSElasticBeanstalk): Seq[ApplicationInfoWithEnvironmentMapper] = {
    services.map(service => {
      val describeEnvironmentsRequest = new DescribeEnvironmentsRequest().withApplicationName(service.serviceName)
      val environments = elasticBeanstalkClient.describeEnvironments(describeEnvironmentsRequest).getEnvironments.asScala
      val canDeploy1 = canDeployPublicService(service.endpoint1, environments, elasticBeanstalkClient)
      val canDeploy2 = canDeployPublicService(service.endpoint2, environments, elasticBeanstalkClient)
      new ApplicationInfoWithEnvironmentMapper(service.serviceName, "public", Seq(ApplicationEnvironment(service.endpoint1, canDeploy1), ApplicationEnvironment(service.endpoint2, canDeploy2)).asJava)
    })
  }

  private def canDeployPublicService(endpoint: String, environments: Seq[EnvironmentDescription], elasticBeanstalkClient: AWSElasticBeanstalk): Boolean = {
    val environmentOpt =  environments.find(_.getCNAME == endpoint)
    environmentOpt match {
      case Some(env) => isTagPresent(env, elasticBeanstalkClient)
      case None =>
        logger.error(s"No environment found for $endpoint")
        false
    }
  }

  private def isNodeAvailable(elasticBeanstalkClient: AWSElasticBeanstalk, applicationName: String, environmentName: String): Option[Boolean] = {
    logger.info(s"isNodeAvailable $applicationName $environmentName")
    val describeConfigurationSettingsRequest = new DescribeConfigurationSettingsRequest().withApplicationName(applicationName).withEnvironmentName(environmentName)
    val result: DescribeConfigurationSettingsResult = elasticBeanstalkClient.describeConfigurationSettings(describeConfigurationSettingsRequest)
    logger.info(s"configuration settings $result")
    result.getConfigurationSettings.asScala.headOption match {
      case Some(configurationSettings) =>
        val minNodeOpt = configurationSettings.getOptionSettings.asScala.find(settings => settings.getResourceName == "AWSEBAutoScalingGroup" && settings.getNamespace == "aws:autoscaling:asg" && settings.getOptionName == "MinSize")
        val maxNodeOpt = configurationSettings.getOptionSettings.asScala.find(settings => settings.getResourceName == "AWSEBAutoScalingGroup" && settings.getNamespace == "aws:autoscaling:asg" && settings.getOptionName == "MaxSize")
        (minNodeOpt, maxNodeOpt) match {
          case (Some(minNode), Some(maxNode)) => Some(minNode.getValue.toInt > 0 && maxNode.getValue.toInt > 0)
          case _ =>
            logger.error(s"Unable to get Node count for $environmentName")
            None
        }
      case None =>
        logger.error(s"Unable to get Node count for $environmentName")
        None
    }
  }

  private def isTagPresent(environment: EnvironmentDescription, elasticBeanstalkClient: AWSElasticBeanstalk): Boolean = {
    val resp = elasticBeanstalkClient.listTagsForResource(new ListTagsForResourceRequest().withResourceArn(environment.getEnvironmentArn))
    val tagOpt = resp.getResourceTags.asScala.find(_.getKey == "isLiveTraffic")
    tagOpt match {
      case Some(tag) => tag.getValue.toBoolean
      case None => false
    }
  }

  private def getS3Content(path: Path) : String  = {
    val client: AmazonS3Client = AmazonS3ClientFactory.get(DefaultAWSCredentialsProviderChain.getInstance())
    val bucketName: String = controlCenterConfig.getString("dynamic.control.center.s3.bucketName")
    val s3Client = new S3Files(client, BucketName(bucketName))
    val inputStream = s3Client.loadAsStream(path)
    try {
      scala.io.Source.fromInputStream(inputStream).mkString
    } catch {
      case e: IOException =>
        logger.error(s"Error in parsing json $e")
        throw new Exception(s"Error in parsing json $e")
    }
  }

  private def throttledServicesJsonStrToMap(jsonStr: String) : Seq[ThrottledServices] = {
    implicit val formats : DefaultFormats.type = org.json4s.DefaultFormats
    parse(jsonStr).extract[Seq[ThrottledServices]]
  }

  private def nonThrottledServicesJsonStrToMap(jsonStr: String) : Seq[NonThrottledServices] = {
    implicit val formats : DefaultFormats.type = org.json4s.DefaultFormats
    parse(jsonStr).extract[Seq[NonThrottledServices]]
  }

  @PostConstruct
  def initializeControlCenterConfig(): Unit = {
    controlCenterConfig  = ConfigFactory.parseProperties(envUtil.getPropertiesPlain("dynamic.control.center"))
  }

  def getUXFeatureFlag(flgName: String, grpName: String): Boolean = {
    val dynamicControlCenterV2Evaluate = DynamicControlCenterV2Factory.getEvaluator(controlCenterConfig)
    val evalResponse = dynamicControlCenterV2Evaluate.evaluate(groupName = grpName, flagName = flgName)
    Await.result(evalResponse,  Duration.create(10, TimeUnit.SECONDS)) match {
      case Right(evalResp) =>
        logger.info(s"Evaluating cc flag for Old Provisioning - groupName=$grpName, flagName=$flgName, Result=$evalResp")
        evalResp.isFlagActive
      case Left(err) =>
        logger.info(s"Evaluating cc flag for Old Provisioning - Error occured: ${err.message}")
        false
    }
  }
}
