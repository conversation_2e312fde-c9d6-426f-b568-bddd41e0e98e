package me.socure.superadmin.api.v2.kyc.recordcorrection;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.typesafe.config.Config;
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate;
import me.socure.kyc.search.unique.id.EntityIdGenerator;
import me.socure.superadmin.api.v2.common.GenericApiController;
import me.socure.superadmin.api.v2.kyc.recordcorrection.model.ListClusterIdRequest;
import me.socure.superadmin.api.v2.kyc.recordcorrection.model.ListClusterIdTypes;
import me.socure.superadmin.authentication.SimpleAuthenticatedToken;
import me.socure.thirdparty.reader.service.ThirdPartyReaderServiceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v2/kyc/record_correction")
public class RecordCorrectionController extends GenericApiController {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RecordCorrectionHelper recordCorrectionHelper;

    @Autowired
    private DynamicControlCenterV2Evaluate dynamicControlCenterV2Evaluate;

    @Autowired
    private Config config;

    private final EntityIdGenerator entityIdGenerator = new EntityIdGenerator(793664750);

    @PostMapping("/configuration")
    @ResponseBody
    public Map<String, List<JsonNode>> getConfiguration() throws Exception {
        final String URL = config.getString("kyc.endpoint") + "/details/get-active-open-search-clusters";
        final ResponseEntity<String> kycOpenSearchConfigResponseEntity = callRestApi(URL, HttpMethod.POST, "", new HttpHeaders());
        if(kycOpenSearchConfigResponseEntity.getStatusCode().value() != 200)  {
            throw new RuntimeException("KYC default open search configuration fetch failed.");
        }
        final JsonNode kycOpenSearchConfig = objectMapper.readTree(kycOpenSearchConfigResponseEntity.getBody());
        final JsonNode defaultEnformionConfig = kycOpenSearchConfig.get("enformion");
        final JsonNode defaultEquifaxConfig = kycOpenSearchConfig.get("equifax");
        final Map<String, List<JsonNode>> result = new HashMap<String, List<JsonNode>>();
        result.put("equifax", Collections.singletonList(defaultEquifaxConfig));
        result.put("enformion", Collections.singletonList(defaultEnformionConfig));
        return result;
    }

    @PostMapping("/get-data")
    @ResponseBody
    public ResponseEntity<String> getData(@RequestBody final String body) {
        final String URL = config.getString("kyc.recordcorrection.endpoint") + "/get-record";
        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(HttpHeaders.CONTENT_TYPE, "application/json");
        return callRestApi(URL, HttpMethod.POST, body, httpHeaders);
    }

    @PostMapping("/list-cluster-id")
    @ResponseBody
    public ResponseEntity<String> listClusterId(@Validated @RequestBody final ListClusterIdRequest request) {
        final int type = request.getType();
        String ID = request.getId();
        if(type == ListClusterIdTypes.TRANSACTION_ID.getValue()) {
            ID = recordCorrectionHelper.getSocureIdFromTransactionId(ID);
        } else if(type == ListClusterIdTypes.ACCOUNT_SPECIFIC_SOCURE_ID.getValue()) {
            final Long accountID = request.getAccountId();
            if(accountID == null ) {
                throw new RuntimeException("Account ID can't be null when requesting type is ACCOUNT_SPECIFIC_SOCURE_ID");
            }
            ID = entityIdGenerator.decode(accountID, ID);
        }
        final String body = String.format("{\"socureID\":\"%s\"}", ID);
        final String URL = config.getString("kyc.recordcorrection.endpoint") + "/get-cluster-ids-for-socure-id";
        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(HttpHeaders.CONTENT_TYPE, "application/json");
        return callRestApi(URL, HttpMethod.POST, body, httpHeaders);
    }

    @PostMapping("/update-data")
    @ResponseBody
    public ResponseEntity<String> updateData(@RequestBody final ObjectNode body) {
        final String username = getAuthenticatedUserName();
        body.set("updatedBy", TextNode.valueOf(username));
        final String URL = config.getString("kyc.recordcorrection.endpoint") + "/update-record";
        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(HttpHeaders.CONTENT_TYPE, "application/json");
        return callRestApi(URL, HttpMethod.POST, body.toString(), httpHeaders);
    }

    private String getAuthenticatedUserName() {
        final SimpleAuthenticatedToken authToken = (SimpleAuthenticatedToken) SecurityContextHolder.getContext().getAuthentication();
        return authToken.username();
    }

}
