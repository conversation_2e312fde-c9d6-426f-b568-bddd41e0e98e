import React from "react";
import SimpleRecordCorrectionController from "./SimpleRecordCorrectionController";
import SimpleRecordCorrectionView from "./SimpleRecordCorrectionView";

export interface ISimpleRecordCorrectionProps {}

const SimpleRecordCorrection: React.FC<ISimpleRecordCorrectionProps> = ({}: ISimpleRecordCorrectionProps) => {
    return <SimpleRecordCorrectionController>
        {(props) => <SimpleRecordCorrectionView {...props} />}
    </SimpleRecordCorrectionController>;
}

export default SimpleRecordCorrection;