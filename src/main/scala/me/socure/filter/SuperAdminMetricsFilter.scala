package me.socure.filter

import java.util.regex.Pattern

import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.servlet.metrics.ServletMetricsFilter
import org.springframework.web.context.support.SpringBeanAutowiringSupport
import javax.servlet.{Filter, Filter<PERSON>hain, FilterConfig, ServletRequest, ServletResponse}
import javax.servlet.http.HttpServletRequest

class SuperAdminMetricsFilter extends Filter{

  private val excludedPaths = List("/resources")
  val servletMetricsFilter = new ServletMetricsFilter(
    metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix),
    baseTags = MetricTags(serviceName = Some("socure-super-admin")),
    apiNameMapping =  Map(
        Pattern.compile("^/superadmin/account/details/1/migrate/\\d+$") -> "/superadmin/account/details/1/migrate",
        Pattern.compile("^/superadmin/account/details/1/associate/user/\\d+/account/\\d+/role/\\d+$") -> "/superadmin/account/details/1/associate/user/account/role",
        Pattern.compile("^/superadmin/account/details/1/update/account/\\d+/accountType/\\d+$") -> "/superadmin/account/details/1/update/account/accountType",
        Pattern.compile("^/superadmin/account/details/1/update/account/\\d+/administer/\\S+$") -> "/superadmin/account/details/1/update/account/administer",
        Pattern.compile("^/superadmin/batchjobs/jobi-[a-zA-Z0-9]{20}/result") -> "/superadmin/batchjobs/result",
        Pattern.compile("^/superadmin/job_definitions/\\d+$") -> "/superadmin/job_definitions",
        Pattern.compile("^/activate/[a-zA-Z0-9-]{36}$") -> "/activate",
        Pattern.compile("^/superadmin/rulecode-service/v1/vendor/\\S+$") -> "/superadmin/rulecode-service/v1/vendor",
        Pattern.compile("^/superadmin/rulecode-service/v1/rulecodes/\\S+$") -> "/superadmin/rulecode-service/v1/rulecodes",
        Pattern.compile("^/superadmin/rulecode-service/v1/rulecodes_v2/\\S+$") -> "/superadmin/rulecode-service/v1/rulecodes_v2",
        Pattern.compile("^/superadmin/rulecode-service/v1/vendor/\\S+/prefix/\\S+/configs$") -> "/superadmin/rulecode-service/v1/vendor/prefix/configs",
        Pattern.compile("^/superadmin/rulecode-service/v1/vendor/\\S+/prefix/\\S+$") -> "/superadmin/rulecode-service/v1/vendor/prefix",
        Pattern.compile("^/superadmin/rulecode-service/v1/vendor/\\S+/prefix$") -> "/superadmin/rulecode-service/v1/vendor/prefix",
        Pattern.compile("^/superadmin/rulecode-service/v1/rulecodes/tests/\\S+/refresh$") -> "/superadmin/rulecode-service/v1/rulecodes/tests/refresh",
        Pattern.compile("^/superadmin/rulecode-service/v1/rulecodes/tests/\\S+/result$") -> "/superadmin/rulecode-service/v1/rulecodes/tests/result",
        Pattern.compile("^/superadmin/1/troubleshooting/event/\\S+$") -> "/superadmin/1/troubleshooting/event",
        Pattern.compile("^/superadmin/files/list/accountId/\\S+/uuid/[a-zA-Z0-9-]{36}$") -> "/superadmin/files/list/accountId/uuid",
        Pattern.compile("^/superadmin/files/download/accountid/\\d+/uuid/[a-zA-Z0-9-]{36}$") -> "/superadmin/files/download/accountId/uuid",
        Pattern.compile("^/superadmin/files/documents/metrics/accountId/\\S+/uuid/[a-zA-Z0-9-]{36}$") -> "/superadmin/files/documents/metrics/accountId/uuid",
        Pattern.compile("^/superadmin/stepUp/process/details/events/\\S+$") -> "/superadmin/stepUp/process/details/events",
        Pattern.compile("^/superadmin/1/account_details/consent_reason/\\d+$") -> "/superadmin/1/account_details/consent_reason",
        Pattern.compile("^/superadmin/1/account_details/consent_reason/\\d+/\\d+$") -> "/superadmin/1/account_details/consent_reason",
        Pattern.compile("^/superadmin/1/document_manager/types/\\S+$") -> "/superadmin/1/document_manager/types",
        Pattern.compile("^/superadmin/1/document_manager/types/provision/\\S+$") -> "/superadmin/1/document_manager/types/provision",
        Pattern.compile("^/superadmin/1/decision_logic/\\d+/revision$") -> "/superadmin/1/decision_logic/revision",
        Pattern.compile("^/superadmin/1/decision_logic/\\d+/revision/\\d+/mark-as-live$") -> "/superadmin/1/decision_logic/revision/mark-as-live",
        Pattern.compile("^/superadmin/1/decision_logic/\\d+/simulate/pasttransactions$") -> "/superadmin/1/decision_logic/simulate/pasttransactions",
        Pattern.compile("^/superadmin/1/docv/authenticId/fetch/associate/strategy/\\S+$") -> "/superadmin/1/docv/authenticId/fetch/associate/strategy",
        Pattern.compile("^/superadmin/1/docv/authenticId/fetch/strategy/\\S+$") -> "/superadmin/1/docv/authenticId/fetch/strategy",
        Pattern.compile("^/superadmin/sandbox/configuration/\\d+/mark-as-live$") -> "/superadmin/sandbox/configuration/mark-as-live",
        Pattern.compile("^/superadmin/sandbox/configuration/\\d+$") -> "/superadmin/sandbox/configuration"
      )
  )

  override def init(filterConfig: FilterConfig): Unit = {
    SpringBeanAutowiringSupport.processInjectionBasedOnServletContext(this,
      filterConfig.getServletContext())
  }

  override def destroy(): Unit = {

  }
  
  override def doFilter(servletRequest: ServletRequest, servletResponse: ServletResponse, filterChain: FilterChain): Unit = {

    val path = servletRequest.asInstanceOf[HttpServletRequest].getServletPath //returns url without context-path

    val isNotExcluded = excludedPaths.filter(path.startsWith(_)).isEmpty
    if (isNotExcluded) {
      servletMetricsFilter.doFilter(servletRequest, servletResponse, filterChain)
    }else {
      filterChain.doFilter(servletRequest, servletResponse)
    }
  }

}
