export interface Account {
  id: number;
  name: string;
  publicId: string;
}

export interface FraudModel {
  name: string;
  publicId: string;
  apiVersion: string;
  scoreName: string;
  version: string;
  associationType: number;
}

export interface AccountsAndModelsResponse {
  accounts: Account[];
  fraudmodels: FraudModel[];
}

export interface AccountAutoComplete {
  label: string;
  id: number;
}

export interface FraudModelAutoComplete {
  label: string;
  publicId: string;
}

export interface Model {
  model: {
    name: string;
    identifier: string;
    version: string;
    scoreName: string;
    normalized: string;
    environment: string;
    publicId: string;
    createdDate: number;
  };
  associationType: number;
  filterCriteria: string;
  rowId: string;
}

export interface MappingsForAccount {
  accountId: number;
  accountName: string;
  models: Model[];
}

export interface ModelInfo {
  id: number | null;
  publicId: string;
  name: string;
  commonName: string | null;
  scoreName: string;
  version: string;
  identifier: string;
  modelType: string;
  engine: string;
  params: string;
  apiVersion: string;
  featureName: string;
  featureLabel: string;
  config: string;
  isDefault: boolean;
  isActive: boolean;
  quantileMapping: string | null;
  description: string;
  updatedAt: string;
  modelFormat: string;
  implicit: boolean;
}

export interface FraudModelMappingRequest {
  userId: number;
  fraudModelId: string;
  associationType: number;
  environment: string;
  filterCriteria: string;
}
