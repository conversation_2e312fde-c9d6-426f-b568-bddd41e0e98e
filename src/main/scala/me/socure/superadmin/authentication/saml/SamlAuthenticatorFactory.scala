package me.socure.superadmin.authentication.saml

import java.util.Collections
import com.typesafe.config.{ConfigFactory, ConfigValueFactory}
import me.socure.common.environment.{AppNameResolver, ConfigurationVersionResolver, Environment, EnvironmentResolver}
import me.socure.common.path.PathUtils
import me.socure.common.resource.EnvironmentResourceLoaderFactory
import me.socure.common.s3.bucket.BucketNameResolver
import me.socure.common.saml.SamlClient
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.stereotype.Component

import java.io.{BufferedReader, ByteArrayInputStream, ByteArrayOutputStream, InputStream}
import java.nio.charset.StandardCharsets
import java.nio.file.{Path, Paths}
import scala.tools.jline_embedded.internal.InputStreamReader
import scala.concurrent.ExecutionContext
import org.slf4j.LoggerFactory

@Component
class SamlAuthenticatorFactory extends FactoryBean[SamlClient] {

  @Autowired
  private[saml] var ec: ExecutionContext = _

  @Autowired
  @Qualifier("envUtil")
  private[saml] var envUtil: EnvUtil = _

  private val logger = LoggerFactory.getLogger(classOf[SamlAuthenticatorFactory])

  override def getObject: SamlClient = {
    return null;
    val samlProperties = envUtil.getPropertiesPlain("saml")
    val config = ConfigFactory
      .parseProperties(samlProperties)
      .getConfig("saml")
      .withValue("requestedAttributes", ConfigValueFactory.fromIterable(Collections.emptyList()))
    val appName = AppNameResolver.resolve()
    val environment = EnvironmentResolver.resolve()
    val region = System.getenv("AWS_REGION")
    val path = s"/saml2_idp_metadata/$region"
    val resourceLoader = EnvironmentResourceLoaderFactory.create(environment, appName, Paths.get(path))
    val idpMetadataStream: InputStream = resourceLoader.load(Paths.get("metadata.xml"))
    val metadataReader = new BufferedReader(new InputStreamReader(idpMetadataStream, StandardCharsets.UTF_8))
    SamlClient.create(
      metadataReader,
      config
    )
  }


  override def getObjectType: Class[SamlClient] = classOf[SamlClient]

  override def isSingleton: Boolean = true
}