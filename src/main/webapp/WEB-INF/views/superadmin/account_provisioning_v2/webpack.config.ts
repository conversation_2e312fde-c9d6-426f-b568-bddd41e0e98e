import webpack from 'webpack';
import path from 'path';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import { Configuration as WebpackDevServerConfiguration } from 'webpack-dev-server';
const CopyPlugin = require('copy-webpack-plugin');

interface Configuration extends webpack.Configuration {
  devServer?: WebpackDevServerConfiguration;
}

const NODE_ENV = process.env.NODE_ENV;
const HOT_RELOAD = process.env.HOT_RELOAD;

const extractCSS = new MiniCssExtractPlugin();

const config: Configuration = {
  entry: [
    './src/index.tsx',
  ],
  output: {
    path: path.resolve(__dirname, 'public/dist'),
    filename: '[name].js',
  },
  devServer: {
    writeToDisk: true,
    hot: HOT_RELOAD !== 'false',
    inline: HOT_RELOAD !== 'false',
    contentBase: path.join(__dirname, 'public/dist'),
    compress: false,
    port: 9000,
    
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
  },
  module: {
    rules: [
      {
        test: /(\.jsx?)|(\.tsx?)$/,
        exclude: /node_modules\/(?!(bitbucket|ky|@socure)\/)/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              happyPackMode: true,
              transpileOnly: true,
            },
          },
        ],
      },
      {
        test: /\.s?css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              publicPath: './',
            },
          },
          {
            loader: 'css-loader',
            options: {
              sourceMap: true,
            },
          },
          {
            loader: 'sass-loader',
            options: {
              sourceMap: true,
              outputStyle: 'compressed',
            },
          },
        ],
      },
      {
        test: /\.svg$/,
        use: ['@svgr/webpack', 'url-loader'],
      },
      {
        test: /node_modules\/.*\.svg$/,
        loader: 'file-loader',
        options: {
          name: 'assets/[name].[ext]',
        },
      },
    ],
  },
  optimization: {
    runtimeChunk: false,
  },
  plugins: [
    new webpack.NormalModuleReplacementPlugin(/^lodash$/, 'lodash-es'),
    new HtmlWebpackPlugin({
      filename: './index.html',
      template: './public/index.html',
      production: NODE_ENV === 'production',
    }),
    new CopyPlugin(
      []
    ),
    extractCSS
  ],
  devtool: NODE_ENV === 'development' ? 'eval-source-map' : false,
  stats: 'minimal'
};

if (NODE_ENV === 'production') {
  config.output = config.output || {};
  config.optimization = config.optimization || {};
  config.output.filename = '[name]-bundle.min.js';
  config.optimization.concatenateModules = false;
  config.stats = 'normal';
}

export default config;
