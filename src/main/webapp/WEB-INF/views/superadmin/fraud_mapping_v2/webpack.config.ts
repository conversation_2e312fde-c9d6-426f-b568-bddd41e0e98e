import webpack from "webpack";
import path from "path";
import HtmlWebpackPlugin from "html-webpack-plugin";
import MiniCssExtractPlugin from "mini-css-extract-plugin";
import { Configuration as WebpackDevServerConfiguration } from "webpack-dev-server";
import CssMinimizerPlugin from "css-minimizer-webpack-plugin";
import TerserPlugin from "terser-webpack-plugin";

interface Configuration extends webpack.Configuration {
  devServer?: WebpackDevServerConfiguration;
}

const NODE_ENV = process.env.NODE_ENV;

const config: Configuration = {
  mode: NODE_ENV === "production" ? "production" : "development",
  entry: ["./src/index.tsx"],
  output: {
    path: path.resolve(__dirname, "public/dist"),
    filename: "[name].js",
  },
  devServer: {
    open: true,
    hot: true,
    static: path.join(__dirname, "public/dist"),
    compress: true,
    port: 9000,
    historyApiFallback: true,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
  },
  resolve: {
    extensions: [".ts", ".tsx", ".js", ".jsx"],
  },
  module: {
    rules: [
      {
        // Typescript loader
        test: /\.tsx?$/,
        exclude: /(node_modules\/(?!(bitbucket|ky|@socure)\/)|\.webpack)/,
        use: {
          loader: "ts-loader",
          options: {
            transpileOnly: true,
          },
        },
      },
      {
        test: /\.(scss|css)$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
          },
          {
            loader: "css-loader",
            options: { sourceMap: true },
          },
          {
            loader: "sass-loader",
            options: { sourceMap: true },
          },
        ],
      },
      {
        test: /\.svg$/i,
        issuer: /\.[jt]sx?$/,
        use: ["@svgr/webpack", "url-loader"],
      },
      {
        test: /node_modules\/.*\.svg$/,
        type: "asset",
        generator: {
          filename: "assets/[hash][ext][query]",
        },
      },
    ],
  },
  optimization: {
    moduleIds: "deterministic",
    runtimeChunk: "single",
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          chunks: "all",
        },
      },
    },
    minimize: true,
    minimizer: [
      new CssMinimizerPlugin(),
      NODE_ENV === "production" &&
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true,
            },
            mangle: true,
            sourceMap: false,
          },
        }),
    ],
  },
  plugins: [
    new HtmlWebpackPlugin({
      filename: "index.html",
      template: path.join(__dirname, "public/index.html"),
      inject: true,
    }),
    new MiniCssExtractPlugin(),
  ],
  devtool: NODE_ENV === "development" ? "source-map" : false,
  stats: "errors-warnings",
};

export default config;
