package me.socure.decision

import com.typesafe.config.{Config, ConfigFactory}
import me.socure.decision.service.client.DecisionServiceClient
import me.socure.decision.service.client.factory.DecisionServiceClientFactory
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}

import scala.concurrent.ExecutionContext

class DecisionServiceFactory extends FactoryBean[DecisionServiceClient]{
  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  override def getObject: DecisionServiceClient = {

    val config: Config  = ConfigFactory.parseProperties(envUtil.getPropertiesPlain("decision.service"))
    val client = DecisionServiceClientFactory.create(config)(executionContext)
    client
  }

  override def getObjectType: Class[DecisionServiceClient] = classOf[DecisionServiceClient]

  override def isSingleton: Boolean = true
}