var settingsFromS3 = "";
$(document).ready(function () {
    getControlCenterSettings();
    getDynamicControlCenterConfigs();
});

const changeTab = function(name) {
    if(name === 'idPlus') {
        $('#idPlus-tab').attr("style", "display:block");
        $('#other-flags-tab').attr("style", "display:none");
    } else {
        $('#idPlus-tab').attr("style", "display:none");
        $('#other-flags-tab').attr("style", "display:block");
    }
}

const displayMessage = function(msg){
    $('#message').attr("style", "display:block");
    $('#message').html(msg);
}
const hideMessage = function(){
    $('#message').attr("style", "display:none");
}

showDynamicControlCenterViewOnly = function () {
    $('#configUploadForm :input').prop('disabled', true);
    $('#configUploadForm :button')[0].classList.add("disabled")
}


const getDynamicControlCenterConfigs = function () {
    $('.loader').attr("style", "display:block");
    const url = 'superadmin/dynamic_control_center/list';
    $.ajax(
        {
            url: url,
            type: 'GET',
            cache: false,
            contentType: "application/json",
            success: function (response) {
                const fileNameArray = JSON.parse(response).data;
                const jsonArray = [];
                for(let i=0;i<fileNameArray.length;i++){
                    const eachFileName = fileNameArray[i];
                    const obj = {};
                    obj["No"] = i+1;
                    obj["file"] = "<a href='superadmin/dynamic_control_center/download?fileName="+eachFileName+"'>"+ eachFileName +"</a>";
                    jsonArray.push(obj);
                }
                const tableHTML = createTable(jsonArray);
                 $("#listTable").append(tableHTML)
                 $('.loader').attr("style", "display:none");
            },
            error: function (error) {
                console.error(error);
                alert("Error while fetching the dynamic control center config");
                hideMessage();
                $('.loader').attr("style", "display:none");
            }
        });
}


const isValidArray = function (v) {
    return Array.isArray(v) && v.length > 0;
}

const createTable = function (jsonArray) {
  let table = "<center><table id=configTable style='border-collapse: collapse;table-layout: fixed;'>"
  if (isValidArray(jsonArray)) {
    const headers = Object.keys(jsonArray[0]);
    table += "<tr>";
    for (let i = 0; i < headers.length; i++) {
      const eachHeader = headers[i];
      table += "<th>" + eachHeader + "</th>";
    }
    table += "</tr>";
    for (let i = 0; i < jsonArray.length; i++) {
      const eachElement = jsonArray[i];
      table += "<tr>";
      for (let j in eachElement) {
        const eachValue = eachElement[j];
        table += "<td>" + eachValue + "</td>";
      }
      table += "</tr>";
    }
  }
  table += "</table></center>";
  if(canAccessControlCentreFlag === "View Only") {
      showDynamicControlCenterViewOnly();
  }
   return table;
};

const uploadDynamicControlCenterConfig = function () {
    if(!$("#ttl").val() || !$("#file").val()){
        alert("Please fill expiry and file fields");
        return;
    }
    const url = 'superadmin/dynamic_control_center/upload';
    const form = document.getElementById('configUploadForm');
    if ($.trim($(form.file).val()).length == 0) {
        alert("Please provide a valid JSON file to import");
        return;
    }
    displayMessage("<b>Uploading .....Please wait. This might take a few seconds.</b>");

    const formData = new FormData();
    formData.append('file', $(form.file).get(0).files[0]);

    const other_data = $('form').serializeArray();
    $.each(other_data,function(key,input){
        formData.append(input.name, input.value);
    });

    $.ajax(
        {
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                alert("Config Uploaded successfully");
                hideMessage();
                form.reset();
                $("#configTable").remove();
                getDynamicControlCenterConfigs();
            },
            error: function (response) {
                form.reset();
                if(response.status == 400){
                    try{
                        alert(response.responseJSON.msg);
                        hideMessage();
                    } catch{
                        alert("Unable to upload config");
                        $('#message').attr("style", "display:none");
                        hideMessage();
                    }
                }else{
                    alert("Unable to upload config.");
                    hideMessage();
                }
            }
        });

}

showControlCenterViewOnly = function () {
    $('#settingsUploadForm :input').prop('disabled', true);
    $('#settingsUploadForm :button')[0].classList.add("disabled")

    var radioButtons = document.getElementById('controlCenterForm').elements;
    $.each(radioButtons, function(index, radio) {
            radio.setAttribute("disabled", true);
            radio.classList.add('disabled');
    });
}
getControlCenterSettings = function () {
    var url = 'superadmin/control_center/settings';
    var form = document.getElementById('control-center-frm');
    $.ajax(
        {
            url: url,
            type: 'GET',
            cache: false,
            contentType: "application/json",
            success: function (response) {
                var jsonResponse = JSON.parse(response).data;
                settingsFromS3 = jsonResponse;
                createTableRows(jsonResponse.toggleableSettings);
            },
            error: function (error) {
                console.log(error);
                alert("Error while fetching the settings");
            }
        });
}

createTableRows = function (toggleableSettings) {
    $('#settingsTableBody').children().remove();
    $.each(toggleableSettings, function (i, item) {
        var trHTML = '';
        trHTML += '<tr><td>'
        trHTML += '<label for="' + item.name + '">';
        trHTML += '<p>' + item.name + '</p>';
        trHTML += '</label>'
        trHTML += '</td>'
        trHTML += '<td>'
        trHTML += '<p>' + item.description + '</p>';
        trHTML += '</td>'
        trHTML += '<td>'
        trHTML += '<input class="radio" type="radio" value="true" name="' + item.name + '" onchange="toggleSetting(\'' + item.name + '\',\'' + item.description + '\',true)" />&nbsp;&nbsp;On&nbsp;&nbsp;';
        trHTML += '<input class="radio" type="radio" value="false" name="' + item.name + '" onchange="toggleSetting(\'' + item.name + '\',\'' + item.description +  '\', false)" />&nbsp;&nbsp;Off&nbsp;&nbsp;';
        trHTML += '</td>';
        trHTML += '</tr>';
        $('#settingsTableBody').append(trHTML);
    });

    $.each(toggleableSettings, function (i, item) {
        var radioButtonGroup = document.getElementsByName(item.name);
        if(item.value === true){
            radioButtonGroup[0].checked = true;
        }else{
            radioButtonGroup[1].checked = true;
        }

        if(canAccessControlCentreFlag === "View Only") {
            showControlCenterViewOnly();
        }
    });
}


toggleSetting = function(settingName, description, newValue) {
    var turnOnMessage = 'Are you sure you want to turn ON ' + settingName + '?'
    var turnOffMessage = 'Are you sure you want to turn OFF ' + settingName + '?'
    var message = newValue === true ? turnOffMessage : turnOffMessage;
    var answer = window.confirm(message);
    if (answer) {
        var updatedObject = {
            name: settingName,
            value: newValue,
            description: description
        }
        updateControlCenterSettings(updatedObject);
    }
}

uploadControlCenterSettings = function () {
    var url = 'superadmin/control_center/settings/upload';
    var form = document.getElementById('settingsUploadForm');
    if ($.trim($(form.file).val()).length == 0) {
        alert("Please provdie a valid JSON file to import");
        return;
    }
    $('#message').attr("style", "display:block");
    $('#message').html("<b>Uploading .....Please wait. This might take a few seconds.</b>");
    var formData = new FormData();
    formData.append('file', $(form.file).get(0).files[0]);

    $.ajax(
        {
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                alert("Settings Uploaded successfully");
                $('#message').attr("style", "display:none");
                form.reset();
                getControlCenterSettings();
            },
            error: function (response) {
                form.reset();
                if(response.status == 400){
                    try{
                        alert(response.responseJSON.msg);
                        $('#message').attr("style", "display:none");
                    } catch{
                        alert("Unable to upload settings.");
                        $('#message').attr("style", "display:none");
                    }
                }else{
                    alert("Unable to upload settings.");
                        $('#message').attr("style", "display:none");
                }
            }
        });

}

updateControlCenterSettings = function (settingToBeUpdated) {
    var url = contexturl + "/superadmin/control_center/settings/toggle";
    var formData = settingToBeUpdated;
    console.log(settingToBeUpdated);
    $.ajax({
            url: url,
            data: JSON.stringify(formData),
            type: 'POST',
            contentType: 'application/json',
            async: false,
            processData: false,
            success: function (data) {
                alert("Updated the setting successfully");
            },
            error: function (error) {
                alert("Some error occured while updating the setting");
                console.log(error);
            }
        });
};