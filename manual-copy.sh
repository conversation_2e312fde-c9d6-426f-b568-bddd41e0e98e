#!/bin/bash

# Navigate to the v2 directory
cd "$(dirname "$0")/src/main/webapp/WEB-INF/views/v2"

# Create the destination directory if it doesn't exist
mkdir -p ../../../resources/v2

# Copy the source files directly (this won't compile them, but will at least copy your changes)
echo "Copying source files to resources directory..."
cp -r src/* ../../../resources/v2/

echo "Manual copy completed. Your source files have been copied to the resources/v2 directory."
echo "Note: This is a temporary solution and won't compile your React code."
echo "You'll need to fix the npm installation issues for proper building."
