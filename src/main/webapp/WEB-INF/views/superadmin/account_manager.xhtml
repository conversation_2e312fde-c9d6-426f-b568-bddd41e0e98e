<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
>
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Account Manager</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>DataTables live example</title>
	<link rel="shortcut icon" type="image/ico"
		href="http://www.datatables.net/favicon.ico" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		media="print" />

	<script>
		var bucketurl = "#{bucket.resourceURL}";
		var contexturl = "#{request.contextPath}";
		var marketingurl = "#{bucket.marketingSite}";
	    var appenv = "#{bucket.appEnv}";
    </script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
	<script src="resources/scripts/bootstrap.min.js"></script>

	<style>
		#AddDelegatedAdminModal{
			margin: 2% 20%;
			border: 1px solid #ccc;
			border-radius: 8px;
		}
		#AddDelegatedAdminModal select,#AddDelegatedAdminModal input{
			margin: auto 10px;
			float: right;
		}
		#AddDelegatedAdminModal label{
			font-weight: bold;
			/* width: 112px; */
			display: inline;
			text-align: right;
		}
		#AddDelegatedAdminModal table td{
			text-align: left;
		}
	</style>

	<script language="javascript">
		function create_account() {
            var url = app.host + '/adminapi/1/create_account';
            var form = document.getElementById('account_form');

            if (form.name.value.trim() == "") {
                $('#message').addClass("alert-error").html('Account name is mandatory.');
            } else {
                var pId;
                var internal = false;
                if(form.parentId.value!=0) {
                    var parentInfo = JSON.parse(form.parentId.value);
                    pId = parentInfo.id;
                    internal = parentInfo.isInternal;
                }
                $.ajax(
                    url,
                    {
                        type: 'POST',
                        data: ({
                            name: form.name.value,
                            industry: form.industry.value,
                            parentId: pId,
                            isInternal: internal
                        }),
                        success: function (data) {
                            if (data['status'].toLowerCase() == 'ok') {
                                window.location.href = 'inactive_users?message=' + 'New account '
									+ form.name.value  + ' created successfully.';
                            } else {
                                $('#message').addClass("alert-error").html(data['msg']);
                            }
                        }
                    });
            }
        }

		function reset_form() {
            var form = document.getElementById('account_form');
            form.reset();
        }

	</script>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px;">
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>

	<div id="MessageModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
			</div>
		</div>
	</div>

	<div class="col-lg-8 col-md-10 col-sm-10 col-xs-12 mx-auto">
		<h3 class="modal-title">New Account</h3>
		<div id="message" class="alert alert-error"></div>
		<form id="account_form" action="#" method="post">
			<div class="form-group row">
				<label for="name" class="col-sm-2 col-form-label">Account Name</label>
				<div class="col-sm-10">
					<input type="text" class="form-control" id="name" />
				</div>
			</div>
			<div class="form-group row">
				<label for="name" class="col-sm-2 col-form-label">Industry</label>
				<div class="col-sm-10">
					<select name="industry" class="form-control" id="industry_list"></select>
				</div>
			</div>
			<div class="form-group row">
				<label for="name" class="col-sm-2 col-form-label">Parent Account</label>
				<div class="col-sm-10">
					<select name="parentId" class="form-control" id="parent_accounts"></select>
				</div>
			</div>
			<button type="button" class="btn btn-primary" onclick="create_account();">Create</button>
			<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="reset_form();">Reset</button>
		</form>
	</div>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/lib.js"></script>

	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>

	<script type="text/javascript"
		src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script>
		$(document).ready(function() {
            get_industry_list('industry_list');
		});

        $(document).ready(function() {
            get_parent_accounts('parent_accounts');
        });
	</script>
</h:body>
</html>