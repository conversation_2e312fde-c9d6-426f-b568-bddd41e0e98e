package me.socure.alertlist.consortium

import me.socure.alertlist.maintainer.client.{ConsortiumImporterClient, ConsortiumImporterClientFactory}
import me.socure.common.hmac.factory.HMACEncrypterFactory
import me.socure.util.EnvUtil
import org.springframework.beans.factory.FactoryBean
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}

import scala.concurrent.ExecutionContext

class ConsortiumImporterClientFactory extends FactoryBean[ConsortiumImporterClient] {

  @Autowired
  @Qualifier("envUtil")
  private var envUtil: EnvUtil = _

  @Autowired
  private var executionContext: ExecutionContext = _

  override def getObject: ConsortiumImporterClient = {

    val hmacEncrypter = HMACEncrypterFactory.get(
      secretKey = conf("hmac.secret.key"),
      keyStrength = conf("hmac.strength").toInt
    )

    val client = ConsortiumImporterClientFactory.create(
      realm = conf("hmac.realm"),
      version = conf("hmac.version"),
      endpoint = conf("endpoint"),
      encrypter = hmacEncrypter
    )(executionContext)

    client
  }

  private def conf(prop: String): String = {
    envUtil.getProperty(s"alertlist.maintainer.$prop")
  }

  override def getObjectType: Class[ConsortiumImporterClient] = classOf[ConsortiumImporterClient]

  override def isSingleton: Boolean = true
}
