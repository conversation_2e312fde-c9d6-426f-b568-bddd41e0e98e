package me.socure.filter

import javax.servlet._
import javax.servlet.http.HttpServletRequest
import me.socure.action.audit.ActionAuditConfiguration
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.context.support.SpringBeanAutowiringSupport

class SuperAdminAuditFilter extends Filter{
  private val logger = LoggerFactory.getLogger(classOf[SuperAdminAuditFilter])
  private val metrics: Metrics = JavaMetricsFactory.get(this.getClass)
  private val excludedPaths = List("/resources", "/superadmin/1/download_file_uuid", "/superadmin/1/download_file", "/superadmin/1/decrypt_input_file","/superadmin/1/troubleshooting")

  override def init(filterConfig: FilterConfig): Unit = {
    SpringBeanAutowiringSupport.processInjectionBasedOnServletContext(this,
      filterConfig.getServletContext())
  }

  override def destroy(): Unit = {

  }

  @Autowired
  var actionAuditConfiguration : ActionAuditConfiguration = _
  override def doFilter(servletRequest: ServletRequest, servletResponse: ServletResponse, filterChain: FilterChain): Unit = {

      val path = servletRequest.asInstanceOf[HttpServletRequest].getServletPath //returns url without context-path

      val isNotExcluded = excludedPaths.filter(path.startsWith(_)).isEmpty
      if (isNotExcluded) {
        ProcessFilter.doFilter(servletRequest, servletResponse, filterChain, actionAuditConfiguration)
      } else {
        filterChain.doFilter(servletRequest, servletResponse)
      }
  }

}
