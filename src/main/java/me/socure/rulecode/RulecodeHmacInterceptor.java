package me.socure.rulecode;

import com.amazonaws.secretsmanager.caching.SecretCache;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import me.socure.util.EnvUtil;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

@Component
public class RulecodeHmacInterceptor extends HandlerInterceptorAdapter {
    private static final Logger logger = LoggerFactory.getLogger(RulecodeHmacInterceptor.class);

    public static final String NONCE = "nonce";
    private static final String AUTHORIZATION = "authorization";
    private static final String SECRET_ID_CONFIG = "superadmin.hmac";
    public static final String HEADER_FIELD_SEPERATOR = ",";
    public static final String HEADER_FILED_VALUE_SEPERATOR = "=";
    public static final String SIGNATURE = "signature";
    public static final String HMAC_SHA_512 = "HmacSHA512";

    @Autowired
    private EnvUtil envUtil;

    private final SecretCache cache  = new SecretCache();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String authorization = request.getHeader(AUTHORIZATION);
        Map<String,String> authorizationHeaders = parse(authorization);
        try{
            if (isValidHmacSignature(authorizationHeaders.get(NONCE), authorizationHeaders.get(
                SIGNATURE))) {
                return true;
            } else {
                logger.info("nonce : "+authorizationHeaders.get(NONCE) + ", signature : "+authorizationHeaders.get(
                    SIGNATURE) + ", calculated Signature : "+ calculateHmacSignature(authorizationHeaders.get(NONCE)));
                logger.error("Invalid HMAC signature");
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid HMAC signature");
                return false;
            }
        }catch(Exception e){
            logger.info("nonce : "+authorizationHeaders.get(NONCE) + ", signature : "+authorizationHeaders.get(
                SIGNATURE) + ", calculated Signature : "+ calculateHmacSignature(authorizationHeaders.get(NONCE)));
            logger.error("Invalid HMAC signature");
            logger.error("Error validating HMAC signature "+e.getCause().getMessage());
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Error validating HMAC signature");
            return false;
        }

    }

    private boolean isValidHmacSignature(String content, String signature) {
        String calculatedSignature = calculateHmacSignature(content);
        return calculatedSignature.equals(signature);
    }

    private String calculateHmacSignature(String content) {
        try {
            Mac hmacSHA512 = Mac.getInstance(HMAC_SHA_512);
            SecretKeySpec secretKey = new SecretKeySpec(cache.getSecretString(envUtil.getProperty(
                SECRET_ID_CONFIG)).getBytes(), HMAC_SHA_512);
            hmacSHA512.init(secretKey);

            byte[] hash = hmacSHA512.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to calculate HMAC signature", e);
        }
    }

    private Map<String,String> parse(String authorization){
        Map<String,String> authorizationHeaders = new HashMap<>();
        Arrays.stream(authorization.split(HEADER_FIELD_SEPERATOR)).forEach(header->{
            String[] values = header.split(HEADER_FILED_VALUE_SEPERATOR,2);
            authorizationHeaders.put(values[0],values[1].substring(1,values[1].length()-1));
        });
        return authorizationHeaders;
    }


}