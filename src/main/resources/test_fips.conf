#Spring social configuration#
application.secureUrl="https://service.dr.ha.socure.com"
facebook.longlived.accesstoken.url="https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token"
facebook.appid.url="https://graph.facebook.com/app?access_token="

#Mysql db configuration

db.driver="com.mysql.cj.jdbc.Driver"


db.url="*************************************************************************************************************************************************************************************************************************************************************************************"
db.username="socure-2018"
db.password="""ENC(QBjOHzjQ7nqrHsGoPi4j475dRu9bvUrMUdGdLQQA/d6t38PEbAhZQHZNMmFDuddOo8KkFg==)"""
db.defaultAutoCommit="true"

# Model Management DB config
mmdb.driver="com.amazonaws.secretsmanager.sql.AWSSecretsManagerMySQLDriver"
mmdb.jdbcUrl="jdbc-secretsmanager:mysql://model-management-dev.cluster-c8dcsxvsf1es.us-east-1.rds.amazonaws.com:3306/model_management"
mmdb.dataSourceName=model-management-db
mmdb.user="rds-model-management-dev-883f8c-app"
mmdb.maxIdleTime=900
mmdb.maxConnectionAge=3600
mmdb.forceUseNamedDriverClass="true"

mmdb.maxPoolSize=50
mmdb.minPoolSize=3
mmdb.initialPoolSize=3
mmdb.testConnectionOnCheckIn="true"
mmdb.testConnectionOnCheckOut="false"
mmdb.idleConnectionTestPeriod=20

mmdb.useNewDB="true"
mmdb.modelIdSize=10

#Hibernate pool
hibernate.c3p0.initialPoolSize=10
hibernate.c3p0.min_size=10
hibernate.c3p0.max_size=25

hibernate.connection.provider_class="org.hibernate.connection.C3P0ConnectionProvider"
hibernate.c3p0.timeout=100
#you need to consider the number of frequently used PreparedStatements in your application, and multiply that by the number of Connections you expect in the pool (maxPoolSize in a busy application)
hibernate.c3p0.max_statements=500
hibernate.c3p0.idle_test_period=90

#Newly added attribute
hibernate.c3p0.unreturnedConnectionTimeout=10
hibernate.c3p0.debugUnreturnedConnectionStackTraces="true"
hibernate.c3p0.acquireincrement=8
hibernate.c3p0.maxconnectionage=3600
hibernate.c3p0.validationQuery="SELECT 1"
hibernate.c3p0.testOnBorrow="true"
hibernate.c3p0.numHelperThreads=200
hibernate.c3p0.automaticTestTable="tbl_hibernate_c3p0TestTable"
hibernate.c3p0.testOnCheckin=true

#Hibernate JConsole
hibernate.generate_statistics="false"

#Memcache Servers
memcache.servers="localhost:11211"
opt.timeout=500

static.resource.url="/resources"

#Debug Settings
debugMode="false"

#Scoring Cache Properties
#1 day(in seconds)
authscore.memcached.timout=86400
#1 day (in seconds)
authscore.low_confidence.memcached.timeout=86400
#7 days(in seconds)
authscore.high_confidence.memcached.timeout=604800
# The timeout(in days) for the scoring of a user. This is used for the second level cache.
authscore.last_scanned_days.cache.timeout=2


#Facebook throttling mail ID
fb.throttling.mail.to="Engineering <<EMAIL>>"

#Scoring Component Memcache
component.email.memcached.timeout=15552000
component.form.memcached.timeout=15552000
component.fb.memcached.timeout=15552000
component.pipl.email.memcached.timeout=15552000
component.pipl.address.memcached.timeout=15552000
component.gplus.memcached.timeout=15552000
component.twitter.memcached.timeout=15552000
component.fullcontact.memcached.timeout=15552000
component.yahoo.memcached.timeout=15552000

#Scoring Component Database
db.component.email.timeout=15552000
db.component.form.timeout=15552000
db.component.fb.timeout=15552000
db.component.pipl.email.timeout=15552000
db.component.pipl.address.timeout=15552000
db.component.gplus.timeout=15552000
db.component.twitter.timeout=15552000
db.component.fullcontact.timeout=15552000
db.yahoo.memcached.timeout=15552000

#AWS Profile Image Bucket

#============== Admin Specific Properties =================

application.authScore.baseUrl="https://service.dr.ha.socure.com"

marketing.url="http://www.socure.com/"

application.logoutUrl="http://dashboard.socure.com.s3-website-us-east-1.amazonaws.com"

application.activation.url="https://dashboard.dr.ha.socure.com/#/activate"
application.forget.pass.url="https://dashboard.dr.ha.socure.com/reset_password/"

application.set.pass.url="https://dashboard.dr.ha.socure.com/#!/reset_password_token"

#static resources


#============== Admin Specific Properties =================

#============= Entity Image ==============
#In Seconds
cache.image.timeout=86400
#============= Entity Image ==============

#============= Hibernate Search ==============
hibernate.search.fuzzy.threshold=15
#============= Hibernate Search ==============


#===Component Timeout Config=================
component.default.timeout=2
fmval.timeout=2
fcval.timeout=2
yoval.timeout=2
pival.timeout=2
paval.timeout=2
boval.tiemout=2
lival.timeout=2
gpval.timeout=2
fbval.timeout=3
twval.timeout=2
kycofacval.timeout=2

api.v2.scheduler.timeout ="2400"
api.v2.overall.timeout ="3000"
api.system.overall.timeout=3200


#=============== Thread pool configuration ==================
thread.pool.executor.pool.size=400
thread.pool.executor.max.pool.size=700
thread.pool.executor.queue.capacity=0
thread.pool.executor.keep.alive.seconds=1

#=============== Thread pool configuration ==================

future.util.wait.duration=50

trace.authscore.factory="false"

#configure search for depth
#SAEARCH_LEVEL_DEPTH=1
#============= SLA ROLE =====================

component.default.timeout=4

default.sla.fmval.timeout=4
default.sla.fcval.timeout=4
default.sla.yoval.timeout=4
default.sla.pival.timeout=4
default.sla.paval.timeout=4
default.sla.boval.tiemout=4
default.sla.lival.timeout=4
default.sla.gpval.timeout=4
default.sla.fbval.timeout=4
default.sla.twval.timeout=4
default.sla.kycofacval.timeout=4

default.sla.api.v2.scheduler.timeout ="5000"
default.sla.api.v2.overall.timeout ="5400"
default.sla.api.system.overall.timeout=6000

#=======================================

highperformace.sla.fmval.timeout=2
highperformace.sla.fcval.timeout=2
highperformace.sla.yoval.timeout=2
highperformace.sla.pival.timeout=2
highperformace.sla.paval.timeout=2
highperformace.sla.boval.tiemout=2
highperformace.sla.lival.timeout=2
highperformace.sla.gpval.timeout=2
highperformace.sla.fbval.timeout=2
highperformace.sla.twval.timeout=2
highperformace.sla.kycofacval.timeout=2

highperformace.sla.api.v2.scheduler.timeout ="2400"
highperformace.sla.api.v2.overall.timeout ="2800"
highperformace.sla.api.system.overall.timeout=2900

#====================================================

#============= Entity Resolution Filter Config =============#
default.er.component.filter.name=0.5
default.er.component.filter.email=0.5
default.er.component.filter.address=0.5
default.er.component.filter.geocode=0.5
default.er.component.filter.companyname=0.5
default.er.component.filter.nationalid=0.5
default.er.component.filter.dob=0.5
default.er.component.filter.mobilenumber=0.5
default.er.component.filter.username=0.5
default.er.component.filter.gender=0.5
default.er.component.filter.image=0.5

#============= Entity Resolution Filter Config =============#

#======App Environment==========#
appEnv="dev"
#======App Environment==========#

#======TPAudit Stats========================#
aws.tp.stats.bucket="thirdparty-stats-dev-************-us-east-1"

#=======TPAudit Stats=======================#

#============= Super Admin Specific Config =============
#static resources
staticurl.super.admin="/resources"
isexternal.app="false"
#=====================================================

#================= Mail Config =================#
socure.support.email="<EMAIL>"
socure.sales.email="<EMAIL>"
mailgun.apikey="""ENC(NBxpbPtaEFulAJH9iONB6mOkttLWekvQGd7YxjZww4i+SFfwRryzUhAlEoLn8HgzthieF9iNCok+Q/H1JtCFZF30Cvw=)"""
mailgun.domain="socure.com"
mailgun.from="Socure Support <<EMAIL>>"
#================= Mail Config =================#

## account service
account.service.url="https://account-service.webapps.us-east-1.product-dev.socure.link/"

#============= Mailgun config =============#
mailgun.endpoint="https://api.mailgun.net/v2/socure.com/messages"
mailgun.key="""ENC(NBxpbPtaEFulAJH9iONB6mOkttLWekvQGd7YxjZww4i+SFfwRryzUhAlEoLn8HgzthieF9iNCok+Q/H1JtCFZF30Cvw=)"""
mailgun.domain_name="socure.com"
#============= Datadog config =============#
#============= Model monitoring error reporting =============#
error.reporter.model.monitoring.subject="Model Monitoring Metrics Reporting Failure - Prod"
error.reporter.model.monitoring.from="<EMAIL>"
error.reporter.model.monitoring.to="<EMAIL>"
error.reporter.model.monitoring.cc=""
error.reporter.model.monitoring.bcc=""
#============= Model monitoring error reporting =============#

#============= Model Mapping =============#
h2opredictor.service.url="http://h2o-predictor-service"
h2opredictor.service.endpoint2="http://h2o-predictor-service"
h2omlpredictor.service.url="https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link"
h2omlpredictor.service.endpoint2 = "https://h2o-ml-predictor.webapps.us-east-1.product-dev.socure.link"
modelmanagement {
  endpoint = "https://model-management.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://model-management.webapps.us-east-1.product-dev.socure.link"
  groupName="IdplusServicesRamp"
  flagName="ModelManagement_Ramp"
  hmac {
    realm = "Socure"
    version = "1.0"
    strength = 512
    secret.refresh.interval= 5000
    aws.secrets.manager.id="model-management/dev/hmac-1a0b2a"
  }
  enableModelManagement = "true"
}

#============= Audit Actioning =============#
sqsEndpoint="https://sqs.us-east-1.amazonaws.com/"
actionAuditS3BucketName=action-auditing-dev-************-us-east-1
actionAuditQueueName=action-auditing-dev
region="us-east-1"
# batchrun-prod
kmsKey="""ENC(MsqzM6jHWCzBeJ0NSqpI+uiOgtsQMPu8F6APNw9ekfq/HC3rMG406fxbhsBLqQgKg0bpo6O1zhQZNCnwrXP+sSLDMA0IrK9AR/ySLce5EcCBeBgM1sab0o2oCM+ftmXyQbCOENSZ08jTPqI=)"""

#============= Audit Actioning =============#

s3.transaction_error_bucket="dev-audit-errors-************-us-east-1"

#============= Client specific encryption =============#
# prod-kms-client
client.specific.encryption.aws.access.key="""ENC(NBxpbPtaEFulAJH9iONB6mOkttLWekvQGd7YxjZww4i+SFfwRryzUhAlEoLn8HgzthieF9iNCok+Q/H1JtCFZF30Cvw=)"""
client.specific.encryption.aws.secret.key="""ENC(NBxpbPtaEFulAJH9iONB6mOkttLWekvQGd7YxjZww4i+SFfwRryzUhAlEoLn8HgzthieF9iNCok+Q/H1JtCFZF30Cvw=)"""
client.specific.encryption.encryption.context.account_id.key="socure_account_id"
client.specific.encryption.operation.timeout.ms=20000
client.specific.encryption.kms.ids.us-east-1="arn:aws:kms:us-east-1:************:alias/socure/client-specific-encryption-dev"
#============= Client specific encryption =============#

#============= Image auditing bucket configuration =============#
idplus.audit.image.bucket="idplus-audit-prod"
idplus.audit.image.encryption.mode="aws:kms"
idplus.audit.image.kms.id="arn:aws:kms:us-east-1:************:alias/idplus-audit-prod"
#============= Image auditing bucket configuration =============#

#Recaptcha Configuration
recaptcha.privatekey = "6LfGEwgTAAAAAFNaWC9Wwumw-HBqioJceicBR5s_"
recaptcha.publickey = "6LfGEwgTAAAAAPithJiufWOSfCifzwKN28Rgb_Yq"

#============= Blacklist DB Configuration =============#
blacklistdb.driver="com.mysql.cj.jdbc.Driver"
blacklistdb.url="*********************************************************************************************************************************************************************************************************************************************************************************************"
blacklistdb.username="a97be25ab"
blacklistdb.password="""ENC(BvsUj5x9cbf+0bptRAYtsU9MX8pud0D6j3gx8ceym2LlzCKm4rKarulo)"""
blacklistdb.defaultAutoCommit="true"

blacklistdb.hibernate.c3p0.initialPoolSize=10
blacklistdb.hibernate.c3p0.min_size=10
blacklistdb.hibernate.c3p0.max_size=25
blacklistdb.hibernate.connection.provider_class="org.hibernate.connection.C3P0ConnectionProvider"
blacklistdb.hibernate.c3p0.timeout=100
blacklistdb.hibernate.c3p0.max_statements=500
blacklistdb.hibernate.c3p0.idle_test_period=90
blacklistdb.hibernate.c3p0.unreturnedConnectionTimeout=10
blacklistdb.hibernate.c3p0.debugUnreturnedConnectionStackTraces="true"
blacklistdb.hibernate.c3p0.acquireincrement=8
blacklistdb.hibernate.c3p0.maxconnectionage=3600
blacklistdb.hibernate.c3p0.validationQuery="SELECT 1"
blacklistdb.hibernate.c3p0.testOnBorrow="true"
blacklistdb.hibernate.c3p0.numHelperThreads=200
blacklistdb.hibernate.generate_statistics="true"
blacklistdb.hibernate.search.fuzzy.threshold=18
blacklistdb.hibernate.c3p0.connectionCustomizerClassName="me.socure.c3p0.ReadOnlyConnectionCustomizer"
#============= Blacklist DB Configuration =============#


transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {
    maxRetries = 10

    primary {
      sqs {
        region=us-east-1
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-dev-************-us-east-1"
      }
      third-party {
        region=us-east-1
        bucket="thirdparty-stats-dev-************-us-east-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

#================ SP metadata endpoint ================#
saml2.sp.metadata {
  endpoint = "https://authentication.webapps.us-east-1.product-dev.socure.link"
  path = """ENC(0q9ZI0Dj3ksXHr2i+7WBOZ6/ebYYHJwh1YpFfyyZNMZDyAza9d0yRh8u+dNb1CqHKpa4yZim3BtwdJYdlAdH6w8RIWFOqzAlB+h+f8RZqM/zCg==)"""
  token = """ENC(IvmSYwfxhaUzvyS1uJEVwyS7thAwSRRwVJVU3UUVu9Yo27gQnIrZ+jW3qXkR5cwTAicQkx1k2a4EZ02BSnZS+QcQMVhgbFx5hp3L+nWiazhs4bOk4ZvZDdII0pzsbyH+IXbygtUhOyAYzW4ImPzlMz9kEMPgqiZqXycwzMVH5wPU+95d)"""
}
#================ SP metadata endpoint ================#

transaction.auditing {
  endpoint = "https://transaction-auditing-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://transaction-auditing-service.webapps.us-east-1.product-dev.socure.link"
  groupName="IdplusServicesRamp"
  flagName="TransactionAuditingService_Ramp"
  hmac {
    secret.key="""ENC(xq5+FuFeQTYCIrosX9AQ45a4yTQR76qKvGutURQOttJTrKsTaOo23+ShURTjKHEg0euJSPYOkGU=)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
}

#================ File storage download config ================#
file.storage.download {
  endpoint="https://file-download.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://file-download.webapps.us-east-1.product-dev.socure.link"
  groupName="DVServicesRamp"
  flagName="FileDownload_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    secret.key = """ENC(jSDj+Bwm8PQn5c26X8pl25xcOyk7UufpvkoCcAOf0MW8Aed/pvPwUVAFEZ/pqaHkyU2zN+y2SXdJ3e+VJRyKHw==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
  metrics.enabled = false
}
#================ File storage download config ================#

#================ AlertList Maintainer =================#
alertlist.maintainer {
  endpoint = "https://alertlist-maintainer.webapps.us-east-1.product-dev.socure.link/"
  hmac {
    secret.key = """ENC(dRxyrFAvTKocUPQ1MCw/wcfcZEUryjP2FEqR/EYTX9LNWES7Q/StXGuEYBhlJlpKoNMZ//Vn3GhkCA==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ AlertList Maintainer =================#

#================ SAML2 ================#
saml {
  assertionConsumerServiceUrl = "https://superadmin.webapps.us-east-1.product-dev.socure.link/saml2/SSO"
  maxAuthenticationAge = 43200 # 12 hours
}

#================ SAML2 ================#

authentication.service {
  endpoint = "https://authentication.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://authentication.webapps.us-east-1.product-dev.socure.link"
  groupName="UXServicesRamp"
  flagName="AuthenticationService_Ramp"

  hmac {
    secret.key="""ENC(6tsUWX0IufPPrq5k4RhxdUjdHX+xnAsDoeyL3ySIJSkkyJ+B1/Ex573Fb/vwBtRYWPjXKs3RCP8Nk0IHlmHGRw==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}

#================ Account Service config ================#
account.service {
  endpoint="https://account-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://account-service.webapps.us-east-1.product-dev.socure.link"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"

  hmac {
    secret.key="""ENC(ID54T0RD/o8QiwFSTM++bxuNQYTsr9cmkd9rsxKbWklbvw/kyPkDhpi4/ESdseL3sb2Aw5yisXfjbLncJuV1vQ==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ Account Service config ================#

#================ Reasoncode Service config ================#
reasoncode.service {
  endpoint="https://reasoncode.webapps.us-east-1.product-dev.socure.link",
  endpoint2="https://reasoncode.webapps.us-east-1.product-dev.socure.link"
  groupName="IdplusServicesRamp"
  flagName="ReasonCodeService_Ramp"
  hmac {
    secret.key="""ENC(ID54T0RD/o8QiwFSTM++bxuNQYTsr9cmkd9rsxKbWklbvw/kyPkDhpi4/ESdseL3sb2Aw5yisXfjbLncJuV1vQ==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#================ Reasoncode Service config ================#

#================ Event Auditing Service =================#
event.auditing{
  security{
    endpoint="https://event-auditing-service.webapps.us-east-1.product-dev.socure.link/api"
    endpoint2 = "https://event-auditing-service.webapps.us-east-1.product-dev.socure.link/api"
    groupName="IdplusServicesRamp"
    flagName="EventAuditingService_Ramp"
    hmac {
      realm="Socure"
      version=1.0
      ttl=5
      time.interval=5
      strength=512
      aws.secrets.manager.id="event-auditing/dev/hmac-3ed08d"
      secret.refresh.interval=5000
    }
  }
}

#================ Event Auditing Service =================#

#================ Batch Job Service ================#

batch.job {
  endpoint="https://batch-job.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://batch-job.webapps.us-east-1.product-dev.socure.link"
  groupName="UXServicesRamp"
  flagName="BatchJobService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "batch-job-service/dev/hmac-3dac6a7fca"
    secret.refresh.interval = 5000
    realm="Socure"
    version = "1.0"
  }
}

#================ Batch Job Service ================#

#================ Rulecode Service config ================#
rulecode.service {
  endpoint = "https://rulecode-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://rulecode-service.webapps.us-east-1.product-dev.socure.link"
  groupName="IdplusServicesRamp"
  flagName="RulecodeService_Ramp"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="rulecode-service/dev/hmac-49397d"
    secret.refresh.interval=5000
  }
}
#================ Rulecode Service config ================#
#=================Rulecode Ingestion config =============#
rulecode.ingestion{
  aws{
    s3{
      bucketname="rulecode-audit-************-us-east-1"
      kmsKey="arn:aws:kms:us-east-1:************:key/7be1b782-93b8-437d-81fb-da11d61e9af7"
    }
  }
}
#=================Rulecode Ingestion config =============#
#=================Rulecode Studio config =============#

rulecode.studio {
  git{
    projectId = "235"
    access_token = """ENC(FM+6KW8hQWnUtHBDPymH8n5zZXQnJ4hjVMwtTS7C4OP8z6dmVk19p9HC2hsCB4D6Ul1pLQ==)"""
    rulecodePath = "rulecode_definitions/"
    rulecodePathV2 = "rulecode_definitions_v2/"
    dependencyPath = "table_definitions/"
    branchName = "master"
    baseUrl = "https://gitlab-ee.us-east-vpc.socure.be/api/v4/projects/"
  }
  s3{
    audit{
      bucketname="rulecode-audit-************-us-east-1"
      kmsKey="arn:aws:kms:us-east-1:************:key/7be1b782-93b8-437d-81fb-da11d61e9af7"
      rulecodePath = "studio/rulecode_definitions/"
      rulecodePathV2 = "studio/rulecode_definitions_v2/"
      dependencyPath = "studio/table_definitions/"
    }
    config{
      bucketname="rulecode-config-************-us-east-1"
      rulecodePath = "rulecode_definitions/"
      rulecodePathV2 = "rulecode_definitions_v2/"
      dependencyPath = "table_definitions/"
    }
    data{
      bucketname="rulecode-data-************-us-east-1"
      bulkFilePath = "rulecode_create_data/"
    }
  }
  test{
    envUrl = "https://service.socure.com"
    inputBucketname="rulecode-data-prod"
    outputBucketname="batch-job-storage-prod"
    prefix = "studio-test/"
  }
}
#=================Rulecode Studio config =============#

#=================Document Manager config =============#
document.manager {
  document.manager {
    endpoint="https://document-manager.webapps.us-east-1.product-dev.socure.link"
    endpoint2="https://document-manager.webapps.us-east-1.product-dev.socure.link"
    groupName="DVServicesRamp"
    flagName="DocumentManager_Ramp"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager/dev/hmac-835044"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
      port=11211
    }
  }

  dynamic.control.center {
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }
}
#=================Document Manager config =============#
#=================Decision Service Config===============#
decision.service {
  endpoint = "https://decision-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://decision-service.webapps.us-east-1.product-dev.socure.link"
  groupName="UXServicesRamp"
  flagName="DecisionService_Ramp"
}
#=================Decision Service Config===============#
#=================Sandbox Service Config===============#
sandbox.service {
  endpoint = "https://idplus-sandbox.webapps.us-east-1.product-dev.socure.link"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="idplus-sandbox/dev/hmac-0ba167/query/ba"
    secret.refresh.interval=5000
  }
}
#=================Sandbox Service Config===============#

#=================StepUp Service=====================#
stepUp.service {
  endpoint="https://step-up-service.webapps.us-east-1.product-dev.socure.link"
  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "step-up-service/dev/hmac-7c48d6"
    secret.refresh.interval = 5000
    realm = "Socure"
    version = "1.0"
  }
  metrics.enabled = false
}
#=================StepUp Service=====================#
#=================Docv Orchestra Service=====================#
docvOrchestra {
  endpoint="https://document-orchestra.webapps.us-east-1.product-dev.socure.link/"
  endpoint2 = "https://document-orchestra.webapps.us-east-1.product-dev.socure.link/"
  groupName="DVServicesRamp"
  flagName="DocVOrchestraService_Ramp"
  hmac {
    realm="Socure"
    version = "1.0"
    strength=512
    aws.secrets.manager.id="docv-orchestra/dev/hmac"
  }
  metrics.enabled = false
}
#=================Docv Orchestra Service=====================#

#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "idplus-global-settings-************-us-east-1"
  }
  memcached {
    host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
    port=11211
    ttl="24 hours"
  }
}

#===================Control Center==========================#

#===================batchjob==========================#
batch.job {
  aws {
    s3 {
      bucket.name = "batch-job-storage-dev-************-us-east-1"
      kms.key = "arn:aws:kms:us-east-1:************:key/7be1b782-93b8-437d-81fb-da11d61e9af7"
    }
  }
}

#===================batchjob==========================#

#===================Dynamic Control Center==========================#

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-************-us-east-1"
  }
  memcached {
    host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

access.control {
  enabled=false
}

#================ ThirdParty Reader Service config ================#
thirdparty.reader.service {
  endpoint = "https://thirdparty-reader.webapps.us-east-1.product-dev.socure.link"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="thirdparty-reader-service/dev/hmac-5bbc4055"
    secret.refresh.interval=5000
  }
  metrics.enabled = false
}
#================ ThirdParty Reader Service config ================#

#===================sftp==========================#
sftp {
  role="arn:aws:iam::************:role/super-admin-dev-deliveries-users20230216084103302900000005"
  homeDirectoryType="LOGICAL"
  region="us-east-1"
  serverId="s-0a36e240408249a5b"
  target="/client-datafile-dev-************-us-east-1/"
  entry="/"
}
#===================sftp==========================#

txn.case.workflow.service {
  endpoint="https://txn-case-workflow-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://txn-case-workflow-service.webapps.us-east-1.product-dev.socure.link"
  groupName="IdplusServicesRamp"
  flagName="TxnCaseWorkflowService_Ramp"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "txn-case-workflow-service/dev/hmac-354cabcc"
    secret.refresh.interval = 5000
  }
}

globalwatchlist.service {
  endpoint = "https://watchlist-private-service.webapps.us-east-1.product-dev.socure.link/api/"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="watchlist-private/dev/hmac-feec12"
    secret.refresh.interval=5000
  }
}

entity.feedback.reader {
  endpoint="https://entity-feedback-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://entity-feedback-service.webapps.us-east-1.product-dev.socure.link"
  groupName="IdplusServicesRamp"
  flagName="EntityFeedbackService_Ramp"
  hmac {
    realm = "Socure"
    version = "1.0"
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "entity-feedback-service/dev/hmac-d8945679"
    secret.refresh.interval = 5000
  }
}

superadmin.hmac="superadmin-backend/stage/hmac"

#===================docv-berbix==========================#
berbix.v1 {
  endpoint="https://docv-bb-backend.webapps.us-east-1.product-dev.socure.link"
  mockHost = "https://socure-dv-mock-vendor-stage.socure.be/api/berbix/v1/"
  hmac {
    secret.key="""ENC(PyurXVZ0I+Z7eHianATx4fwqa4ztxNQNano15nBO1LtGJyUAmxUEMMhmqeqb+bRXoYRroDqdHLWj1JecfcfzKP0xQSekInf4nx2wUDlAcPB04eZOtkQ=)"""
    strength=512
    realm="Socure"
    version="1.0"
  }
}

cors{
  alloweddomains = ["https://superadmin.webapps.us-east-1.product-dev.socure.link/"]
}

launchdarkly {
  sdk.key = "ENC(TrxFojosIw6l3A3Bt8udc4Hzbr0fCgLst7mxoN8FoisAdndsWNbuk4fbB998X787EvGdtAl7VMcHEl2diXMMYYIhubVwYmjK)"
  use.fedramp.version = false
}

kyc {
  endpoint = "https://kyc-search-service.webapps.us-east-1.product-dev.socure.link"
  recordcorrection.endpoint = "https://transaction-resolved-entity-worker.webapps.us-east-1.product-dev.socure.link/es/record-correction"
}