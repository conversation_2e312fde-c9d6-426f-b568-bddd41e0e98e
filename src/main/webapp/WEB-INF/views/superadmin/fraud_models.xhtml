<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Fraud Service Model</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<link rel="shortcut icon" type="image/ico"
		href="https://www.datatables.net/favicon.ico" />
	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		media="print" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

	<h:outputStylesheet>
		.truncate {
			width: 150px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	</h:outputStylesheet>

	<script>
		var bucketurl = "#{bucket.resourceURL}";
		var contexturl = "#{request.contextPath}";
		var marketingurl = "#{bucket.marketingSite}";
		loggedinUsername = "#{loggedinUsername}";
		appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px; margin-right:20px;">
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="fraudmanager">Fraud Model Manager</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>

	<div class="modal fade" id="ViewQuantileMappingModal" tabindex="-1" role="dialog" aria-labelledby="ViewQuantileMappingModalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="ViewQuantileMappingModalLabel">Quantile Mapping</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<pre id="quantile_mappings"></pre>
				</div>
				<div class="modal-footer">
					<a href="#IndustryModal" data-toggle="modal" data-target="#IndustryModal"
					   class="btn btn-secondary" data-dismiss="modal">Close</a>
				</div>
			</div>
		</div>
	</div>
	<div class="modal fade" id="MessageModal" tabindex="-1" role="dialog" aria-labelledby="MessageModalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="MessageModalLabel">Fraud Model Mapping</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="delete_msg"></label>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
					<button type="button" class="btn btn-primary" onclick="delete_fraudmodel();">Deprecate</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="UpdateQuantileMappingModal" tabindex="-1" role="dialog" aria-labelledby="UpdateQuantileMappingModalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="UpdateQuantileMappingModalLabel">Fraud Model Mapping</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="update_msg"></label>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
					<button type="button" class="btn btn-primary" onclick="save_fraudmodel(update_fraudmodel);">Update</button>
				</div>
			</div>
		</div>
	</div>
	<div class="accordion" id="accordion2">
		<div class='container' style='margin-top: 10px; max-width: 1850px; padding-left: 8px;'>
			<table cellpadding='0' cellspacing='0' border='0'
				class='table table-striped table-bordered' id='industry_table'>
				<thead>
					<tr id="headers">
						<th>Show Active <input type="checkbox" id="showActive" onclick="showActive();" checked="checked" /></th>
						<th>Image</th>
						<th>Public ID</th>
						<th>Fraud Model</th>
						<th>Identifier</th>
						<th>Common Name</th>
						<th>Score Name</th>
						<th>Version</th>
						<th>Feature</th>
						<th>Quantile Mapping</th>
						<th><input type="checkbox" id="FraudModelAll"
							name="FraudModelAll" value="select All" /></th>
						<th>Params</th>
						<th>Config</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<div style="padding-left: 215px;">
		<a href="javascript:show_fraudmodel_add_form();">Add New</a>&nbsp;&nbsp; | &nbsp;
		&nbsp; <a href="javascript:show_fraudmodel_delete_form ();">Deprecate</a>
	</div>

	<div id="ViewQuantileMapping" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Quantile Mapping</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="AddFraudModal" tabindex="-1" role="dialog" aria-labelledby="AddFraudModalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="AddFraudModalLabel">Fraud Service Model</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="message" class="alert alert-error"></div>
					<form id="add_fraud_model" action="#" method="post">
						<div class="form-group row">
							<label for="fraudmodel" class="col-sm-3 col-form-label">Fraud Model</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="fraudmodel" placeholder="Fraud Model" />
							</div>
						</div>

						<div class="form-group row">
							<label for="commonname" class="col-sm-3 col-form-label">Common Name</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="commonname" placeholder="Common Name" value="" required="false" />
							</div>
						</div>

						<div class="form-group row">
							<label for="file" class="col-sm-3 col-form-label">Description</label>
							<div class="col-sm-9">
								<textarea type="text" id="description" value="" required="false"/>
							</div>
						</div>

						<div class="form-group row">
							<label for="fraudmodelformat" class="col-sm-3 col-form-label">Model Format</label>
							<div class="col-sm-9 model-format-dropdown format-selector">
								<select id="fraudmodelformat" onchange="javascript:get_identifiers(this);" >
									<option value="2" selected="selected">MOJO</option>
								</select>
								<div class="error-msg" id="formaterrormsg"></div>
							</div>
						</div>

						<div class="form-group row">
							<label for="fraudidentity" class="col-sm-3 col-form-label">Identifier</label>
							<div class="col-sm-9 model-format-dropdown format-selector">
								<input id="fraudidentity" class="form-control" list="fraudidentitylist" />
								<datalist id="fraudidentitylist">
								</datalist>
								<div class="error-msg" id="identityerrormsg"></div>
							</div>
						</div>

						<div class="form-group row">
							<label for="fraudscorename" class="col-sm-3 col-form-label">Score Name</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="fraudscorename" placeholder="Score Name" />
							</div>
						</div>

						<div class="form-group row">
							<label for="fraudversion" class="col-sm-3 col-form-label">Version</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="fraudversion" placeholder="Version" />
							</div>
						</div>

						<div class="form-group row">
							<label for="fraudfeature" class="col-sm-3 col-form-label">Feature</label>
							<div class="col-sm-9">
								<select id="fraudfeature" onchange="javascript:change_item();">
									<option value="1">Sigma Identity</option>
									<option value="2">Sigma Identity (US)</option>
									<option value="3">Sigma Identity (Int)</option>
									<option value="4">Correlation Scores - Name vs Address (US)</option>
									<option value="5">Correlation Scores - Name vs Email (US)</option>
									<option value="6">Correlation Scores - Name vs Phone (US)</option>
									<option value="7">Correlation Scores - Name vs Address (International)</option>
									<option value="8">Correlation Scores - Name vs Email (International)</option>
									<option value="9">Correlation Scores - Name vs Phone (International)</option>
									<option value="19">Correlation Scores - Device vs Email</option>
									<option value="10">Address Risk Score (US)</option>
									<option value="11">Email Risk Score (US)</option>
									<option value="12">Phone Risk Score (US)</option>
									<option value="13">Address Risk Score (Int)</option>
									<option value="14">Email Risk Score (Int)</option>
									<option value="15">Phone Risk Score (Int)</option>
									<option value="18">Device Risk Score</option>
									<option value="20">Synthetic</option>
									<option value="16">Synthetic (US)</option>
									<option value="17">Synthetic (International)</option>
									<option value="21">First Party Fraud (US)</option>
								</select>
							</div>
						</div>


						<div class="form-group row" id="lt_container">
							<label for="lowerthreshold" class="col-sm-3 col-form-label">Lower Threshold</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="lowerthreshold" placeholder="Lower Threshold" />
							</div>
						</div>

						<div class="form-group row" id="ut_container">
							<label for="upperthreshold" class="col-sm-3 col-form-label">Upper Threshold</label>
							<div class="col-sm-9">
								<input id="upperthreshold" type="number" min="0.0" max="1.0" placeholder="0.0 - 1.0" />
							</div>
						</div>


						<div class="form-group row" id="pt_container">
							<label for="positivethreshold" class="col-sm-3 col-form-label">Positive Threshold</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" id="positivethreshold" placeholder="Positive Threshold" />
							</div>
						</div>

						<div class="form-group row" id="nt_container">
							<label for="negativethreshold" class="col-sm-3 col-form-label">Negative Threshold</label>
							<div class="col-sm-9">
								<input id="negativethreshold" type="number" min="0.0" max="1.0" placeholder="0.0 - 1.0" />
							</div>
						</div>

						<div class="form-group row" id="mute_params_container">
							<label for="mutedparams" class="col-sm-3 col-form-label">Params to be muted</label>
							<div class="col-sm-9">
								<input id="mutedparams" placeholder="comma separated rulecodes in the format FMVAL.xxxx,FMVAL.yyyy,..." />
							</div>
						</div>

						<div class="form-group row">
							<label for="fraudmodelType" class="col-sm-3 col-form-label">Model Type</label>
							<div class="col-sm-9">
								<select id="fraudmodelType">
									<option value="h2o">h2o</option>
								</select>
							</div>
						</div>
						<div class="form-group row" id="isDefault_container">
							<label for="isDefault" class="col-sm-3 col-form-label">Default Model</label>
							<div class="col-sm-9">
								<select id="isDefault">
									<option value="false">False</option>
									<option value="true">True</option>
								</select>
							</div>
						</div>

						<div class="form-group row">
							<label for="file" class="col-sm-3 col-form-label">Quantile Mapping</label>
							<div class="col-sm-9">
								<input type="file" id="file" name="file" required="true"/>
							</div>
						</div>
						<div class="form-group row" id="implicit_container">
							<label for="implicit" class="col-sm-3 col-form-label">Implicit</label>
							<div class="col-sm-9">
								<select id="implicit">
									<option value="false">False</option>
									<option value="true">True</option>
								</select>
							</div>
						</div>
						<input id="idvalue" type="hidden" value="-1"/>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
					<button type="button" class="btn btn-primary" onclick="save_fraudmodel(null)">Save</button>
				</div>
			</div>
		</div>
	</div>

	<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="resources/scripts/bootstrap.min.js"></script>
	<script type="text/javascript"
		src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>
	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript" src="resources/scripts/fraud_model.js"></script>
	<script>
		$(function(){
   			function callback() {
      			get_fraudmodel("active");
   			}
   			app.subscribe(callback);
		})
	</script>
</h:body>
</html>
