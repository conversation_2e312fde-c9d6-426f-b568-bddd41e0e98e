Beanstalk Configuration
=============================================

Publish Metrics to CloudWatch
----------------------------------------------------
See 01_CloudWatchMetics.config.

Example. Cloudwatch graph for memory on an instance: https://console.aws.amazon.com/cloudwatch/home?region=us-east-1#metrics:graph=!D04!E07!ET1!MN5!NS6!PD2!SS3!ST0!VA2014-02-13T20%253A30%253A00Z~2014-02-25T20%253A30%253A00Z~60~Average~InstanceId~MemoryUtilization~System%252FLinux~i-4a76e96a

Example. Cloudwatch graph for disk usage on an auto scaling group: https://console.aws.amazon.com/cloudwatch/home?region=us-east-1#metrics:graph=!D04!D16!D28!E011!E12!E21!ET9!MN5!NS10!PD3!SS7!ST0!VA-P12D~%252F~%252Fdev%252Fxvda1~60~AutoScalingGroupName~DiskSpaceUtilization~Filesystem~Maximum~MountPath~P0D~System%252FLinux~awseb-e-eziinqsnmt-stack-AWSEBAutoScalingGroup-LKBUWT8GCPDY

Create an Alarm for Disk Usage on the Autoscale Group
------------------------------------------------------
See 02_CloudWatchMetics.config.

Example: https://console.aws.amazon.com/cloudwatch/home?region=us-east-1#alarm:alarmFilter=inOk;name=awseb-e-uckzdfjznt-stack-HighDiskUsageAlarm-8PWXFV1AZDYA
