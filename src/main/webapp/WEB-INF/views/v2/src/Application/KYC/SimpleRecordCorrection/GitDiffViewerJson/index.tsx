import React, { useState } from "react";
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Typography } from "@mui/material";
import { replaceWithVendorShortCode } from "../utilities";

interface IGitDiffViewerJsonProps {
    filename: string;
    oldJson: any;
    newJson: any;
}

const GitDiffViewerJson: React.FC<IGitDiffViewerJsonProps> = ({
    filename, oldJson, newJson
}: IGitDiffViewerJsonProps) => {
    const [oldText] = useState<string>(JSON.stringify(oldJson, undefined, 4));
    const [newText] = useState<string>(JSON.stringify(newJson, undefined, 4));

    return <Accordion variant="outlined"  >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">{replaceWithVendorShortCode(filename)}</Typography>
        </AccordionSummary>
        <AccordionDetails>
            <ReactDiffViewer
                oldValue={oldText}
                compareMethod={DiffMethod.LINES}
                newValue={newText}
                splitView={false}
            />
        </AccordionDetails>
    </Accordion>
}

export default GitDiffViewerJson;