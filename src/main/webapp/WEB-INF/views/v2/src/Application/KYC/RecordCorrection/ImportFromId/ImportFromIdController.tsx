import React, { ReactElement, useCallback, useState } from 'react';
import { IImportFromIdViewPropsBase } from './ImportFromIdView';
import { apiCall } from '../../../../Utilities';
import { toaster } from '../../../../Components/Toaster';
import { IRetrievedIdData } from './ImportFromIdTypes';

const allowedSources = {
    "equifax": "equifax",
    "enformion": "enformion",
    "locate-smarter": "enformion"
}

interface IImportFromIdControllerProps {
    children: (props:IImportFromIdViewPropsBase) => ReactElement<any, any> | null
}

const ImportFromIdController: React.FC<IImportFromIdControllerProps> = ({ children }) => {
    const [type, setType] = useState<number>(1);
    const [id, setId] = useState<string>("");
    const [account, setAccount] = useState<number>(0);
    const [locked, setLocked] = useState<boolean>(false);
    const [retrievedData, setRetrievedData] = useState<IRetrievedIdData | null>(null);
    const [cluster, setCluster] = useState<string | null>(null);

    const loadCluserIDs = useCallback(async () => {
        const requestBody = {
            type,
            id,
            accountId: (type === 3 ? account : undefined)
        }
        try {
            const result = await apiCall("/kyc/record_correction/list-cluster-id", requestBody);
            if (result.status === 200 && result.data.status === "ok") {
                let retData: IRetrievedIdData = result.data.data;
                retData = {
                    ...retData,
                    clusters: retData.clusters
                        .map(_ => ({ ..._, source: (allowedSources as any)[_.source] }))
                        .filter(_ => _.source !== undefined)
                };
                if (retData.clusters.length === 0) {
                    toaster.error("No usable clusterID associated with the ID", 8000);
                } else {
                    const { cluster_id, source } = retData.clusters[0];
                    setLocked(true);
                    setRetrievedData(retData);
                    setCluster(`${cluster_id}:${source}`);
                }

            } else {
                toaster.error("Error occured while retrieveing data." + JSON.stringify(result.data), 5000)
            }
        } catch (e) {
            toaster.error((e as any).toString(), 8000);
        }
    }, [type, id, account, setLocked, setRetrievedData, setCluster]);

    const clearData = useCallback(() => {
        setType(1);
        setId("")
        setAccount(0);
        setLocked(false);
        setRetrievedData(null);
        setCluster(null);
    }, [setType, setId, setAccount, setLocked, setRetrievedData, setCluster])

    const viewProperties: IImportFromIdViewPropsBase = {
        type,
        setType,
        id,
        setId,
        account,
        setAccount,
        locked,
        setLocked,
        loadCluserIDs,
        cluster,
        retrievedData,
        setCluster,
        clearData
    };

    return children(viewProperties);
}

export default ImportFromIdController;