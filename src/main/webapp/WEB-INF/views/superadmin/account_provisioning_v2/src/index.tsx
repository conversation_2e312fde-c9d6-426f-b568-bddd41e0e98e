import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';

const AppContainer = document.getElementById('tab_roles_permissions');
const isSubaccount = (AppContainer && AppContainer.getAttribute('data-accountType') === 'SubAccount') as boolean;
const isReadOnly = false; // temporarily setting it to true and keeping it in framework //(AppContainer && AppContainer.getAttribute('data-writeAccess') !== 'true') as boolean;
const loggedinUsername = AppContainer?.getAttribute('data-loggedinUsername');
const piiRetentionAccessPermission = AppContainer?.getAttribute('data-piiRetentionAccessPermission');
const isInternal = AppContainer?.getAttribute('data-isInternal') === "true" ? 1 : 0;

ReactDOM.render(
  <App isReadOnly={isReadOnly} isSubaccount={isSubaccount} loggedinUsername={loggedinUsername} piiRetentionAccessPermission={piiRetentionAccessPermission} isInternal={isInternal} />,
  AppContainer
);
