<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p" 
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
	   		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

	<bean id="jsfViewResolver" class="org.springframework.web.servlet.view.UrlBasedViewResolver">  
            <property name="viewClass" value="org.springframework.faces.mvc.JsfView"/>  
            <property name="prefix" value="/WEB-INF/views/" />  
            <property name="suffix" value=".xhtml" />  
            <property name="order" value="1" />   
         </bean>

	<!-- Declare a view resolver -->
	<bean id="jspViewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver" 
    		p:prefix="/WEB-INF/jsp/" p:suffix=".jsp" p:order="2"/>
    		
    <bean id="multipleViewResolver" class="me.socure.util.view.MultipleViewResolver">
    	<property name="viewResolvers">
    		<list>
    			<ref bean="jsfViewResolver"/>
    			<ref bean="jspViewResolver"/>
    		</list>
    	</property>
    </bean>
    		
    		
</beans>