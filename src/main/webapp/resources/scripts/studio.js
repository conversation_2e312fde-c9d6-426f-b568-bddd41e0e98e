$(document).ajaxStart(function() {
    $("#Loading").show();
});

$(document).ajaxStop(function() {
    $("#Loading").hide();
});

$(document).keydown(function(e) {
    // ESCAPE key pressed
    if (e.keyCode == 27) {
        closeForm();
        closeCreateRulecodeForm();
        closeCreateBulkRulecodeForm();
    }
});


function loadTable() {
    rulecodeTable = $("#rulecodes").dataTable({
        "bServerSide": true,
        "bLengthChange": false,
        "bDestroy": true,
        "bRetrieve": true,
        "bFilter": true,
        "bpaginate": true,
        "bProcessing": true,
        "sAjaxSource": contexturl + "/superadmin/rulecode-service/v1/rulecodes?vendorId=" + selectedVendor,
        "aoColumns": [{
                "mData": "rule_code_name"
            },
            {
                "mData": "operator.name"
            },
            {
                "mData": "operator.inputs"
            },
            {
                "mData": function (data, type, dataToSet) {
                        var configs = '';

                        if(data.operator.config){
                            Object.entries(data.operator.config).forEach(([key, value]) => {
                                configs = configs + `${key}:${value}; `;
                         });
                        }

                        return configs;
                }
            },
            {
                "mData": "rulecodeDeploymentStatus"
            },
            {
                "mData": function (data, type, dataToSet) {
                    if(ishigherEnv()){
                            return "<div><button disabled=disabled > Edit </button> ";
                    }
                    else{
                         var configs = '';

                         if(data.operator.config){
                            Object.entries(data.operator.config).forEach(([key, value]) => {
                              configs = configs + `${key}:${value};`;
                            });
                         }

                         var text = data.rule_code_name.replace('.', '_')  + '|||' + data.operator.name + '|||' + data.operator.inputs + '|||' + configs;
                         var idToBeDeleted = data.rule_code_name.replace('.', '_');
                         if(data.rulecodeDeploymentStatus=='NEW')
                            return "<div><button id=\"" + text + "\" onClick=openCreateRuleCodeForm(this.id) > Edit </button> <button id=\"" + idToBeDeleted + "\" onClick=deleteRow(this.id) > Delete </button></div>";
                         else
                            return "<div><button id=\"" + text + "\" onClick=openCreateRuleCodeForm(this.id) > Edit </button> ";
                    }
                }
            }
        ]
    });

    $('#rulecodes_filter input').unbind();
    $('#rulecodes_filter input').bind('keyup', function(e) {
        if (e.keyCode == 13) {
            rulecodeTable.fnFilter($(this).val());
        }
    });
}

function loadTableV2() {
    rulecodeTable = $("#rulecodesV2").dataTable({
        "bServerSide": true,
        "bLengthChange": false,
        "bDestroy": true,
        "bRetrieve": true,
        "bFilter": true,
        "bpaginate": true,
        "bProcessing": true,
        "sAjaxSource": contexturl + "/superadmin/rulecode-service/v1/rulecodes_v2?vendorId=" + selectedVendor,
        "aoColumns": [
            {
                "mData": "rule_code_name"
            },
            {
                "mData": "type"
            },
            {
                "mData": (data) => {
                    if(data.operators.length == 1) {
                        return data.operators[0].name;
                    }
                    const result = data.operators.map((operator) => `<li>${operator.name}</li>`);
                    return `<ul>${result.toString().replaceAll(/,/g, "")}<ul>`;
                }
            },
            {
                "mData": "default"
            },
            {
                "mData": (data) => {
                    if(!!data.computeOnEmptyLookup) return "true";
                    return "false";
                }
            },
            {
                "mData": "rulecodeDeploymentStatus"
            },
            {
                "mData":  (data) => {
                    if(ishigherEnv()){
                            return "<div><button disabled=disabled > Edit </button> ";
                    }
                    else{
                        const ruleCodeName = data.rule_code_name.replace('.', '_');
                        const rulecodeDeploymentStatus = data.rulecodeDeploymentStatus;
                        const _data = {...data};
                        ["rulecodeDeploymentStatus", "lastModified"].forEach(item => delete _data[item]);
                        const text = JSON.stringify(_data, null, 2);
                        if(rulecodeDeploymentStatus=='NEW')
                        return "<div><button id=\"" + ruleCodeName + "\" data=\'"+ text +"\' onClick=openCreateRuleCodeFormV2(this) > Edit </button> <button id=\"" + ruleCodeName + "\" onClick=deleteRulecodeV2(this.id) > Delete </button></div>";
                        else
                        return "<div><button id=\"" + ruleCodeName + "\" data=\'"+ text +"\' onClick=openCreateRuleCodeFormV2(this) > Edit </button> ";
                    }
                }
            }
        ]
    });

    $('#rulecodesV2_filter input').unbind();
    $('#rulecodesV2_filter input').bind('keyup', function(e) {
        if (e.keyCode == 13) {
            rulecodeTable.fnFilter($(this).val());
        }
    });
}

function createRulecode() {

    var val = $('#create_submit_btn').val();
    if (val != '') {
        var $row = $(this).closest('tr');
        var rowsdata = rulecodeTable.fnGetData($row[0]);
        if (typeof rowsdata === 'undefined')
            return;

        for (var i = 0; i < rowsdata.length; i++) {
            var rulecodeid = rowsdata[i].rule_code_name;
            rulecodeid = rulecodeid.replace('.', '_')
            if (val.localeCompare(rulecodeid) == 0) {
                var payload = getCreateRCPayload();
                $.ajax({
                    url: contexturl + "/superadmin/rulecode-service/v1/rulecodes/" + val,
                    data: JSON.stringify(payload),
                    type: 'PUT',
                    contentType: 'application/json',
                    dataType: 'json',
                    error: function(request, status, error) {
                        alert("Rulecode Updation Failed ! " + request.responseText);
                    },
                    success: function(res) {
                        var status = 'NEW';
                        if ("DEPLOYED".localeCompare(rowsdata[i].rulecodeDeploymentStatus) == 0 || "MODIFIED".localeCompare(rowsdata[i].rulecodeDeploymentStatus) == 0)
                            status = 'MODIFIED';
//                        rulecodeTable.fnUpdate(payload.operator, i, 1, false);
//                        rulecodeTable.fnUpdate(payload.inputs, i, 2, false);;
//                        rulecodeTable.fnUpdate(status, i, 3, false);;
                        closeCreateRulecodeForm();
                        removeOperatorConfigs();
                        rulecodeTable.fnDraw();
                        alert("Rulecode Updated Sucessfully!");
                    }
                });
                break;
            }
        }
    } else {

        $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/rulecodes",
            data: JSON.stringify(getCreateRCPayload()),
            type: 'POST',
            contentType: 'application/json',
            dataType: 'json',
            error: function(request, status, error) {
                alert("Rulecode Creation Failed ! " + request.responseText);
            },
            success: function(data) {
                closeCreateRulecodeForm();
                removeOperatorConfigs();
                rulecodeTable.fnDraw();
                alert("Rulecode Added Sucessfully!");
            }
        });
    }
}

function createRulecodeV2() {
    const value = $('#create_submit_btn_v2').val();
    const configStr = $("#rulecodeConfigText").text();
    const configJson = JSON.parse(configStr);
    const rulecodePayload = getCreateRCPayloadV2(configJson);
    if (value != '') {
        console.log(rulecodePayload);
        $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/rulecodes_v2/" + value,
            data: JSON.stringify(rulecodePayload),
            type: 'PUT',
            contentType: 'application/json',
            dataType: 'json',
            error: (err) => {
                alert("Rulecode Updation Failed ! " + err.responseText);
            },
            success: (res) => {
                closeCreateRulecodeFormV2();
                rulecodeTable.fnDraw();
                alert("Rulecode Updated Sucessfully!");
            }
        });
    } else {
        $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/rulecodes_v2",
            data: JSON.stringify(rulecodePayload),
            type: 'POST',
            contentType: 'application/json',
            dataType: 'json',
            error: (request) => {
                alert("Rulecode Creation Failed ! " + request.responseText);
            },
            success: (data) => {
                closeCreateRulecodeFormV2();
                rulecodeTable.fnDraw();
                alert("Rulecode Added Sucessfully!");
            }
        });
    }
}

function submitVendorConfig(){

    var selectedPrefix = $("#prefix").val();
    var selectedVendorConfigArr =$("#vendorConfig > :input").find(":selected");
    var valuesOfVendorConfig = $("#vendorConfig > :text");
    var vendorConfigsAdded = {}
    for(var i=0;i<selectedVendorConfigArr.length;i++){
        vendorConfigsAdded[selectedVendorConfigArr[i].text] = valuesOfVendorConfig[i].value;
    }

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+selectedVendor+"/prefix/"+selectedPrefix+"/configs",
        data: JSON.stringify(vendorConfigsAdded),
        type: 'POST',
        contentType: 'application/json',
        error: function(request, status, error) {
            alert("Vendor Config addition Failed ! " + request.responseText);
        },
        success: function(data) {
            closeAddVendorConfigForm();
            alert("Vendor configs Added Sucessfully!");
        }
    });
}

function populateNewRulecodes() {

    var studioPayload = {
        vendorId: selectedVendor,
        iDisplayStart: 0
    };
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/rulecodes",
        data: studioPayload,
        type: 'GET',
        async: false,
        error: function(request, status, error) {
            alert("Unable to fetch rulecodes!");
        },
        success: function(data) {
            rulecodeTable.fnClearTable();
            rulecodeTable.fnAddData(data);
            rulecodeTable.fnDraw();
            closeCreateRulecodeForm();
        }
    });
}

function deleteRow(id) {

    var studioPayload = {
        vendorId: selectedVendor
    }

    var $row = $(this).closest('tr');
    var data = rulecodeTable.fnGetData($row[0]);
    if (typeof data === 'undefined')
        return;

    for (var i = 0; i < data.length; i++) {
        var rulecodeid = data[i].rule_code_name;
        rulecodeid = rulecodeid.replace('.', '_')
        if (id.localeCompare(rulecodeid) == 0) {
            //Call ajax
            $.ajax({
                url: contexturl + "/superadmin/rulecode-service/v1/rulecodes/" + id,
                data: JSON.stringify(studioPayload),
                type: 'DELETE',
                contentType: 'application/json',
                dataType: 'json',
                error: function(request, status, error) {
                    alert("Rulecode Delete Failed ! " + request.responseText);
                },
                success: function(data) {
                    alert("Rulecode Deleted Sucessfully!");
                    rulecodeTable.fnDeleteRow(data[i]);
                }
            });
            break;
        }
    }

}

function deleteRulecodeV2(id) {
    const studioPayload = {
        vendorId: selectedVendor
    }

    const $row = $(this).closest('tr');
    const data = rulecodeTable.fnGetData($row[0]);
    const ruleCodeId = id.replace('_', '.');
    const row = data.find(item => item.rule_code_name === ruleCodeId);
    debugger;
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/rulecodes_v2/" + id,
        data: JSON.stringify(studioPayload),
        type: 'DELETE',
        contentType: 'application/json',
        error: (err) => {
            alert("Rulecode Delete Failed ! " + err.responseText);
        },
        success: (data) => {
            rulecodeTable.fnDeleteRow(row);
            alert("Rulecode Deleted Sucessfully!");
        }
    });
}

function selectVendor(selectObject) {
    selectedVendor = selectObject.value;
    if(selectedVendor.trim() == ""){
        $('#remove').hide();
        $('#studioHomePage').hide();
        return;
    }
    $('#studioHomePage').show();
    if (rulecodeTable != null) {
        rulecodeTable.fnDestroy();
    }
    $('#sideNav').show();

    if(ishigherEnv()){
        $("#studioHomePage").find('button').attr("disabled", true);
        getConfigStatus();
    }
    else
        $('#remove').show();

    $('#studioPage').show();
    $('#studioHomeTab').click();
}

function selectVendorV2(selectObject) {
    selectedVendor = selectObject.value;
    if(selectedVendor.trim() == ""){
        $('#studio_v2_remove').hide();
        $('#studioV2HomePage').hide();
        return;
    }
    $('#studioV2HomePage').show();
    if (rulecodeTable != null) {
        rulecodeTable.fnDestroy();
    }
    $('#sideNav').show();

    if(ishigherEnv()){
        $("#studioV2HomePage").find('button').attr("disabled", true);
        getConfigStatus();
    }
    else
        $('#studio_v2_remove').show();

    $('#studioV2Page').show();
    $('#studioHomeTabV2').click();
}

function openAddVendorConfigForm(id) {
     getPrefixes(selectedVendor);
     removeOperatorConfigs();
     document.getElementById("add_vendor_config_container").style.display = "block";
}

function openCreateRuleCodeForm(id) {
     $("#operatorDesc").empty();
     $("#inputDesc").empty();
     $("#addOperatorConfigBtn").prop("disabled", false);

    if (id == '') {
        $('#rulecodeName').val(id);
        $('#rulecodeName').prop("disabled", false);
        $('#inputs').val('');
        $('#create_submit_btn').val('');
        $('#operator').val('default')
    } else {
        var splitStr = id.split('|||');

        $('#rulecodeName').val(splitStr[0].replace('_', '.'));
        $('#rulecodeName').prop("disabled", true);
        $('#inputs').val(splitStr[2]);
        $('#operator').val(splitStr[1]);


        if(splitStr[3].trim()){
            var alreadyAddedConfigs = splitStr[3].split(";");
            for(index in alreadyAddedConfigs){
                if(alreadyAddedConfigs[index]){
                    var configArr = alreadyAddedConfigs[index].split(":");
                    addConfigBox(configArr[0], configArr[1]);
                }
            }
        }
        $('#create_submit_btn').val(splitStr[0]);
    }

    document.getElementById("create_rulecode_container").style.display = "block";
}

function validateConfig() {
    const configText = $("#rulecodeConfigText").text();
    try {
        JSON.parse(configText);
        $("#configValidity").text("Valid JSON");
        $("#configValidity").css("color","green");
        $("#create_submit_btn_v2").prop("disabled", false);
    }
    catch(err) {
        $("#configValidity").text(`Invalid JSON - ${err}`);
        $("#configValidity").css("color", "red")
        $("#create_submit_btn_v2").prop("disabled", true);
    }
}

function openCreateRuleCodeFormV2(id) {
    debugger;
    if (id === "") {
        $('#rulecodeNameV2').val(id);
        $('#rulecodeNameV2').prop("disabled", false);
        $("#rulecodeConfigText").text("");
        $('#create_submit_btn_v2').val('');
    } else {
        const dataStr = $(id).attr("data");
        const data = JSON.parse(dataStr);
        $('#rulecodeNameV2').val(data.rule_code_name.replace('_', '.'));
        $('#rulecodeNameV2').prop("disabled", true);
        $("#rulecodeConfigText").text(dataStr);
        $('#create_submit_btn_v2').val(data.rule_code_name.replace('.', '_'));
    }
    document.getElementById("create_rulecode_container_v2").style.display = "block";
}

var count=0;
function addConfigBox(selected , inputValue){
    var configsAdded = $("#operatorConfig > :text");
    if(configsAdded.length <4){
        if(count>3)
            count = 0;
        var $operatorConfigDiv = $("#operatorConfig");
        var id = 'operatorConfigDD'+count;
        var selectStr = "<select id='"+id+"'>";
        var selectedIndex = 0;
        for (index in configs) {
            selectStr = selectStr + "<option>"+configs[index]+"</option>";
            if(configs[index]==selected)
                selectedIndex = index;
        }
        selectStr = selectStr+ "</select>";
        $operatorConfigDiv.append($(selectStr));
        $operatorConfigDiv.append($("<input type='text' value='"+inputValue+"' /> "));
        count++;

        document.getElementById(id).selectedIndex = selectedIndex;
    }
    else
        $('#addOperatorConfigBtn').attr("disabled",true);
}


function closeAddVendorConfigForm() {
    document.getElementById("add_vendor_config_container").style.display = "none";
    removeOperatorConfigs();
}

function closeCreateRulecodeForm() {
    document.getElementById("create_rulecode_container").style.display = "none";
    removeOperatorConfigs();
}

function closeCreateRulecodeFormV2() {
    document.getElementById("create_rulecode_container_v2").style.display = "none";
    $("#configValidity").text("");
}

function openCreateBulkRuleCodeForm(id) {
    $('#bulkFileName').val('');
    document.getElementById("create_rulecode_bulk_container").style.display = "block";
}

function openCreateBulkRuleCodeFormV2() {
    $('#bulkFileNameV2').val('');
    document.getElementById("create_rulecode_bulk_container_v2").style.display = "block";
}

function closeCreateBulkRulecodeForm() {
    document.getElementById("create_rulecode_bulk_container").style.display = "none";
}

function closeCreateBulkRulecodeFormV2() {
    document.getElementById("create_rulecode_bulk_container_v2").style.display = "none";
}

function bulkcreateRulecodes() {
    var file = $('#bulkFileName').val();

      if(file.trim() ==''){
        alert('Please enter fileName! ');
        return;
      }

      console.log(file);

    var bulkPayload = {
        vendorId: selectedVendor,
        filePath: file
    };
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/rulecodes/bulk",
        data: JSON.stringify(bulkPayload),
        dataType: 'json',
        contentType: 'application/json',
        type: 'PUT',
        error: function(request, status, error) {
            alert("Bulk Created Failed ! " + request.responseText);
             closeCreateBulkRulecodeForm();
        },
        success: function(data) {
             closeCreateBulkRulecodeForm();
             rulecodeTable.fnDraw();
             alert("Rulecodes Added/Modified Sucessfully!");
        }
    });
}

function bulkcreateRulecodesV2() {
    const filePath = $('#bulkFileNameV2').val();
    if(filePath.trim() ==''){
        alert('Please enter fileName! ');
        return;
    }
    console.log(filePath);
    const bulkPayload = {
        vendorId: selectedVendor,
        filePath
    };
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/rulecodes_v2/bulk",
        data: JSON.stringify(bulkPayload),
        dataType: 'json',
        contentType: 'application/json',
        type: 'PUT',
        error: (err) => {
            alert("Bulk rulecode creation failed ! " + err.responseText);
             closeCreateBulkRulecodeFormV2();
        },
        success: function() {
            closeCreateBulkRulecodeFormV2();
            rulecodeTable.fnDraw();
            alert("Rulecodes Added/Modified Sucessfully!");
        }
    });
}

function openVendorCreation() {
    $("#myForm").show();
    //document.getElementById("myForm").style.display = "block";
}

function closeForm() {
    document.getElementById("myForm").style.display = "none";
}

function createVendorSuccessCallback(vendorPayload) {
    var select = document.getElementById("vendors");
    select.add(new Option(vendorPayload.id + "-" + vendorPayload.name, vendorPayload.id));
}

function createVendorV2SuccessCallback(vendorPayload) {
    var select = document.getElementById("studio_v2_vendors");
    select.add(new Option(vendorPayload.id + "-" + vendorPayload.name, vendorPayload.id));
}

function performCreateVendorRequest(vendorPayload) {
    return $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor",
        type: 'POST',
        data: JSON.stringify(vendorPayload),
        contentType: "application/json"
    });
}

function createVendor() {

    if (!document.getElementById("vendorName") || document.getElementById("vendorName").value.trim().length == 0) {
        alert("Please enter vendor name");
        return;
    }

    if (!document.getElementById("vendorId") || document.getElementById("vendorId").value.trim().length == 0) {
        alert("Please enter vendor id");
        return;
    }

    const vendorPayload = {
        name: document.getElementById("vendorName").value.trim(),
        id: document.getElementById("vendorId").value.trim()
    }

    performCreateVendorRequest(vendorPayload).then(() => createVendorSuccessCallback(vendorPayload), () => alert('Vendor Id already exists !'));

    closeForm();
}

function createVendorV2() {
    if (!document.getElementById("vendorNameV2") || document.getElementById("vendorNameV2").value.trim().length == 0) {
        alert("Please enter vendor name");
        return;
    }
    if (!document.getElementById("vendorIdV2") || document.getElementById("vendorIdV2").value.trim().length == 0) {
        alert("Please enter vendor id");
        return;
    }
    const vendorPayload = {
        name: document.getElementById("vendorNameV2").value.trim(),
        id: document.getElementById("vendorIdV2").value.trim()
    }
    performCreateVendorRequest(vendorPayload).then(() => createVendorV2SuccessCallback(vendorPayload), () => alert('Vendor Id already exists !'));
    closeForm();
}

function getCreateRCPayload() {

    var rc = ($("#rulecodeName").val() || '').trim();
    if (rc == '' || !rc.includes(".")) {
        alert('Invalid Rulecode Name! ');
        return;
    }

    var inputStr = ($('#inputs').val() || '').trim();
    var inputArr = inputStr.split(",");

    var selectedOperatorConfigArr =$("#operatorConfig > :input").find(":selected");
    var valuesOfOperatorConfig = $("#operatorConfig > :text");
    var operatorConfigsAdded = {}
    for(var i=0;i<selectedOperatorConfigArr.length;i++){
        if(valuesOfOperatorConfig[i].value)
            operatorConfigsAdded[selectedOperatorConfigArr[i].text] = valuesOfOperatorConfig[i].value;
    }

    var payload = {
        name: rc,
        vendorId: selectedVendor,
        operator: ($('#operator').val() || '').trim(),
        inputs: inputArr,
        operatorConfigs: operatorConfigsAdded
    }

    return payload;

}

function getCreateRCPayloadV2(configJson) {
    const payload = {
        name: configJson.rule_code_name,
        vendorId: selectedVendor,
        type: configJson.type,
        defaultVal: configJson.default,
        operators: configJson.operators,
        computeOnEmptyLookup: configJson.computeOnEmptyLookup
    }
    return payload;
}

function removeOperatorConfigs(){
    var selectedOperatorConfigArr =$("select[id^='operatorConfigDD']");
    selectedOperatorConfigArr.remove();
    var valuesOfOperatorConfig = $("#operatorConfig > :text");
    valuesOfOperatorConfig.remove();
}

function fetchVendors(callbackFn) {
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendors",
        type: 'GET',
        async: false,
        error: function(e) {
            alert('Unable to fetch vendors. Please refresh the Page!');
        },
        success: callbackFn
    });
}

const studioSuccessCallback = function(data) {
    var select = document.getElementById("vendors");
    select.options.length = 0;
    select.add(new Option("Choose Vendor",""));
    for (index in data) {
        select.add(new Option(data[index].id + "-" + data[index].name, data[index].id));
    }
}

const studioV2SuccessCallback = function(data) {
    var select = document.getElementById("studio_v2_vendors");
    select.options.length = 0;
    select.add(new Option("Choose Vendor",""));
    for (index in data) {
        select.add(new Option(data[index].id + "-" + data[index].name, data[index].id));
    }
}

function getVendors() {
    fetchVendors(studioSuccessCallback)
}

function getStudioV2Vendors() {
    fetchVendors(studioV2SuccessCallback);
}

function getOperators() {

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/operators",
        type: 'GET',
        error: function(e) {
             alert('Unable to fetch Operators. Please refresh the Page!')
        },
        success: function(data) {
            var select = document.getElementById("operator");
            for (index in data) {
                var option = new Option(data[index].name)
                option.setAttribute("title",data[index].input.example);
                option.setAttribute("description" , data[index].description);
                option.setAttribute("format",data[index].input.format);
                select.add(option);
            }
        }
    });
}

function getGenericOperators() {
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/operators/generic",
        type: 'GET',
        error: function(e) {
             alert('Unable to fetch generic operators. Please refresh the Page!')
        },
        success: function(data) {
            console.log(data);
        }
    });
}

function getPrefixes(vendorId) {

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/"+vendorId+"/prefix",
        type: 'GET',
        error: function(e) {
             alert('Unable to fetch Prefixes. Please refresh the Page!')
        },
        success: function(data) {
            var select = document.getElementById("prefix");
            for (index in data) {
                var option = new Option(data[index])
                select.add(option);
            }

            select.selectedIndex=0;
        }
    });
}

var configs=[];
function getVendorConfigs() {

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/configs",
        type: 'GET',
        error: function(e) {
             alert('Unable to fetch Vendor Configs. Please refresh the Page!')
        },
        success: function(data) {
            for (index in data) {
                configs[index]=data[index].name;
            }
        }
    });
}

var count1=0;
function addOperatorConfig(){
    var configsAdded = $("#operatorConfig > :text");
    if(configsAdded.length <4){
        if(count1>3)
            count1 = 0;
        var $operatorConfigDiv = $("#operatorConfig");
        var id = 'operatorConfigDD'+count1;
        var selectStr = "<select id='"+id+"'>";
        for (index in configs) {
            selectStr = selectStr + "<option>"+configs[index]+"</option>";
        }
        selectStr = selectStr+ "</select>";
        $operatorConfigDiv.append($(selectStr));
        $operatorConfigDiv.append($("<input type='text' /> "));

        document.getElementById(id).selectedIndex = count1;
        count1++;
    }
    else
        $('#addOperatorConfigBtn').attr("disabled",true);
}

function addVendorConfig(){
    var configsAdded = $("#vendorConfig > :text");
    if(configsAdded.length <4){
        var $operatorConfigDiv = $("#vendorConfig");
        var id = 'vendorConfigDD'+count;
        var selectStr = "<select id='"+id+"'>";
        for (index in configs) {
            selectStr = selectStr + "<option>"+configs[index]+"</option>";
        }
        selectStr = selectStr+ "</select>";
        $operatorConfigDiv.append($(selectStr));
        $operatorConfigDiv.append($("<input type='text' /> "));

        document.getElementById(id).selectedIndex = count;
        count++;
    }
    else
        $('#addVendorConfigBtn').attr("disabled",true);
}


function selectOperator(selectedOperator){
    var example = selectedOperator.options[selectedOperator.selectedIndex].getAttribute("title")
    var desc = selectedOperator.options[selectedOperator.selectedIndex].getAttribute("description")
    var format = selectedOperator.options[selectedOperator.selectedIndex].getAttribute("format")
    $("#inputs").val(example);
    $("#operatorDesc").text(desc);
    $("#inputDesc").text(format);
}

function removeVendor() {
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/" + selectedVendor,
        type: 'DELETE',
        async: false,
        error: function(e) {
            console.log(e.responseText);
            alert('Unable to delete vendor. ' + e.responseText);
        },
        success: function(data) {
            alert('Vendor deleted successfully');
            $('#studioPage').hide();
            $('#remove').hide();
            getVendors();
        }
    });
}

function removeVendorV2() {
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/" + selectedVendor,
        type: 'DELETE',
        async: false,
        error: function(e) {
            alert('Unable to delete vendor. ' + e.responseText);
        },
        success: function(data) {
            alert('Vendor deleted successfully');
            $('#studioPageV2').hide();
            $('#studio_v2_remove').hide();
            getStudioV2Vendors();
        }
    });
}

function syncVendors(){

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/configs",
        type: 'PATCH',
        async: false,
        error: function(e) {
            alert(e.responseText);
        },
        success: function(data) {
            alert('Vendor configs fetched successfully');
            $('#studioPage').hide();
            $('#remove').hide();
            getVendors();
        }
    });

}

// pullVendorConfig
function syncVendorsV2(){
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/configs",
        type: 'PATCH',
        async: false,
        error: function(e) {
            alert(e.responseText);
        },
        success: function(data) {
            alert('Vendor configs fetched successfully');
            $('#studioPageV2').hide();
            $('#studremove').hide();
            getVendors();
        }
    });

}

function getConfigStatus(){
    if(ishigherEnv()){
        $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/configs/status?vendorId=" + selectedVendor,
            type: 'GET',
            error: function(e) {
                console.log(e.responseText);
                alert('Unable to get config Status! ' + e.responseText);
            },
            success: function(data) {
                if(data=='MODIFIED')
                  $("#pullConfigButton").attr("disabled",false);
            }
        });
    }
}

function pullConfig(){

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/configs?vendorId=" + selectedVendor,
        type: 'PATCH',
        contentType: "application/json",
        error: function(e) {
            alert("Unable to deploy new version! " + e.responseText);
        },
        success: function(data) {
            alert("Deployed New Version Successfully!");
            $("#pullConfigButton").attr("disabled",true);
        }
    });

}
