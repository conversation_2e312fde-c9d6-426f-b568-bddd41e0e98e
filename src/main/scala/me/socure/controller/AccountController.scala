package me.socure.controller

import me.socure.common.json.bean.APIOutput
import me.socure.resolver.AccountDetailsResolver
import me.socure.service.constants.IConstants
import me.socure.superadmin.factory.ObjectMapperFactory
import org.springframework.beans.factory.annotation.{Autowired, Qualifier}
import org.springframework.http.{HttpHeaders, HttpStatus, ResponseEntity}
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{PathVariable, RequestMapping, RequestMethod, RequestParam}

@Controller
@RequestMapping(Array("/superadmin/1/account_details/*"))
class AccountController {

  @Autowired
  @Qualifier("accountDetailsResolver")
  var accountDetailsResolver: AccountDetailsResolver = _

  @RequestMapping(value = Array("/consent_reason/{account_id}"), method = Array(RequestMethod.GET))
  def getConsentReason(@PathVariable("account_id") accountId: Long): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val consentId = accountDetailsResolver.getConsentReason(accountId.toLong)
    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
    apiOutput.setData(consentId)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  @RequestMapping(value = Array("/consent_reason/{account_id}/{consent_id}"), method = Array(RequestMethod.PUT))
  def updateConsentReason(@PathVariable("account_id") accountId: Long, @PathVariable("consent_id") consentId: Integer): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = accountDetailsResolver.updateConsentReason(accountId, consentId.toInt);
    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
    apiOutput.setData(result)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  @RequestMapping(value = Array("/reset_password/{email}"), method = Array(RequestMethod.GET))
  def resetPassword(@PathVariable("email") email: String): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = accountDetailsResolver.resetPassword(email);
    apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
    apiOutput.setData(result)
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

}
