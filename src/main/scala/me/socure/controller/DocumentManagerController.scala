package me.socure.controller

import java.util

import me.socure.common.json.bean.APIOutput
import me.socure.resolver.DocumentManageResolver
import me.socure.service.constants.IConstants
import me.socure.superadmin.factory.ObjectMapperFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.{HttpHeaders, HttpStatus, MediaType, ResponseEntity}
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{PathVariable, RequestBody, RequestMapping, RequestMethod}
import me.socure.document.{DtoAccountProvision, DtoDocType}
import me.socure.document.manager.model.doctype.{DocumentCategories, DocumentSubCategories, DocumentTags, DtoDocumentType, DtoDocumentTypeAccount, DtoProvisionAccount}
import java.util.{List => JList}
import scala.collection.JavaConverters._


@Controller
@RequestMapping(Array("/superadmin/1/document_manager/*"))
class DocumentManagerController {

  @Autowired
  var documentResolver : DocumentManageResolver = _


  @RequestMapping(value = Array("/types"), method = Array(RequestMethod.GET))
  def getDoctypes(): ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = documentResolver.listDocTypes()
    result match {
      case Right(docTypes) => {
        var docTypesasJava  = new util.ArrayList[DtoDocType]()
        docTypes.foreach(x => docTypesasJava.add(getDocTypeAsJava(x)));
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(docTypesasJava);
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  @RequestMapping(value = Array("/types/{accountPublicId}"), method = Array(RequestMethod.GET))
  def getProvisionDoctypes(@PathVariable("accountPublicId") accountPublicId : String) : ResponseEntity[Object] = {
    val apiOutput = new APIOutput()
    val result = documentResolver.listDocTypeByAccountPublicId(accountPublicId)
    result match {
      case Right(docTypes) => {
        var docTypesasJava = new util.ArrayList[DtoDocType]()
        docTypes.foreach(x => docTypesasJava.add(getDocTypeAsJava(x)));
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(docTypesasJava);
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  def scalaList2JavaList[A, B](scalaList: List[A])
                              (implicit a2bConversion: A => B): JList[B] =
    (scalaList map a2bConversion).asJava

  def getDocTypeAsJava(dtoDocumentType : DtoDocumentType) : DtoDocType = {
    val tags : List[Int] = List()
    new DtoDocType().setId(dtoDocumentType.id).setName(dtoDocumentType.name).setPublicId(dtoDocumentType.publicId)
      .setDescription(dtoDocumentType.description.getOrElse("")).setCategory(DocumentCategories.withId(dtoDocumentType.category).toString).setSubcategory(DocumentSubCategories.withId(dtoDocumentType.subCategory).toString)
      .setTags(scalaList2JavaList(dtoDocumentType.tags.getOrElse(tags).map(x => DocumentTags.withId(x).get.toString)))
  }

  def getDtoAccountProvision(dtoDocumentTypeAccount : DtoDocumentTypeAccount) : DtoAccountProvision = {
    new DtoAccountProvision().setId(dtoDocumentTypeAccount.id).setAccountPublicId(dtoDocumentTypeAccount.accountPublicId).setDocTypePublicId(dtoDocumentTypeAccount.docTypePublicId);
  }

  @RequestMapping(value = Array("types"), method = Array(RequestMethod.POST), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def createUpdateDocType(@RequestBody dtoDocType: DtoDocType) : ResponseEntity[Object] = {
    val description: Option[String] = if (dtoDocType.getDescription != null)  Option(dtoDocType.getDescription) else None
    val tags = if (dtoDocType.getTags != null) Option(dtoDocType.getTags.asScala.toList map{(x:java.lang.String ) => DocumentTags.withId(x).get.id}) else None
    val dtoDocumentType = DtoDocumentType(
          id=dtoDocType.getId,
          publicId = dtoDocType.getPublicId,
          name = dtoDocType.getName,
          description=  description,
          category = DocumentCategories.withId(dtoDocType.getCategory).id,
          subCategory = DocumentSubCategories.withId(dtoDocType.getSubCategory).id, tags=tags)
    val result = documentResolver.createDocumentType(dtoDocumentType)
    val apiOutput = new APIOutput()
    result match {
      case Right(doc) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(getDocTypeAsJava(doc));
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  @RequestMapping(value = Array("types/provision"), method = Array(RequestMethod.POST), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def provisionAccount(@RequestBody dtoAccountProvision: DtoAccountProvision) : ResponseEntity[Object] = {
    val dtoProvisionAccount = DtoProvisionAccount(dtoAccountProvision.getDocTypePublicId, dtoAccountProvision.getAccountPublicId)
    val result = documentResolver.provisionDocumentType(dtoProvisionAccount)
    val apiOutput = new APIOutput()
    result match {
      case Right(doc) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(getDtoAccountProvision(doc))
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)

  }

  @RequestMapping(value = Array("types/provision/{accountPublicId}"), method = Array(RequestMethod.POST))
  def provisionAllDocuments(@PathVariable("accountPublicId") accountPublicId : String) : ResponseEntity[Object] = {
    val result = documentResolver.provisionAllDocumentType(accountPublicId)
    val apiOutput = new APIOutput()
    result match {
      case Right(doc) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(doc);
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }

  @RequestMapping(value = Array("types/deprovision"), method = Array(RequestMethod.POST), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def deprovisionAccount(@RequestBody dtoAccountProvision: DtoAccountProvision) : ResponseEntity[Object] = {
    val dtoProvisionAccount = DtoProvisionAccount(dtoAccountProvision.getDocTypePublicId, dtoAccountProvision.getAccountPublicId)
    val result = documentResolver.deProvisionDocumentType(dtoProvisionAccount)
    val apiOutput = new APIOutput()
    result match {
      case Right(doc) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
        apiOutput.setData(doc);
      }
      case Left(error) => {
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
        apiOutput.setData(error.code)
        apiOutput.setMsg(error.message)
      }
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)

  }


}
