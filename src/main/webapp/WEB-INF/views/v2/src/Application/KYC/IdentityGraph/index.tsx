import { But<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import React, { useState } from "react";

const validURLRegex = /^v2\/kyc\/identity-graph\/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?$/

const validateGetTransactionID = (referrer: string): string | undefined => {
    const validated = validURLRegex.exec(referrer.split("/").slice(3).join("/"));
    if (validated) {
        return validated[1];
    } else {
        return undefined;
    }
}

const IdentityGraph: React.FC<any> = (props: any) => {
    const transactionID = validateGetTransactionID(window.location.href);
    const [txnID, setTxnID] = useState<string>(transactionID || "");
    return <div key="id-graph">
        <Grid container spacing={1} direction="row" key={"toolkit"}>
            <Grid item key="text-box">
                <TextField
                    sx={{ minWidth: "25vw"}}
                    label="Transaction ID"
                    value={txnID}
                    disabled={transactionID !== undefined}
                    size="small"
                    onChange={e => setTxnID(e.target.value)}
                />
            </Grid>
            <Grid item key="submit">
                <Button variant="contained" disabled={transactionID !== undefined} onClick={() => { window.location.href = `/v2/kyc/identity-graph/${txnID}`; }}>{"Submit"}</Button>
            </Grid>
            <Grid item key="clear">
                <Button variant="contained" color="error" disabled={transactionID === undefined} onClick={() => { window.location.href = `/v2/kyc/identity-graph`; }}>{"Clear"}</Button>
            </Grid>
        </Grid>
        {
            transactionID &&
            <iframe
                src="/identity_graph"
                title="Identity Graph"
                style={{
                    width: "99vw",
                    height: "86vh",
                    border: "none"
                }}
            />
        }
    </div>;
}

export default IdentityGraph;