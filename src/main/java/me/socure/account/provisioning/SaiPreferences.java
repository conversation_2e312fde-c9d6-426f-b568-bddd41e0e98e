package me.socure.account.provisioning;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Builder
@Data
@Jacksonized
public class SaiPreferences {
	private String memo;
	private String depositorName;
	private String physicalAddress;
	private String city;
	private String state;
	private String zip;
	private String country;

	public String getMemo() {
		return memo;
	}

	public String getDepositorName() {
		return depositorName;
	}

	public String getPhysicalAddress() {
		return physicalAddress;
	}

	public String getCity() {
		return city;
	}

	public String getState() {
		return state;
	}

	public String getZip() {
		return zip;
	}

	public String getCountry() {
		return country;
	}
}