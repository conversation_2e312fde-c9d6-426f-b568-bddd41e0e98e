package me.socure.controller

import me.socure.sftp.{CreateSFTPUserDto, SFTPResolver}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.{GetMapping, PostMapping, RequestBody, RequestMapping}
import me.socure.common.json.bean.APIOutput
import me.socure.model.account.AccountSftpUser
import me.socure.service.constants.IConstants
import me.socure.superadmin.factory.ObjectMapperFactory
import org.springframework.http.{HttpHeaders, HttpStatus, MediaType, ResponseEntity}
import java.util

@Controller
@RequestMapping(Array("/sftp/*"))
class SFTPController {

  @Autowired
  private var SFTPResolver: SFTPResolver = _

  @GetMapping(Array("/users"))
  def listSFTPUsers(): ResponseEntity[Object] = {

    val apiOutput = new APIOutput

    SFTPResolver.listSFTPUsers match {
      case Right(response) =>
        var usersAsJava = new util.ArrayList[AccountSftpUser]()
        response.foreach(user => usersAsJava.add(user))
        apiOutput.setData(usersAsJava)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
      case Left(errorResponse) =>
        apiOutput.setMsg(errorResponse)
        apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
    }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](apiOutput, headers, HttpStatus.OK)
  }

  @PostMapping(value = Array("/user"), consumes = Array(MediaType.APPLICATION_JSON_VALUE))
  def createSFTPUser(@RequestBody payload: CreateSFTPUserDto): ResponseEntity[Object] = {
    val apiOutput = new APIOutput
   SFTPResolver.createSFTPUser(payload.getAccountId,
                               payload.getPublicAccountId,
                               payload.getUsername,
                               payload.getSshKey) match {
     case Right(response) =>
       apiOutput.setData(response)
       apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_OK)
     case Left(errorResponse) =>
       apiOutput.setMsg(errorResponse.message)
       apiOutput.setStatus(IConstants.API_OUTPUT_STATUS_ERROR)
   }
    val headers = new HttpHeaders()
    headers.add("Content-Type", "application/json")
    new ResponseEntity[Object](ObjectMapperFactory.get().writeValueAsString(apiOutput), headers, HttpStatus.OK)
  }
}
