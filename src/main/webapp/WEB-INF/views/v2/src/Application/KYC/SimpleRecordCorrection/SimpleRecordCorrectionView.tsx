import React, { Fragment, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, Checkbox, Divider, FormControl, FormControlLabel, FormGroup, Grid, InputLabel, LinearProgress, List, ListItem, ListItemAvatar, ListItemText, ListSubheader, MenuItem, Select, Step, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Text<PERSON>ield, Typography } from "@mui/material";
import steps from "./steps.json"
import { BorderRight, Check as CheckIcon, Close as CloseIcon, GppBad as GppBadIcon } from "@mui/icons-material";
import PiiEditor from "./PiiEditor";
import GitDiffViewerJson from "./GitDiffViewerJson";
import { replaceWithVendorShortCode } from "./utilities";

export interface ISimpleRecordCorrectionViewProps {
    step: number;
    setStep: React.Dispatch<React.SetStateAction<number>>;
    securityKey: string;
    setSecurityKey: (e: any) => void;
    id: string;
    account: number;
    setId: React.Dispatch<React.SetStateAction<string>>;
    setAccount: React.Dispatch<React.SetStateAction<number>>;
    idType: number;
    setIdType: React.Dispatch<React.SetStateAction<number>>;
    nextButtonDisabled: boolean;
    configuration: null | { equifax: any, enformion: any };
    onNextButtonClick: () => Promise<void>;
    clusterList: null | { editable: Array<string>, uneditable: Array<string>, socureID: String };
    description: string;
    socureTicket: string;
    vendorTicket: string;
    setDescription: React.Dispatch<React.SetStateAction<string>>;
    setSocureTicket: React.Dispatch<React.SetStateAction<string>>;
    setVendorTicket: React.Dispatch<React.SetStateAction<string>>;
    shouldUpdateDeceased: boolean;
    setShouldUpdateDecease: React.Dispatch<React.SetStateAction<boolean>>;
    shouldUpdateSource: boolean;
    setShouldUpdateSource: React.Dispatch<React.SetStateAction<boolean>>;
    fields: Array<{ display: string, value: string }> | undefined;
    field: string;
    setField: React.Dispatch<React.SetStateAction<string>>;
    socureID: string;
    editingData: any;
    setEditingData: React.Dispatch<React.SetStateAction<any>>;
    back: () => void;
    backDisabled: boolean;
}



const SimpleRecordCorrectionView: React.FC<ISimpleRecordCorrectionViewProps> = ({
    step, setStep, securityKey, setSecurityKey, id, setId, account, setAccount,
    idType, setIdType, nextButtonDisabled, configuration, onNextButtonClick,
    clusterList, description, socureTicket, vendorTicket, setDescription,
    setSocureTicket, setVendorTicket, shouldUpdateDeceased, setShouldUpdateDecease,
    shouldUpdateSource, setShouldUpdateSource, fields, field, setField, socureID,
    editingData, setEditingData, backDisabled, back
}: ISimpleRecordCorrectionViewProps) => {

    const renderStepper = useMemo(() => {
        return <Stepper activeStep={step}>
            {steps.map(({ name }, i) => {
                return <Step key={name} completed={step > i}>
                    <StepLabel>{name}</StepLabel>
                </Step>;
            })}
        </Stepper>;
    }, [step]);


    const renderAccount = useMemo(() => {
        return <Grid item key="account" sm={3}>
            <TextField
                sx={{ width: "30vw" }}
                label="Account"
                value={account}
                onChange={e => {
                    const v = e.target.value;
                    if (v) {
                        setAccount(Math.round(parseInt(v)));
                    } else {
                        setAccount(0);
                    }
                }}
                size="small"
            />
        </Grid>;
    }, [account, setAccount]);

    const renderStepOne = useMemo(() => {
        return <Grid
            container
            sx={{ height: "67vh" }}
            direction="column"
            spacing={3}
            justifyContent="center"
            alignItems="center"
        >
            <Grid item key="security-key">
                <TextField
                    type="password"
                    sx={{ width: "30vw" }}
                    size="small"
                    label="Security Key"
                    required
                    value={securityKey}
                    onChange={setSecurityKey}
                />
            </Grid>
            <Grid item key="id-type">
                <FormControl fullWidth>
                    <InputLabel size="small" id="kyc-es-record-correction-id-type-selector-label">
                        {"Type"}
                    </InputLabel>
                    <Select
                        labelId="kyc-es-record-correction-id-type-selector-label"
                        fullWidth
                        value={idType}
                        onChange={e => setIdType(e.target.value as number)}
                        label="Type"
                        size="small"
                        sx={{ width: "30vw" }}
                    >
                        <MenuItem value={1} key={1}>{"Transaction ID"}</MenuItem>
                        <MenuItem value={3} key={3}>{"Account Specific Socure ID (Default Socure ID)"}</MenuItem>
                        <MenuItem value={2} key={2}>{"Universal Socure ID"}</MenuItem>
                    </Select>
                </FormControl>
            </Grid>
            <Grid item key="id">
                <TextField
                    sx={{ width: "30vw" }}
                    size="small"
                    label="ID"
                    required
                    value={id}
                    onChange={e => setId(e.target.value)}
                />
            </Grid>
            {idType === 3 ? renderAccount : null}
        </Grid>;
    }, [
        securityKey, setSecurityKey, idType, setIdType, id, setId,
        renderAccount
    ]);

    const renderDescription = useMemo(() => {
        return <Grid item key="description">
            <TextField
                label="Description"
                fullWidth
                size="small"
                multiline
                required
                rows={10}
                value={description}
                onChange={e => setDescription(e.target.value)}
            />
        </Grid>;
    }, [setDescription, description]);

    const renderSocureID = useMemo(() => {
        return <Grid item key="socure-id">
            <TextField
                label="Socure ID"
                fullWidth
                disabled
                size="small"
                value={socureID}
            />
        </Grid>;
    }, [socureID]);

    const renderSocureTicket = useMemo(() => {
        return <Grid item key="socure-ticket">
            <TextField
                label="Socure Ticket (Optional)"
                fullWidth
                size="small"
                value={socureTicket}
                onChange={e => setSocureTicket(e.target.value)}
            />
        </Grid>;
    }, [setSocureTicket, socureTicket]);

    const renderVendorTicket = useMemo(() => {
        return <Grid item key="vendor-ticket">
            <TextField
                label="Vendor Ticket (Optional)"
                fullWidth
                size="small"
                value={vendorTicket}
                onChange={e => setVendorTicket(e.target.value)}
            />
        </Grid>;
    }, [setVendorTicket, vendorTicket]);

    const renderUpdateFieldSelection = useMemo(() => {
        return <Grid item key="update-fields">
            <FormGroup>
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={shouldUpdateDeceased}
                            onChange={e => setShouldUpdateDecease(e.target.checked)} />
                    }
                    label="Modify deceased status"
                />
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={shouldUpdateSource}
                            onChange={e => setShouldUpdateSource(e.target.checked)}
                        />
                    }
                    label="Modify PII"
                />
            </FormGroup>
        </Grid>;
    }, [shouldUpdateDeceased, setShouldUpdateDecease, shouldUpdateSource, setShouldUpdateSource]);

    const renderStepTwo = useMemo(() => {
        if (step === 1 && clusterList !== null) {
            return <Grid sx={{ minHeight: "67vh" }} container direction="row" spacing={2}>
                <Grid item sm={3} key="record-list">
                    <Grid container direction="column" spacing={2}>
                        <Grid item key="editable">
                            <Card sx={{ maxHeight: "33vh", overflow: "scroll" }} variant="outlined"  >
                                <List
                                    subheader={<ListSubheader>{`Editable Records (${clusterList.editable.length})`}</ListSubheader>}
                                >
                                    {clusterList.editable.map(_ =>
                                        <ListItem key={_}>
                                            <ListItemAvatar>
                                                <CheckIcon color="success" />
                                            </ListItemAvatar>
                                            <ListItemText primary={replaceWithVendorShortCode(_)} />
                                        </ListItem>
                                    )}
                                </List>
                            </Card>
                        </Grid>
                        <Grid item key="uneditable">
                            <Card sx={{ maxHeight: "33vh", overflow: "scroll" }} variant="outlined"  >
                                <List
                                    subheader={<ListSubheader>{`Uneditable Records (${clusterList.uneditable.length})`}</ListSubheader>}
                                >
                                    {clusterList.uneditable.map(_ =>
                                        <ListItem key={_}>
                                            <ListItemAvatar>
                                                <CloseIcon color="error" />
                                            </ListItemAvatar>
                                            <ListItemText primary={replaceWithVendorShortCode(_)} />
                                        </ListItem>
                                    )}
                                </List>
                            </Card>
                        </Grid>
                    </Grid>
                </Grid>
                <Grid item sm={6} key="details">
                    <Grid container direction="column" spacing={1}>
                        {renderSocureID}
                        {renderDescription}
                        {renderSocureTicket}
                        {renderVendorTicket}
                        {renderUpdateFieldSelection}
                    </Grid>
                </Grid>
            </Grid>

        }
        return null;
    }, [
        clusterList, renderDescription, renderSocureTicket, renderVendorTicket,
        renderUpdateFieldSelection, renderSocureID, step
    ]);

    const rendorPiiEditor = useMemo(() => {
        if (step === 2 && editingData) {
            return <PiiEditor
                editingData={editingData}
                setEditingData={setEditingData}
                field={field}
            />;
        }
        return null;
    }, [editingData, step, field, setEditingData])

    const renderStepThree = useMemo(() => {
        if (step === 2 && fields !== undefined) {
            return <Grid container spacing={1} direction="column">
                <Grid item key="pii-editor-selector">
                    <FormControl>
                        <InputLabel id="pii-editor-selector-label">{"PII To Edit"}</InputLabel>
                        <Select
                            labelId="pii-editor-selector-label"
                            value={field}
                            label="PII To Edit"
                            size="small"
                            onChange={e => setField(e.target.value)}
                        >
                            {fields.map((_) => {
                                return <MenuItem value={_.value} key={_.value}>
                                    {_.display}
                                </MenuItem>
                            })}
                        </Select>
                    </FormControl>
                </Grid>
                <Grid sx={{ maxHeight: "63vh", overflow: "scroll" }} item key="pii-editor">
                    {rendorPiiEditor}
                </Grid>
            </Grid>
        }
        return null;
    }, [step, fields, field, setField, rendorPiiEditor]);

    const renderStepFour = useMemo(() => {
        if (step !== 3) return null;
        const { retrievedData, updatedRecordCount } = editingData;
        return <Box sx={{ maxHeight: "68vh", overflow: "scroll" }}>
            <Grid container direction="column" spacing="15px">
                {
                    updatedRecordCount === 0 &&
                    <Grid item key="alert">
                        <Alert icon={<GppBadIcon fontSize="inherit" />} severity="error">
                            {"No valid updation to record has been specified."}
                        </Alert>
                    </Grid>
                }
                <Grid item key="Legend">
                    <Grid container direction="row">
                        <Grid item key="Title">
                            <Typography>Legend &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</Typography>
                        </Grid>
                        <Grid sm={0.12} item key="dark-green">
                            <Box
                                style={{
                                    backgroundColor: "rgb(187, 246, 193)",
                                    border: "1px solid black"
                                }}
                            >
                                &nbsp;&nbsp;&nbsp;&nbsp;
                            </Box>
                        </Grid>
                        <Grid sm={0.12} item key="light-green">
                            <Box
                                style={{
                                    backgroundColor: "rgb(235, 254, 238)",
                                    border: "1px solid black",
                                    borderLeft: "none"
                                }}
                            >
                                &nbsp;&nbsp;&nbsp;&nbsp;
                            </Box>
                        </Grid>
                        <Grid item key="green-label">
                            <Typography variant="caption">
                                &nbsp;&nbsp;Added Data&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            </Typography>
                        </Grid>
                        <Grid sm={0.12} item key="dark-red">
                            <Box
                                style={{
                                    backgroundColor: "rgb(243, 187, 193",
                                    border: "1px solid black"
                                }}
                            >
                                &nbsp;&nbsp;&nbsp;&nbsp;
                            </Box>
                        </Grid>
                        <Grid sm={0.12} item key="light-red">
                            <Box
                                style={{
                                    backgroundColor: "rgb(252, 239, 240)",
                                    border: "1px solid black",
                                    borderLeft: "none"
                                }}
                            >
                                &nbsp;&nbsp;&nbsp;&nbsp;
                            </Box>
                        </Grid>
                        <Grid item key="red-label">
                            <Typography variant="caption">
                                &nbsp;&nbsp;Removed Data&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            </Typography>
                        </Grid>
                    </Grid>
                </Grid>
                {retrievedData.map((d: any) => {
                    const {
                        type,
                        result: {
                            attributes,
                            updatedAttributes,
                            clusterId
                        }
                    } = d;
                    const filename = `${type}/${clusterId}`;
                    return <Grid item key={filename}>
                        <GitDiffViewerJson
                            filename={filename}
                            oldJson={attributes}
                            newJson={updatedAttributes}
                        />
                    </Grid>
                })}
            </Grid>
        </Box>
    }, [step, editingData])

    const renderStepFive = useMemo(() => {
        if (step !== 4) return null;
        const { finalUpdationData } = editingData;
        return <Grid container direction="column" spacing="10px">
            {finalUpdationData.map((d: any, idx: any) => {
                return <Grid item key={idx}>
                    <Alert severity={d.success ? "success" : "error"}>
                        {replaceWithVendorShortCode(d.text)}
                    </Alert>
                </Grid>;
            })}
        </Grid>
    }, [step, editingData])

    const renderStepContents = useMemo(() => {
        if (step === 0) {
            return renderStepOne;
        } else if (step === 1) {
            return renderStepTwo;
        } else if (step === 2) {
            return renderStepThree;
        } else if (step === 3) {
            return renderStepFour;
        } else if (step === 4) {
            return renderStepFive;
        }
        return null;
    }, [step, renderStepOne, renderStepTwo, renderStepThree, renderStepFour, renderStepFive]);

    const renderActions = useMemo(() => {
        return <Grid container spacing={1} direction="row">
            <Grid item sm={1.5} key="next">
                <Button
                    disabled={nextButtonDisabled}
                    onClick={onNextButtonClick}
                    variant="contained"
                    fullWidth
                >
                    {step === 3 ? "Finish" : "Next"}
                </Button>
            </Grid>
            <Grid item sm={1.5} key="back">
                <Button
                    disabled={backDisabled}
                    onClick={back}
                    variant="contained"
                    fullWidth
                    color="warning"
                >
                    {"Back"}
                </Button>
            </Grid>
            <Grid item sm={1.5} key="start-over">
                <Button
                    onClick={() => window.location.reload()}
                    variant="contained"
                    color="error"
                    fullWidth
                >
                    {"Start Over"}
                </Button>
            </Grid>
        </Grid>;
    }, [nextButtonDisabled, onNextButtonClick, back, backDisabled, step]);

    if (configuration === null) {
        return <LinearProgress />
    }

    return <Fragment>
        <Grid container spacing={2} direction="column">
            <Grid item key="stepper">
                {renderStepper}
            </Grid>
            <Grid sx={{ minHeight: "68vh" }} item key="step-contents">
                {renderStepContents}
            </Grid>
            <Grid item key="divider">
                <Divider />
            </Grid>
            <Grid item key="actions">
                {renderActions}
            </Grid>
        </Grid>
    </Fragment>;
}

export default SimpleRecordCorrectionView;