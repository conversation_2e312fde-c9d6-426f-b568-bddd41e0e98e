var selectedTestVendor;
var totalTestJobPages;
var selectedTestPage;


function openTestWindow() {
    if (selectedTestVendor) {
        populateTests(1);
    } else {
        alert("Please select a vendor");
    }
}

function populateTests(page) {
    selectedTestPage = page;
    var testsHTML = "";
    $('#testResult').children().remove();
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/rulecodes/tests?vendorId=" + selectedTestVendor + "&page=" + page,
        type: 'GET',
        error: function(e) {
            console.log(e);
        },
        success: function(data) {
            $("#testStatus").empty();
            $("#testStatus").text("Current Config is Not Tested");
            $.each(data.ruleCodeTestJobList, function(i, item) {

                if(data.ruleCodeTestJobList[i].jobId != null){
                    if (data.ruleCodeTestJobList[i].status == "SUCCEEDED") {
                        testsHTML += "<tr><td>" + data.ruleCodeTestJobList[i].jobId + "</td><td>" + data.ruleCodeTestJobList[i].file + "</td><td>" + data.ruleCodeTestJobList[i].status + "</td><td>" + data.ruleCodeTestJobList[i].triggeredBy + "</td><td>" + moment.utc(data.ruleCodeTestJobList[i].triggeredAt).local().format('YYYY-MM-DD HH:mm:ss') + "</td>" +
                            "<td><input type=\"button\" value=\"View Results\" class=\"pad\" onClick=\"getJobResult(\'" + data.ruleCodeTestJobList[i].jobId + "\')\" /></td>" +
                            "<td><input type=\"button\" value=\"Download Results\" class=\"pad\" onClick=\"downloadJobResult(\'" + data.ruleCodeTestJobList[i].jobId + "\')\" /></td></tr>";
                    } else if (data.ruleCodeTestJobList[i].status == "FAILED") {
                        testsHTML += "<tr><td>" + data.ruleCodeTestJobList[i].jobId + "</td><td>" + data.ruleCodeTestJobList[i].file + "</td><td>" + data.ruleCodeTestJobList[i].status + "</td><td>" + data.ruleCodeTestJobList[i].triggeredBy + "</td><td>" + moment.utc(data.ruleCodeTestJobList[i].triggeredAt).local().format('YYYY-MM-DD HH:mm:ss') + "</td></tr>";
                    } else {
                        testsHTML += "<tr><td>" + data.ruleCodeTestJobList[i].jobId + "</td><td>" + data.ruleCodeTestJobList[i].file + "</td><td>" + data.ruleCodeTestJobList[i].status + "</td><td>" + data.ruleCodeTestJobList[i].triggeredBy + "</td><td>" + moment.utc(data.ruleCodeTestJobList[i].triggeredAt).local().format('YYYY-MM-DD HH:mm:ss') + "</td>" +
                            "<td><input type=\"button\" value=\"Refresh status\" class=\"pad\" onClick=\"getStatusofTestJob(\'" + data.ruleCodeTestJobList[i].jobId + "\')\" /></td></tr>";
                    }
                } else {
                    testsHTML += "<tr><td>Yet to be created</td><td>" + data.ruleCodeTestJobList[i].file + "</td><td>" + data.ruleCodeTestJobList[i].status + "</td><td>" + data.ruleCodeTestJobList[i].triggeredBy + "</td><td>" + moment.utc(data.ruleCodeTestJobList[i].triggeredAt).local().format('YYYY-MM-DD HH:mm:ss') + "</td>" +
                    "<td><input type=\"button\" value=\"Refresh status\" class=\"pad\" onClick=\"populateTests(\'" + selectedTestPage + "\')\" /></td></tr>";
                }
            });
            $('#testResult').append(testsHTML);
            totalTestJobPages = data.totalPages;

            $('#testpage').children().remove();
            var testpageselect = document.getElementById("testpage");
            for (var i = 1; i <= totalTestJobPages; i++) {
                testpageselect.add(new Option(i, i));
            }

            testpageselect.style.display = "block";
            document.getElementById("testwindow").style.display = "block";

             var commitButton = document.getElementById("commitButton");
            if(!ishigherEnv() && data.enableCommit){
                commitButton.style.display = "block";
                $("#testStatus").empty();
                $("#testStatus").text("Current Config is Tested");
            }
            else
                commitButton.style.display = "none";

        }
    });
}

function getStatusofTestJob(jobId) {
    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/rulecodes/tests/" + jobId + "/refresh?vendorId=" + selectedTestVendor,
        type: 'PATCH',
        error: function(e) {
            console.log(e);
        },
        success: function(data) {
            populateTests(selectedTestPage);
        }
    });
}

function getJobResult(jobId) {

    $('#jobResult').children().remove();
    $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/rulecodes/tests/" + jobId + "/result?vendorId=" + selectedTestVendor,
            type: 'GET',
            error: function(e) {
                console.log(e);
            },
            success: function(data) {
                var result = JSON.parse(data);
                var jobresultHTML = "";
                    $.each(result.report, function(i, item) {
                        jobresultHTML += "<tr><td onClick=\"openDetails('" + item.customerUserId + "')\">+</td><td>" + item.customerUserId + "</td><td>" + item.status + "</td></tr>";
                        if (item.summary) {
                            jobresultHTML += "<tr><td style=\"display:none\" id=\"" + item.customerUserId + "\" colspan=\"3\"><p>";
                            if (item.summary.ruleCodes) {
                                $.each(item.summary.ruleCodes, function(i, rulecode) {
                                    jobresultHTML += "RULECODE : " + rulecode.code + "&emsp; ACTUAL SCORE : " + rulecode.actualScore + "&emsp; EXPECTED SCORE : " + rulecode.expectedScore + "&emsp; STATUS : " + rulecode.status + "<br/>";
                                });
                            }
                            if (item.summary.decision) {
                                jobresultHTML += "ACTUAL DECISION : " + item.summary.decision.actual + "&emsp; EXPECTED DESCION : " + item.summary.decision.expected + "&emsp; STATUS : " + item.summary.decision.status + "<br/>";
                            }
                            if (item.summary.reasonCodes) {
                                $.each(item.summary.reasonCodes, function(i, reasoncode) {
                                    jobresultHTML += "REASONCODE : " + reasoncode.code + "&emsp; STATUS : " + reasoncode.status + "<br/>";
                                });
                            }
                            jobresultHTML += "</p></td></tr>";
                        }

                    });
                    $('#jobResult').append(jobresultHTML);

                    document.getElementById("testwindow").style.display = "none";
                    document.getElementById("jobwindow").style.display = "block";
            }
        });
}

function downloadJobResult(jobId) {

    $('#jobResult').children().remove();
    $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/rulecodes/tests/" + jobId + "/result?vendorId=" + selectedTestVendor,
            type: 'GET',
            error: function(e) {
                console.log(e);
            },
            success: function(data) {
              var contentType = "application/json;charset=utf-8;"
              var a = document.createElement('a');
              a.download = jobId + ".json";
              a.href = 'data:' + contentType + ',' + encodeURIComponent(data);
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            }
        });
}

function closeJobWindow() {
    document.getElementById("testwindow").style.display = "block";
    document.getElementById("jobwindow").style.display = "none";
}

function openDetails(custUserId) {
    if (document.getElementById(custUserId).style.display == "table-cell") {
        document.getElementById(custUserId).style.display = "none";
    } else {
        document.getElementById(custUserId).style.display = "table-cell";
    }
}

function submitTestJob() {
    if (!selectedTestVendor) {
        alert("Please select a vendor");
    } else if (!document.getElementById("testFilePath") || document.getElementById("testFilePath").value.trim().length == 0) {
        alert("Please give a proper file path");
    } else {
        var payload = {
            file: document.getElementById("testFilePath").value.trim(),
            vendorId: selectedTestVendor
        }

        $.ajax({
            url: contexturl + "/superadmin/rulecode-service/v1/rulecodes/tests",
            type: 'POST',
            data: JSON.stringify(payload),
            contentType: "application/json",
            error: function(e) {
                console.log(e);
            },
            success: function(data) {
                populateTests(1);
                document.getElementById("testFilePath").value = "";
            }
        });
    }
}

function syncRuleCodes(type){

    var payload = {
        "action" : type,
        "vendorId" : selectedTestVendor
    };

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/sync",
        type: 'PATCH',
        data: JSON.stringify(payload),
        contentType: "application/json",
        error: function(e) {
            alert(type + " is unsuccessful");
        },
        success: function(data) {
            alert(type + " is successful");
            populateTests(1);
        }
    });

}

function selectTestVendor(selectObject) {
    selectedTestVendor = selectObject.value;
    if(selectedTestVendor.trim() == ""){
        $('#testPage').hide();
        return;
    }
    $('#testPage').show();
    openTestWindow();
}

function getTestVendors() {

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendors",
        type: 'GET',
        async: false,
        error: function(e) {
            console.log(e);
        },
        success: function(data) {
            var select = document.getElementById("testVendors");
            select.options.length = 0;
            select.add(new Option("Choose Vendor",""));
            for (index in data) {
                select.add(new Option(data[index].id + "-" + data[index].name, data[index].id));
            }
        }
    });
}

function syncVendorsTestTab(){

    $.ajax({
        url: contexturl + "/superadmin/rulecode-service/v1/vendor/configs",
        type: 'PATCH',
        async: false,
        error: function(e) {
            alert(e.responseText);
        },
        success: function(data) {
            alert('Vendor configs fetched successfully');
            $('#testPage').hide();
            getTestVendors();
        }
    });

}
