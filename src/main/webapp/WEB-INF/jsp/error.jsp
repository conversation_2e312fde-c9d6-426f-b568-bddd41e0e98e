<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<title>Socure</title>
	
	<base href="https://s3.amazonaws.com/static.socure.com/">
	
	<link rel="stylesheet" type="text/css" href="http://fonts.googleapis.com/css?family=Droid+Sans:400,700">
	<link rel="stylesheet" type="text/css" href="styles/error.css">
	<link rel="shortcut icon" href="#{bucket.resourceURL}/favicon.ico">
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
</head>
<body>

	<section id="error404">
		<div id="logo">
			<h1><a href="/">Socure</a></h1>
		</div>
		<h2>We're sorry&hellip;</h2>
		<p id="error-message">Sorry but an unexpected error has occured. Our engineers have been notified.</p>
		<p><a href="/">&laquo; Return to homepage</a></p>
	</section>


	
	<script type="text/javascript">
		var _gaq = _gaq || [];
		_gaq.push(['_setAccount', 'UA-********-1']);
		_gaq.push(['_setDomainName', 'socure.com']);
		_gaq.push(['_setAllowLinker', true]);
		_gaq.push(['_trackPageview']);

		(function() {
			var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
			ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
			var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
		})();
	</script>
	

	<script src="js/vendor/purl.js"></script>
	<script src="js/error.js"></script>

</body>
</html>