<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
>
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>Invite primary user to selected account</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>DataTables live example</title>
	<link rel="shortcut icon" type="image/ico"
		href="https://www.datatables.net/favicon.ico" />
	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

	<link rel="stylesheet"
		href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		media="print" />
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

	<script>
		var bucketurl = "#{bucket.resourceURL}";
		var contexturl = "#{request.contextPath}";
		var marketingurl = "#{bucket.marketingSite}";
		var appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>

	<style>
		#AddDelegatedAdminModal{
			margin: 2% 20%;
			border: 1px solid #ccc;
			border-radius: 8px;
		}
		#AddDelegatedAdminModal select,#AddDelegatedAdminModal input{
			margin: auto 10px;
			float: right;
		}
		#AddDelegatedAdminModal label{
			font-weight: bold;
			/* width: 112px; */
			display: inline;
			text-align: right;
		}
		#AddDelegatedAdminModal table td{
			text-align: left;
		}
	</style>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px;">
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>

	<div id="MessageModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Delegated Admin</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
			</div>
		</div>
	</div>

	<div class="col-lg-10 mx-auto" tabindex="-1" role="dialog">
		<div id="message" class="alert"></div>
		<form name="primary_user_form" id="primary_user_form">
			<div class="form-group row">
				<label for="parent_account_list" class="col-sm-2 col-form-label">Select Parent Account</label>
				<div class="col-sm-10">
					<select name="parent_account_details" id="parent_account_list" />
				</div>
			</div>
			<div class="form-group row">
				<label for="firstName" class="col-sm-2 col-form-label">First Name</label>
				<div class="col-sm-10">
					<input name="firstName" id="firstName" type="text" />
				</div>
			</div>
			<div class="form-group row">
				<label for="lastName" class="col-sm-2 col-form-label">Last Name</label>
				<div class="col-sm-10">
					<input name="lastName" id="lastName" type="text" />
				</div>
			</div>
			<div class="form-group row">
				<label for="email" class="col-sm-2 col-form-label">Email</label>
				<div class="col-sm-10">
					<input name="email" id="email" type="text" />
				</div>
			</div>
			<div class="form-group row">
				<label for="contactNumber" class="col-sm-2 col-form-label">Contact Number</label>
				<div class="col-sm-10">
					<input name="contactNumber" id="contactNumber" type="text" />
				</div>
			</div>
		</form>
		<button type="button" class="btn btn-primary" onclick="create_primary_parent_account('primary_user_form', 'parent_account_list');">Create</button>
		<button type="button" class="btn btn-secondary" onclick="reset_invite_primary_form('primary_user_form');">Reset</button>
	</div>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="resources/scripts/bootstrap.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/lib.js"></script>

	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>

	<script type="text/javascript"
		src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
		src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script>
		$(document).ready(function() {
            get_parent_account_no_primary_list('parent_account_list');
		});
	</script>
</h:body>
</html>
