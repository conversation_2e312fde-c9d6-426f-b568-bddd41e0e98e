import React from 'react';
import styled from 'styled-components';
import { Icon, dimensions } from '@socure/components';
import { ModifiedModules } from './AccountProvisioning.type';

const ModuleList = styled.div`
    display: flex;
    align-items: center;
    border: 1px solid #E1E0DF;
    border-radius: 4px;
    width: fit-content;
    padding: 9px 16px;
    margin-bottom: 24px;
    background-color: #fff;
`

const Span = styled.span`
     padding-left: 8px;
`

const UpdateSummaryLabel = styled.span`
    font-size: 12px;
    font-weight: 700;
    color: #858585;
`

const Label = styled.span`
    font-size: 14px;
    font-weight: 400;
    color: #22272F
`

export type UpdateSummaryProps = {
    selectedBundle: string;
    modifiedProducts: ModifiedModules[]
}

const UpdateSummary: React.FC<UpdateSummaryProps> = ({ selectedBundle, modifiedProducts }) => {
    return modifiedProducts.length > 0 ? <div>
        <UpdateSummaryLabel>UPDATE SUMMARY</UpdateSummaryLabel>
        <ModuleList>
            <Label>{selectedBundle} </Label>
            {modifiedProducts.map(module => 
            {
               let icon, color;
               if(module.update === 'ADD') {
                   color = 'green';
                   icon = 'plus';
               } else {
                   color = 'red';
                   icon = 'minus';
               }
           return <>
           <Span></Span>
           <Icon name={icon} color={color} size={dimensions.XS} />
           <Label>{module.name}</Label>
           </>
            }
       )}
          
        </ModuleList>
    </div> : <></>
}

export default UpdateSummary;