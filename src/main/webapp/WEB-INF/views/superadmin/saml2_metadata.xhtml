<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=1200" />
	<title>SAML 2.0 Metadata Management</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<link rel="shortcut icon" type="image/ico"
		  href="https://www.datatables.net/favicon.ico" />
	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

	<link rel="stylesheet"
		  href="#{bucket.resourceURL}/styles/leaflet/leaflet.css" />

	<link rel="stylesheet" type="text/css"
		  href="#{bucket.resourceURL}/styles/DT_bootstrap.css" />
	<link rel="stylesheet" type="text/css"
		  href="#{bucket.resourceURL}/styles/pagination.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/print.css"
		  media="print" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/chosen.min.css"/>
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
	<script src="resources/scripts/bootstrap.min.js"></script>
	<style>
		.chosen-search input{
			margin-left:0 !important;
		}
		.modal-body{
			overflow-y: inherit;
		}
	</style>

	<script>
        var bucketurl = "#{bucket.resourceURL}";
        var contexturl = "#{request.contextPath}";
        var marketingurl = "#{bucket.marketingSite}";
        loggedinUsername = "#{loggedinUsername}";
        appenv = "#{bucket.appEnv}";
	</script>
	<script src="#{bucket.resourceURL}/scripts/modernizr.js"></script>
</h:head>
<h:body class="">
	<div align="right" style="padding-top: 15px;">
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="test_endpoints">Test Endpoint</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="logout">Logout</h:outputLink>
	</div>
	<div align="center">
		<font color='blue' id='msg_font'><label id="super_admin_msg"></label>
		</font>
	</div>

	<div id="MessageModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Idp Metadata Management</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="delete_msg"></label>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="delete_idp_metadata();">Delete</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>


	<div id="AddMessageModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Idp Metadata Management</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<label id="add_msg"></label>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="add_idp_metadata();">Add</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				</div>
			</div>
		</div>
	</div>

	<div class="accordion" id="accordion2">
		<div class='container' style='margin-top: 10px'>
			<div>
				<h4>IDP Metadata Management</h4>
				<br/>
			<table cellpadding='0' cellspacing='0' border='0'
				   class='table table-striped table-bordered' id='example'>
				<thead>
				<tr>
					<th></th>
					<th>Account ID</th>
					<th>Account Name</th>
					<th>Created</th>
					<th>Download SP Metadata</th>
				</tr>
				</thead>
			</table>
			</div>
		</div>
	</div>
	<div style="padding-left: 215px;">
		<a href="javascript:show_add_idp_metadata_form();">Add New</a>&nbsp;&nbsp; | &nbsp;
		&nbsp; <a href="javascript:show_delete_idp_metadata_form();">Delete</a>&nbsp;&nbsp;
	</div>

	<div id="AddIdpMetadataModal" class="modal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<section id="Loading" class="overlay show">
					<p>Loading ...</p>
					<div class="loader">
						<img src="#{bucket.resourceURL}/assets/images/socure-loader.svg" />
						<span class="dot"></span>
					</div>
				</section>
				<div class="modal-header">
					<h5 class="modal-title">Idp Metadata</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div id="message" class="alert alert-error"></div>
					<form id="add-idp-metadata" action="#" method="post" enctype="multipart/form-data">
						<div class="form-group row">
							<label for="parent_account_list" class="col-sm-2 col-form-label">Select Account</label>
							<div class="col-sm-10">
								<select name="accountid" id="parent_account_list" class="chosen"/>
							</div>
						</div>
						<div class="form-group row">
							<label for="file" class="col-sm-2 col-form-label">File</label>
							<div class="col-sm-10">
								<input type="file" id="file" name="file" accept="*.xml" required="true" />
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" onclick="add_idp_metadata();" class="btn btn-primary">Save changes</button>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/chosen.jquery.min.js"></script>
	<script src="#{bucket.resourceURL}/scripts/lib.js"></script>
	<script src="#{bucket.resourceURL}/scripts/datadog/datadog_initializer.js"></script>

	<script src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
	<script src="#{bucket.resourceURL}/scripts/jquery-dateFormat.min.js"></script>

	<script type="text/javascript"
			src="#{bucket.resourceURL}/scripts/bootstrap-collapse.js"></script>

	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/jquery.dataTables.js"></script>
	<script type="text/javascript" charset="utf-8" language="javascript"
			src="#{bucket.resourceURL}/scripts/DT_bootstrap.js"></script>
	<script>

	    window.onload = function() {
	      if(DD_RUM) {
            DD_RUM.startView({name: "/saml2_metadata"});
            DD_RUM.setUser({ "id": loggedinUsername});
          }
	    }

        $(document).ready(function() {
            show_accounts_with_idp_metadata();
            $(".chosen").chosen({
                width:"55%",
                search_contains : true
            });
        });
	</script>
</h:body>
</html>