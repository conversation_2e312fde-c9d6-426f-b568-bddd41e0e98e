package me.socure.superadmin.authentication

import com.socure.common.Random
import me.socure.control.center.constants.ControlCenterPermission
import me.socure.service.constants.Countries
import me.socure.superadmin.authentication.SimpleUserDetailsAuthenticationProvider.{UnknownAuthenticationException, UnsupportedAuthenticationTokenException}
import org.joda.time.DateTime
import org.mockito.Mockito
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}
import org.springframework.security.authentication._
import org.springframework.security.core.userdetails.{UserDetails, UserDetailsService, UsernameNotFoundException}

class SimpleUserDetailsAuthenticationProviderTest extends FreeSpec with Matchers with MockitoSugar with BeforeAndAfter {

  private val userDetailsService: UserDetailsService = mock[UserDetailsService]
  private val provider = new SimpleUserDetailsAuthenticationProvider()
  private val samlAttribute = SamlAttribute(Countries.INDIA, ControlCenterPermission.NO_ACCESS, accountProvisioningAccess = true, piiRetentionAccessPermission = "Full Access" )
  provider.userDetailsService = userDetailsService

  before {
    Mockito.reset(userDetailsService)
  }

  "should authenticate valid token properly" in {
    val username = Random.alphaNumeric(30)
    val userDetails = mock[UserDetails]
    Mockito.when(userDetails.isAccountNonExpired).thenReturn(true)
    Mockito.when(userDetails.isAccountNonLocked).thenReturn(true)
    Mockito.when(userDetails.isCredentialsNonExpired).thenReturn(true)
    Mockito.when(userDetails.isEnabled).thenReturn(true)
    val expirationTime = DateTime.now()
    Mockito.when(userDetailsService.loadUserByUsername(username)).thenReturn(userDetails)
    val unAuthToken = SimpleUnauthenticatedToken(username = username, expirationTime = Some(expirationTime), samlAttribute)
    val result = provider.authenticate(authentication = unAuthToken)
    result match {
      case token: SimpleAuthenticatedToken =>
        token shouldBe SimpleAuthenticatedToken(
          username = username,
          userDetails = userDetails,
          expirationTime = Some(expirationTime),
          samlAttribute
        )
        Mockito.verify(userDetailsService).loadUserByUsername(username)
        Mockito.verify(userDetails).isAccountNonExpired
        Mockito.verify(userDetails).isAccountNonLocked
        Mockito.verify(userDetails).isCredentialsNonExpired
        Mockito.verify(userDetails).isEnabled
      case token => fail(s"Expected SimpleAuthenticatedToken but found ${token.getClass}")
    }
  }

  "should throw error when unsupported authentication token provided" in {
    intercept[UnsupportedAuthenticationTokenException] {
      provider.authenticate(authentication = mock[UsernamePasswordAuthenticationToken])
    }
  }

  "should throw error when no credential provided" in {
    intercept[BadCredentialsException] {
      provider.authenticate(authentication = null)
    }
  }

  "should throw error when there is an internal exception" in {
    intercept[UnknownAuthenticationException] {
      provider.authenticate(authentication = SimpleUnauthenticatedToken(username = null, expirationTime = None, samlAttribute))
    }
  }

  "should throw error when user not found" in {
    val username = Random.alphaNumeric(30)
    val expirationTime = DateTime.now()
    Mockito.when(userDetailsService.loadUserByUsername(username)).thenReturn(null)
    val unAuthToken = SimpleUnauthenticatedToken(username = username, expirationTime = Some(expirationTime), samlAttribute)
    intercept[UsernameNotFoundException] {
      provider.authenticate(authentication = unAuthToken)
    }
    Mockito.verify(userDetailsService).loadUserByUsername(username)
  }

  "should throw error when user is locked" in {
    val username = Random.alphaNumeric(30)
    val userDetails = mock[UserDetails]
    Mockito.when(userDetails.isAccountNonLocked).thenReturn(false)
    val expirationTime = DateTime.now()
    Mockito.when(userDetailsService.loadUserByUsername(username)).thenReturn(userDetails)
    val unAuthToken = SimpleUnauthenticatedToken(username = username, expirationTime = Some(expirationTime), samlAttribute)
    intercept[LockedException] {
      provider.authenticate(authentication = unAuthToken)
    }
    Mockito.verify(userDetailsService).loadUserByUsername(username)

    Mockito.verify(userDetails).isAccountNonLocked
    Mockito.verify(userDetails, Mockito.never()).isEnabled
    Mockito.verify(userDetails, Mockito.never()).isAccountNonExpired
    Mockito.verify(userDetails, Mockito.never()).isCredentialsNonExpired
  }

  "should throw error when user is disabled" in {
    val username = Random.alphaNumeric(30)
    val userDetails = mock[UserDetails]
    Mockito.when(userDetails.isAccountNonLocked).thenReturn(true)
    Mockito.when(userDetails.isEnabled).thenReturn(false)
    val expirationTime = DateTime.now()
    Mockito.when(userDetailsService.loadUserByUsername(username)).thenReturn(userDetails)
    val unAuthToken = SimpleUnauthenticatedToken(username = username, expirationTime = Some(expirationTime), samlAttribute)
    intercept[DisabledException] {
      provider.authenticate(authentication = unAuthToken)
    }
    Mockito.verify(userDetailsService).loadUserByUsername(username)

    Mockito.verify(userDetails).isAccountNonLocked
    Mockito.verify(userDetails).isEnabled
    Mockito.verify(userDetails, Mockito.never()).isAccountNonExpired
    Mockito.verify(userDetails, Mockito.never()).isCredentialsNonExpired
  }

  "should throw error when account has expired" in {
    val username = Random.alphaNumeric(30)
    val userDetails = mock[UserDetails]
    Mockito.when(userDetails.isAccountNonLocked).thenReturn(true)
    Mockito.when(userDetails.isEnabled).thenReturn(true)
    Mockito.when(userDetails.isAccountNonExpired).thenReturn(false)
    val expirationTime = DateTime.now()
    Mockito.when(userDetailsService.loadUserByUsername(username)).thenReturn(userDetails)
    val unAuthToken = SimpleUnauthenticatedToken(username = username, expirationTime = Some(expirationTime), samlAttribute)
    intercept[AccountExpiredException] {
      provider.authenticate(authentication = unAuthToken)
    }
    Mockito.verify(userDetailsService).loadUserByUsername(username)

    Mockito.verify(userDetails).isAccountNonLocked
    Mockito.verify(userDetails).isEnabled
    Mockito.verify(userDetails).isAccountNonExpired
    Mockito.verify(userDetails, Mockito.never()).isCredentialsNonExpired
  }

  "should throw error when credentials have expired" in {
    val username = Random.alphaNumeric(30)
    val userDetails = mock[UserDetails]
    Mockito.when(userDetails.isAccountNonLocked).thenReturn(true)
    Mockito.when(userDetails.isEnabled).thenReturn(true)
    Mockito.when(userDetails.isAccountNonExpired).thenReturn(true)
    Mockito.when(userDetails.isCredentialsNonExpired).thenReturn(false)
    val expirationTime = DateTime.now()
    Mockito.when(userDetailsService.loadUserByUsername(username)).thenReturn(userDetails)
    val unAuthToken = SimpleUnauthenticatedToken(username = username, expirationTime = Some(expirationTime), samlAttribute)
    intercept[CredentialsExpiredException] {
      provider.authenticate(authentication = unAuthToken)
    }
    Mockito.verify(userDetailsService).loadUserByUsername(username)

    Mockito.verify(userDetails).isAccountNonLocked
    Mockito.verify(userDetails).isEnabled
    Mockito.verify(userDetails).isAccountNonExpired
    Mockito.verify(userDetails).isCredentialsNonExpired
  }
}
