package me.socure.account.provisioning;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DtoUpdateProduct {
    Long id;
    Boolean provisioned;
    Boolean enabled;

    public DtoUpdateProduct() {
    }

    public DtoUpdateProduct(Long id, Boolean provisioned, Boolean enabled) {
        this.id = id;
        this.provisioned = provisioned;
        this.enabled = enabled;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getProvisioned() {
        return provisioned;
    }

    public void setProvisioned(Boolean provisioned) {
        this.provisioned = provisioned;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
