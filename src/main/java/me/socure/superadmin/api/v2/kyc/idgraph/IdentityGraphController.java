package me.socure.superadmin.api.v2.kyc.idgraph;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import me.socure.superadmin.api.v2.common.GenericApiController;
import me.socure.superadmin.api.v2.kyc.idgraph.model.ExtendedConsortiumRequest;
import me.socure.superadmin.api.v2.kyc.idgraph.model.TransactionConsortiumRequest;
import me.socure.superadmin.api.v2.kyc.idgraph.model.TransactionConsortiumResponse;
import me.socure.superadmin.api.v2.kyc.recordcorrection.IdentityGraphHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import scala.Tuple3;

import java.util.List;

@RestController
@RequestMapping("/api/v2/kyc/identity_graph")
public class IdentityGraphController extends GenericApiController {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IdentityGraphHelper identityGraphHelper;

    @Autowired
    private Config config;

    @PostMapping("/get-merged-id")
    @ResponseBody
    public ResponseEntity<TransactionConsortiumResponse> getBaseEntityForTransaction(@Validated @RequestBody final TransactionConsortiumRequest request) {
        final Tuple3<String, JsonNode, List<String>> result = identityGraphHelper.getDetailsFromTransaction(request.getTransactionID());
        return ResponseEntity
                .ok()
                .body(
                        TransactionConsortiumResponse.builder()
                                .socureId(result._1())
                                .entity(result._2())
                                .entityId("MERGED_ENTITY")
                                .underlyingRecordIDs(result._3())
                                .build()
                );
    }

    @PostMapping("/get-extended-entities")
    @ResponseBody
    public ResponseEntity<JsonNode> getExtendedEntities(@Validated @RequestBody final ExtendedConsortiumRequest body) throws JsonProcessingException {
        final JsonNode httpResponse = identityGraphHelper.getAdditionalEntitiesBasedOnPII(body.getEntityType(), body.getValue());
        return ResponseEntity.ok().body(httpResponse);
    }
}
