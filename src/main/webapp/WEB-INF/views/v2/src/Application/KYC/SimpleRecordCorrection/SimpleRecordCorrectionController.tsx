import React, { ReactElement, useCallback, useEffect, useMemo, useState } from "react";
import { ISimpleRecordCorrectionViewProps } from "./SimpleRecordCorrectionView";
import { apiCall, deepClone } from "../../../Utilities";
import { toaster } from "../../../Components/Toaster";
import { IListedClustersData } from "./types";
import { allowedSources, editorFields } from "./data";
import { generateEditingDataDiff, processRetrievedRecords } from "./utilities";

export interface ISimpleRecordCorrectionControllerProps {
    children: (props: ISimpleRecordCorrectionViewProps) => ReactElement<any, any> | null
}

const security_key_local_storage_id = "sa.kyc.record_correction.security_key";

const SimpleRecordCorrectionController: React.FC<ISimpleRecordCorrectionControllerProps> = ({ children }: ISimpleRecordCorrectionControllerProps) => {
    const [step, setStep] = useState<number>(0);
    const [securityKey, setSecurityKeyUtil] = useState<string>(localStorage.getItem(security_key_local_storage_id) || "");
    const [id, setId] = useState<string>("");
    const [account, setAccount] = useState<number>(0);
    const [idType, setIdType] = useState<number>(1);
    const [configuration, setConfiguration] = useState<null | { equifax: any, enformion: any }>(null);
    const [clusterList, setClusterList] = useState<null | { editable: Array<string>, uneditable: Array<string>, socureID: String }>(null);
    const [description, setDescription] = useState<string>("");
    const [socureTicket, setSocureTicket] = useState<string>("");
    const [vendorTicket, setVendorTicket] = useState<string>("");
    const [shouldUpdateDeceased, setShouldUpdateDecease] = useState<boolean>(false);
    const [shouldUpdateSource, setShouldUpdateSource] = useState<boolean>(false);
    const [fields, setFields] = useState<Array<{ display: string, value: string }> | undefined>(undefined);
    const [field, setField] = useState<string>("");
    const [socureID, setSocureID] = useState<string>("");
    const [editingData, setEditingData] = useState<any>(undefined);

    const back = useCallback((): void => {
        if (step > 0 && step < 4) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    const backDisabled = useMemo((): boolean => {
        if (step > 0 && step < 4) {
            return false;
        }
        return true;
    }, [step]);


    const loadConfiguration = useCallback(async () => {
        try {
            const apiResult = await apiCall("/kyc/record_correction/configuration");
            if (apiResult.status === 200) {
                const { data } = apiResult;
                if (Array.isArray(data.equifax) && Array.isArray(data.enformion) && data.enformion.length >= 1 && data.equifax.length >= 1) {
                    setConfiguration({
                        equifax: data.equifax[0],
                        enformion: data.enformion[0]
                    });
                } else {
                    toaster.error("Invalid Configuration returned");
                }
            } else {
                toaster.error("The response status code was not 200!");
                console.log("The response status code was not 200!", apiResult);
            }
        } catch (e) {
            toaster.error("An unknown error occured during loading configuration.")
        }
    }, [setConfiguration]);

    useEffect(() => {
        loadConfiguration();
    }, []);


    const setSecurityKey = useCallback((e: any): void => {
        localStorage.setItem(security_key_local_storage_id, e.target.value)
        setSecurityKeyUtil(e.target.value)
    }, [setSecurityKeyUtil]);

    const nextButtonDisabled: boolean = useMemo((): boolean => {
        let result = true;
        if (step === 0 && id !== "" && securityKey !== "" && (idType === 3 ? account !== 0 : true)) {
            result = false;
        } else if (step === 1 && description !== "" && (shouldUpdateDeceased || shouldUpdateSource)) {
            result = false;
        } else if (step === 2) {
            result = false;
        } else if (step === 3 && editingData.updatedRecordCount !== 0) {
            result = false;
        }
        return result;
    }, [step, id, idType, account, securityKey, description, shouldUpdateDeceased, shouldUpdateSource, editingData]);

    const retrieveRecordData = useCallback(async () => {
        try {
            if (clusterList && configuration) {
                const apiResponsePromise = clusterList.editable.map(_ => _.split("/")).map(async (r) => {
                    const [type, clusterId] = r;
                    let conf;
                    if (type === "equifax") {
                        conf = configuration.equifax
                    } else {
                        conf = configuration.enformion
                    }
                    const body = {
                        host: `https://${conf.host}`,
                        region: conf.region,
                        indexName: conf.index,
                        dynamoDBTable: conf.dynamo,
                        token: securityKey,
                        clusterId
                    }
                    return {
                        result: await apiCall("/kyc/record_correction/get-data", body),
                        type
                    };
                })
                const apiResponse = await Promise.all(apiResponsePromise);
                if (apiResponse.some(_ => _.result.status !== 200)) {
                    toaster.error("Data retrival failed for one or more records!")
                    throw new Error("Data retrival failed for one or more records!");
                } else {
                    return apiResponse.map(_ => ({ result: _.result.data, type: _.type }));
                }
            }
            throw new Error("An Unexpected error occured");
        } catch (e) {
            toaster.error("An Unexpected error occured");
            throw e;
        }
    }, [clusterList, configuration, securityKey]);

    const updateRecordData = useCallback(async(recordsToUpdate:Array<any>) => {
        try {
            if (configuration) {
                const { deceased } = editingData;
                const apiResponsePromise = recordsToUpdate.map(async(r) => {
                    const {type, result} = r;
                    const { updatedAttributes, clusterId } = result;
                    let conf;
                    let vendor;
                    if (type === "equifax") {
                        conf = configuration.equifax
                        vendor = "equifax";
                    } else {
                        conf = configuration.enformion
                        vendor = "enformion";
                    }
                    const update:any = {
                        clusterId
                    };
                    if(shouldUpdateDeceased) {
                        update["ssnDeceased"] = deceased;
                        update["cidDeceased"] = deceased;
                    }
                    if(shouldUpdateSource) {
                        update["fullSource"] = updatedAttributes;
                    }
                    const body = {
                        host: `https://${conf.host}`,
                        region: conf.region,
                        indexName: conf.index,
                        dynamoDBTable: conf.dynamo,
                        token: securityKey,
                        vendor,
                        description,
                        vendorCorrectionRequestTicket: vendorTicket,
                        correctionRequestTicket: socureTicket,
                        update,
                        socureId: socureID
                    }
                    return {
                        response : await apiCall("/kyc/record_correction/update-data", body),
                        cluster: `${type}/${clusterId}`
                    };
                })
                const apiResponse = await Promise.all(apiResponsePromise);
                return apiResponse.map((r) => {
                    if(r.response.status === 200 && r.response.data.status === "ok" && r.response.data.data.endsWith("Success")) {
                        return {
                            success: true,
                            text: `${r.cluster} => Success`
                        }
                    } else {
                        return {
                            success: false,
                            text: `${r.cluster} => Failure`
                        }
                    }
                })
            } else {
                throw new Error("An unexpected error occured!");
            }
        } catch (e) {
            toaster.error("An Unexpected error occured!");
            throw e;
        }
    }, [
        configuration, securityKey, socureTicket, vendorTicket, description, 
        shouldUpdateSource, shouldUpdateDeceased, editingData, socureID
    ])

    const onNextButtonClick = useCallback(async () => {
        if (step === 0) {
            const requestBody = {
                type: idType,
                id,
                accountId: (idType === 3 ? account : undefined)
            };
            try {
                const result = await apiCall("/kyc/record_correction/list-cluster-id", requestBody);
                if (result.status === 200 && result.data.status === "ok") {
                    let data: IListedClustersData = result.data.data;
                    const { socureID } = data;
                    const editable: Array<string> = [];
                    const uneditable: Array<string> = [];
                    for (let i = 0; i !== data.clusters.length; i++) {
                        const { cluster_id, source } = data.clusters[i];
                        const src = (allowedSources as any)[source];
                        if (src) {
                            editable.push(`${src}/${cluster_id}`);
                        } else {
                            uneditable.push(`${source}/${cluster_id}`)
                        }
                    }
                    if (editable.length === 0) {
                        toaster.error("No editable records found for this socure/txn identity.");
                    } else {
                        setSocureID(socureID);
                        setClusterList({
                            editable,
                            uneditable,
                            socureID: data.socureID
                        });
                        setStep(1);
                    }
                } else {
                    toaster.error("Unknown error occured.");
                }
            } catch (e) {
                console.log(e)
                toaster.error("Unknown error occured.");
            }
        } else if (step === 1) {
            const recordData = await retrieveRecordData()
            const editingData = processRetrievedRecords(recordData)
            const fields = editorFields.filter(l => (shouldUpdateDeceased && l.value === "DECEASED") || (shouldUpdateSource && l.value !== "DECEASED"))
            setFields(fields);
            if (shouldUpdateDeceased) {
                setField("DECEASED");
            } else {
                setField("NF");
            }
            setEditingData(editingData)
            setStep(2);
        } else if (step === 2) {
            const ed = generateEditingDataDiff(editingData, shouldUpdateSource, shouldUpdateDeceased)
            setEditingData(deepClone(ed))
            setStep(3)
        } else if (step === 3) {
            const recordsToUpdate = editingData.retrievedData.filter((_: any) =>
                JSON.stringify(_.result.attributes) !== JSON.stringify(_.result.updatedAttributes)
            )
            const result = await updateRecordData(recordsToUpdate)
            editingData.finalUpdationData = result;
            setStep(4);
        }
    }, [
        step, id, idType, account, setClusterList, setStep, shouldUpdateSource, shouldUpdateDeceased,
        setFields, setField, setSocureID, retrieveRecordData, setEditingData, editingData, updateRecordData
    ]);

    const viewProps: ISimpleRecordCorrectionViewProps = {
        step,
        setStep,
        securityKey,
        setSecurityKey,
        id,
        setId,
        account,
        setAccount,
        idType,
        setIdType,
        nextButtonDisabled,
        configuration,
        onNextButtonClick,
        clusterList,
        description,
        socureTicket,
        vendorTicket,
        setDescription,
        setSocureTicket,
        setVendorTicket,
        shouldUpdateDeceased,
        setShouldUpdateDecease,
        shouldUpdateSource,
        setShouldUpdateSource,
        fields,
        field,
        setField,
        socureID,
        editingData,
        setEditingData,
        backDisabled,
        back
    }

    return children(viewProps);
}

export default SimpleRecordCorrectionController;