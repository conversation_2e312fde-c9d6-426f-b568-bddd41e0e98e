{"version": 3, "file": "static/css/main.8fce7066.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCZA,KAIE,qBAAsB,CAFtB,YAAa,CADb,iBAIF,CAEA,WAJE,YAMF,CAEA,UACE,kBACF,CAEA,UACE,qBACF,CAEA,QACE,QACF,CAEA,OACE,eACF,CAEA,OACE,eACF,CAEA,OACE,gBACF,CAEA,OACE,gBACF,CAEA,MACE,YACF,CAEA,cACE,4BACF,CAEA,aACE,sBACF,CAEA,gBACE,sBACF,CAEA,aACE,2BACF,CAEA,SAME,+BAAoC,CADpC,WAAY,CAFZ,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAGF,CAEA,kBAEE,YAAa,CACb,kBAAmB,CACnB,wBAAyB,CAHzB,eAIF,CAEA,cACE,cAAe,CACf,eAAiB,CACjB,gBAAiB,CAEjB,eAAgB,CADhB,iBAEF,CAEA,gBAEE,YAAa,CAEb,QAAO,CADP,kBAAmB,CAFnB,eAAgB,CAIhB,eACF,CAEA,UAKE,iBAAkB,CAClB,uCAA0C,CAF1C,eAAgB,CAFhB,WAMF,CAEA,yBATE,YAAa,CAEb,qBAAsB,CAItB,YAUF,CAPA,eAIE,sBAAuB,CADvB,QAAS,CAGT,aACF,CAEA,aACE,+BAAoC,CACpC,iBAAkB,CAElB,eAAgB,CADhB,YAEF,CAEA,eACE,cAAe,CACf,gBAAiB,CACjB,aAAc,CACd,sBAAuB,CACvB,kBACF,CAEA,cACE,eAAgB,CAChB,eACF,CAGA,0DAOE,sCAAwC,CACxC,wBAAyB,CACzB,iBAAkB,CAPlB,oBAAqB,CAGrB,cAAe,CACf,gBAAiB,CAFjB,YAAa,CAHb,iBAAkB,CAElB,WAOF,CAEA,iBAEE,kBAAmB,CAKnB,WAAY,CACZ,cAAe,CAPf,mBAAoB,CAGpB,cAAe,CACf,eAAgB,CAFhB,sBAAuB,CAGvB,gBAGF,CAEA,0BACE,kBAAmB,CACnB,UACF,CAEA,yBAOE,wBAAyB,CACzB,+BAAoC,CANpC,iBAAkB,CADlB,UAAc,CAId,WAAY,CADZ,cAAe,CAEf,gBAAiB,CAHjB,uBAMF,CAEA,sBACE,UACF,CAEA,2BAOE,4BAA6B,CAC7B,+BAAoC,CANpC,iBAAkB,CADlB,UAAW,CAIX,WAAY,CADZ,cAAe,CAEf,gBAAiB,CAHjB,uBAMF,CAEA,qBAGE,kBAAmB,CACnB,cAAe,CAHf,YAAa,CACb,kBAAmB,CAGnB,aACF,CAEA,uBAKE,+BAAoC,CADpC,iBAAkB,CAHlB,oBAAqB,CAErB,WAAY,CADZ,UAIF,CAEA,+CAEE,wBAAyB,CADzB,wBAEF,CAEA,yBACE,YAAa,CACb,qBACF,CAYA,0EATE,YAAa,CACb,kBAYF,CAJA,oBAGE,kBACF,CAEA,sBACE,aAAc,CAId,cAAe,CAHf,cAAe,CACf,gBAAiB,CACjB,aAEF,CAEA,yBAEE,+BAAoC,CADpC,WAEF,CAEA,iBAME,sBAAuB,CALvB,qBAAsB,CACtB,uCAA0C,CAG1C,YAAa,CAFb,cAAe,CAKf,aAAc,CADd,eAAgB,CAHhB,SAKF,CAEA,oBAYE,+BAAoC,CACpC,iBAAkB,CAVlB,uCAA0C,CAE1C,cAAe,CAIf,cAAe,CAEf,eAAiB,CADjB,gBAAiB,CANjB,YAAa,CAFb,YAAa,CADb,eAAgB,CAMhB,sBAAuB,CADvB,yBAA2B,CAE3B,kBAMF,CAEA,0BACE,+BAAoC,CACpC,uCACF,CAEA,OACE,UACF,CAEA,8BAEE,+BAAoC,CADpC,YAEF,CAEA,eACE,eACF,CAEA,qBACE,sCAA2C,CAC3C,mBACF,CAEA,iBAOE,cAAe,CALf,oBAAqB,CAErB,cAAe,CADf,WAAY,CAEZ,eAAgB,CAJhB,iBAAkB,CAKlB,qBAEF,CAEA,wBAEE,cAAe,CACf,SAAU,CAFV,iBAGF,CAEA,yBAKE,oBAAqB,CACrB,kBAAmB,CAJnB,oBAAqB,CAErB,WAAY,CAHZ,iBAAkB,CAElB,UAIF,CAEA,gCASE,qBAAsB,CACtB,iBAAkB,CAFlB,UAAW,CAHX,aAAc,CAEd,WAAY,CAHZ,QAAS,CADT,gBAAiB,CAFjB,iBAAkB,CAClB,OAAQ,CASR,yCAAkC,CAAlC,iCAAkC,CAAlC,gEAAkC,CALlC,UAMF,CAEA,0DACE,UACF,CAEA,yDACE,sBACF,CAEA,gEACE,kCAA2B,CAA3B,0BACF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ".App {\n  text-align: center;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-row {\n  flex-direction: row;\n}\n\n.flex-col {\n  flex-direction: column;\n}\n\n.flex-1 {\n  flex: 1;\n}\n\n.mt-16 {\n  margin-top: 16px;\n}\n\n.mt-32 {\n  margin-top: 32px;\n}\n\n.ml-16 {\n  margin-left: 16px;\n}\n\n.ml-32 {\n  margin-left: 32px;\n}\n\n.p-24 {\n  padding: 24px;\n}\n\n.align-center {\n  align-items: center!important;\n}\n\n.align-start {\n  align-items: flex-start;\n}\n\n.justify-center {\n  justify-content: center;\n}\n\n.text-center {\n  text-align: center!important;\n}\n\n.overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n}\n\n.topbar-container {\n  margin-top: 64px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-end;\n}\n\n.page-heading {\n  font-size: 24px;\n  font-weight: bold;\n  line-height: 24px;\n  text-align: center;\n  margin-top: 24px;\n}\n\n.page-container {\n  margin-top: 32px;\n  display: flex;\n  flex-direction: row;\n  flex: 1;\n  overflow: hidden;\n}\n\n.left-bar {\n  display: flex;\n  width: 240px;\n  flex-direction: column;\n  overflow-y: auto;\n  border-radius: 8px;\n  box-shadow: 0 32px 64px rgb(44 9 12 / 10%);\n  padding: 24px;\n}\n\n.config-editor {\n  display: flex;\n  flex-direction: column;\n  flex: 1 1;\n  align-items: flex-start;\n  padding: 24px;\n  overflow: auto;\n}\n\n.flag-editor {\n  border: solid 1px rgba(0, 0, 0, 0.7);\n  border-radius: 8px;\n  padding: 16px;\n  margin-top: 24px;\n}\n\n.generic-label {\n  font-size: 16px;\n  line-height: 24px;\n  padding: 0 8px;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.config-label {\n  min-width: 240px;\n  text-align: left;\n}\n\n\n.socure-c-text-box, .scoure-c-dropdown, .socure-c-text-area {\n  position: relative;\n  display: inline-block;\n  width: 240px;\n  padding: 12px;\n  font-size: 16px;\n  line-height: 24px;\n  background-color: transparent !important;\n  border: 1px solid #454d5f;\n  border-radius: 4px;\n}\n\n.socure-c-button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 24px;\n  border: none;\n  cursor: pointer;\n}\n\n.socure-c-button.disabled {\n  cursor: not-allowed;\n  opacity: 0.2;\n}\n\n.socure-c-button-primary {\n  color: #ffffff;\n  border-radius: 8px;\n  transition-duration: .2s;\n  min-width: 79px;\n  height: 36px;\n  padding: 6px 16px;\n  background-color: #FF6900;\n  border: solid 1px rgba(0, 0, 0, 0.5);\n}\n\n.socure-c-button-link {\n  color: #0000EE;\n}\n\n.socure-c-button-secondary {\n  color: #333;\n  border-radius: 8px;\n  transition-duration: .2s;\n  min-width: 79px;\n  height: 36px;\n  padding: 6px 16px;\n  background-color: transparent;\n  border: solid 1px rgba(0, 0, 0, 0.5);\n}\n\n.socure-c-radio-item {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  cursor: pointer;\n  padding: 0 8px;\n}\n\n.socure-c-radio-circle {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  border: solid 1px rgba(0, 0, 0, 0.5);\n}\n\n.socure-c-radio-circle.socure-c-radio-selected {\n  border: solid 1px #FF6900;\n  background-color: #FF6900;\n}\n\n.socure-c-radio-vertical {\n  display: flex;\n  flex-direction: column;\n}\n\n.socure-c-radio-horizontal {\n  display: flex;\n  flex-direction: row;\n}\n\n.socure-c-topbar-container {\n  display: flex;\n  flex-direction: row;\n}\n\n.socure-c-each-menu {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n}\n\n.socure-c-topbar-menu {\n  color: #FF6900;\n  font-size: 16px;\n  line-height: 24px;\n  padding: 0 8px;\n  cursor: pointer;\n}\n\n.socure-c-menu-separator {\n  height: 16px;\n  border: solid 1px rgba(0, 0, 0, 0.5);\n}\n\n.popup-container {\n  background-color: #fff;\n  box-shadow: 0 32px 64px rgb(44 9 12 / 10%);\n  max-height: 75%;\n  width: 50%;\n  display: flex;\n  align-items: flex-start;\n  text-align: left;\n  overflow: auto;\n}\n\n.socure-config-item {\n  text-align: left;\n  padding: 16px;\n  box-shadow: 0 16px 16px rgb(44 9 12 / 10%);\n  margin: 8px 0;\n  cursor: pointer;\n  transition: all linear 0.2s;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  font-size: 16px;\n  line-height: 24px;\n  font-weight: bold;\n  border: solid 1px rgba(0, 0, 0, 0.1);\n  border-radius: 4px;\n}\n\n.socure-config-item:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n  box-shadow: 0 16px 32px rgb(44 9 12 / 10%);\n}\n\n.w-100 {\n  width: 100%;\n}\n\n.flags-list th, .flags-list td {\n  padding: 16px;\n  border: solid 1px rgba(0, 0, 0, 0.1);\n}\n\n.flags-list td {\n  text-align: left;\n}\n\n.each-prop-container {\n  border-bottom: solid 1px rgba(0, 0, 0, 0.5);\n  padding-bottom: 32px;\n}\n\n.socure-c-switch {\n  position: relative;\n  display: inline-block;\n  height: 21px;\n  font-size: 14px;\n  line-height: 1.5;\n  vertical-align: middle;\n  cursor: pointer;\n}\n\n.socure-c-switch__input {\n  position: absolute;\n  cursor: pointer;\n  opacity: 0;\n}\n\n.socure-c-switch__toggle {\n  position: relative;\n  display: inline-block;\n  width: 40px;\n  height: 22px;\n  background-color: red;\n  border-radius: 30px;\n}\n\n.socure-c-switch__toggle::before {\n  position: absolute;\n  top: 50%;\n  margin-top: -11px;\n  left: 1px;\n  display: block;\n  width: 22px;\n  height: 22px;\n  content: \"\";\n  background-color: #fff;\n  border-radius: 50%;\n  transition: transform .25s ease 0s;\n}\n\n.socure-c-switch__input:disabled + .socure-c-switch__toggle {\n  opacity: 0.2;\n}\n\n.socure-c-switch__input:checked + .socure-c-switch__toggle {\n  background-color: green;\n}\n\n.socure-c-switch__input:checked + .socure-c-switch__toggle::before {\n  transform: translateX(16px);\n}\n  "], "names": [], "sourceRoot": ""}