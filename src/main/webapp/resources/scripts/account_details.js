$(document)
		.ready(
				function() {
					if (typeof (window.socure) === 'undefined'
							|| window.socure === null) {
						window.socure = {};
					}

					if (typeof (window.socure.accountdetails) === 'undefined'
							|| window.socure.accountdetails === null) {
						window.socure.accountdetails = {};
						window.ad = window.socure.accountdetails;
					}

					$(document).on("change", "#consentReason", function() {
                        var optionSelected = $("option:selected", this);
                        var consentId = this.value;
                        updateConsentReason(consentId)
                     });

					ad.CONSTS = {};
					ad.CONSTS.API_URLS = {
						"BASE_URL" : contexturl + "/superadmin/account/details/1/",
						"ROLES_PERMISSIONS" : "permissions",
						"PREFERENCES" : "preferences",
						"USERS" : "users",
						"IP_WHITELISTING" : "ip_whitelisting",
						"API_KEYS" : "api_keys",
                        "DOCUMENT_TYPES" : "document_manager",
                        "PARTNER_AND_SUBACCOUNT" : "partner_and_subaccount",
                        "DECISION_LOGIC" : "decision_logic",
                        "SETTINGS": "settings"
					};
					ad.CONSTS.JQ_DT = {
						"ROLES_PERMISSIONS" : $("#tbl_account_details_roles_permissions"),
						"PREFERENCES" : $("#tbl_account_details_preferences"),
						"USERS" : $("#tbl_account_details_users"),
						"IP_WHITELISTING" : $("#tbl_account_details_ip_whitelisting"),
						"API_KEYS" : $("#tbl_account_details_api_keys"),
                        "DOCUMENT_TYPES" : $("#tbl_account_details_docTypes"),
                        "PARTNER_AND_SUBACCOUNT" : $("#tbl_account_details_partner_and_subaccount"),
						"DECISION_LOGIC" : $("#tbl_account_details_decision_logic")
					};
					ad.CONSTS.JQ_DP = {
                        "ACCOUNT_ID" : $("#adAccountId"),
                        "PUBLIC_ID": $("#publicId")
                    };
                    ad.CONSTS.ROLE_MAP = {
                        "ROLE_WATCHLIST_3_0": 65,
                        "ROLE_DOCUMENT_VERIFICATION": 35,
                        "ROLE_DOCUMENT_VERIFICATION_V2": 79,
                        "ROLE_MLA_SERVICE":108,
                        "ROLE_ECBSV":94,
                        "ROLE_DECISION_MODULE_DETAILS":111,
                        "ROLE_NEW_CAPTURE_APP_INTERFACE":112,
                        "ROLE_IDM":180,
                        "ROLE_MULTI_MODEL_DECISIONING": 247,
                        "ROLE_ENABLE_MULTIPLE_LIVE_LOGICS":258,
                        "ROLE_DECISION_SERVICE":56
                    }
                    ad.CONSTS.ROLE_GROUP = [
                        {
                          roleIds: [81, 80, 84, 85, 83, 82, 116, 117],
                          group: {
                            id: -100,
                            label: "Document Verification Role",
                            provisioned: false
                          },
                          parentRoleId: 79
                        }
                    ];
                    ad.CONSTS.STANDALONE_ROLE_GROUP = [
                                {
                              roleIds: [160,162],
                                   group: {
                                     id: 163,
                                     label: "WLM Billing Cadence",
                                     provisioned: true,
                                     defaultCadence:162
                                   }
                                }

                    ];

                    ad.CONSTS.ROLE_SUBSCRIPTION_TYPE_ASSOCIATION = {
                        1: 65,
                        2: 35
                    }
                    ad.rolesCache = {
                    }
                    ad.CONSTS.PREFILL = {
                        parentGroupId: 110, // PrefillModule
                        groupId: 155, // EnhancedPrefillReporting
                        roleIds: [143, 156, 157] 
                    }
                    ad.CONSTS.ROLE_INTEGRATION = {
                        112: {
                          roleIds: [113, 114],
                          show: function() {
                             return ad.rolesCache[112]
                          },
                          enableRoles: [113],
                          disableRoles: [114]
                        },
                        179: {
                            roleIds: [181, 182],
                            show: function() {
                               return ad.rolesCache[179]
                            },
                            enableRoles: [181],
                            disableRoles: [182]
                        },
                        195: {
                            roleIds: [196, 197],
                            show: function() {
                               return ad.rolesCache[195]
                            },
                            enableRoles: [196],
                            disableRoles: [197]
                        },
                        201: {
                            roleIds: [202, 203],
                            show: function() {
                               return ad.rolesCache[201]
                            },
                            enableRoles: [202],
                            disableRoles: [203]
                        },
                        94: {
                            roleIds: [145,146,147],
                            show: function() {
                                return ad.rolesCache[94]
                            },
                            enableRoles: [147],
                            disableRoles: [145,146,147]
                        },
                        108: {
                            roleIds: [148, 149, 150],
                            show: function() {
                                return ad.rolesCache[108]
                            },
                            enableRoles: [150],
                            disableRoles: [148, 149, 150]
                        },
                        13: {
                          roleIds: [164, 176],
                          show: function() {

                              return ad.rolesCache[13]
                          },
                          enableRoles: [164]
                        },
                        220: {
                            roleIds: [221, 222],
                            show: function() {
                                return ad.rolesCache[220]
                            },
                            enableRoles: [221],
                            disableRoles: [222]
                        },
                        223: {
                            roleIds: [224, 225],
                            show: function() {
                                return ad.rolesCache[223]
                            },
                            enableRoles: [224],
                            disableRoles: [225]
                        },
                        226: {
                            roleIds: [227, 228],
                            show: function() {
                                return ad.rolesCache[226]
                            },
                            enableRoles: [227],
                            disableRoles: [228]
                        },

                    }
                    ad.CONSTS.ROLE_AUTO_ACTIVATE = {
                        146: [147],
                        145: [146, 147],
                        149: [150],
                        148: [149, 150]
                    }
                    ad.CONSTS.ROLE_AUTO_DEACTIVATE = {
                        149: [148],
                        150: [149, 148, 108],
                        146: [145],
                        147: [145, 146, 94]
                    }
                    // Default initialization
                    var defaultADTarget = "ROLES_PERMISSIONS";
                    //var defaultADTarget = "PREFERENCES";

                    function getDataTableDefaultSettings() {
                        return {
                            "bPaginate": false
                        };
                    }

                    function getPerferenceColumnDef () {
                        return [
                            {
                                "fnRender": function (oObj) {
                                    return "Label";
                                },
                                "aTargets": [1]
                            }, {
                                "fnRender": function(o) {
                                    return JSON.stringify(o);
                                },
                                "aTargets": [2]
                            }
                        ];
                    }
                    function constructPerferenceTable(tbljq) {
                        var tableSettings = Object.extends(getDataTableDefaultSettings(), {

                            "aoColumns": getPerferenceColumnDef(),
                            "aaData": [{internal: false}, {active: true}]
                        });
                        tbljq.dataTable(tableSettings)

                    }

                    const environmentType = {
                        1: "Production",
                        2: "Certification",
                        3: "Sandbox"
                    }

					// Function Initializations
                    ad.loadAccountDetails = function(adTarget, tbljq, apiUrl, accountId, publicId) {
                        !adTarget && adTarget!=='RATE_LIMITING' || $.ajax({
                                url : constructUrl(apiUrl, accountId, publicId),
                                beforeSend: function() {
                                    $('#Loading').show();
                                },
                                complete: function() {
                                    $('#Loading').hide();
                                },
                                success : function(data) {
                                    var result = data.data;
                                    var dataMap = {};
                                    var groupIds = [];
                                    var standaloneGroupIds = []
                                    if(adTarget === "ROLES_PERMISSIONS") {
                                      var roleIdsToFilter = ad.CONSTS.ROLE_GROUP.reduce(function(accm, i) {
                                           groupIds.push(i.group.id);
                                           return accm.concat(i.roleIds)
                                        }, []);
                                      for(i in ad.CONSTS.ROLE_INTEGRATION) {
                                         roleIds = ad.CONSTS.ROLE_INTEGRATION[i].roleIds;
                                         roleIdsToFilter = roleIdsToFilter.concat(roleIds);
                                      }
                                      roleIdsToFilter = roleIdsToFilter.concat(ad.CONSTS.PREFILL.roleIds)
                                      roleIdsToFilter.push(ad.CONSTS.PREFILL.groupId)
                                      result = result.filter(function(r){
                                          ad.rolesCache[r.id] = r.provisioned;
                                          if (roleIdsToFilter.indexOf(r.id) > -1) {
                                             dataMap[r.id] = r;
                                             return false;
                                          }
                                          if(r.id == 97) {
                                              if(!r.provisioned) {
                                                $("#migrationBtnWrapper").show();
                                                $("#nav-users-tab").attr("hidden", false);
                                                $("#nav-partner-subaccount-tab").attr("hidden", true);
                                                $("#nav-partner-subaccount-tab").attr("hidden", true);
                                                $("#nav-partner-subaccount-tab").removeClass('active');
                                                $("#tab_partner_and_subaccount").removeClass('active');
                                                if(window.sessionStorage.getItem("ImprovedAccountProvisioningActive") === 'false') {
                                                    if(showOldProvisioning === 'false') {
                                                      $("#nav-old-provisioning-tab").removeClass('active');
                                                      $("#tab_roles_permissions_old").removeClass('active');
                                                      $("#nav-old-provisioning-tab").attr("hidden", true);
                                                      $("#nav-provisioning-tab").addClass('active');
                                                      $("#tab_roles_permissions").addClass('active');
                                                    } else {
                                                      $("#nav-old-provisioning-tab").attr("hidden", false);
                                                      $("#nav-old-provisioning-tab").addClass('active');
                                                      $("#tab_roles_permissions_old").addClass('active');
                                                      $("#nav-provisioning-tab").removeClass('active');
                                                      $("#tab_roles_permissions").removeClass('active');
                                                    }
                                                }
                                              } else if(window.sessionStorage.getItem("ImprovedAccountProvisioningActive") === 'true') {
                                              // handle subaccount tab as we reload page when Improved provisioning is clicked
                                                $("#nav-partner-subaccount-tab").removeClass('active');
                                                $("#tab_partner_and_subaccount").removeClass('active');
                                                 if(showOldProvisioning === 'false') {
                                                  $("#nav-old-provisioning-tab").attr("hidden", true);
                                                } else {
                                                  $("#nav-old-provisioning-tab").attr("hidden", false);
                                                }
                                              } else if(showOldProvisioning === 'false') {
                                                  $("#nav-old-provisioning-tab").attr("hidden", true);
                                                  $("#nav-old-provisioning-tab").removeClass('active');
                                                  $("#tab_roles_permissions_old").removeClass('active');
                                                } else {
                                                  $("#nav-old-provisioning-tab").attr("hidden", false);
                                                }
                                                return false
                                              }
                                          return true;
                                      });
                                       ad.CONSTS.ROLE_GROUP.forEach(function(r) {
                                          result.push(r.group);
                                       })
                                      ad.CONSTS.STANDALONE_ROLE_GROUP.forEach(function(parent) {
                                         var subRoleIds= parent.roleIds
                                          var subRolesInApiResult = $.grep(result, function (resultRoleElem, i) {
                                              return subRoleIds.indexOf(resultRoleElem.id) > -1  ;
                                          });
                                          if(subRolesInApiResult.length > 0){
                                              subRolesInApiResult.forEach(function(roleElem){
                                                  dataMap[roleElem.id] = roleElem
                                                  var index = result.findIndex(elem => elem.id === roleElem.id);
                                                  if(index > -1){
                                                    result.splice(index, 1);
                                                  }
                                              })
                                              standaloneGroupIds.push(parent.group.id)
                                              result.push(parent.group);
                                              if(parent.group.defaultCadence != undefined){
                                                    var subRoleProvisioned = $.grep(subRolesInApiResult, function (roleElem, i) {
                                                         return roleElem.provisioned ;
                                                     });
                                                     if(subRoleProvisioned == undefined || subRoleProvisioned.length == 0 ){
                                                         dataMap[parent.group.defaultCadence].provisioned=true
                                                     }
                                                }
                                          }
                                     })

                                    }
                                    if (tbljq && $.fn.DataTable.fnIsDataTable(tbljq[0])) {
                                        dt = tbljq.dataTable();
                                        dt.fnClearTable();
                                        dt.fnAddData(ad.transformData(adTarget, result));
                                        dt.fnDraw();
                                        if(adTarget === "ROLES_PERMISSIONS"){
                                            setConsentReason();
                                        }
                                    } else {
                                        if(adTarget === "ROLES_PERMISSIONS"){
                                            tbljq.dataTable({
                                                "aaSorting": [[ 1, 'asc' ]],
                                                "bPaginate": false,
                                                "aoColumns" : [
                                                    {
                                                        "bVisible": false
                                                    },
                                                    null,
                                                    {"fnRender" : function(oObj) {
                                                        var allowEdit = $("#allowEdit").val();
                                                        if (groupIds.indexOf(oObj.aData[0]) === -1) {
                                                            if (!(allowEdit === "true")) {
                                                                return "<span>" + (oObj.aData[2] ? "Yes" : "No") + "</span>";
                                                            }
                                                            var oc = "javascript:setProvisioning("+accountId+","+oObj.aData[0]+", \""+ publicId+"\")";

                                                            var additionalTemplate = "";

                                                            if (oObj.aData[0] == -103) {
                                                                return '<select id="consentReason" name="ConsentReason">'+
                                                                                                    '					<option value="1">OpenBankAccount</option>'+
                                                                                                    '					<option value="2">ApplyForMortgage</option>'+
                                                                                                    '					<option value="3">ApplyForCreditCard</option>'+
                                                                                                    '					<option value="4">ApplyForLoan</option>'+
                                                                                                    '					<option value="5">OpenRetirementAccount</option>'+
                                                                                                    '					<option value="6">ApplyForJob</option>'+
                                                                                                    '					<option value="7">MeetLicensingRequirement</option>'+
                                                                                                    '				</select>';

                                                            }
                                                            if (standaloneGroupIds.indexOf(oObj.aData[0]) > -1) {
                                                                 var fieldSets=""

                                                                ad.CONSTS.STANDALONE_ROLE_GROUP.forEach(function(r){
                                                                    if(oObj.aData[0]===r.group.id){
                                                                        fieldSets = fieldSets+ " <select id='wlmCadence' onchange='changeCadence("+accountId+",parseInt(this.value))'> "
                                                                        r.roleIds.forEach(function(rId){
                                                                            var selectAttribIfSelected = (dataMap[rId].provisioned  ? "selected='selected'" : "")
                                                                            var option = " <option value="+rId+ " "+ selectAttribIfSelected+">"+dataMap[rId].label+"</option>"
                                                                            fieldSets = fieldSets + option

                                                                        })
                                                                        fieldSets = fieldSets + "</select>"
                                                                      }
                                                                })
                                                               return fieldSets;

                                                            }
                                                            if(oObj.aData[0] === ad.CONSTS.PREFILL.parentGroupId) {
                                                                var show = oObj.aData[2];
                                                                if(dataMap[ad.CONSTS.PREFILL.groupId]) {
                                                                    var showSubGroup = dataMap[ad.CONSTS.PREFILL.groupId].provisioned
                                                                    additionalTemplate = ad.CONSTS.PREFILL.roleIds.map(function(roleId) {
                                                                        var onc = "javascript:setProvisionByGroup("+accountId+","+roleId+", \""+ publicId+"\",\""+ dataMap[roleId].label +"\", " + oObj.aData[0] + ")";
                                                                        return "<div id='group" + roleId + "' data-curstate=" + dataMap[roleId].provisioned + "><input type='checkbox' id='prole" + roleId + "' name='pg" + oObj.aData[0] + "' onclick='"+onc+"'" + (dataMap[roleId].provisioned ? "  checked='checked'" : "") + "/> " + (dataMap[roleId].label) + "</div>"
                                                                      });
                                                                      var onc = "javascript:setProvisionByGroup("+accountId+","+ad.CONSTS.PREFILL.groupId+", \""+ publicId+"\",\""+ dataMap[ad.CONSTS.PREFILL.groupId].label +"\", " + oObj.aData[0] + ")";
                                                                     additionalTemplate = "<div id='chkGrp" + oObj.aData[0] + "' " + (show ? "" : "style='display: none'") + "><br/><h6><input type='checkbox' id='prole" + ad.CONSTS.PREFILL.groupId + "' name='pg" + oObj.aData[0] + "' onclick='"+onc+"'" + (dataMap[ad.CONSTS.PREFILL.groupId].provisioned ? "  checked='checked'" : "") + "/> "+dataMap[ad.CONSTS.PREFILL.groupId].label+"</h6><fieldset id='chkGrp" + ad.CONSTS.PREFILL.groupId + "' " + (showSubGroup ? "" : "style='display: none'") + ">" + additionalTemplate.join("") + "</fieldset></div>";
                                                                 }
                                                            }
                                                            if (oObj.aData[0] === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION && oObj.aData[2] == true) {
                                                                $("#nav-tab_docTypes").attr("hidden", false);
                                                                $("#nav-tab_docv").attr("hidden", false);
                                                            }
                                                            if (oObj.aData[0] === ad.CONSTS.ROLE_MAP.ROLE_DECISION_SERVICE && oObj.aData[2] == true) {
                                                                $("#nav-tab_decision_logic").attr("hidden", false);
                                                            }
                                                            if (ad.CONSTS.ROLE_INTEGRATION[oObj.aData[0]]) {
                                                              var show = ad.CONSTS.ROLE_INTEGRATION[oObj.aData[0]].show && typeof ad.CONSTS.ROLE_INTEGRATION[oObj.aData[0]].show === 'function' && ad.CONSTS.ROLE_INTEGRATION[oObj.aData[0]].show();
                                                              var onc;
                                                              additionalTemplate = ad.CONSTS.ROLE_INTEGRATION[oObj.aData[0]].roleIds.map(function(roleId) {
                                                                onc = "javascript:setProvisionByGroup("+accountId+","+roleId+", \""+ publicId+"\",\""+ dataMap[roleId].label +"\", " + oObj.aData[0] + ")";
                                                                return "<div id='group" + roleId + "' data-curstate=" + dataMap[roleId].provisioned + "><input type='checkbox' id='prole" + roleId + "' name='pg" + oObj.aData[0] + "' onclick='"+onc+"'" + (dataMap[roleId].provisioned ? "  checked='checked'" : "") + "/> " + (dataMap[roleId].label) + "</div>"
                                                              });
                                                             if(oObj.aData[0] == 13) {
                                                                  additionalTemplate = "<div id='chkGrp" + oObj.aData[0] + "' " + (show ? "" : "style='display: none'") + "><br/><fieldset>" + additionalTemplate.join("") + "</fieldset></div>";
                                                             } else {
                                                                  additionalTemplate = "<div id='chkGrp" + oObj.aData[0] + "' " + (show ? "" : "style='display: none'") + "><br/><h6>Choose Environments</h6><fieldset>" + additionalTemplate.join("") + "</fieldset></div>";
                                                             }

                                                            }

                                                            var isKyc = (oObj.aData[0] == 13);
                                                            if (oObj.aData[2] === true) {
                                                                (oObj.aData[0] == ad.CONSTS.ROLE_MAP.ROLE_WATCHLIST_3_0 || oObj.aData[0] === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) && getSubscriptionTypeProvisions(accountId);

                                                                    return "<fieldset class='radiogroup' data-role='"+oObj.aData[1]+"' data-curstate=true id='group" + oObj.aData[0] + "'>"
                                                                        + " <input type='radio' value='on' name='p" + oObj.aData[0] + "' id='py" + oObj.aData[0] + "'" + (isKyc ? " onchange='handleKYC(this)'" : "") +  "' data-dd-action-name=Provision'" + " onclick='"+oc+"' checked='checked'/> On "
                                                                        + " <input type='radio' value='off' name='p" + oObj.aData[0] + "' id='pn" + oObj.aData[0] + "'" + (isKyc ? " onchange='handleKYC(this)'" : "") + "' data-dd-action-name=DeProvision'" + " onclick='"+oc+"' /> Off "
                                                                        + " </fieldset>"
                                                                        + additionalTemplate;
                                                            } else {
                                                                (oObj.aData[0] == ad.CONSTS.ROLE_MAP.ROLE_WATCHLIST_3_0 || oObj.aData[0] === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) && getSubscriptionTypeProvisions(accountId);

                                                                     return "<fieldset class='radiogroup' data-role='"+oObj.aData[1]+"' data-curstate=false id='group" + oObj.aData[0] + "'" + (oObj.aData[0] === ad.CONSTS.ROLE_MAP.ROLE_MULTI_MODEL_DECISIONING ? "disabled" : "")+ ">"
                                                                            + " <input type='radio' value='on' name='p" + oObj.aData[0] + "' id='py" + oObj.aData[0] + "'" + (isKyc ? " onchange='handleKYC(this)'" : "") + "' data-dd-action-name=Provision'" + "  onclick='"+oc+"' /> On "
                                                                            + " <input type='radio' value='off' name='p" + oObj.aData[0] + "' id='pn" + oObj.aData[0] + "'" + (isKyc ? " onchange='handleKYC(this)'" : "") +  "' data-dd-action-name=DeProvision'" + "  onclick='"+oc+"'  checked='checked'/> Off "
                                                                            + "</fieldset>"
                                                                            + additionalTemplate;
                                                            }
                                                        } else {
                                                            if (!(allowEdit === "true")) {
                                                                return "<span>" + ad.CONSTS.ROLE_GROUP.filter(function(role){
                                                                  return role.group.id === oObj.aData[0]
                                                                }).map(function(role){
                                                                  var returnData = "";
                                                                  role.roleIds.forEach(function(id){
                                                                      if (dataMap[id] && dataMap[id].provisioned) {
                                                                        returnData = dataMap[id].label;
                                                                      }
                                                                  });
                                                                  return returnData || "None";
                                                                }).join("") + "</span>";
                                                            } else {
                                                               return "<span>" + ad.CONSTS.ROLE_GROUP.filter(function(role){
                                                                 return role.group.id === oObj.aData[0]
                                                               }).map(function(role){
                                                                 var returnData = [];
                                                                 var selectedRole;
                                                                 role.roleIds.forEach(function(id){
                                                                    if(dataMap[id]) {
                                                                         dataMap[id].provisioned && (selectedRole = id);
                                                                         var onc = "javascript:provisionRoleByAccount("+accountId+","+id+", \""+ publicId+"\","+oObj.aData[0]+", \""+ dataMap[id].label +"\")";
                                                                         returnData.push(
                                                                            "<div><input type='radio' value='off' id='prole" + id + "' name='pg" + oObj.aData[0] + "' onclick='"+onc+"'" + (dataMap[id].provisioned ? "  checked='checked'" : "") + "/> " + (dataMap[id].label) + "</div>"
                                                                          )
                                                                    }
                                                                 });
                                                                 return "<fieldset class='radiogroup' data-role='"+ role.group.label +"' data-curstate='" + (selectedRole||'') + "' id='group" + role.group.id + "'>" + returnData.join("") + "</fieldset>";
                                                               }).join("") + "</span>";
                                                            }
                                                        }
                                                    },
                                                    "aTargets" : [ 2 ]
                                                    }
                                                ],
                                                "aaData" : ad.transformData(adTarget, result)
                                            });
                                            setConsentReason();
                                        }else if(adTarget === "IP_WHITELISTING"){
                                            tbljq.dataTable({
                                                "aaSorting" : [ [ 0,
                                                    "asc" ] ],
                                                "bPaginate": false,
                                                "oLanguage" : {
                                                    "sLengthMenu" : "_MENU_ records per page"
                                                },
                                                "aaData" : ad.transformData(adTarget, data.data)
                                            });
                                        }else if(adTarget === "PARTNER_AND_SUBACCOUNT") {
                                            ad.accountInfoV2 = data.data
                                            tbljq.dataTable({
                                                "bPaginate": false,
                                                "bFilter": false, 
                                                "bInfo": false,
                                                "oLanguage" : {
                                                    "sLengthMenu" : "_MENU_ records per page"
                                                },
                                                "aaData" : ad.transformData(adTarget, data.data)
                                            });
                                        }else if(adTarget === "PREFERENCES"){
                                            tbljq.dataTable({
                                                "bPaginate": false,
                                                "aoColumns" : [
                                                    {
                                                        "fnRender" : function(oObj, label) {
                                                            return "<span class='text-capitalize'>" + label + "</span>";
                                                        },
                                                        "aTargets" : [ 1 ]
                                                    },
                                                    {"fnRender" : function(oObj) {
                                                        var allowEdit = $("#allowEdit").val(),
                                                            value = oObj.aData[3];
                                                        if (!(allowEdit === "true")) {
                                                            return oObj.aData[1] ? "Yes" : "No";
                                                        }

                                                        if (typeof oObj.aData[2] === 'function') {
                                                            return oObj.aData[2]();
                                                        }
                                                        var oc = "javascript:setProvisioning("+accountId+",\"" + value + "\", \"" + publicId + "\")";
                                                        if (oObj.aData[1] === true) {
                                                            return "<fieldset class='radiogroup' data-role='" + value +"' data-curstate=true id='group" + value + "'>"
                                                                + " <input type='radio' value='on' name='p" + value + "' id='py" + value + "' data-dd-action-name=Provision'" + "' onclick='"+oc+"' checked='checked'/> On "
                                                                + " <input type='radio' value='off' name='p" + value + "' id='pn" + value + "' data-dd-action-name=DeProvision'" + "' onclick='"+oc+"' /> Off "
                                                                + "</fieldset>"
                                                        }else{
                                                            return "<fieldset class='radiogroup' data-role='" + value + "' data-curstate=false id='group" + value + "'>"
                                                                + " <input type='radio' value='on' name='p" + value + "' id='py" + value + "' data-dd-action-name=Provision'" + "' onclick='"+oc+"' /> On "
                                                                + " <input type='radio' value='off' name='p" + value + "' id='pn" + value + "' data-dd-action-name=DeProvision'" + "' onclick='"+oc+"'  checked='checked'/> Off "
                                                                + "</fieldset>"
                                                        }
                                                    },
                                                    "aTargets" : [ 2 ]
                                                    }
                                                ],
                                                "aaData" : ad.transformData(adTarget, data.data)
                                            });

                                        } else if(adTarget === "API_KEYS"){
                                            var groupColumn = 0;
                                            tbljq.dataTable({
                                                "fnDrawCallback": function ( oSettings ) {
                                                    if ( oSettings.aiDisplay.length == 0 )
                                                    {
                                                        return;
                                                    }

                                                    var nTrs = $('#tbl_account_details_api_keys tbody tr');
                                                    var iColspan = nTrs[0].getElementsByTagName('td').length;
                                                    var sLastGroup = "";
                                                    for ( var i=0 ; i<nTrs.length ; i++ )
                                                    {
                                                        var iDisplayIndex = oSettings._iDisplayStart + i;
                                                        var sGroup = oSettings.aoData[ oSettings.aiDisplay[iDisplayIndex] ]._aData[0];
                                                        var acDetails = "Account ID: "+ sGroup + "</br>Account Name: " + oSettings.aoData[ oSettings.aiDisplay[iDisplayIndex] ]._aData[1];
                                                        if ( sGroup != sLastGroup )
                                                        {
                                                            var nGroup = document.createElement( 'tr' );
                                                            var nCell = document.createElement( 'td' );
                                                            nGroup.setAttribute("style", "background-color:#ddd");
                                                            nCell.setAttribute("style", "background-color:#ddd");
                                                            nCell.colSpan = iColspan;
                                                            nCell.className = "group";
                                                            nCell.innerHTML = acDetails;
                                                            nGroup.appendChild( nCell );
                                                            nTrs[i].parentNode.insertBefore( nGroup, nTrs[i] );
                                                            sLastGroup = sGroup;
                                                        }
                                                    }
                                                },
                                                "aoColumnDefs": [
                                                    { "bVisible": false, "aTargets": [ 0,1 ] }
                                                ],
                                                "aaSortingFixed": [[ 0, 'asc' ]],
                                                "aaSorting": [[ 0, 'asc' ]],
                                                "aaData" : ad.transformData(adTarget, data.data)
                                            });
                                        } else if (adTarget === "USERS") {
                                            tbljq.dataTable({
                                                "aoColumnDefs": [
                                                    {
                                                       "fnRender": usersEmailRenderer,
                                                       "aTargets": [2],
                                                       "fnCreatedCell": onUsersEmailCreatedCell
                                                    },
                                                    {
                                                        "fnRender": function (oObj) {
                                                            return arrayToHtmlList(oObj.aData[4]);
                                                        },
                                                        "aTargets": [4]
                                                    },
                                                    {
                                                        "fnRender": function (oObj) {
                                                            return csvToHtmlList(oObj.aData[5]);
                                                        },
                                                        "aTargets": [5]
                                                    },
                                                    {
                                                        "fnRender": function (oObj) {
                                                            return csvToHtmlList(oObj.aData[6]);
                                                        },
                                                        "aTargets": [6]
                                                    },
                                                    {
                                                        "fnRender": function (oObj) {
                                                            var cellValue = oObj.aData[7],
                                                                randomId = Math.random().toString(36).substring(7),
                                                                allowEdit = $("#allowEdit").val();
                                                            if (allowEdit === "true") {
                                                                return '<div class="form-check">' +
                                                                    '<input type="checkbox" class="form-check-input is-primary" id="' +
                                                                    (randomId + '" ' + (cellValue == true ? "checked=\"checked\" disabled" : "")) +
                                                                    '/><label class="form-check-label" for="' +
                                                                    (randomId + '"> ' + cellValue)  +
                                                                  '</label></div>';
                                                            } else {
                                                                return cellValue;

                                                            }
                                                        },
                                                        "aTargets": [7],
                                                        "fnCreatedCell": onIsPrimaryAdminColumnCreated
                                                    }
                                                ],
                                                "aaSorting" : [ [ 0,
                                                    "asc" ] ],
                                                "sPaginationType" : "bootstrap",
                                                "oLanguage" : {
                                                    "sLengthMenu" : "_MENU_ records per page"
                                                },
                                                "aaData" : ad.transformData(adTarget, data.data)
                                            });
                                        } else if (adTarget === "DOCUMENT_TYPES") {
                                              tbljq.dataTable({
                                                  "aoColumnDefs": [
                                                      {
                                                          "fnRender": function (oObj) {
                                                              return oObj.aData[0];
                                                          },
                                                          "aTargets": [0]
                                                      },
                                                      {
                                                          "fnRender": function (oObj) {
                                                              return oObj.aData[1];
                                                          },
                                                           "aTargets": [1]
                                                      },
                                                     {
                                                         "fnRender": function (oObj) {
                                                             return oObj.aData[2];
                                                         },
                                                          "aTargets": [2]
                                                     }
                                                  ],
                                                  "aaSorting" : [ [ 0,
                                                      "asc" ] ],
                                                  "sPaginationType" : "bootstrap",
                                                  "oLanguage" : {
                                                      "sLengthMenu" : "_MENU_ records per page"
                                                  },
                                                  "aaData" : ad.transformData(adTarget, data.data)
                                              });
                                          } else if(adTarget == "SETTINGS") {
                                            const responseData = data.data;
                                            // sort dataset on environment id to display data in environment order
                                            Object.keys(responseData).forEach(function(item) {
                                                  var displayDiv = '';
                                                  responseData[item].sort(function(a,b) {
                                                      if(a['environmentTypeId']< b['environmentTypeId']) {
                                                              return -1
                                                      }
                                                      if(a['environmentTypeId'] > b['environmentTypeId']) {
                                                              return 1;
                                                      }
                                                  })

                                                  responseData[item].forEach(function(settingObj, i) {

                                                      displayDiv += "<div style='padding-left:10px;' class='font-orange'><h5>" + environmentType[responseData[item][i]['environmentTypeId']] +  "</h5></div>"
                                                                    + "<div style='height:auto; padding:10px; margin-bottom: 20px; width:70%'>";


                                                      var displaySettings = '';
                                                      settingObj && Object.keys(settingObj).forEach(function(setting) {

                                                          if(setting == 'environmentTypeId') { return;} // skip env type
                                                          if((setting == 'kycPreferences' || setting == 'caWatchlistPreferenceView' || setting == 'cmView')) { // process object & display
                                                              displaySettings +=  "<table id='settings_table' style='border: 1px solid gray;'>";

                                                              if(Array.isArray(settingObj[setting])) {
                                                                  var arrayVal = settingObj[setting].join(',') || null;

                                                                  displaySettings += "<tr><td style='border-right: none;'><b>" + setting + "</b></td><td>" +
                                                                                arrayVal + "</td></tr>";
                                                              } else {

                                                                  Object.keys(settingObj[setting]).forEach(function(prop) {
                                                                      displaySettings += "<tr><td>" + prop + "</td><td>" + settingObj[setting][prop] + "</td></tr>";
                                                                  })
                                                              }

                                                          }
                                                          if(setting == 'subscriptionChannelRegistry') {
                                                                var webhook_table = '';
                                                                settingObj[setting].forEach(function(prop) { //process array of objects
                                                                    webhook_table += "<table id='settings_table' style='border: 1px solid gray; margin-bottom:15px;'>";
                                                                    Object.keys(prop).forEach(function(key) {
                                                                        var value = prop[key];
                                                                        if(key == 'secretKey') {
                                                                            value = value.replace(/.(?=.{4,}$)/g,'*'); // mask secret key
                                                                        }

                                                                        if(key == 'environmentTypeId') {return;}

                                                                        if(key === 'metadata') {
                                                                            if(Object.keys(prop[key]).length > 0) {
                                                                                webhook_table += "<tr><td style='border-right: none;'><b>" + key + "</b></td></tr>";
                                                                                Object.keys(prop[key]).forEach(function(mData) {
                                                                                   webhook_table += "<tr><td>" + mData + "</td><td>" + prop[key][mData] + "</td></tr>";
                                                                                });
                                                                                webhook_table += "<tr><td style='border-right: none;'></td></tr>";
                                                                            }
                                                                        } else {
                                                                            webhook_table += "<tr><td>" + key + "</td><td>" + value + "</td></tr>";
                                                                        }
                                                                    });
                                                                    webhook_table += "</table>";
                                                                });
                                                                displaySettings += webhook_table;
                                                          }
                                                          if(setting == 'dvConfigurationViewMapper') {
                                                              // to handle checkbox values in docv
                                                              var booleanConfigs = [
                                                                  "FirstNameMatchWithNickNameDB"
                                                              ]
                                                              var docv_table = "<table id='settings_table_dv' style='border: 1px solid gray; margin-bottom:15px;'>";
                                                              docv_table += "<thead><tr><th>Configuration</th><th>Value</th><th>Decision</th></tr></thead>";
                                                              settingObj[setting]['dvConfigurations'].forEach(function(prop) { //process array of objects
                                                                  docv_table += "<tr>" +
                                                                  "<td>" + prop['configName'] + "</td>";

                                                              if(booleanConfigs.indexOf(prop['configName']) > -1) {
                                                                  docv_table += "<td>" + (prop['configValue'] === "0" ? "False" : "True") + "</td>" +
                                                                                "<td></td>";
                                                              }
                                                              else {
                                                                    docv_table += "<td>" + prop['configValue'] + "</td>" +
                                                                                  "<td>" + prop['decision'] + "</td>";
                                                              }
                                                              docv_table += "</tr>";

                                                              });
                                                              displaySettings += docv_table + "</table>";
                                                          }
                                                          if(setting == 'watchlistPreference') { // process object & display
                                                            displaySettings +=  "<table id='settings_table' style='border: 1px solid gray;'>";
                                                            displaySettings += "<tr><td><b>General Settings</b></td><td style='width:625px'><table>"+
                                                            "<tr><td><b>AutoMonitoringEnabled</b></td><td>"+settingObj[setting].generalSettings.watchlistAutoMonitoringEnabled+"</td></tr>"+
                                                            "<tr><td><b>Entity Types</b></td><td>"+settingObj[setting].generalSettings.entityTypes.join(", ")+"</td></tr>"+
                                                            "</table></td></tr>"
                                                            displaySettings += "<tr><td><b>Screening Policies</b></td><td style='width:625px'>"
                                                            settingObj[setting].screeningPolicies.forEach(p => {
                                                                displaySettings += "<table style='margin-bottom:5px; margin-top:5px; border:1.5px solid black;'>"
                                                                displaySettings += "<tr><td><b>Name</b></td><td><b>"+p.name+"</b></td></tr>"
                                                                displaySettings += "<tr><td><b>Default</b></td><td>"+p.default+"</td></tr>"
                                                                displaySettings += "<tr><td><b>Enabled</b></td><td>"+p.enabled+"</td></tr>"
                                                                displaySettings += "<tr><td><b>Sources</b></td><td style='width:625px'>"
                                                                displaySettings += "<input type='text' onkeyup='filterNames(this)' placeholder='Search...' style='width:100%; margin-bottom:5px;'>"
                                                                displaySettings += "<div style='max-height:200px; overflow-y:auto; border:1px solid #ccc; padding:5px;'>"
                                                                displaySettings += "<ul style='padding-left: 20px; margin:0; list-style-type: none;'>";
                                                                for (let category in p.source) {
                                                                    if (p.source.hasOwnProperty(category)) {
                                                                        displaySettings += "<li class='source-category-item' style='list-style-type:none;'>";
                                                                        displaySettings += "<span class='toggle-arrow' onclick='toggleCategory(this)' style='cursor:pointer;'>▶</span> ";
                                                                        displaySettings += "<span class='category-text'>" + category + "</span>";
                                                                        displaySettings += "<ul class='source-name-list' style='display:none; list-style-type: disc; padding-left:20px; margin:0;'>";
                                                                        p.source[category].forEach(item => {
                                                                            displaySettings += "<li class='source-name-item' data-id='" + Object.keys(item)[0] + "'>" + Object.values(item)[0] + "</li>";
                                                                        });
                                                                        displaySettings += "</ul>";
                                                                        displaySettings += "</li>";
                                                                    }
                                                                }
                                                                displaySettings += "</ul>"
                                                                displaySettings += "</div>"
                                                                displaySettings += "</td></tr>"
                                                                displaySettings += "<tr><td><b>Threshold Criteria</b></td><td>"
                                                                const thresholdCriteria = p.filterCriteria.thresholdCriteria.flatMap(item => 
                                                                    item.criteria.map(criteria => ({
                                                                        comparator: criteria.comparator,
                                                                        inputs: criteria.inputs,
                                                                        name: criteria.name,
                                                                        conditionType: item.conditionType
                                                                    }))
                                                                );
                                                                thresholdCriteria.forEach(c => {
                                                                    displaySettings += "<table style='margin-bottom:5px;'>"
                                                                    displaySettings += "<table><tr><td>Name</td><td>"+c.name+"</td></tr>"
                                                                    displaySettings += "<tr><td>value</td><td>"+c.inputs.join(", ")+"</td></tr>"
                                                                    displaySettings += "<tr><td>Comparator</td><td>"+c.comparator+"</td></tr>"
                                                                    displaySettings += "<tr><td>Condition Type</td><td>"+c.conditionType+"</td></tr>"
                                                                    displaySettings += "</table>"
                                                                })
                                                                displaySettings += "</td></tr>"
                                                                displaySettings += "<tr><td><b>Reduction Criteria</b></td><td>"
                                                                const reductionCriteria = p.filterCriteria.reductionCriteria.flatMap(item => 
                                                                    item.criteria.map(criteria => ({
                                                                        comparator: criteria.comparator,
                                                                        inputs: criteria.inputs,
                                                                        name: criteria.name,
                                                                        conditionType: item.conditionType
                                                                    }))
                                                                );
                                                                reductionCriteria.forEach(c => {
                                                                    displaySettings += "<table style='margin-bottom:5px;'>"
                                                                    displaySettings += "<table><tr><td>Name</td><td>"+c.name+"</td></tr>"
                                                                    displaySettings += "<tr><td>value</td><td>"+c.inputs.join(", ")+"</td></tr>"
                                                                    displaySettings += "<tr><td>Comparator</td><td>"+c.comparator+"</td></tr>"
                                                                    displaySettings += "<tr><td>Condition Type</td><td>"+c.conditionType+"</td></tr>"
                                                                    displaySettings += "</table>"
                                                                })
                                                                displaySettings += "</td></tr>"
                                                                displaySettings += "</table>"
                                                            })
                                                            displaySettings += "</td></tr>"
                                                            displaySettings += "<tr><td><b>Monitoring Policies</b></td><td style='width:625px'>"
                                                            settingObj[setting].monitoringPolicies.forEach(p => {
                                                                displaySettings += "<table style='margin-bottom:5px; margin-top:5px; border:1.5px solid black;'>"
                                                                displaySettings += "<tr><td><b>Name</b></td><td><b>"+p.name+"</b></td></tr>"
                                                                displaySettings += "<tr><td><b>Default</b></td><td>"+p.default+"</td></tr>"
                                                                displaySettings += "<tr><td><b>Enabled</b></td><td>"+p.enabled+"</td></tr>"
                                                                displaySettings += "<tr><td><b>Sources</b></td><td style='width:625px'>"
                                                                displaySettings += "<input type='text' onkeyup='filterNames(this)' placeholder='Search...' style='width:100%; margin-bottom:5px;'>"
                                                                displaySettings += "<div style='max-height:200px; overflow-y:auto; border:1px solid #ccc; padding:5px;'>"
                                                                displaySettings += "<ul style='padding-left: 20px; margin:0; list-style-type: none;'>";
                                                                for (let category in p.source) {
                                                                    if (p.source.hasOwnProperty(category)) {
                                                                        displaySettings += "<li class='source-category-item' style='list-style-type:none;'>";
                                                                        displaySettings += "<span class='toggle-arrow' onclick='toggleCategory(this)' style='cursor:pointer;'>▶</span> ";
                                                                        displaySettings += "<span class='category-text'>" + category + "</span>";
                                                                        displaySettings += "<ul class='source-name-list' style='display:none; list-style-type: disc; padding-left:20px; margin:0;'>";
                                                                        p.source[category].forEach(item => {
                                                                            displaySettings += "<li class='source-name-item' data-id='" + Object.keys(item)[0] + "'>" + Object.values(item)[0] + "</li>";
                                                                        });
                                                                        displaySettings += "</ul>";
                                                                        displaySettings += "</li>";
                                                                    }
                                                                }
                                                                displaySettings += "</ul>"
                                                                displaySettings += "</div>"
                                                                displaySettings += "</td></tr>"
                                                                displaySettings += "<tr><td><b>Threshold Criteria</b></td><td>"
                                                                const thresholdCriteria = p.filterCriteria.thresholdCriteria.flatMap(item => 
                                                                    item.criteria.map(criteria => ({
                                                                        comparator: criteria.comparator,
                                                                        inputs: criteria.inputs,
                                                                        name: criteria.name,
                                                                        conditionType: item.conditionType
                                                                    }))
                                                                );
                                                                thresholdCriteria.forEach(c => {
                                                                    displaySettings += "<table style='margin-bottom:5px;'>"
                                                                    displaySettings += "<table><tr><td>Name</td><td>"+c.name+"</td></tr>"
                                                                    displaySettings += "<tr><td>value</td><td>"+c.inputs.join(", ")+"</td></tr>"
                                                                    displaySettings += "<tr><td>Comparator</td><td>"+c.comparator+"</td></tr>"
                                                                    displaySettings += "<tr><td>Condition Type</td><td>"+c.conditionType+"</td></tr>"
                                                                    displaySettings += "</table>"
                                                                })
                                                                displaySettings += "</td></tr>"
                                                                displaySettings += "<tr><td><b>Reduction Criteria</b></td><td>"
                                                                const reductionCriteria = p.filterCriteria.reductionCriteria.flatMap(item => 
                                                                    item.criteria.map(criteria => ({
                                                                        comparator: criteria.comparator,
                                                                        inputs: criteria.inputs,
                                                                        name: criteria.name,
                                                                        conditionType: item.conditionType
                                                                    }))
                                                                );
                                                                reductionCriteria.forEach(c => {
                                                                    displaySettings += "<table style='margin-bottom:5px;'>"
                                                                    displaySettings += "<table><tr><td>Name</td><td>"+c.name+"</td></tr>"
                                                                    displaySettings += "<tr><td>value</td><td>"+c.inputs.join(", ")+"</td></tr>"
                                                                    displaySettings += "<tr><td>Comparator</td><td>"+c.comparator+"</td></tr>"
                                                                    displaySettings += "<tr><td>Condition Type</td><td>"+c.conditionType+"</td></tr>"
                                                                    displaySettings += "</table>"
                                                                })
                                                                displaySettings += "</td></tr>"
                                                                displaySettings += "</table>"
                                                            })
                                                            displaySettings += "</td></tr>"
                                                        }
                                                      }),
                                                      displayDiv += displaySettings + "</table></div>";
                                                  })
                                                  displayDiv = displayDiv || "<span></span>"; // in case of no data
                                                  $('#tab_' + item).html("");
                                                  $('#tab_' + item).append(displayDiv);
                                            });
                                          }
                                          else {
                                            tbljq.dataTable({
                                                    "aaSorting" : [ [ 0,
                                                        "asc" ] ],
                                                    "sPaginationType" : "bootstrap",
                                                    "oLanguage" : {
                                                        "sLengthMenu" : "_MENU_ records per page"
                                                    },
                                                    "aaData" : ad.transformData(adTarget, data.data)
                                            });
                                    }
                                }
                                },
                                error : function() {
                                    console.log("account_details page has some errors...")
                                }
                            });
                    };

                    function clearAccCacheRenderer(data) {
                        return '<button type="button" class="btn btn-primary" onclick="clearAccountCache(' + data.id + ')">Clear Account Cache</button>';
                    }

                    function findCustomRenderer(key, data) {
                       if  (key === "clearAccountCache") {
                           return function () { return clearAccCacheRenderer(data); }
                       }
                    }

                    function setAccountUsers(fetchedUsers, roles) {
                        var users = []
                        fetchedUsers.forEach(user => {
                            var userRoles = []
                            // adding system defined roles in roles list
                            user.userAccountAssociation.roleType.forEach(r => {
                                const systemRole = getRoleType(r)
                                if(systemRole) userRoles.push(systemRole)
                            })
                            user.userAccountAssociation.userRoles.forEach(roleId => {

                                var roleName = roles.find(role => role.id == roleId);
                                if(roleName) {
                                    // adding custom roles in roles list
                                    userRoles.push(roleName.name+"("+roleId+")")
                                } else {
                                    userRoles.push(roleId)
                                }
                            })
                            var associateButtonHtml = "<button type=\"button\" class=\"btn btn-secondary\" onclick=\"associationUserClickHandler(" + user.userAccountAssociation.userId + ")\" >Associate</button>"
                            var swapButtonHtml = ""
                            if(user.userAccountAssociation.roleType.includes(1)) {
                                swapButtonHtml = "<button type=\"button\" class=\"btn btn-secondary\" onclick=\"showSwapRoleModal(" + user.userAccountAssociation.userId + ")\" >Swap</button>"
                            }
                            users.push([user.userAccountAssociation.userId,user.firstName+" "+user.lastName, user.email, getUserStatusString(user.userAccountAssociation.status), userRoles.join(), `<div style='display:flex'>${associateButtonHtml} ${swapButtonHtml}</div>` ])
                        })

                        var tbljq = $("#tbl_account_details_partner_and_subaccount_users");
                        if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
                            dt = tbljq.dataTable();
                            dt.fnClearTable();
                            dt.fnAddData(users);
                            dt.fnDraw();
                        } else {
                            tbljq.dataTable({
                                "sPaginationType" : "bootstrap",
                                "oLanguage" : {
                                    "sLengthMenu" : "_MENU_ records per page"
                                },
                                "aaData": users
                            })
                        }
                    }

                    function setPermissionTemplate(fetchedPermissionTemplates) {
                        var permissionTemplates = []
                        fetchedPermissionTemplates.forEach(permission => {
                            permission.environmentPermissions.forEach(env => {
                                env.permissions.sort()
                                var permissionHtml = "<ul>"
                                env.permissions.forEach(permission => {
                                    permissionHtml+="<li>"+permission+"</li>"
                                })
                                permissionHtml+="</ul>"
                                permissionTemplates.push([permission.name, permission.roleName , env.environmentName, permissionHtml,  env.globalScope.join()])
                            })
                        })

                        var tbljq = $("#tbl_account_details_permission_templates");
                        if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
                            dt = tbljq.dataTable();
                            dt.fnClearTable();
                            dt.fnAddData(permissionTemplates);
                            dt.fnDraw();
                        } else {
                            tbljq.dataTable({
                                "sPaginationType" : "bootstrap",
                                "oLanguage" : {
                                    "sLengthMenu" : "_MENU_ records per page"
                                },
                                "aaData": permissionTemplates
                            })
                        }
                    }

                    function setAccountRoles(fetchedRoles) {
                        var roles = []
                        fetchedRoles.forEach(role => {
                            roles.push([role.id, role.name, role.description])
                        })
                        var tbljq = $("#tbl_account_details_roles");
                        if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
                            dt = tbljq.dataTable();
                            dt.fnClearTable();
                            dt.fnAddData(roles);
                            dt.fnDraw();
                        } else {
                            tbljq.dataTable({
                                "sPaginationType" : "bootstrap",
                                "oLanguage" : {
                                    "sLengthMenu" : "_MENU_ records per page"
                                },
                                "aaData": roles
                            })
                        }
                    }

                    function setSubAccountsNotMigrated(fetchedSubAccounts) {
                        var subAccounts = []
                        fetchedSubAccounts.forEach(subAccount => {
                            subAccounts.push(["<input value='"+subAccount.id+"' id='subaccount-check-"+subAccount.id+"' class='subaccount-check' type='checkbox'/>",subAccount.id, subAccount.name, subAccount.industry])
                        })
                        var tbljq = $("#tbl_account_details_subaccounts_not_migrated");
                        if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
                            dt = tbljq.dataTable();
                            dt.fnClearTable();
                            dt.fnAddData(subAccounts);
                            dt.fnDraw();
                        } else {
                            tbljq.dataTable({
                                "sPaginationType" : "bootstrap",
                                "oLanguage" : {
                                    "sLengthMenu" : "_MENU_ records per page"
                                },
                                "aaData": subAccounts
                            })
                        }
                    }

                    function setLinkedPrograms(accountId, fetchLinkedPrograms) {
                        get_accounts();
                        var linkedPrograms = []
                        fetchLinkedPrograms.forEach(linkedProgram => {
                            var unlinkButtonHtml = "<button type='button' class='btn btn-secondary' onclick='confirmUnlinkProgram(" + accountId + "," + linkedProgram.programId + ",\"" + linkedProgram.programName + "\")' >Unlink</button>"
                            linkedPrograms.push([linkedProgram.programId, linkedProgram.programName, linkedProgram.createdBy, new Date(parseInt(linkedProgram.createdAt)).toLocaleString(), `<div style='display:flex'>${unlinkButtonHtml}</div>`])
                        })

                        var tbljq = $("#tbl_account_details_linked_programs");
                        if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
                            dt = tbljq.dataTable();
                            dt.fnClearTable();
                            dt.fnAddData(linkedPrograms);
                            dt.fnDraw();
                        } else {
                            tbljq.dataTable({
                                "sPaginationType" : "bootstrap",
                                "oLanguage" : {
                                    "sLengthMenu" : "_MENU_ records per page"
                                },
                                "aaData": linkedPrograms
                            })
                        }
                    }

                    function getParentId(hierarchyPath) {
                        var paths = hierarchyPath.split("/")
                        if(paths.length >= 3)
                            return paths[paths.length-3]
                        return null
                    }



                    var root = {id: parseInt(accountId), name: accountName, parent: null, children: []}

                    // ************** Generate the tree diagram	 *****************
                    var margin = {top: 20, right: 120, bottom: 20, left: 120},
                    width = 960 - margin.right - margin.left,
                    height = 500 - margin.top - margin.bottom;
                
                
                    var tree = d3.layout.tree()
                    .size([height, width]);
                
                    var diagonal = d3.svg.diagonal()
                    .projection(function(d) { return [d.y, d.x]; });
                
                    var svg = d3.select("#sub-accounts-tree").append("svg")
                    .attr("width", width + margin.right + margin.left)
                    .attr("height", height + margin.top + margin.bottom)
                    .append("g")
                    .attr("transform", "translate(" + margin.left + "," + margin.top + ")");
                
                    updateTree();

                    function setSubAccountTree(subAccounts) {
                        var data = subAccounts.map(arr => {
                            return {id: arr.id ,name: arr.name, parent: getParentId(arr.hierarchyPath)}
                        })
                        data.push({id: parseInt(accountId), name: accountName, parent: null})
                        var dataMap = data.reduce(function(map, node) {
                            map[node.id] = node;
                            return map;
                           }, {});

                           var treeData = [];
                           data.forEach(function(node) {
                            var parent = dataMap[node.parent];
                            if (parent) {
                             (parent.children || (parent.children = []))
                              .push(node);
                            } else {
                             treeData.push(node);
                            }
                           });
                        root = treeData[0];
                        updateTree();
                    }

                    function loadPartnerAndSubAccount(accountId) {
                        ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
                            ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
                            ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
                            accountId,
                            null);
                    }



                    function updateTree() {
                        // Compute the new tree layout.
                        var nodes = tree.nodes(root).reverse(),
                            links = tree.links(nodes);
                      
                        // Normalize for fixed-depth.
                        nodes.forEach(function(d) { d.y = d.depth * 180; });
                      
                        // Declare the nodes…
                        var node = svg.selectAll("g.node")
                        .data(nodes, function(d) { return d.id});
                      
                        // Enter the nodes.
                        var nodeEnter = node.enter().append("g")
                            .attr("class", "node")
                            .attr("transform", function(d) { 
                                return "translate(" + d.y + "," + d.x + ")"; });
                      
                        nodeEnter.append("circle")
                            .attr("r", 10)
                            .style("fill", "#fff")
                      
                        nodeEnter.append("text")
                            .attr("x", function(d) { 
                                return d.children || d._children ? -13 : 13; })
                            .attr("dy", ".35em")
                            .attr("text-anchor", function(d) { 
                                return d.children || d._children ? "end" : "start"; })
                            .html(function(d) { 
                                return "<a href='#'>"+d.id+"("+d.name+")</a>"; })
                            .style("fill-opacity", 1);

                        nodeEnter.on("click", function (d) {
                            loadPartnerAndSubAccount(d.id)
                        })
                      
                        // Declare the links…
                        var link = svg.selectAll("path.link")
                            .data(links, function(d) { return d.target.id; });
                      
                        // Enter the links.
                        link.enter().insert("path", "g")
                            .attr("class", "link")
                            .attr("d", diagonal);
                      
                      }

                    ad.transformData = function(adTarget, data) {
                        var ret = [];
                        if (typeof (data) !== 'undefined' && data !== null) {
                            if(adTarget === 'PREFERENCES'){

                                var ignoredKeys = ["id"],
                                    keys = Object.keys(data),
                                    finalKeys = keys.filter(function(ignoredKey) {
                                        return ignoredKeys.indexOf(ignoredKey) === -1;
                                    });

                                finalKeys.forEach(function(key) {
                                    ret.push([key, data[key], findCustomRenderer(key, data), key])
                                });
                            } else if(adTarget === 'PARTNER_AND_SUBACCOUNT') {
                                const isSubAccount = data.accountTypeId == 4
                                const isProspect = data.accountTypeId == 5
                                const isDirectEffectiv = data.accountTypeId == 6
                                const partnerFirstlevelSubAccount = (ad.accountInfoV2.accountTypeId == 2 || ad.accountInfoV2.accountTypeId == 3) && data.hierarchyPath.split("/").length == 3
                                const administerOptions = [
                                    {id: 'isAdministerTrue', name: "True", val: true},
                                    {id: 'isAdministerFalse', name: "False", val: false},
                                ]
                                let administerHtml = ""
                                if(partnerFirstlevelSubAccount) {
                                    administerOptions.forEach(a => {
                                        let checked = a.val === data.administer ? 'checked': ''
                                        administerHtml+= "<label for='"+a.id+"' style='justify-content: flex-start;align-items: flex-start'><input type='radio' id='"+a.id+"' value='"+a.val+"' name='account-administer-radio' "+checked+" onclick=\"confirmAdminister()\" >"+a.name+"</label>"
                                    })
                                } else {
                                    administerHtml = "-"
                                }
                                let isSBDisabled = false;
                                if(data.sponsorBankDetails!==null){
                                    isSBDisabled = true;
                                }
                                const sponsorBankOptions = [
                                    {id: 'isSponsorBankTrue', name: "True", val: true, disabled: isSBDisabled? true : false},
                                    {id: 'isSponsorBankFalse', name: "False", val: false, disabled: isSBDisabled? true : false}
                                ]
                                let sponsorBankHtml = ""

                                sponsorBankOptions.forEach(a => {
                                    let checked = a.val === data.isSponsorBank ? 'checked': ''
                                    let disabled = a.disabled ? 'disabled' : ''
                                    sponsorBankHtml+= "<label for='"+a.id+"' style='justify-content: flex-start;align-items: flex-start'><input type='radio' id='"+a.id+"' value='"+a.val+"' name='account-issponsorbank-radio' "+checked+" "+disabled+" onclick=\"confirmIsSponsorBank()\" >"+a.name+"</label>"
                                })

                                const accountOptions = [
                                    {id: 'accountTypeDirectCustomer', name: "DirectCustomer", val: 1, disabled: !isProspect ?? true},  //shown only for prospects
                                    {id: 'accountTypeReseller', name: "Reseller", val: 2, disabled: (isSubAccount || isProspect || isDirectEffectiv) ? true : false},
                                    {id: 'accountTypeAggregator', name: "Aggregator", val: 3, disabled: true},
                                    {id: 'accountTypeSubAccount', name: "SubAccount", val: 4, disabled: true},
                                    {id: 'accountTypeProspect', name: "PROSPECT", val: 5, disabled: true},
                                    {id: 'accountTypeDirectEffectiv', name: "Direct_RiskOS", val: 6, disabled: true}
                                ]
                                let accountTypeHtml = ""
                                accountOptions.forEach(a => {
                                    let checked = data.accountType === a.name ? 'checked': ''
                                    let disabled = a.disabled ? 'disabled' : ''
                                    accountTypeHtml+= "<label for='"+a.id+"' style='justify-content: flex-start;align-items: flex-start'><input type='radio' id='"+a.id+"' value='"+a.val+"' name='account-type-radio' "+checked+" "+disabled+" onclick=\"confirmAccountType()\" >"+a.name+"</label>"
                                })
                                ret.push(["<span class='selected-account'>"+data.accountId+"</span>", data.name, data.publicId, accountTypeHtml, sponsorBankHtml, data.hierarchyPath, administerHtml])
                                if(data.accountId == accountId) {
                                    setSubAccountTree(data.subAccounts)
                                }
                                if(data.users) setAccountUsers(data.users, data.roles)
                                if(data.permissionTemplates) setPermissionTemplate(data.permissionTemplates)
                                if(data.roles) setAccountRoles(data.roles)
                                if(data.subAccountsNotMigrated) setSubAccountsNotMigrated(data.subAccountsNotMigrated)
                                if(data.isSponsorBank === true) {
                                    $("#nav-linked-programs-tab").attr("hidden", false);
                                    $("#sponsorBankDetailsContainer").attr("hidden", true);
                                    setLinkedPrograms(data.accountId, data.linkedPrograms)
                                } else {
                                    $("#nav-linked-programs-tab").attr("hidden", true);
                                    $("#sponsorBankDetailsContainer").attr("hidden", false);
                                    if(data.sponsorBankDetails){
                                          sponsorBankDetailsHtml = "Linked to Sponsor Bank: <a href='javascript:redirect(" + data.sponsorBankDetails.id + ");'>" + data.sponsorBankDetails.name + "</a>";
                                          $("#sponsorBankDetailsContainer").html(sponsorBankDetailsHtml);
                                      }
                                }
                            } else {
                                for (var d in data) {
                                    var tmp = data[d];
                                    var t = [];
                                    if (adTarget === 'ROLES_PERMISSIONS') {
                                        t.push(tmp.id);
                                        t.push(tmp.label);
                                        t.push(tmp.provisioned);
                                    } else if (adTarget === 'USERS') {
                                        t.push(tmp.id);
                                        t.push(tmp.firstName.concat(" ", tmp.lastName));
                                        t.push(tmp.email);
                                        t.push(tmp.contactNumber);
                                        t.push(tmp.commonRoles);
                                        t.push(tmp.prodRoles);
                                        t.push(tmp.devRoles);
                                        t.push(tmp.primaryAdmin);
                                    } else if (adTarget === 'IP_WHITELISTING') {
                                        if(tmp.environmentName.toUpperCase() === "DEVELOPMENT")
                                            t.push("Certification")
                                         else
                                            t.push(tmp.environmentName);
                                        t.push(tmp.domains);
                                    } else if (adTarget === 'API_KEYS') {
                                        t.push(tmp.accountId);
                                        t.push(tmp.accountName);
                                        if(tmp.environment.toUpperCase() === "DEVELOPMENT")
                                            t.push("Certification")
                                         else
                                            t.push(tmp.environment);
                                        t.push(tmp.activeApiKey);
                                        t.push(tmp.newApiKey);
                                    } else if (adTarget === "DOCUMENT_TYPES") {
                                        t.push(tmp.publicId);
                                        t.push(tmp.name);
                                        t.push("<input type=\"button\" value=\"deprovision\" onclick=\"deprovisionDocType('" + tmp.name + "','" + tmp.publicId + "')\" class=\"DeprovisionDocType\"/>")
                                    } else if (adTarget === "DECISION_LOGIC") {
                                        t.push('<span class="socure-downlaod-button" onclick="downloadLogic(' + tmp.id + ')">' + tmp.id + '</span>')
                                        t.push(tmp.modelName)
                                        t.push(tmp.modelVersion)
                                        t.push(tmp.version)
                                        t.push(tmp.revision)
                                        t.push(tmp.tag)
                                        t.push(tmp.live)
                                        t.push(tmp.createdBy)
                                        t.push(new Date(parseInt(tmp.createdAt)).toLocaleString())
                                        if(ad.rolesCache[ad.CONSTS.ROLE_MAP.ROLE_ENABLE_MULTIPLE_LIVE_LOGICS]===true){
                                            if (Array.isArray(tmp.eventMappings) && tmp.eventMappings.length > 0) {
                                                t.push(tmp.eventMappings.map(mapping => `<span>${mapping}</span>`).join(", "));
                                            } else {
                                                t.push("No Event Mappings");
                                            }
                                            t.push("<input type=\"button\" value=\"mark-as-live\" onclick=\"markAsLive('" + tmp.id + "')\" disabled />")
                                        } else {
                                         t.push("No Event Mappings");
                                         t.push("<input type=\"button\" value=\"mark-as-live\" onclick=\"markAsLive('" + tmp.id + "')\"  />")
                                        }
                                    }
                                    ret.push(t);
                                }
                                if (adTarget === 'ROLES_PERMISSIONS') {
                                    var t = [];
                                    t.push(-103);
                                    t.push("Consent Reason");
                                    t.push(true)
                                    ret.push(t);
                                }
                            }
                        }
                        return ret;
                    };

                    $('#accountdetails-tabs .account-tab').click(
                        function(e) {
                            $this = $(this);
                            $this.tab('show');
                            var adTarget = $this.attr('data-ad-target');
                            var accountId =  $this.attr('data-ad-accountid');
                            var publicId =  $this.attr('data-ad-publicid');
                            if (adTarget === "DOCUMENT_TYPES") {
                                 getUnprovisionedDocTypes();
                            }
                            else if(adTarget === "RATE_LIMITING"){
                                loadRateLimitingPage(accountId);
                            }

                            ad.loadAccountDetails(adTarget,
                                ad.CONSTS.JQ_DT[adTarget],
                                ad.CONSTS.API_URLS[adTarget],
                                accountId,
                                publicId);
                        });

                    ad.loadAccountDetails(defaultADTarget,
                        ad.CONSTS.JQ_DT[defaultADTarget],
                        ad.CONSTS.API_URLS[defaultADTarget],
                        ad.CONSTS.JQ_DP.ACCOUNT_ID.val(),
                        ad.CONSTS.JQ_DP.PUBLIC_ID.val());

                    ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
                        ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
                        ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
                        ad.CONSTS.JQ_DP.ACCOUNT_ID.val(),
                        ad.CONSTS.JQ_DP.PUBLIC_ID.val());

                    $('#revisionMessage').hide();
                    $(document).ajaxStart(function(){
                        $("body").addClass('ajaxLoading');
                    });
                    $(document).ajaxStop(function(){
                        $("body").removeClass('ajaxLoading');
                    });


    function registerEditableAccountRegister() {
        var accountNameEditContainer = $("#accountNameEditContainer");
        accountNameEditContainer.find(".edit-icon").click(function(e) {
            $(this).closest(".email-row").addClass("edit-mode");
        });

        accountNameEditContainer.find(".save-icon").click(function() {

            updateAccountName();
        });

        accountNameEditContainer.find(".cancel-icon").click(function() {
            $(this).closest(".email-row").removeClass("edit-mode");
        });
    }

    registerEditableAccountRegister();

    		$("#provisionDocType").click(function() {
    		    var docPublicId = $("#otherDocTypes option:selected").val();
    		    if (docPublicId === "All") {
    		        provisionAllDocType();
    		    } else {
    		        var name = $( "#otherDocTypes option:selected" ).text();
                    provisionDocType(name, docPublicId)
    		    }
    		});
 });


function toggleCategory(toggleElem) {
    let nameList = toggleElem.parentElement.querySelector('.source-name-list');
    if(window.getComputedStyle(nameList).display === "none") {
        nameList.style.display = "block";
        toggleElem.textContent = "▼";
    } else {
        nameList.style.display = "none";
        toggleElem.textContent = "▶";
    }
}

function filterNames(input) {
    let filter = input.value.toLowerCase();
    let categoryItems = input.nextElementSibling.querySelectorAll('.source-category-item');
    categoryItems.forEach(categoryItem => {
        let nameList = categoryItem.querySelector('.source-name-list');
        let names = nameList.querySelectorAll('.source-name-item');
        let matchCount = 0;
        names.forEach(nameItem => {
            if (nameItem.textContent.toLowerCase().includes(filter) || nameItem.getAttribute('data-id').toLowerCase().includes(filter)) {
                nameItem.style.display = "";
                matchCount++;
            } else {
                nameItem.style.display = "none";
            }
        });
        categoryItem.style.display = matchCount > 0 ? "" : "none";
    });
}

function redirect(id) {
  DD_RUM.track("Open Account Details", { accountId: id});
  if(id) window.location.href = "account_details?"+"accountid="+id;
}

var provisionType = "OldProvisioning";
// reload on click of Improved provisioning tab so latest values if any changes in old provisioning are reflected
function reloadAccountProvisioning(event) {
    handleTabClick(event);
    window.sessionStorage.setItem("ImprovedProvisioningReloaded", true);
    window.location.reload();
}

/************** Datadog track tab view **************/
function handleTabClick(event) {
    DD_RUM.track("Tab viewed " + event.innerText , { tabName: event.innerText, accountId: accountId });
}
/***************************************************/

window.onload = function() {
    DD_RUM && DD_RUM.startView({name: "/account_details"});
    DD_RUM && DD_RUM.setUser({ "id": loggedinUsername});

    if(window.location.href.includes("account_details") && window.sessionStorage.getItem('ImprovedProvisioningReloaded') === 'true') {
        // set these so new provisioning does not keep loading always only when old provisioning tab is active
        $('#nav-old-provisioning-tab').removeClass('active');
        $('#tab_roles_permissions_old').removeClass('active');
        $('#nav-provisioning-tab').addClass('active');
        $('#tab_roles_permissions').addClass('active');
        window.sessionStorage.setItem("ImprovedProvisioningReloaded", false);
        window.sessionStorage.setItem("ImprovedAccountProvisioningActive", true);
    } else {
        window.sessionStorage.setItem("ImprovedAccountProvisioningActive", false);
    }
}


function downloadLogic(id){

    var url = contexturl + '/superadmin/1/decision_logic/v2/logic/' + id + '?accountId=' + accountId + "&environmentTypeId=" + decisionLogicEnvironmentId;
    $.ajax(url, {
        type : 'GET',
        success : function(data) {
            if (data['status'].toLowerCase() == 'ok') {
                var jsonData = vkbeautify.json(data['data'], 4)
                var textToSaveAsBlob = new Blob([jsonData], {type: "text/json"});
                var textToSaveAsURL = window.URL.createObjectURL(textToSaveAsBlob);
                var fileNameToSaveAs = `logic-${id}.json`

                var downloadLink = document.createElement("a");
                downloadLink.download = fileNameToSaveAs;
                downloadLink.innerHTML = "Download File";
                downloadLink.href = textToSaveAsURL;
                downloadLink.onclick = destroyClickedElement;
                downloadLink.style.display = "none";
                document.body.appendChild(downloadLink);
                downloadLink.click();
            } else {
                console.log(`Download logic ${id} failed`)
            }
        }
    });
}

function confirmAccountType() {
    config = {message: "Are you sure you change Account Type?"}
    confirmAction(config).then(function(cleanupPopup) {
        cleanupPopup()
        const accountId = parseInt($(".selected-account").text())
        const accountType = $('input:radio[name=account-type-radio]:checked').val();
        $.ajax({
            url : contexturl + `/superadmin/account/details/1/update/account/${accountId}/accountType/${accountType}`,
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            success : function() {
                ad.accountInfoV2.accountTypeId = accountType
                ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
                ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
                ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
                accountId,
                null);
                  redirect(accountId)
            },
            error : function(error) {
                alert("Error in user updating account type")
                console.log(error);
            }
        })
    }, function cancelHandler() {
        if(ad.accountInfoV2.accountTypeId === 1) {
            $('#accountTypeDirectCustomer').prop('checked', true);
        } else if(ad.accountInfoV2.accountTypeId === 2) {
            $('#accountTypeReseller').prop('checked', true);
        } else if(ad.accountInfoV2.accountTypeId === 3) {
            $('#accountTypeAggregator').prop('checked', true);
        } else if(ad.accountInfoV2.accountTypeId === 4) {
            $('#accountTypeSubAccount').prop('checked', true);
        } else if(ad.accountInfoV2.accountTypeId === 5) {
            $('#accountTypeProspect').prop('checked', true);
        }
    })
}

function confirmAdminister() {
    config = {message: "Are you sure you change Administer?"}
    const accountId = parseInt($(".selected-account").text())
    const administer = $('input:radio[name=account-administer-radio]:checked').val();
    confirmAction(config).then(function(cleanupPopup) {
        cleanupPopup()
        $.ajax({
            url : contexturl + `/superadmin/account/details/1/update/account/${accountId}/administer/${administer}`,
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            success : function() {
                ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
                ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
                ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
                accountId,
                null);            },
            error : function(error) {
                alert("Error in user updating administer flag")
                console.log(error);
            }
        })

    }, function cancelHandler() {
        if(ad.accountInfoV2.administer) {
            $('#isAdministerTrue').prop('checked', true);
        } else {
            $('#isAdministerFalse').prop('checked', true);
        }
    })
}

function toggleLinkedPrograms(isSponsorBank) {
    if(isSponsorBank=="true") {
        $("#tab_linked_programs").show();
        $("#nav-linked-programs-tab").trigger("click");
    } else {
        $("#tab_linked_programs").hide();
        $("#nav-hierarchy-tab").trigger("click");
    }
}

function confirmIsSponsorBank() {
   const isSponsorBank = $('input:radio[name=account-issponsorbank-radio]:checked').val();
   if(isSponsorBank=="true") {
    config = {message: "Are you sure, you want to switch ON the IsSponsorBank Option?"}
   } else {
   config = {message: "Are you sure, you want to switch OFF the IsSponsorBank Option?"}
   }

   const accountId = parseInt($(".selected-account").text())
   confirmAction(config).then(function(cleanupPopup) {
       cleanupPopup()
       $.ajax({
           url : contexturl + `/superadmin/account/details/1/update/account/${accountId}/isSponsorBank/${isSponsorBank}`,
           type: 'POST',
           dataType: 'json',
           contentType: 'application/json',
           success : function() {
               ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
               ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
               ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
               accountId,
               null);
               toggleLinkedPrograms(isSponsorBank)
           },
           error : function(error) {
               alert("Error in updating isSponsorBank Option")
               console.log(error);
           }
       })

   }, function cancelHandler() {
       if(ad.accountInfoV2.isSponsorBank) {
           $('#isSponsorBankTrue').prop('checked', true);
       } else {
           $('#isSponsorBankFalse').prop('checked', true);
       }
   })
}

function get_accounts() {
      $.ajax({
          url: contexturl + "/superadmin/sponsorbank/non-linked",
          type: "GET",
          beforeSend: function() {
              $('#Loading').show();
          },
          complete: function() {
              $('#Loading').hide();
              $("#non-linked-programs.chosen").chosen({
                              width:"40%",
                              search_contains : true
                          });
              $("#non-linked-programs").chosen()
          },
          error: function(e) {
             console.log("error ", e);
          },
          success: function(data) {
              var accountList = data["data"];
              if(data["status"].toLowerCase() === "ok" && accountList.length > 0) {
                  var accountSelectInput = $('#non-linked-programs');
                  for (var i = 0; i<accountList.length; i++){
                      var opt = document.createElement('option');
                      var accountId = accountList[i].id;
                      var accountName = accountList[i].name;
                      opt.value = accountId + "-" + accountName;
                      opt.text = accountId + "-" + accountName;
                      accountSelectInput[0].appendChild(opt);
                  }
              }
              if(data["status"].toLowerCase() === "error") {
                  $('#super_admin_msg')[0].innerHTML = "Failed to load account list to link.";
                  $('#super_admin_msg').css("color", "red");
              }
          }

      })
}

function linkSponsorBank (accountId) {
    var payload = {};
     if(getSelectedAccountId() == 'none') {
        alert("Please select an account");
     }
        payload["programId"] =  getSelectedAccountId();
        payload["username"] = sftpUsername;
        payload["publicAccountId"] = getSelectedPublicAccountId();
        payload["sshKey"] = sshKey;

          $.ajax({
              url: contexturl + "/sftp/user",
              type: "POST",
              cache: false,
              contentType: false,
              processData: false,
              contentType: 'application/json',
              data: JSON.stringify(payload),
              beforeSend: function() {
                  $('#Loading').show();
              },
              complete: function() {
                  $('#Loading').hide();
              },
              error: function(e) {
                  console.log("error creating sftp user ", e);
              },
              success: function(data) {
                  if(data["status"].toLowerCase() === "ok") {
                      $('#super_admin_msg')[0].innerHTML = "Successfully created SFTP user " + payload["username"] + "!!!";
                      $('#super_admin_msg').css("color", "blue");
                      loadSFTPUsers();
                      DD_RUM.track("Create SFTP User success", { accountId: payload["accountId"], sftpUserName: payload["username"], publicAccountId: payload["publicAccountId"]  });
                  }
                  if(data["status"].toLowerCase() === "error") {
                      var errorMsg  = data["msg"] ? data["msg"].split("(")[0] : "";
                      $('#super_admin_msg')[0].innerHTML = "Failed to create SFTP user " + payload["username"] + ". " + errorMsg;
                      $('#super_admin_msg').css("color", "red");
                      DD_RUM.track("Create SFTP User failed", { accountId: payload["accountId"], sftpUserName: payload["username"], publicAccountId: payload["publicAccountId"]  });
                  }
                  //reset input fields
                  $('#sshKey').val('');

              }

          });
      }

function toggleSubAccountCheckBox() {
    $('.subaccount-check').each(function() {
        $(this).prop('checked', $("#selectallSubAccount").get(0).checked);
    });
}

function migratedAllSubAccounts() {
    var table = $("#tbl_account_details_subaccounts_not_migrated").DataTable();
    var data = table.fnGetData()
    var subAccounts = []
    var usersOption = ""
    if(ad.accountInfoV2.users) {
        ad.accountInfoV2.users.forEach(user => {
            var userName = user.firstName+" "+user.lastName
            usersOption += "<option value='"+user.userAccountAssociation.userId+"'>"+userName+" - ("+accountName+") </option>"
        })
    }
    data.forEach(d => {
        var subAccountId = d[1]
        var subAccountusersOption = ""
        var subAccount = ad.accountInfoV2.subAccountsNotMigrated.find(sub => sub.id == subAccountId)
        subAccount.users.forEach(user => {
            var name = user.firstName+" "+user.lastName
            subAccountusersOption += "<option value='"+user.id+"'>"+name+" - ("+subAccount.name+") </option>"
        })
        var usersHtml = "<select class='subaccount-migration-primary-user'>"+usersOption+subAccountusersOption+"</select>"
        subAccounts.push([subAccountId, subAccount.name, usersHtml, "<input value='"+subAccount.id+"' class='sub-account-administer-check' type='checkbox'/>"])
    })
    if(subAccounts.length == 0) {
        alert("No sub accounts present")
    } else {
        var tbljq = $("#tbl_migration_subaccount");
        if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
            dt = tbljq.dataTable();
            dt.fnClearTable();
            dt.fnAddData(subAccounts);
            dt.fnDraw();
        } else {
            tbljq.dataTable({
                "bPaginate": false,
                "bFilter": false, 
                "bInfo": false,
                "oLanguage" : {
                    "sLengthMenu" : "_MENU_ records per page"
                },
                "aaData": subAccounts
            })
            var associate_all_users_subaccount = $("#associate_all_users_subaccount")[0].checked
            tbljq.DataTable().fnSetColumnVis(2,!associate_all_users_subaccount);
            tbljq.DataTable().fnSetColumnVis(3,ad.accountInfoV2.accountTypeId != 1);
        }
        handleSubAccountMigrationModal(true)
    }
}

function migrateSelectedSubAccountHandler() {
    var table = $("#tbl_account_details_subaccounts_not_migrated").DataTable();
    var data = table.fnGetData()
    var subAccounts = []
    var usersOption = ""
    if(ad.accountInfoV2.users) {
        ad.accountInfoV2.users.forEach(user => {
            var userName = user.firstName+" "+user.lastName
            usersOption += "<option value='"+user.userAccountAssociation.userId+"'>"+userName+" - ("+accountName+") </option>"
        })
    }
    data.forEach(d => {
        var subAccountCheckbox = $(`#subaccount-check-${d[1]}`)
        if(subAccountCheckbox && subAccountCheckbox[0] && subAccountCheckbox[0].checked) {
            var subAccountId = d[1]
            var subAccountusersOption = ""
            var subAccount = ad.accountInfoV2.subAccountsNotMigrated.find(sub => sub.id == subAccountId)
            subAccount.users.forEach(user => {
                var name = user.firstName+" "+user.lastName
                subAccountusersOption += "<option value='"+user.id+"'>"+name+" - ("+subAccount.name+") </option>"
            })
            var usersHtml = "<select class='subaccount-migration-primary-user'>"+usersOption+subAccountusersOption+"</select>"
            subAccounts.push([subAccountId, subAccount.name, usersHtml, "<input value='"+subAccount.id+"' class='sub-account-administer-check' type='checkbox'/>"])
        }
    })
    if(subAccounts.length == 0) {
        alert("No sub account selected")
    } else {
        var tbljq = $("#tbl_migration_subaccount");
        if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
            dt = tbljq.dataTable();
            dt.fnClearTable();
            dt.fnAddData(subAccounts);
            dt.fnDraw();
        } else {
            tbljq.dataTable({
                "bPaginate": false,
                "bFilter": false, 
                "bInfo": false,
                "oLanguage" : {
                    "sLengthMenu" : "_MENU_ records per page"
                },
                "aaData": subAccounts
            })
            var associate_all_users_subaccount = $("#associate_all_users_subaccount")[0].checked
            tbljq.DataTable().fnSetColumnVis(2,!associate_all_users_subaccount);
            tbljq.DataTable().fnSetColumnVis(3,ad.accountInfoV2.accountTypeId != 1);
        }
        handleSubAccountMigrationModal(true)
    }
}

function migrateSubAccount() {
    var table = $("#tbl_migration_subaccount").DataTable();
    var data = table.fnGetData()
    var primaryUsers = $(".subaccount-migration-primary-user") 
    var adminsters = $(".sub-account-administer-check")
    var associateAllUsers = $("#associate_all_users_subaccount")[0].checked
    var subaccounts = []
    data.forEach((d,i) => {
        var primaryUser = null
            if(!associateAllUsers) {
                primaryUser = primaryUsers[i].value
            }
        if(ad.accountInfoV2.accountTypeId == 1) {
            subaccounts.push({
                accountId: d[0],
                userIdOption: primaryUser ? parseInt(primaryUser) : null,
                administerOption: true
            })
        } else {
            var adminster = adminsters[i].checked
            subaccounts.push({
                accountId: d[0],
                userIdOption: primaryUser ? parseInt(primaryUser) : null,
                administerOption: adminster
            })
        }
    })
    config = {message: "Are you sure you want to migrate these sub accounts?"}
    confirmAction(config).then(function(cleanupPopup) {
        cleanupPopup()
        $.ajax({
            url : contexturl + '/superadmin/account/details/1/migrate/subaccount',
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify({
                accountId: parseInt(accountId),
                subAccounts: subaccounts,
                associateAllUsersToSubAccount: associateAllUsers
            }),
            contentType: 'application/json',
            success : function(data) {
                handleSubAccountMigrationModal(false)
                ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
                ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
                ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
                accountId,
                null);
            },
            error : function(error) {
                closeMigrationModal()
                alert("Error in Migrating sub account")
            }
        })
    })
}

function handleSubAccountMigrationModal(flag) {
    if(flag) {
        $('#SubAccountMigrationModal').modal('show');
    } else {
        $('#SubAccountMigrationModal').modal('hide');
    }
}

function toggleMigrationCheckBox() {
    $('.migration-subaccount-check').each(function() {
        $(this).prop('checked', $("#selectall").get(0).checked);
    });
}

function getAccountType(accountId) {
    if(accountId == 1) return "Direct Customer"
    else if(accountId == 2) return "Reseller"
    else if(accountId == 3) return "Aggregator"
    else if(accountId == 5) return "Prospect"
}

const RoleType = {
    "AccountOwner": 1,
    "Administrator": 2,
    "Analyst": 3,
    "Developer": 4,
    "CaseAnalyst": 5,
    "CaseSupervisor": 6,
    "CaseOfficer": 7,
    "DocVAnalyst": 8,
    "BSAOfficer": 9,
    "ComplianceAnalyst":10,
    "IntegrationManager":11,
    "Prospect":12,
    "RiskOsSupportViewer":13,
    "RiskOsSupportAdmin":14
 };

 Object.freeze(RoleType);

function getRoleType(roleType) {
    if(roleType == RoleType.AccountOwner) return "Account Owner"
    else if(roleType == RoleType.Administrator) return "Administrator"
    else if(roleType == RoleType.Analyst) return "Analyst"
    else if(roleType == RoleType.Developer) return "Developer"
    else if(roleType == RoleType.CaseAnalyst) return "Case Analyst"
    else if(roleType == RoleType.CaseSupervisor) return "Case Supervisor"
    else if(roleType == RoleType.CaseOfficer) return "Case Officer"
    else if(roleType == RoleType.DocVAnalyst) return "DocV Analyst"
    else if(roleType == RoleType.BSAOfficer) return "BSA Officer"
    else if(roleType == RoleType.ComplianceAnalyst) return "Compliance Analyst"
    else if(roleType == RoleType.IntegrationManager) return "Integration Manager"
    else if(roleType == RoleType.Prospect) return "Prospect"
    else if(roleType == RoleType.RiskOsSupportViewer) return "RiskOS Support Viewer"
    else if(roleType == RoleType.RiskOsSupportAdmin) return "RiskOS Support Admin"
}

function switchMerge(isMerge) {
    if(isMerge) {
        $("#account-type").hide();
        $("#migrated_account_form").show()
        $("#migrated_account_form_label").html("Merge Account")
        $("#migrate-btn").html("Merge")
        fetchMigratedParentAccount()
    } else {
        $("#migrate-btn").html("Migrate")
        $("#account-type").show();
        updateAccountType()
    }
}

function updateAccountType() {
    var accountType = $("#migration_account_type").val()
    var table = $("#tbl_migration_subaccount_details").DataTable();
    if(accountType == 4) {
        $("#migrated_account_form").show()
        $("#migrated_account_form_label").html("Parent Account")
        fetchMigratedParentAccount()
    } else {
        $("#migrated_account_form").hide()
    }
    table.fnSetColumnVis(4, accountType != 1 );
}

function fetchMigratedParentAccount() {
    $.ajax({
        url : contexturl + '/superadmin/account/details/1/migrated/parents',
        type : 'GET',
        contentType: 'application/json',
        success : function(data) {
            var migratedAccountHtml = ""
            data.data.forEach(account => {
                migratedAccountHtml+="<option account-type='"+account.accountType+"' value='"+account.id+"'>"+account.name+" ("+account.id+") ("+getAccountType(account.accountType)+")</option>"
            })
            $("#migrated_account_dropdown").html(migratedAccountHtml)
            updateMigratedAccount()
        },error : function(error) {
            alert("Error in fetching migrated account")
        }
    })
}

function updateMigratedAccount() {
    var selectedAccountType = $("#migrated_account_dropdown").find(':selected').attr('account-type')
    var table = $("#tbl_migration_subaccount_details").DataTable();
    var isAccountMerge = $('input[name=migration-type]:checked').val() == 'merge'
    if(isAccountMerge) {
        $("#account_administer_form").hide()
        if(selectedAccountType != 1) {
            table.fnSetColumnVis(4, true);
        } else {
            table.fnSetColumnVis(4, false);
        }
    } else {
        if(selectedAccountType != 1) {
            $("#account_administer_form").show()
        } else {
            $("#account_administer_form").hide()
        }
        table.fnSetColumnVis(4, false);
    }
}

function associateAllUserClick() {
    var tbljq = $("#tbl_migration_subaccount_details");
    if($("#associate_all_users")[0].checked) {
        tbljq.DataTable().fnSetColumnVis(3,false);
    } else {
        tbljq.DataTable().fnSetColumnVis(3,true);
    }
}

function associateAllUserSubAccountClick() {
    var tbljq = $("#tbl_migration_subaccount");
    if($("#associate_all_users_subaccount")[0].checked) {
        tbljq.DataTable().fnSetColumnVis(2,false);
    } else {
        tbljq.DataTable().fnSetColumnVis(2,true);
    }
}

function closeMigrationModal() {
    $('#migrationModal').modal('hide');
}

function showMigrationModal() {
    $.ajax({
        url : contexturl + '/superadmin/account/details/1/migrate/'+accountId,
        type : 'GET',
        contentType: 'application/json',
        success : function(data) {
            var migrationAccountDetails = data.data
            
            var users = []
            var usersOption = ""
            var accountName = migrationAccountDetails.accountName
            migrationAccountDetails.users.forEach(user => {
                var name = user.firstName+" "+user.lastName
                usersOption += "<option value='"+user.id+"'>"+name+" - ("+accountName+") </option>"
                users.push([user.id, name, user.email, "<input value='"+user.id+"' class='assign-permissions-check' type='checkbox'/>" , user.contactNumber, user.commonRoles.join(" ,"), user.productionRoles, user.developmentRoles ,user.sandboxRoles, user.primaryAdmin])
            })
            
            var tbljq = $("#tbl_migration_user_details");
            if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
                dt = tbljq.dataTable();
                dt.fnClearTable();
                dt.fnAddData(users);
                dt.fnDraw();
            } else {
                tbljq.dataTable({
                    "bPaginate": false,
                    "bFilter": false, 
                    "bInfo": false,
                    "oLanguage" : {
                        "sLengthMenu" : "_MENU_ records per page"
                    },
                    "aaData": users
                })
            }

            var subAccounts = []
            migrationAccountDetails.subAccounts.forEach((subAccount,i) => {
                var subAccountusersOption = ""
                subAccount.users.forEach(user => {
                    var name = user.firstName+" "+user.lastName
                    subAccountusersOption += "<option value='"+user.id+"'>"+name+" - ("+subAccount.accountName+") </option>"
                })
                var usersHtml = "<select class='migration-primary-user'>"+usersOption+subAccountusersOption+"</select>"
                subAccounts.push(["<input value='"+subAccount.accountId+"' class='migration-subaccount-check' type='checkbox'/>", subAccount.accountId, subAccount.accountName, usersHtml,"<input value='"+subAccount.accountId+"' class='account-administer-check' type='checkbox'/>"])
            })
            var tbljq = $("#tbl_migration_subaccount_details");
            if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
                dt = tbljq.dataTable();
                dt.fnClearTable();
                dt.fnAddData(subAccounts);
                dt.fnDraw();
            } else {
                tbljq.dataTable({
                    "bPaginate": false,
                    "bFilter": false, 
                    "bInfo": false,
                    "oLanguage" : {
                        "sLengthMenu" : "_MENU_ records per page"
                    },
                    "aaData": subAccounts
                })
                tbljq.DataTable().fnSetColumnVis(4,false);
            }

        },
        error : function(error) {
            alert("Error in fetching account details")
        }
    })
    $('#migrationModal').modal('show');
}

function migrate() {
    var table = $("#tbl_migration_subaccount_details").DataTable();
    var data = table.fnGetData()
    var accountType = parseInt($("#migration_account_type").val())
    var subaccounts = []
    var subAccountCheckbox = $(".migration-subaccount-check")
    var primaryUsers = $(".migration-primary-user") 
    var adminsters = $(".account-administer-check")
    var associateAllUsers = $("#associate_all_users")[0].checked
    var parentAccountId = null
    var mergeAccountId = null
    var parentAccountType  = null
    var administer = null
    var isValid = true
    var isAccountMerge = $('input[name=migration-type]:checked').val() == 'merge'
    if(accountType == 4) {
        parentAccountId = parseInt($("#migrated_account_dropdown").val())
        parentAccountType = $("#migrated_account_dropdown").find(':selected').attr('account-type')
        if(parentAccountType != 1) {
            administer = $("#parent_account_administer_check")[0].checked
        }
    }
    if(isAccountMerge) {
        mergeAccountId = parseInt($("#migrated_account_dropdown").val())
        accountType = $("#migrated_account_dropdown").find(':selected').attr('account-type')

    }
    data.every((d,i) => {
        if(subAccountCheckbox[i].checked) {
            var primaryUser = null
            if(!associateAllUsers) {
                primaryUser = primaryUsers[i].value
            }
            if(accountType == 1 || accountType == 4) {
                subaccounts.push({
                    accountId: d[1],
                    userIdOption: primaryUser ? parseInt(primaryUser) : null,
                    administerOption: true
                })
            } else {
                var adminster = adminsters[i].checked
                subaccounts.push({
                    accountId: d[1],
                    userIdOption: primaryUser ? parseInt(primaryUser) : null,
                    administerOption: adminster
                })
                if(!primaryUser && !adminster && !associateAllUsers) {
                    isValid = false
                    return false
                }
            }
        }
        return true
    })
    const userIdsWithAllPermission = [];
    const permissionCheckbox = $(".assign-permissions-check")
    for(let i=0;i < permissionCheckbox.length;i++) {
        if(permissionCheckbox[i].checked) {
            userIdsWithAllPermission.push(parseInt(permissionCheckbox[i].value))
        }
    }
    if(isValid) {
        config = {message: `Are you sure you want to ${isAccountMerge?'merge':'migrate'} this account?`}
        confirmAction(config).then(function(cleanupPopup) {
            cleanupPopup()
            if(isAccountMerge) {
                $.ajax({
                    url : contexturl + '/superadmin/account/details/1/merge',
                    type: 'POST',
                    dataType: 'json',
                    data: JSON.stringify({
                        mergeAccountId: parseInt(mergeAccountId),
                        accountId: parseInt(accountId),
                        userIdsWithAllPermission: userIdsWithAllPermission,
                        subAccounts: subaccounts,
                        associateAllUsersToSubAccount: associateAllUsers
                    }),
                    contentType: 'application/json',
                    success : function(data) {
                        $("#nav-partner-subaccount-tab").attr("hidden", false);
                        $("#migrationBtnWrapper").hide()
                        closeMigrationModal()
                    },
                    error : function(error) {
                        closeMigrationModal()
                        alert("Error in Merging account")
                    }
                })
            } else {
                $.ajax({
                    url : contexturl + '/superadmin/account/details/1/migrate',
                    type: 'POST',
                    dataType: 'json',
                    data: JSON.stringify({
                        accountId: parseInt(accountId),
                        accountType: accountType,
                        userIdsWithAllPermission: userIdsWithAllPermission,
                        subAccounts: subaccounts,
                        associateAllUsersToSubAccount: associateAllUsers,
                        parentAccountId: parentAccountId,
                        administer: administer
                    }),
                    contentType: 'application/json',
                    success : function(data) {
                        $("#nav-partner-subaccount-tab").attr("hidden", false);
                        $("#migrationBtnWrapper").hide()
                        closeMigrationModal()
                    },
                    error : function(error) {
                        closeMigrationModal()
                        alert("Error in Migrating account")
                    }
                })
            }
        })
    } else {
        alert("For partner account, primary user is required if adminster is not present")
    }
    
}


function usersEmailRenderer(oObj) {
    var allowEdit = $("#allowEdit").val();
    if (allowEdit === "true") {
        return "<div class=\"email-row\">" +
            "<div class=\"email-label\"><span class=\"text\">" +
            oObj.aData[2] + "</span>" +
            "<span class=\"edit-icon icon clickable\">" +
            "</span></div>"+
            "<div class=\"email-editable\">" +
            "<input type=\"text\" class=\"form-control email-edit-input\" value=\"" + oObj.aData[2] + "\" />" +
            "<span class=\"save-icon icon clickable\"></span>" +
            "<span class=\"cancel-icon icon clickable\"></span>" +
        "</div>";
    } else {
        return oObj.aData[2];
    }
}

function onUsersEmailCreatedCell(td, cellData, rowData) {
    var $td = $(td);
    $td.addClass("nowrap");
    $td.find(".edit-icon").click(function(e) {
        $(this).closest(".email-row").addClass("edit-mode");
    });
    $td.find(".save-icon").click(function() {
        confirmAction().then(function(cleanupPopup) {
            updateEmail(rowData, $td).done(function() {
                DD_RUM.track("User email edited", { accountId: accountId });
                cleanupPopup();
                $td.find(".email-row").removeClass("edit-mode");
            })
        });
    });
    $td.find(".cancel-icon").click(function() {
        $(this).closest(".email-row").removeClass("edit-mode");
    });
}

function onIsPrimaryAdminColumnCreated(td, cellData, rowData) {
    var $td = $(td),
        config = {message: "Are you sure you want to promote this user as the primary account user?"}
    $td.find("input.is-primary").change(function(e) {
        var isPrimary = e.currentTarget.checked;

        confirmAction(config).then(function(cleanupPopup) {
            togglePrimaryAdmin(rowData[0]).then(function() {
                cleanupPopup();
                $('#nav-users-tab').click();
                toastr.success("Account details updated successfully.")
            }, function() {
                toastr.showGenericErrorMessage();
                e.currentTarget.checked = false;
                cleanupPopup();
            });
        }, function() {
            e.currentTarget.checked = false;
        });

    });
}

function togglePrimaryAdmin(userId) {
    var def = $.Deferred()
    $.ajax({
        url : ad.CONSTS.API_URLS.BASE_URL + 'user/toggle/primary',
        data : JSON.stringify({
            userId : userId
        }),
        type : 'PUT',
        contentType: 'application/json',
        success : function(data) {
            if (data.status.toLowerCase() === "error") {
                def.reject(data);
            } else {
                def.resolve(data);
            }
        },
        error : function(error) {
            def.reject(error);
        }
    });
    return def.promise();
}

function updateEmail(rowData, $td) {
    var def = $.Deferred();
    var userId = rowData[0];
    var emailId = $td.find(".email-editable input.email-edit-input").val()

    $.ajax({
        url : contexturl + "/superadmin/account/edit/1/update/email",
        data : JSON.stringify({
            accountid: $("#adAccountId")[0].value,
            userId : userId,
            email: emailId
        }),
        type : 'PUT',
        contentType: 'application/json',
        success : function(data) {
            if (data.status.toLowerCase() === "error") {
                toastr.error("Email update failed");
                def.reject(data);
            } else {
                toastr.success("Email updated successfully");
                $td.find(".email-label span.text").text(emailId);
                def.resolve(data);
            }
        },
        error : function(error) {
            def.reject(error);
        }
    });
    return def.promise();
}

function closePopup() {
    this.modal('hide');
    $(this).find("#message").text("Are you sure?");
}

function confirmAction(config) {
    var def = $.Deferred(),
        modalElement = $('#saveConfirmModal');

    if (config && config.message) {
        modalElement.find("#message").text(config.message);
    }

    modalElement.on('shown.bs.modal', function() {
        var $this = $(this);
        $this.find('.btn-save').click(function() {
            def.resolve(closePopup.bind(modalElement));
        });

        $this.find('.btn-cancel').click(function() {
            closePopup.call(modalElement);
            def.reject();
        });
    });
    modalElement.on('hidden.bs.modal', function() {
        // resetting related events
        var $this = $(this);
        $this.find('.btn-save').off('click');
        $this.find('.btn-cancel').off('click');
        modalElement.off('shown.bs.modal');
    });
    modalElement.modal('show');
    return def.promise();
}

function arrayToHtmlList(arrayList){
    if(arrayList !== undefined && arrayList.length !== 0) arrayList.sort();
    var html = '<ul>';
    for(var i=0; i<arrayList.length; i++) {
        html += '<li>' + arrayList[i] + '</li>';
    }
    html += '</ul>';
    return(html);
}

function csvToHtmlList(str){
    if(str !== undefined && str.trim() !== "") {
        return(arrayToHtmlList(str.split(",")));
    } else return "";
}

function updateAccountName() {
    var newAccountName = $('#accountNameInput').val();
    if (newAccountName) {
        confirmAction().then(function(cleanupPopup) {
            $.ajax({
                url: contexturl + "/superadmin/account/edit/1/update/name-by-public-id",
                data: {
                    "publicid": $('#publicId').val(),
                    "accountname": newAccountName
                },
                type : 'POST',
                success: function(data) {
                    cleanupPopup();
                    if (data.status.toLowerCase === "error") {
                        toastr.error("Problem in updating Account name");
                    } else {
                        toastr.success("Account name updated successfully");
                        var accountNameEditContainer = $("#accountNameEditContainer");
                        accountNameEditContainer.find(".email-row").removeClass("edit-mode");
                        accountNameEditContainer.find(".email-row .email-label > span.label").text(newAccountName);
                    }
                },
                error: function(error) {
                    cleanupPopup();
                    toastr.error("Problem in updating Account name");
                    console.log(error);
                }
            });
        }, genericErrorMessageHandler);
    }
}

function showNewRevisionForm() {
    $('#errorMsg').hide();
    $('#AddNewRevision').off('shown.bs.modal').on('shown.bs.modal', function() {
        setTimeout(3000);
    }).modal('show');
    document.getElementById('add_new_revision').reset()
};

function showSimulateExecutionForm() {
    $('#sfErrorMsg').hide();
    $('#simulate_csv_file').show();
    $('#simulate_past_txn_count').hide();
    $('#simulate_decision_filter').hide();
    $('#simulate_past_txn_end_dt').hide();
    $('#simulate_past_txn_start_dt').hide();
    $('#SimulateExecution').off('shown.bs.modal').on('shown.bs.modal', function() {
        setTimeout(3000);
    }).modal('show');
    document.getElementById('simulate_execution').reset()
}

function csvClick() {
    $('#simulate_csv_file').show();
    $('#simulate_past_txn_count').hide();
    $('#simulate_decision_filter').hide();
    $('#simulate_past_txn_end_dt').hide();
    $('#simulate_past_txn_start_dt').hide();
}

function transactionClick() {
    $('#simulate_csv_file').hide();
    $('#simulate_past_txn_count').show();
    $('#simulate_decision_filter').show();
    $('#simulate_past_txn_end_dt').show();
    $('#simulate_past_txn_start_dt').show();
}

function getFileExtension(form) {
    var fileName = $(form.file).get(0).files[0].name
    return (/[.]/.exec(fileName)) ? /[^.]+$/.exec(fileName)[0] : undefined;
}

function isNumber(n) {
	return !isNaN(parseInt(n)) && isFinite(n);
}

function simulateExecution(form) {
    var endpoint = "";
    var logicSimulation = new FormData();
    var errorMsg = document.getElementById("sfErrorMsg")
    form = document.getElementById('simulate_execution')
    var revision = form.revision.value
    var accountId = $("#adAccountId")[0].value
    if (isNumber(revision)) {
        logicSimulation.append("revision", parseInt(revision))
    } else if (revision) {
        $('#sfErrorMsg').show().delay(5000).hide("slow");
        errorMsg.innerHTML = "Revision should be a number!"
        return
    }
    if (form.isCSV.value === "true") {
        if($.trim($(form.file).val()).length === 0 || !getFileExtension(form) || getFileExtension(form) !== "csv"){
            $('#sfErrorMsg').show().delay(5000).hide("slow");
            errorMsg.innerHTML = "Please choose a valid .csv file!"
            return
        }
        var file = $(form.file).get(0).files[0]
        logicSimulation.append("inputCSV", file)
        endpoint = contexturl + "/superadmin/1/decision_logic/" + accountId + "/simulate/csv"
    } else {
        if (form.isCSV.value === "false" && (!isNumber(form.pastTransactionsCount.value))) {
            $('#sfErrorMsg').show().delay(5000).hide("slow");
            errorMsg.innerHTML = "Please fill all the fields!"
            return
        }
        logicSimulation.append("transactionsCount", parseInt(form.pastTransactionsCount.value))
        logicSimulation.append("transactionsStartDate", form.pastTransactionStartDate.value)
        logicSimulation.append("transactionsEndDate", form.pastTransactionEndDate.value)
        logicSimulation.append("decision", form.pastTransactionDecisionFilter.value)
        endpoint = contexturl + "/superadmin/1/decision_logic/" + accountId + "/simulate/pasttransactions"
    }
    $('#sfErrorMsg').html("Processing simulation request...");
    $('#sfErrorMsg').show();
    fetch(endpoint, {
        method: 'POST',
        body: logicSimulation
    }).then(response => {
        if (!response.ok) {
            return response.json();
        } else {
            return response.blob();
        }
    }).then(myBlob => {
        if (myBlob["size"]) {
            var textToSaveAsURL = window.URL.createObjectURL(myBlob);
            var fileNameToSaveAs = "output.csv";
            var downloadLink = document.createElement("a");
            downloadLink.download = fileNameToSaveAs;
            downloadLink.innerHTML = "Download File";
            downloadLink.href = textToSaveAsURL;
            downloadLink.onclick = destroyClickedElement;
            downloadLink.style.display = "none";
            document.body.appendChild(downloadLink);
            downloadLink.click();
            $('#accountdetails-tabs').find('a#nav-tab_decision_logic').trigger('click')
            $("#SimulateExecution").modal("hide");
            form.reset();
        } else {
            form.reset();
            $("#SimulateExecution").modal("show");
            $('#sfErrorMsg').html(myBlob);
            $('#sfErrorMsg').show().delay(5000).hide("slow");
        }
    }).catch(err => {
        $("#SimulateExecution").modal("show");
        $('#sfErrorMsg').html(err);
        $('#sfErrorMsg').show().delay(5000).hide("slow");
        console.error(err)
    });
}

function markAsLive(logicId) {
    var accountId = $("#adAccountId")[0].value
    fetch(contexturl + "/superadmin/1/decision_logic/v2/logic/" + logicId + "/activate", {
        method: 'POST'
    }).then(response => {
        if (!response.ok) {
            $('#revisionMessage').show().delay(5000).hide("slow");
            document.getElementById("revisionMessage").innerHTML = "Error occurred while marking the revision as live!";
        } else {
            $('#accountdetails-tabs').find('a#nav-tab_decision_logic').trigger('click')
            document.getElementById("revisionMessage").innerHTML = "Selected revision is marked as live successfully!";
            $('#revisionMessage').show().delay(5000).hide("slow");
        }
    }).catch(function (err) {
        $('#revisionMessage').show().delay(5000).hide("slow");
        document.getElementById("revisionMessage").innerHTML = "Error occurred while marking the revision as live!";
    });
}

function destroyClickedElement(event) {
    document.body.removeChild(event.target);
}

function saveNewRevision(form) {
    var decisionLogicRevision = new FormData();
    var errorMsg = document.getElementById("errorMsg")
    form = document.getElementById('add_new_revision')
    var isLive = document.getElementById('isLive').checked
    if (form.modelName.value === "" || form.modelVersion.value === "" || form.tag.value === "" || isLive === "") {
        $('#errorMsg').show();
        errorMsg.innerHTML = "Please fill all the fields!"
        return
    }
    if($.trim($(form.file).val()).length != 0 && $(form.file).get(0).files[0] != undefined && getFileExtension(form) === "conf"){
        var file = $(form.file).get(0).files[0]
        decisionLogicRevision.append("logic", file)
    } else {
        $('#errorMsg').show();
        errorMsg.innerHTML = "Please choose a valid .conf file!"
        return
    }
    decisionLogicRevision.append("accountIds", $("#adAccountId")[0].value)
    if(form.decision_logic_create_environment.value == "0"){
        decisionLogicRevision.append("envIds", "1,2,3")
    } else {
        decisionLogicRevision.append("envIds", form.decision_logic_create_environment.value)
    }
    decisionLogicRevision.append("modelVersion", form.modelVersion.value)
    decisionLogicRevision.append("modelName", form.modelName.value)
    decisionLogicRevision.append("tag", form.tag.value)
    decisionLogicRevision.append("isLive", isLive)

    fetch(contexturl + "/superadmin/1/decision_logic/v2/logic/bulk", {
        method: 'POST',
        body: decisionLogicRevision
    }).then(async function  (response) {
        if (response.ok) {
            if(form.decision_logic_create_environment.value == 0){
                decisionLogicEnvironmentId = "1"
            } else {
                decisionLogicEnvironmentId = form.decision_logic_create_environment.value
            }
            $("#decision_logic_environment").val(decisionLogicEnvironmentId)
            $('#accountdetails-tabs').find('a#nav-tab_decision_logic').trigger('click')
            document.getElementById("revisionMessage").innerHTML = "New Revision is added successfully!";
            $('#revisionMessage').show().delay(5000).hide("slow");
            $("#AddNewRevision").modal("hide");
            form.reset();
        } else {
             const json = await response.json()
            $('#errorMsg').show().delay(5000).hide("slow");
            $("#AddNewRevision").modal("show");
            $('#errorMsg').html(json['msg'] || data['msg']);
            form.reset();
        }
    }).catch(function (err) {
        $("#AddNewRevision").modal("show");
        $('#errorMsg').html(err);
        $('#errorMsg').show().delay(5000).hide("slow");
        form.reset();
    });
}

function genericErrorMessageHandler(error) {
    toastr.showGenericErrorMessage();
}

function setMarkAsInternal(accountId) {
    $('#account_details_msg').html('');
    var selValue = $('input[name=pInternal]:checked').val();
    if(checkState('Internal', selValue))
        show_confirm_provision_dialog(selValue,accountId, 'Internal');
}

function setProvisioning(accountId, roleId, publicId) {
    $('#account_details_msg').html('');
    var selValue = $('input[name=p'+roleId+']:checked').val();
    if(selValue === "on") {
       DD_RUM.track("Provision", { accountId: accountId, provType: provisionType });
    }
    if(selValue === "off") {
         DD_RUM.track("DeProvision", { accountId: accountId, provType: provisionType });
    }
    if(checkState(roleId, selValue))
        show_confirm_provision_dialog(selValue,accountId, roleId, publicId);
}

function handleKYC(obj) {
    var kyc = $('#' + obj.id).val();
    var bme = $('#prole164');
    var kycPlus = $('#prole176');

    if(kyc === 'on') {
        $('#chkGrp13')[0].style.display = "block";
        bme[0].checked = true;
    }
    if(kyc === 'off') {
        $('#chkGrp13')[0].style.display = "none";
        bme[0].checked = false;
        $('#group164').attr('data-curstate', false);
        kycPlus[0].checked = false;
        $('#group176').attr('data-curstate', false);
        $('#group164').children().attr("disabled", false);

    }
}

function checkState(roleId, selValue) {
    var curState = $('#group'+roleId).data('curstate');

    if(curState && selValue === "on" || !curState && selValue === "off"){
        return(false);
    }else{
        return(true);
    }
}

function revertSelection(flag){
     DD_RUM.track("Reset Provision", { accountId: accountId, provType: provisionType });
    var roleId;
    if(flag){
        roleId = $('#ProvisionMessageModal').data('roleid');
        $('#ProvisionMessageModal').modal('hide');
    }else{
        roleId = $('#DeprovisionMessageModal').data('roleid');
        $('#DeprovisionMessageModal').modal('hide');
    }
    var curState = $('#group'+roleId).data('curstate');
    if(curState){
        $('#py'+roleId).prop('checked', true);
    }else{
        $('#pn'+roleId).prop('checked', true);
    }

}

function activateMla(field1, field2, accountId, roleId, curState) {

    var url = contexturl + "/superadmin/mla/update/field";
    $.ajax({
           url : url,
           data : {
             accountId : accountId,
             memberNumber : field1,
             securityCode : field2
           },
           type : 'POST',
           success : function(data) {
               if (data['status'].toLowerCase() === 'ok') {
                   provisionRole(accountId, roleId, curState)
               }else{
                   if(!curState){
                       $('#pn'+roleId).prop('checked', true);
                   }
                   var errorMessage = 'Role not updated...some thing went wrong';
                   if (data.data.errorCode === "500") {
                       errorMessage = "Role not updated due to one of the critical parent permission is not enabled."
                   }
                   $('#account_details_msg').html(errorMessage);
                   (roleId == ad.CONSTS.ROLE_MAP.ROLE_MLA_SERVICE) && getSubscriptionTypeProvisions(accountId);
               }
           }
       });
}

function activateEcbsv(ein, accountId, roleId) {
    var url = contexturl + "/superadmin/ein/update/field";
    $.ajax({
        url : url,
        data : {
            accountId : accountId,
            ein : ein
        },
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok') {
                provisionRole(accountId, roleId, true)
            }else{
                $('#pn'+roleId).prop('checked', true);
                var errorMessage = 'Role not updated...some thing went wrong';
                $('#account_details_msg').html(errorMessage);
            }
        },
        error : function() {
            $('#pn'+roleId).prop('checked', true);
            var errorMessage = 'Role not updated...some thing went wrong';
            $('#account_details_msg').html(errorMessage);
        }
    });
}

function provisionRole(accountId, roleId, curState){
    var url = contexturl + "/superadmin/account/edit/1/provision";
    $.ajax({
        url : url,
        data : {
            accountid : accountId,
            roleid : roleId,
            async : false
        },
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                if (roleId === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) {
                    $("#nav-tab_docTypes").attr("hidden", false);
                    $("#nav-tab_docv").attr("hidden", false);
                }
                if (roleId === ad.CONSTS.ROLE_MAP.ROLE_DECISION_SERVICE) {
                    $("#nav-tab_decision_logic").attr("hidden", false);
                }
                $('accountdetails-tabs').find('a[href=#tab_roles_permissions]').trigger('click');
                $('#account_details_msg').html('Role updated.');
                $('#group'+roleId).data('curstate', true);
                if(roleId === ad.CONSTS.PREFILL.parentGroupId) {
                    $('#chkGrp'+ad.CONSTS.PREFILL.parentGroupId).show()
                }
                ad.rolesCache[roleId] = true;
                showHideAdditionalSection(roleId);
                handleAdditionalAction(roleId);
                (roleId == ad.CONSTS.ROLE_MAP.ROLE_WATCHLIST_3_0 || roleId === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) && getSubscriptionTypeProvisions(accountId);
                (roleId == ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION_V2 && provisionAllDocTypes() || roleId == ad.CONSTS.ROLE_MAP.ROLE_NEW_CAPTURE_APP_INTERFACE)
            }else{
                if(!curState){
                    $('#pn'+roleId).prop('checked', true);
                }
                var errorMessage = 'Role not updated...some thing went wrong';
                if (data.data.errorCode === "500") {
                    errorMessage = "Role not updated due to one of the critical parent permission is not enabled."
                }
                if(roleId === 127 && data.data.errorCode === "405") {
                    errorMessage = "Not able to enable because the account is not in V2"
                }
                $('#account_details_msg').html(errorMessage);
                (roleId == ad.CONSTS.ROLE_MAP.ROLE_WATCHLIST_3_0 || roleId === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) && getSubscriptionTypeProvisions(accountId);
            }
        }
    });
}

function provisionAllDocTypes() {
    var accountId = $('#publicId').val();
    var url = contexturl + "/superadmin/1/document_manager/types/provision/" + accountId;
    $.ajax({
        url : url,
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok') {
                $('#account_details_msg').html('All document types provisioned successfully...');
            } else {
                $('#account_details_msg').html(data['msg']);
            }
        }
    });
}

function deProvisionRole(accountId, roleId, curState) {
    var url = contexturl + "/superadmin/account/edit/1/deprovision";
    $.ajax({
        url : url,
        data : {
            accountid : accountId,
            roleid : roleId,
            async : false
        },
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                if (roleId === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) {
                    $("#nav-tab_docTypes").attr("hidden", true);
                    $("#nav-tab_docv").attr("hidden", true);
                }
                if (roleId === ad.CONSTS.ROLE_MAP.ROLE_DECISION_SERVICE) {
                    $("#nav-tab_decision_logic").attr("hidden", true);
                }
                $('accountdetails-tabs').find('a[href=#tab_roles_permissions]').trigger('click');
                $('#account_details_msg').html('Role updated.');
                $('#group'+roleId).data('curstate', false);
                if(roleId === ad.CONSTS.PREFILL.parentGroupId) {
                    $('#chkGrp'+ad.CONSTS.PREFILL.parentGroupId).hide()
                }
                ad.rolesCache[roleId] = false;
                showHideAdditionalSection(roleId);
                handleAdditionalAction(roleId);
                //handleAdditionalActionForDeprovision(roleId);
                (roleId == ad.CONSTS.ROLE_MAP.ROLE_WATCHLIST_3_0 || roleId === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) && getSubscriptionTypeProvisions(accountId);
            }else{
                if(curState){
                    $('#py'+roleId).prop('checked', true);
                }
                $('#account_details_msg').html("Role not updated...some thing went wrong");
            }
        }
    });
}

function markAsInternal(accountId, roleId, curState) {
    var url = contexturl + "/superadmin/account/edit/1/mark/internal";
    $.ajax({
        url : url,
        data : {
            accountid : accountId,
            async : false
        },
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                $('accountdetails-tabs').find('a[href=#tab_preferences]').trigger('click');
                $('#account_details_msg').html('Marked as Internal.');
                $('#group'+roleId).data('curstate', true);
            }else{
                if(curState){
                    $('#pn'+roleId).prop('checked', true);
                }
                $('#account_details_msg').html("Preferences not updated...some thing went wrong");
            }
        }
    });
}

function unmarkAsInternal(accountId, roleId, curState) {
    var url = contexturl + "/superadmin/account/edit/1/unmark/internal";
    $.ajax({
        url : url,
        data : {
            accountid : accountId,
            async : false
        },
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                $('accountdetails-tabs').find('a[href=#tab_preferences]').trigger('click');
                $('#account_details_msg').html('Unmarked Internal.');
                $('#group'+roleId).data('curstate', false);
            }else{
                if(curState){
                    $('#py'+roleId).prop('checked', true);
                }
                $('#account_details_msg').html("Preferences not updated...some thing went wrong");
            }
        }
    });
}

function updateDeleted(publicId, roleId, deleteFlag) {
    var url = contexturl + "/superadmin/account/edit/1/update/deleted";
    $.ajax({
        url : url,
        data : {
            publicid : publicId,
            deleted : deleteFlag,
            async : false
        },
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                $('accountdetails-tabs').find('a[href=#tab_preferences]').trigger('click');
                $('#account_details_msg').html('Toggled Deleted status.');
                $('#group'+roleId).data('curstate', deleteFlag);
            }else{
                if(deleteFlag){
                    $('#pn'+roleId).prop('checked', true);
                }
                $('#account_details_msg').html("Preferences not updated...some thing went wrong");
            }
        }
    });
}

function activate(publicId, roleId, activateFlag) {
    var url = contexturl + "/superadmin/account/edit/1/update/activate";
    $.ajax({
        url : url,
        data : {
            publicid : publicId,
            activate : activateFlag,
            async : false
        },
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                $('accountdetails-tabs').find('a[href=#tab_preferences]').trigger('click');
                $('#account_details_msg').html('Toggled Activate status.');
                $('#group'+roleId).data('curstate', activateFlag);
            }else{
                if(activateFlag){
                    $('#pn'+roleId).prop('checked', true);
                }
                $('#account_details_msg').html("Preferences not updated...some thing went wrong");
            }
        }
    });
}

function updateConsentReason(consentId) {
    var accountId = $("#adAccountId")[0].value;
    var url = contexturl + "/superadmin/1/account_details/consent_reason/" + accountId  + "/" + consentId;
    $.ajax({
        url : url,
        type : 'PUT',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                $('#account_details_msg').html('Consent reason updated successfully...');
            }else{
                $('#account_details_msg').html("Consent reason not updated...some thing went wrong");
            }
        }
    });
}

function setConsentReason() {
		var accountId = $("#adAccountId")[0].value;
    var url = contexturl + "/superadmin/1/account_details/consent_reason/" + accountId;
    $.ajax({
        url : url,
        type : 'GET',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
				$('#consentReason').val(data['data']);
            }else{
				$('#consentReason').val(1);
            }
        }
    });
}

function clearAccountCache(accountId) {
    var url = contexturl + "/superadmin/account/edit/1/clear/cache";
    $.ajax({
        url : url,
        data : {
            accountid : accountId,
            async : false
        },
        type : 'POST',
        success : function(data) {
            if (data['status'].toLowerCase() === 'ok' && data['data'] == "true") {
                $('accountdetails-tabs').find('a[href=#tab_preferences]').trigger('click');
                $('#account_details_msg').html('Account Cache cleared for all environments.');
            }else{
                $('#account_details_msg').html("Unable to Clear Account cache...some thing went wrong");
            }
        }
    });
}

function provision() {
    var accountId = $('#ProvisionMessageModal').data('accountid');
    var roleId = $('#ProvisionMessageModal').data('roleid');
    var publicId = $('#ProvisionMessageModal').data('publicid');
    var curState = $('#group'+roleId).data('curstate');
    if(curState) return;
    DD_RUM.track("Provision Confirmation", { accountId: accountId, provType: provisionType });
    if(roleId == ad.CONSTS.ROLE_MAP.ROLE_MLA_SERVICE) {
           var memberNumber = $('#memberNumber').val();
           var securityCode = $('#securityCode').val();

           if(memberNumber.trim() != '' && securityCode.trim() != '')
           {
              $('#ProvisionMessageModal').modal('hide');
              activateMla(memberNumber, securityCode, accountId, roleId, curState);
           }
           else {
            if(memberNumber.trim() == '')  document.getElementById("memberNumber").style.borderColor='#e52213'; else document.getElementById("memberNumber").style.borderColor='#000000';
            if(securityCode.trim() == '')  document.getElementById("securityCode").style.borderColor='#e52213'; else document.getElementById("securityCode").style.borderColor='#000000';
           }
    } else if(roleId == ad.CONSTS.ROLE_MAP.ROLE_ECBSV) {
        var ein = $('#ein').val();
        if(ein.trim() != '')
        {
            $('#ProvisionMessageModal').modal('hide');
            activateEcbsv(ein, accountId, roleId)
        }
        else {
            if(ein.trim() == '')  document.getElementById("ein").style.borderColor='#e52213'; else document.getElementById("ein").style.borderColor='#000000';
        }
    } else {
    $('#ProvisionMessageModal').modal('hide');
    if(roleId === "internal") markAsInternal(accountId,roleId, curState);
    else if(roleId === "deleted") updateDeleted(publicId, roleId, true);
    else if(roleId === "active") activate(publicId, roleId, true);
        else provisionRole(accountId, roleId, curState);
   }
}

function deprovision() {
    $('#DeprovisionMessageModal').modal('hide');
    var accountId = $('#DeprovisionMessageModal').data('accountid');
    var roleId = $('#DeprovisionMessageModal').data('roleid');
    var publicId = $('#DeprovisionMessageModal').data('publicid');
    var curState = $('#group'+roleId).data('curstate');
    if(!curState) return;
    DD_RUM.track("DeProvision Confirmation", { accountId: accountId, provType: provisionType });
    if(roleId == ad.CONSTS.ROLE_MAP.ROLE_IDM) {
        deActivateIDM(accountId, roleId, curState);
    } else if(roleId === "internal") unmarkAsInternal(accountId,roleId, curState);
    else if(roleId === "deleted") updateDeleted(publicId,roleId, false);
    else if(roleId === "active") activate(publicId,roleId, false);
        else deProvisionRole(accountId, roleId, curState);
}

function constructUrl(apiUrl, accountId, publicId) {
    if (apiUrl == "document_manager") {
       return contexturl + "/superadmin/1/document_manager/types/" + publicId
    }
    if (apiUrl == "decision_logic") {
       return contexturl + "/superadmin/1/decision_logic/v2/list/" + accountId + "?envId=" + decisionLogicEnvironmentId
    }
    var url = apiUrl + "?accountid="+accountId;
    return ad.CONSTS.API_URLS.BASE_URL + url;
}

function show_confirm_provision_dialog(selValue, accountId, roleId, publicId) {
    var roleName = $('#group'+roleId).data('role');
    document.getElementById("mlainput").style.display="none";
    document.getElementById("ecbsvinput").style.display="none";

    var ignoreRoles = ["internal", "deleted", "activate"];
    var msg = "";
    if(selValue === "on"){
          DD_RUM.track("Show Provision Confirmation", { accountId: accountId, provType: provisionType });
          if(roleName == 'MLA') {
            document.getElementById("mlainput").style.display="table"
          }
          else if(roleName == 'Ecbsv') {
            document.getElementById("ecbsvinput").style.display="table"
          }

        var roleMap = { 'internal': ' mark isInternal', 'deleted': 'Delete', 'active': 'Activate' };
        var rm = roleMap[roleName];
        if(rm === undefined)
            msg = "provision " + roleName +" to ";
        else
            msg = rm;
        $('#provision-msg').html("Are you sure you want to '"+ msg + "' this account ");
        $('#ProvisionMessageModal').data("accountid",accountId);
        $('#ProvisionMessageModal').data("roleid",roleId);
        $('#ProvisionMessageModal').data("publicid",publicId);
        $('#ProvisionMessageModal').modal({backdrop: 'static', keyboard: false});
    }else if(selValue === "off"){
        DD_RUM.track("Show DeProvision Confirmation", { accountId: accountId, provType: provisionType });
        var roleMap = { 'internal': ' unmark isInternal', 'deleted': ' add back ', 'active': 'Deactivate' };
        var rm = roleMap[roleName];
        if(rm === undefined)
            msg = "deprovision " + roleName +" to ";
        else
            msg = rm;
        $('#deprovision-msg').html("Are you sure you want to '"+ msg + "' this account ");
        $('#DeprovisionMessageModal').data("accountid",accountId);
        $('#DeprovisionMessageModal').data("roleid",roleId);
        $('#DeprovisionMessageModal').data("publicid",publicId);
        $('#DeprovisionMessageModal').modal({backdrop: 'static', keyboard: false});
    }
}

function changeCadence(accountId, roleId) {

var url = contexturl + "/superadmin/account/edit/1/provision";
                    $.ajax({
                        url : url,
                        data : {
                            accountid : accountId,
                            roleid : roleId,
                            async : false
                        },
                        type : 'POST',
                        success : function(data) {
                            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                               $('#account_details_msg').html('WLM Cadence updated successfully...');
                           }else{
                               $('#account_details_msg').html("WLM Cadence not updated...some thing went wrong");
                           }
                        }
                    });
}

function provisionRoleByAccount(accountId, roleId, publicId, groupId, roleName) {
    var selectedRole = $('#group'+groupId).data('curstate');
    if (roleId === selectedRole) {
      return;
    }
     confirmAction({message: "Are you sure want to provision '" + roleName + "' to this account?"})
            .then(function confirmHandler(cleanupPopup){
                cleanupPopup();
                var url = contexturl + "/superadmin/account/edit/1/provision";
                    $.ajax({
                        url : url,
                        data : {
                            accountid : accountId,
                            roleid : roleId,
                            async : false
                        },
                        type : 'POST',
                        success : function(data) {
                            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                                $('accountdetails-tabs').find('a[href=#tab_roles_permissions]').trigger('click');
                                $('#account_details_msg').html('Role updated.');
                                $('#group'+groupId).data('curstate', roleId);
                                ad.rolesCache[roleId] = true;
                                (roleId == ad.CONSTS.ROLE_MAP.ROLE_WATCHLIST_3_0 || roleId === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) && getSubscriptionTypeProvisions(accountId);
                            }else{
                                if (selectedRole) {
                                    $('#prole'+selectedRole).prop('checked', true);
                                } else {
                                  $('#prole'+roleId).prop('checked', false);
                                }

                                var errorMessage = 'Role not updated...some thing went wrong';
                                if (data.data.errorCode === "500" || data.data.errorCode === "192") {
                                    errorMessage = "Role not updated due to one of the critical parent permission is not enabled."
                                }
                                $('#account_details_msg').html(errorMessage);
                                (roleId == ad.CONSTS.ROLE_MAP.ROLE_WATCHLIST_3_0 || roleId === ad.CONSTS.ROLE_MAP.ROLE_DOCUMENT_VERIFICATION) && getSubscriptionTypeProvisions(accountId);
                            }
                        }
                    });
            }, function cancelHandler() {
                        if (selectedRole) {
                            $('#prole'+selectedRole).prop('checked', true);
                        } else {
                          $('#prole'+roleId).prop('checked', false);
                        }
             })
}


function setProvisionByGroup (accountId, roleId, publicId, roleName, parentRoleId) {
    var previousState = $('#group'+roleId).data('curstate');
    var newState = $('#prole'+roleId).prop('checked');

    if (previousState === newState) {
      return;
    }

    if (newState) {
      provisionRoleByGroup(accountId, roleId, publicId, roleName, parentRoleId)

      var roles = ad.CONSTS.ROLE_AUTO_ACTIVATE[roleId];
      if (roles && roles.length) {
          roles.forEach(function(id) {
              $('#prole' + id).prop('checked', true);
              $('#group'+id).data('curstate', true);
              ad.rolesCache[id] = true;
          });
      }
    } else {
      deProvisionRoleByGroup(accountId, roleId, publicId, roleName)

      var roles = ad.CONSTS.ROLE_AUTO_DEACTIVATE[roleId];
      if (roles && roles.length) {
          roles.forEach(function(id) {
              $('#prole' + id).prop('checked', false);
              $('#pn' + id).prop('checked', true);
              $('#group'+id).data('curstate', false);
              ad.rolesCache[id] = false;
          });
      }
    }
}

function showHideAdditionalSection(roleId) {
    if (ad.CONSTS.ROLE_INTEGRATION[roleId]) {
     if(ad.CONSTS.ROLE_INTEGRATION[roleId].show && typeof ad.CONSTS.ROLE_INTEGRATION[roleId].show === 'function') {
       var show = ad.CONSTS.ROLE_INTEGRATION[roleId].show();
       show ? $('#chkGrp' + roleId).show() : $('#chkGrp' + roleId).hide();
     }
    }
}

function handleAdditionalAction(roleId) {
    if (ad.CONSTS.ROLE_INTEGRATION[roleId] && ad.CONSTS.ROLE_INTEGRATION[roleId].enableRoles) {
       ad.CONSTS.ROLE_INTEGRATION[roleId].enableRoles.forEach(function(id) {
         $('#group'+id).data('curstate', true);
         $('#prole'+id).prop('checked', true);
         ad.rolesCache[id] = true;
       });
     }
}



function handleAdditionalActionForDeprovision(roleId) {
    if (ad.CONSTS.ROLE_INTEGRATION[roleId] && ad.CONSTS.ROLE_INTEGRATION[roleId].disableRoles) {
       ad.CONSTS.ROLE_INTEGRATION[roleId].disableRoles.forEach(function(id) {
         $('#group'+id).data('curstate', false);
         $('#prole'+id).prop('checked', false);
         ad.rolesCache[id] = false;
       });
     }
}

function provisionRoleByGroup(accountId, roleId, publicId, roleName, parentRoleId) {
     confirmAction({message: "Are you sure want to provision '" + roleName + "' to this account?"})
            .then(function confirmHandler(cleanupPopup){
                cleanupPopup();
                var url = contexturl + "/superadmin/account/edit/1/provision";
                    $.ajax({
                        url : url,
                        data : {
                            accountid : accountId,
                            roleid : roleId,
                            async : false
                        },
                        type : 'POST',
                        success : function(data) {
                            if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                                $('accountdetails-tabs').find('a[href=#tab_roles_permissions]').trigger('click');
                                $('#account_details_msg').html('Role updated.');
                                if(roleId === ad.CONSTS.PREFILL.groupId) {
                                    $('#chkGrp'+ad.CONSTS.PREFILL.groupId).show()
                                }
                                $('#group'+roleId).data('curstate', true);
                                ad.rolesCache[roleId] = true;

                                showHideAdditionalSection(parentRoleId);
                            }else{
                                $('#prole'+roleId).prop('checked', false);
                                var errorMessage = 'Role not updated...some thing went wrong';
                                if (data.data.errorCode === "500" || data.data.errorCode === "192") {
                                    errorMessage = "Role not updated due to one of the critical parent permission is not enabled."
                                }
                                $('#account_details_msg').html(errorMessage);
                            }
                        }
                    });
            }, function cancelHandler() {
                 $('#prole'+roleId).prop('checked', false);
             })
}

function deProvisionRoleByGroup(accountId, roleId, publicId, roleName) {
     confirmAction({message: "Are you sure want to deprovision '" + roleName + "' from this account?"})
            .then(function confirmHandler(cleanupPopup){
                cleanupPopup();
                var url = contexturl + "/superadmin/account/edit/1/deprovision";
                $.ajax({
                    url : url,
                    data : {
                        accountid : accountId,
                        roleid : roleId,
                        async : false
                    },
                    type : 'POST',
                    success : function(data) {
                        if (data['status'].toLowerCase() === 'ok' && data['data'] > 0) {
                            $('accountdetails-tabs').find('a[href=#tab_roles_permissions]').trigger('click');
                            $('#account_details_msg').html('Role updated.');
                            $('#group'+roleId).data('curstate', false);
                            if(roleId === ad.CONSTS.PREFILL.groupId) {
                                $('#chkGrp'+ad.CONSTS.PREFILL.groupId).hide()
                            }
                            ad.rolesCache[roleId] = false;
                        }else{
                            $('#prole'+roleId).prop('checked', true);
                            var errorMessage = 'Role not updated...some thing went wrong';
                            if (data.data.errorCode === "500" || data.data.errorCode === "192") {
                                errorMessage = "Role not updated due to one of the critical parent permission is not enabled."
                            }
                            $('#account_details_msg').html(errorMessage);
                        }
                    }
                });
            }, function cancelHandler() {
                 $('#prole'+roleId).prop('checked', true);
             })
}

 function deprovisionDocType(name, docPublicId) {
                         var obj = {}
                         obj['docTypePublicId'] = docPublicId;
                         obj['accountPublicId'] = $('#publicId').val();
                         var json = JSON.stringify(obj);
                         confirmAction({message: "Are you sure want to deprovision '" + name + "' from this account?"})
                                .then(function confirmHandler(cleanupPopup){
                                    cleanupPopup();
                                    var url = contexturl + "/superadmin/1/document_manager/types/deprovision";
                                    $.ajax({
                                        url : url,
                                        data : json,
                                        contentType: "application/json",
                                        type : 'POST',
                                        success : function(data) {
                                            if (data['status'].toLowerCase() === 'ok') {
                                                $('#accountdetails-tabs').find('a#nav-tab_docTypes').trigger('click')
                                                $('#account_details_msg').html('deprovisioned ' + name + ' successfully...');
                                            }
                                        }
                                    });
                                })
                    }

                    function provisionDocType(name, docPublicId) {
                                              if ($('#tbl_account_details_docTypes tr > td:contains(' + docPublicId + ') + td:contains(' + name + ')').length) {
                                                  alert(name + " already provisioned!!!")
                                                  return
                                              }
                                             var obj = {}
                                             obj['docTypePublicId'] = docPublicId;
                                             obj['accountPublicId'] = $('#publicId').val();
                                             var json = JSON.stringify(obj);
                                             confirmAction({message: "Are you sure want to provision '" + name + "' from this account?"})
                                                    .then(function confirmHandler(cleanupPopup){
                                                        cleanupPopup();
                                                        var url = contexturl + "/superadmin/1/document_manager/types/provision";
                                                        $.ajax({
                                                            url : url,
                                                            data : json,
                                                            contentType: "application/json",
                                                            type : 'POST',
                                                            success : function(data) {
                                                                if (data['status'].toLowerCase() === 'ok') {
                                                                    $('#accountdetails-tabs').find('a#nav-tab_docTypes').trigger('click')
                                                                    $('#account_details_msg').html('provisioned ' + name + ' successfully...');
                                                                }
                                                            }
                                                        });
                                                    })
                    }
                     function provisionAllDocType() {

                                                                 var obj = {}
                                                                 var accountId = $('#publicId').val();
                                                                 confirmAction({message: "Are you sure want to provision all document types to this account?"})
                                                                        .then(function confirmHandler(cleanupPopup){
                                                                            cleanupPopup();
                                                                            var url = contexturl + "/superadmin/1/document_manager/types/provision/" + accountId;
                                                                            $.ajax({
                                                                                url : url,
                                                                                type : 'POST',
                                                                                success : function(data) {
                                                                                    if (data['status'].toLowerCase() === 'ok') {
                                                                                        $('#accountdetails-tabs').find('a#nav-tab_docTypes').trigger('click')
                                                                                        $('#account_details_msg').html('All document types provisioned successfully...');
                                                                                    } else {
                                                                                         $('#account_details_msg').html(data['msg']);
                                                                                    }
                                                                                }
                                                                            });
                                                                        })
                                        }



                    function getUnprovisionedDocTypes() {
                     var url = contexturl + "/superadmin/1/document_manager/types";
                     $.ajax({
                            url : url,
                            type : 'GET',
                            success : function(data) {
                                if (data['status'].toLowerCase() === 'ok') {
                                    $('#otherDocTypes').empty()
                                    var otherDocs = $("#otherDocTypes");
                                    var option = $("<option />");
                                    option.html("All");
                                    option.val("All");
                                    otherDocs.append(option);
                                    $(data['data']).each(function () {
                                        var option = $("<option />");
                                        option.html(this.name);
                                        option.val(this.publicId);
                                        otherDocs.append(option);
                                    });
                                }
                            }
                        });
                    }

/*******************************************/
/********Subscription Type Section *********/
/*******************************************/

function setSubscriptionTypeProvisioning(accountId, subscriptionTypeId) {
    $('#account_details_msg').html('');

    var checked = $('input[name=st' + subscriptionTypeId + ']:checked').val() === "on";

    var curState = $('#st_group' + subscriptionTypeId).data('curstate');

    var subscriptionTypeName = $('#st_group' + subscriptionTypeId).data('description');

    if (checked !== curState) {
        confirmAction({
            message: "Are you sure want to " + (checked ? "provision '" : "deprovision '") + subscriptionTypeName + "' to this account?"})
        .then(function confirmHandler(cleanupPopup){
            cleanupPopup();
            $.ajax({
                url: contexturl + "/superadmin/subscritions/update?accountid=" + accountId + "&subscriptionTypeId=" + subscriptionTypeId + "&operation=" + (checked ? "subscribe" : "unsubscribe"),
                data: {},
                type: 'POST',
                success: function (data) {
                    if (data.status.toLowerCase === "error") {
                        toastr.error("Unable to " + (checked ? "provision" : "de-provision") + " subscription type " + subscriptionTypeName);
                        curState ? $('#sty' + subscriptionTypeId).prop('checked', true) : $('#stn' + subscriptionTypeId).prop('checked', true);
                    } else {
                        toastr.success("Subscription Type " + subscriptionTypeName + (checked ? " provisioned" : " de-provisioned") + " successfully");
                        $('#st_group' + subscriptionTypeId).data('curstate', checked);
                    }
                },
                error: function (error) {
                    toastr.error("Unable to " + (checked ? "provision" : "de-provision") + " subscription type " + subscriptionTypeName);
                    console.log(error);
                    curState ? $('#sty' + subscriptionTypeId).prop('checked', true) : $('#stn' + subscriptionTypeId).prop('checked', true);
                }
            });
            curState ? $('#sty' + subscriptionTypeId).prop('checked', true) : $('#stn' + subscriptionTypeId).prop('checked', true);
        }) 
    }
}

function loadSubscriptionTypeProvisions(accountId, data) {
    var tbljq = $("#tbl_account_details_subscription_types");

    if ($.fn.DataTable.fnIsDataTable(tbljq[0])) {
        dt = tbljq.dataTable();
        dt.fnClearTable();
        dt.fnAddData(data);
        dt.fnDraw();
    } else {
        tbljq.dataTable({
            "aaSorting": [[0, 'asc']],
            "bPaginate": false,
            "aoColumns": [
                {
                    "bVisible": false
                },
                null,
                null,
                {
                    "fnRender": function (oObj) {
                        var allowEdit = $("#allowEdit").val();
                        if (!(allowEdit === "true")) {
                            return "<span>" + (oObj.aData[3] ? "Yes" : "No") + "</span>";
                        }
                        var oc = "javascript:setSubscriptionTypeProvisioning(" + accountId + "," + oObj.aData[0] + ")";
                        if (oObj.aData[3] === true) {
                            return "<fieldset class='radiogroup' data-description='" + oObj.aData[1] + "' data-curstate=true id='st_group" + oObj.aData[0] + "'>"
                                + " <input type='radio' value='on' name='st" + oObj.aData[0] + "' id='sty" + oObj.aData[0] + "' onclick='" + oc + "' checked='checked'/> On "
                                + " <input type='radio' value='off' name='st" + oObj.aData[0] + "' id='stn" + oObj.aData[0] + "' onclick='" + oc + "' /> Off "
                                + "</fieldset>";
                        } else {
                            return "<fieldset class='radiogroup' data-description='" + oObj.aData[1] + "' data-curstate=false id='st_group" + oObj.aData[0] + "'>"
                                + " <input type='radio' value='on' name='st" + oObj.aData[0] + "' id='sty" + oObj.aData[0] + "' onclick='" + oc + "' /> On "
                                + " <input type='radio' value='off' name='st" + oObj.aData[0] + "' id='stn" + oObj.aData[0] + "' onclick='" + oc + "'  checked='checked'/> Off "
                                + "</fieldset>";
                        }
                    },
                    "aTargets": [3]
                }
            ],
            "aaData": data
        });
    }
}

function getSubscriptionTypeProvisions(accountId) {
    $.ajax({
        url: contexturl + "/superadmin/subscritions/list?accountid=" + accountId,
        success: function (data) {
            var subscriptionData = [];
            data.data.forEach(function (subscriptionType) {
                if(ad.rolesCache[ad.CONSTS.ROLE_SUBSCRIPTION_TYPE_ASSOCIATION[subscriptionType.id]]) {
                    var row = [];
                    row.push(subscriptionType.id);
                    row.push(subscriptionType.name);
                    row.push(subscriptionType.description);
                    row.push(subscriptionType.provisioned);
                    subscriptionData.push(row);
                }
            });
            loadSubscriptionTypeProvisions(accountId, subscriptionData);
        },
        error: function (error) { 
            console.log("Failed to retrieve subscription types.", error) 
        }
    });
}

function confirmUnlinkProgram(accountId, programId, programName) {
   config = {message: "Are you sure to Unlink Program '"+ programName +"'?"}
   confirmAction(config).then(function(cleanupPopup) {
       cleanupPopup()
       $.ajax({
           url : contexturl + `/superadmin/sponsorbank/unlink/${accountId}/program/${programId}`,
           type: 'POST',
           dataType: 'json',
           contentType: 'application/json',
           success : function() {
               ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
               ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
               ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
               accountId,
               null);            },
           error : function(error) {
               alert("Error, Unable to unlink program")
               console.log(error);
           }
       })

   })
}

function confirmLinkProgram() {
   var accountId = $('#adAccountId').val();
   var nonLinkedPrograms = $('#non-linked-programs')[0];
   var selectedIndex = nonLinkedPrograms.options[nonLinkedPrograms.selectedIndex].text.split("-");
   var programId = selectedIndex[0].trim();
   var programName = selectedIndex[1].trim();
   config = {message: "Are you sure to Link Program '"+ programName +"'?"}
   confirmAction(config).then(function(cleanupPopup) {
       cleanupPopup()
       $.ajax({
           url : contexturl + `/superadmin/sponsorbank/link/${accountId}/program/${programId}`,
           type: 'POST',
           dataType: 'json',
           contentType: 'application/json',
           success : function() {
               ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
               ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
               ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
               accountId,
               null);            },
           error : function(error) {
               alert("Error, Unable to link program")
               console.log(error);
           }
       })

   })
}

function showSwapRoleModal(userId) {
    const usersHtml = [];
    ad.accountInfoV2.users.forEach(user => {
        if(user.userAccountAssociation.userId != userId && user.userAccountAssociation.status == 1) {
            usersHtml.push("<option value="+user.userAccountAssociation.userId+">"+user.userAccountAssociation.userId+" - "+user.email+"</option>")
        }
    })
    $("#swapping-user-id").html(userId)
    $("#active-users").html(usersHtml)
    showRoleSwapPopup();
}

function showRoleSwapPopup() {
    $('#swapRoleModal').show();
}

function closeRoleSwapPopup() {
    $('#swapRoleModal').hide();
}

function swapRoleHandler() {
    const selectedAccount = parseInt($('.selected-account').text())
    const swappingUserId = parseInt($("#swapping-user-id").text())
    const userId = parseInt($('#active-users').val())
    $.ajax({
        url : contexturl + `/superadmin/account/details/1/swap/user/${userId}/swappingUserId/${swappingUserId}/accountId/${selectedAccount}`,
        type: 'POST',
        dataType: 'json',
        contentType: 'application/json',
        success : function(data) {
            closeRoleSwapPopup()
            ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
                ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
                ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
                parseInt($(".selected-account").text()),
                null);
        },
        error : function(error) {
            closeRoleSwapPopup()
            alert("Error in swapping roles")
            console.log(error);
        }
    })
}

function associationUserClickHandler(userId) {
    const rolesHtml = [];
    const accountsHtml = ["<option value="+ad.accountInfoV2.accountId+">"+ad.accountInfoV2.name+"</option>"];
    ad.accountInfoV2.subAccounts.forEach(subAccount => {
        accountsHtml.push("<option value="+subAccount.id+">"+subAccount.name+"</option>")
    })
    ad.accountInfoV2.roles.forEach(role => {
        rolesHtml.push("<option value="+role.id+">"+role.name+"</option>")
    })

    $("#user-association").html(userId)
    $("#account-association").html(accountsHtml)
    $("#role-association").html(rolesHtml)
    
    showAccountAssocationPopup()
}

function showAccountAssocationPopup() {
    $('#accountAssociationModal').show();
}

function closeAccountAssocationPopup() {
    $('#accountAssociationModal').hide();
}

function assocaiteUser() {
    const associatedUserId = parseInt($("#user-association").text())
    const associatedAccountId = parseInt($('#account-association').val())
    const associatedRoleId = parseInt($('#role-association').val())
    var url = contexturl + `/superadmin/account/details/1/associate/user/${associatedUserId}/account/${associatedAccountId}/role/${associatedRoleId}?isSystemRole=false`
    if(associatedRoleId == -1) {
        const selectedRole = $('#role-association option:selected').text()
        const associatedRoleTypeId = ad.accountInfoV2.roles.find(r => r.name == selectedRole).type
        url = contexturl + `/superadmin/account/details/1/associate/user/${associatedUserId}/account/${associatedAccountId}/role/${associatedRoleTypeId}?isSystemRole=true`
    }
    $.ajax({
        url : url,
        type: 'POST',
        dataType: 'json',
        contentType: 'application/json',
        success : function(data) {
            closeAccountAssocationPopup()
            ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
                ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
                ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
                parseInt($(".selected-account").text()),
                null);
        },
        error : function(error) {
            closeAccountAssocationPopup()
            alert("Error in user account association")
            console.log(error);
        }
    })
}

function modifyAccountPrimaryUser(userAccountAssociationId, userId, accountId) {
    var isPrimary = $('#primary-checkbox-'+userAccountAssociationId).is(":checked")
    const confirmMsg = `Are you sure you want to ${isPrimary ? 'add' : 'remove'} this user as primary user?`
    config = {message: confirmMsg}
    confirmAction(config).then(function(cleanupPopup) {
        cleanupPopup()
        $.ajax({
            url : ad.CONSTS.API_URLS.BASE_URL + 'user/modify/primary_user_association',
            data : JSON.stringify({
                userId : userId,
                accountId : accountId,
                isPrimary : isPrimary,
            }),
            type : 'PUT',
            contentType: 'application/json',
            success : function(data) {
                ad.loadAccountDetails("PARTNER_AND_SUBACCOUNT",
                    ad.CONSTS.JQ_DT["PARTNER_AND_SUBACCOUNT"],
                    ad.CONSTS.API_URLS["PARTNER_AND_SUBACCOUNT"],
                    parseInt($(".selected-account").text()),
                    null);
            },
            error : function(error) {
                alert(error.responseJSON.msg)
                $('#primary-checkbox-'+userAccountAssociationId).prop('checked', !isPrimary)
            }
        });
    }, function cancelHandler() {
        $('#primary-checkbox-'+userAccountAssociationId).prop('checked', !isPrimary)
    })
}

function getUserStatusString(status) {
    switch(status) {
        case 0:
            return 'Inactive';
        case 1:
            return 'Active';
        case 2:
            return 'Deleted';
        case 3:
            return 'Locked';
        default:
            return '-';
    }
}

var decisionLogicEnvironmentId = '1'
function updateDecisionLogicEnvironment() {
    decisionLogicEnvironmentId = $("#decision_logic_environment").val()
    var adTarget = $this.attr('data-ad-target');
    var accountId =  $this.attr('data-ad-accountid');
    ad.loadAccountDetails(adTarget,
        ad.CONSTS.JQ_DT[adTarget],
        ad.CONSTS.API_URLS[adTarget],
        accountId,
        null);
}


