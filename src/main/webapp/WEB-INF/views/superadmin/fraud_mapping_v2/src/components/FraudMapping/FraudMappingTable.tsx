import React, { FC, useContext, useMemo, useState, useCallback } from "react";
import { Autocomplete, Box } from "@mui/material";
import { FraudModelMappingContext } from "../context/FraudModelMappingContext";
import {
  Button,
  DataGrid,
  Modal,
  TextField,
  Typography,
  useToast,
} from "@socure/components";
import { GridCellParams } from "@mui/x-data-grid";
import { FraudModelAutoComplete, Model } from "../../types";
import { unAssociate, unmapModel, updateModelMapping } from "../../services";
import {
  getModelAndSetDescription,
  getAssociationNameByType,
} from "../../utils/helper";
import "./FraudMappingTable.scss";

const FraudMappingTable: FC = () => {
  const { showToast } = useToast();
  const {
    activeAccount,
    mapping,
    models,
    modelsAndDescriptions,
    setModelsAndDescriptions,
    getAndUpdateMappingsForAccount,
    setShowLoader,
  } = useContext(FraudModelMappingContext);
  const [showMappingDialog, setShowMappingDialog] = useState(false);
  const [oldModel, setOldModel] = useState<Model>(null);
  const [activeModel, setActiveModel] = useState<FraudModelAutoComplete>(null);
  const [description, setDescription] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const handleShowMappingDialog = useCallback(
    (model: Model) => {
      const modelName = model.model.name;
      const modelAssociationType = model.associationType;
      const accountId = mapping.accountId;
      if (!modelName || !modelAssociationType || !accountId) {
        showToast({
          type: "error",
          title: "Error",
          description: "Please select the model mapping to update.",
        });
        return;
      }
      setShowMappingDialog(true);
      setOldModel(model);
      const newModel = {
        label: modelName,
        publicId: model.model.publicId,
      };
      setActiveModel(newModel);
      getModelAndSetDescription(
        newModel,
        modelsAndDescriptions,
        setDescription,
        setModelsAndDescriptions
      );
    },
    [mapping, modelsAndDescriptions, setModelsAndDescriptions, showToast]
  );

  const handleDeleteMapping = useCallback(
    (model: Model) => {
      setShowLoader(true);
      if (app.modelManagement) {
        unmapModel(
          mapping.accountId,
          model.model.publicId,
          model.model.environment,
          model.associationType
        )
          .then((message) => {
            getAndUpdateMappingsForAccount(activeAccount?.id);
            showToast({
              type: "success",
              title: "Success",
              description: message || "Model unmapped successfully.",
            });
          })
          .catch((errorMessage) => {
            showToast({
              type: "error",
              title: "Error",
              description: errorMessage,
            });
          })
          .finally(() => setShowLoader(false));
      } else {
        unAssociate(
          mapping.accountId,
          model.model.publicId,
          model.model.environment,
          model.associationType
        )
          .then(() => {
            getAndUpdateMappingsForAccount(activeAccount?.id);
            showToast({
              type: "success",
              title: "Success",
              description: "Model unmapped successfully.",
            });
          })
          .catch((errorMessage) => {
            showToast({
              type: "error",
              title: "Error",
              description: errorMessage,
            });
          })
          .finally(() => setShowLoader(false));
      }
    },
    [
      activeAccount,
      getAndUpdateMappingsForAccount,
      mapping.accountId,
      showToast,
      setShowLoader,
    ]
  );

  const columns = useMemo(() => {
    return [
      {
        field: "edit",
        headerName: "Edit",
        flex: 1,
        sortable: false,
        filterable: false,
        disableColumnMenu: true,
        renderCell: ({ row }: GridCellParams<Model>) => {
          return (
            <Button onClick={() => handleShowMappingDialog(row)}>Edit</Button>
          );
        },
      },
      {
        field: "name",
        headerName: "Name",
        flex: 1,
        valueGetter: (_, row: Model) => row.model.name,
      },
      {
        field: "identifier",
        headerName: "Identifier",
        flex: 1,
        valueGetter: (_, row: Model) => row.model.identifier,
      },
      {
        field: "version",
        headerName: "Version",
        flex: 1,
        valueGetter: (_, row: Model) => row.model.version,
      },
      {
        field: "scoreName",
        headerName: "Score Name",
        flex: 1,
        valueGetter: (_, row: Model) => row.model.scoreName,
      },
      {
        field: "associationType",
        headerName: "Association Type",
        flex: 1,
        valueGetter: (_, row: Model) =>
          getAssociationNameByType(row.associationType),
      },
      {
        field: "normalized",
        headerName: "Normalized",
        flex: 1,
        valueGetter: (_, row: Model) => row.model.normalized,
      },
      {
        field: "environment",
        headerName: "Environment",
        flex: 1,
        valueGetter: (_, row: Model) => row.model.environment,
      },
      {
        field: "filterCriteria",
        headerName: "Filter Criteria",
        flex: 1,
        valueGetter: (_, row: Model) => row.filterCriteria,
      },
      {
        field: "delete",
        headerName: "Delete",
        flex: 1,
        sortable: false,
        filterable: false,
        disableColumnMenu: true,
        renderCell: ({ row }: GridCellParams<Model>) => (
          <Button onClick={() => handleDeleteMapping(row)}>Delete</Button>
        ),
      },
    ];
  }, [handleDeleteMapping, handleShowMappingDialog]);

  const onClose = useCallback(() => {
    setShowMappingDialog(false);
  }, []);

  const handleUpdateMapping = useCallback(() => {
    if (!activeModel) {
      showToast({
        type: "error",
        title: "Error",
        description: "Please select a valid model.",
      });
      return;
    }
    const oldModelId = oldModel.model.publicId;
    const newModelId = activeModel.publicId;
    const associationType = getAssociationNameByType(
      oldModel.associationType
    ).toLowerCase();
    setIsLoading(true);
    updateModelMapping(
      newModelId,
      oldModelId,
      mapping.accountId,
      associationType
    )
      .then((message) => {
        getAndUpdateMappingsForAccount(activeAccount?.id);
        showToast({
          type: "success",
          title: "Success",
          description: message || "Successfully updated model mapping.",
        });
        onClose();
      })
      .catch((errorMessage) => {
        showToast({
          type: "error",
          title: "Error",
          description: errorMessage || "Unexpected Error",
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [
    activeAccount,
    oldModel,
    activeModel,
    mapping,
    onClose,
    getAndUpdateMappingsForAccount,
    showToast,
  ]);

  const handleModelSelection = useCallback(
    (_, value: FraudModelAutoComplete) => {
      setActiveModel(value);
      if (value) {
        getModelAndSetDescription(
          value,
          modelsAndDescriptions,
          setDescription,
          setModelsAndDescriptions
        );
      } else {
        setDescription("");
      }
    },
    [modelsAndDescriptions, setModelsAndDescriptions]
  );

  return (
    <div className="fraud_mapping_table-container">
      <div className="fraud_mapping_account-id-name-wrapper">
        <span>Account Id: {mapping.accountId}</span>
        <span>Account Name: {mapping.accountName}</span>
      </div>
      <DataGrid
        rows={mapping.models}
        columns={columns}
        disableRowSelectionOnClick
        rowHeight={52}
        getRowId={(row: Model) => row.rowId}
      />
      <Modal
        id="update-model-mapping-modal"
        open={showMappingDialog}
        onClose={onClose}
        title="Update Model Mapping"
        footer={[
          {
            label: "Cancel",
            onClick: onClose,
          },
          {
            label: "Update",
            onClick: handleUpdateMapping,
            variant: "primary",
            loading: isLoading,
          },
        ]}
        maxWidth="xs"
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            rowGap: "0.5rem",
            marginBottom: "1rem",
          }}
        >
          <Typography variant="label">Model Name</Typography>
          <Autocomplete
            options={models}
            renderOption={(props, option) => (
              <li
                {...props}
                title={option.label}
                key={`${option.label}-${option.publicId}`}
              >
                {option.label}
              </li>
            )}
            value={activeModel}
            onChange={handleModelSelection}
            renderInput={(params) => (
              <TextField
                {...params}
                hiddenLabel
                id="outlined-model"
                placeholder="Select Fraud Model"
              />
            )}
            sx={{ flex: "1 1 50%" }}
            loading={models.length === 0}
          />
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            rowGap: "0.5rem",
            marginBottom: "1rem",
          }}
        >
          <Typography variant="label">Model Description</Typography>
          <TextField hiddenLabel disabled value={description} />
        </Box>
      </Modal>
    </div>
  );
};

export default FraudMappingTable;
