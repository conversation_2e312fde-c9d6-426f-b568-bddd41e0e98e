package me.socure.document;

public class DtoAccountProvision {
    Long id = 0L;
    String docTypePublicId;
    String accountPublicId;

    public DtoAccountProvision setId(Long id) {
        if (id != null) {
            this.id = id;
        }
        return this;
    }

    public Long getId() {
        return this.id;
    }

    public DtoAccountProvision setDocTypePublicId(String docTypePublicId) {
        this.docTypePublicId = docTypePublicId;
        return this;
    }

    public DtoAccountProvision setAccountPublicId(String accountPublicId) {
        this.accountPublicId = accountPublicId;
        return this;
    }

    public String getDocTypePublicId() {
        return this.docTypePublicId;
    }

    public String getAccountPublicId() {
        return this.accountPublicId;
    }
}
