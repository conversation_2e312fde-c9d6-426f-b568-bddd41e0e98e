package me.socure.superadmin.api.v2.kyc.recordcorrection

import me.socure.thirdparty.reader.service.ThirdPartyReaderServiceClient
import org.json4s.{Formats, DefaultFormats}
import org.springframework.beans.factory.annotation.Autowired
import org.json4s.native.JsonMethods.parse
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

import scala.concurrent.ExecutionContext

@Component
@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
class RecordCorrectionHelper {

  private implicit val formats:Formats = DefaultFormats

  @Autowired
  implicit var executionContext: ExecutionContext = _

  @Autowired
  val thirdPartyReaderServiceClient: ThirdPartyReaderServiceClient = null

  def getSocureIdFromTransactionId(transactionId: String): String = {
    val tpAudit = thirdPartyReaderServiceClient.fetchThirdPartyAuditData(transactionId, scala.Option.empty, scala.Option.empty)
    val kycResponseBody = parse(tpAudit.get("EQUIFAX").values().iterator().next().get("Response"))
    (kycResponseBody \ "data" \ "uniqueEntityId").extract[String]
  }

}
