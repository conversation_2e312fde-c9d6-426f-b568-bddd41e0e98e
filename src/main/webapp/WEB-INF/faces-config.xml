<?xml version="1.0" encoding="UTF-8"?>
<faces-config version="2.0" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee

http://java.sun.com/xml/ns/javaee/web-facesconfig_2_0.xsd">

	<application>
		<!-- <el-resolver>org.springframework.web.jsf.el.SpringBeanFacesELResolver</el-resolver> -->
		<el-resolver>org.springframework.web.jsf.el.SpringBeanFacesELResolver</el-resolver>
		<!-- <resource-handler>org.paradise.sandbox.CustomResourceHandler</resource-handler> -->
				
		<locale-config>
     	        <!-- <default-locale>en</default-locale> -->
   	  	 </locale-config>
			<resource-bundle>
				<base-name>messages.messages</base-name>
				<var>msg</var>
			</resource-bundle>
			
		
	</application>
	 <factory>
   <render-kit-factory>
     org.omnifaces.renderkit.Html5RenderKitFactory
   </render-kit-factory>
 </factory>
 <render-kit>
    <renderer>
        <component-family>javax.faces.Output</component-family>
        <renderer-type>javax.faces.Link</renderer-type>
        <renderer-class>me.socure.util.OutputLinkRender</renderer-class>
    </renderer>
</render-kit>
	
	</faces-config>
