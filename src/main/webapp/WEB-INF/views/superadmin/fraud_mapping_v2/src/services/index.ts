import axios, { AxiosResponse } from "axios";
import {
  AccountsAndModelsResponse,
  FraudModelMappingRequest,
  MappingsForAccount,
  ModelInfo,
} from "../types";
import { getSuccessMessage, handleError } from "../utils/helper";
export const getAccountsAndFraudModels =
  (): Promise<AccountsAndModelsResponse> => {
    return axios
      .get("/fraudmapping/v2/get_accounts_and_models")
      .then((response) => response.data.data)
      .catch((error) => {
        throw handleError(error);
      });
  };

export const getMappingsForAccount = (
  accountId: string
): Promise<MappingsForAccount[]> => {
  return axios
    .get(`/fraudmapping/v2/account/${accountId}`)
    .then((response) => response.data.data)
    .catch((ex) => {
      throw handleError(ex);
    });
};

export const getFraudModels = (): Promise<ModelInfo[]> => {
  return axios
    .get("/api/1/fraudmodel/models")
    .then((response) => response.data.data)
    .catch((ex) => {
      throw handleError(ex);
    });
};

export const getFraudModelList = (): Promise<ModelInfo[]> => {
  return axios
    .get("api/1/fraudmodel/list")
    .then((response) => response.data.data)
    .catch((ex) => {
      throw handleError(ex);
    });
};

export const addMapping = (
  url: string,
  data: FraudModelMappingRequest
): Promise<AxiosResponse> => {
  return axios
    .get(url, { params: data })
    .then((response: AxiosResponse) => {
      return response;
    })
    .catch((ex) => {
      throw handleError(ex);
    });
};

//NOTE: Used fetch instead of axios to avoid redirect issue.
export const unmapModel = async (
  accountId: number,
  publicId: string,
  environment: string,
  associationType: number
): Promise<string> => {
  const params = new URLSearchParams({
    accountId: accountId.toString(),
    modelId: publicId,
    environment: environment,
    associationType: associationType.toString(),
  });

  try {
    const response = await fetch(`/api/1/fraudmodel/unmap?${params}`, {
      method: "GET",
      redirect: "manual",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (response.type === "opaqueredirect") {
      return "";
    } else if (!response.ok) {
      throw await response.json();
    }

    const data = await response.json();
    return getSuccessMessage(data);
  } catch (ex) {
    throw handleError(ex);
  }
};

//NOTE: Used fetch instead of axios to avoid redirect issue.
export const unAssociate = async (
  accountId: number,
  publicId: string,
  environment: string,
  associationType: number
): Promise<string> => {
  const params = new URLSearchParams({
    accountId: accountId.toString(),
    modelId: publicId,
    environment: environment,
    associationType: associationType.toString(),
  });

  try {
    const response = await fetch(`/api/1/fraudmodel/unassociate?${params}`, {
      method: "GET",
      redirect: "manual",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (response.type === "opaqueredirect") {
      return "";
    } else if (!response.ok) {
      throw await response.json();
    }

    const data = await response.json();
    return getSuccessMessage(data);
  } catch (ex) {
    throw handleError(ex);
  }
};

export const updateModelMapping = (
  newFraudModelId: string,
  oldFraudModelId: string,
  userId: number,
  associationType: string
): Promise<string> => {
  const formData = new FormData();
  formData.append("newFraudModelId", newFraudModelId);
  formData.append("oldFraudModelId", oldFraudModelId);
  formData.append("userId", userId.toString());
  formData.append("associationType", associationType);

  return axios
    .post("/api/1/fraudmodel/update_mapping", formData)
    .then((response) => {
      return getSuccessMessage(response);
    })
    .catch((ex) => {
      throw handleError(ex);
    });
};
