package me.socure.application;

import java.util.List;

public class ApplicationInfoWithEnvironmentMapper {
    private String applicationName;
    private String serviceType;
    private List<ApplicationEnvironment> environments;

    public ApplicationInfoWithEnvironmentMapper(String applicationName, String serviceType, List<ApplicationEnvironment> environments) {
        this.applicationName = applicationName;
        this.serviceType = serviceType;
        this.environments = environments;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public List<ApplicationEnvironment> getEnvironments() {
        return environments;
    }

    public void setEnvironments(List<ApplicationEnvironment> environments) {
        this.environments = environments;
    }
}
