<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets">
<h:head>
	<meta charset="UTF-8"/>
	<title>Fraud Model Mapping</title>
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/socure-dashboard.css" />
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/awesomplete.base.css"/>
	<link rel="stylesheet" href="#{bucket.resourceURL}/styles/awesomplete.theme.css"/>
	<link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />

	<link rel="stylesheet" type="text/css" href="resources/fraud_mapping/main.css" />

	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/jquery.min.js"></script>
	<script type="text/javascript">
		bucketurl = "#{bucket.resourceURL}";
		contexturl = "#{request.contextPath}";
		marketingurl = "#{bucket.marketingSite}";
		servicehost = '#{bucket.authScoreBase}';
		loggedinUsername = "#{loggedinUsername}";
		appenv = "#{bucket.appEnv}";
	</script>
	<script type="text/javascript" charset="utf-8" language="javascript" src="#{bucket.resourceURL}/scripts/socure-dashboard.js"></script>
	<script defer="defer" type="text/javascript" src="resources/fraud_mapping/runtime.js"></script>
	<script defer="defer" type="text/javascript" src="resources/fraud_mapping/vendors.js"></script>
	<script defer="defer" type="text/javascript" src="resources/fraud_mapping/main.js"></script>
</h:head>
<h:body>
	<div align="right">
	   <h5>
		<h:outputLink value="industry">Manage Industry</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="account_admin">Manage Delegated Admin</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="active_users">Active Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="fraudmanager">Fraud Model Manager</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;
		<h:outputLink value="inactive_users">Inactive Users</h:outputLink>
		&nbsp;&nbsp; | &nbsp; &nbsp;


		<h:outputLink value="logout">Logout</h:outputLink>
		&nbsp;&nbsp;  &nbsp; &nbsp;  </h5>
	</div>
	<br/>
	<br/>
	<div id="fraud_mapping_v2_root"></div>
</h:body>

</html>
