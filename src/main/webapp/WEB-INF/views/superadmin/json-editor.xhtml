<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets">
    <h:head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=1200" />
        <title>Json Editor</title>
        <meta http-equiv="content-type" content="text/html; charset=utf-8" />

        <link rel="stylesheet" type="text/css" href="#{bucket.resourceURL}/styles/bootstrap.min.css" />
        <link rel="stylesheet"
              href="#{bucket.resourceURL}/styles/socure-dashboard.css" />

        <!-- load the minimalist version of JSONEditor, which doesn't have Ace embedded -->
        <script src="//cdnjs.cloudflare.com/ajax/libs/ace/1.2.6/ace.js"></script>
        <link rel="stylesheet"
              href="#{bucket.resourceURL}/styles/jsoneditor.css" type="text/css" />
        <script src="resources/scripts/jsoneditor-minimalist.js"></script>

        <script src="resources/scripts/jquery.min.js"></script>
        <script src="resources/scripts/bootstrap.min.js"></script>
        <style>
            #jsoneditor {
              height: 500px;
              font-family: "DejaVu Sans", sans-serif;
            }
        </style>
    </h:head>
    <h:body class="">
        <div class="mx-auto col-lg-10">
            <div align="right">
                <h5>
                    <h:outputLink value="active_users">Active Users</h:outputLink>
                    &nbsp;&nbsp; | &nbsp; &nbsp;
                    <h:outputLink value="logout">Logout</h:outputLink>
                </h5>
            </div>
            <div id="jsoneditor"></div>
        </div>

        <script type="text/javascript">
              var container = document.getElementById('jsoneditor');
              var options = {
                modes: ['text', 'code', 'tree', 'form', 'view'],
                mode: 'code',
                ace: ace
              };
              var json = {};
              var editor = new JSONEditor(container, options, json);

        </script>
    </h:body>
</html>