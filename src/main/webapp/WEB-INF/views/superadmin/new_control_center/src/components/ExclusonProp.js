import React, { useCallback, useEffect, useMemo, useState } from "react";
import { THROTTLE_PERCENT } from "../Constants";
import Button from "./Button";
import Dropdown from './Dropdown';
import IdsTextarea from "./IdsTextArea";
import Textbox from "./Textbox";

const AddDefaultConfig = ({ configs, handleAddConfig, onCancel, addText }) => {
  const [ configName, setConfigName ] = useState('');

  useEffect(() => {
    configs && configs.length > 0 && setConfigName(configs[0].name);
  }, [ configs ])

  const onAddFlag = useCallback(() => {
    configName
      && handleAddConfig
      && handleAddConfig(configName)
  }, [ configName, handleAddConfig ])

  const properties = useMemo(() => {
    return configs.map(conf => ({
      label : conf.name,
      value: conf.name
    }))
  }, [ configs ])

  return <div className='flex flex-row align-center mt-16'>
    <Dropdown value={configName} options={properties} onChange={setConfigName} />
    <div className='ml-16'>
      <Button text={addText} type='link' onClick={onAddFlag} />
    </div>
    <div className='ml-16'>
      <Button text='Cancel' onClick={onCancel} />
    </div>
  </div>
}

const ExclusionProp = ({
  id,
  propName,
  value,
  showAddConfig,
  configs,
  defaultConfigs,
  onChange
}) => {
  const [ showAddExistingConfig, setShowAddExistingConfig ] = useState(false);

  const properties = useMemo(() => ([{
    label : 'Account Id',
    value : 'accountId'
  }, {
    label : "User Id",
    value : "userId"
  }, {
    label : 'API key',
    value : 'apiKey'
  }, {
    label : 'Module',
    value : 'module'
  }]), [])

  const handlePropChange = useCallback((val) => {
    onChange && onChange(id, val, value, configs);
  }, [ id, onChange, value, configs ])

  const handleValueChange = useCallback((val) => {
    onChange && onChange(id, propName, val, configs);
  }, [ id, propName , onChange, configs ])

  const handleAddConfig = useCallback(() => setShowAddExistingConfig(true), []);

  const handleAddConfigName = useCallback((name) => {
    const existingConfigs = configs || [];
    onChange && onChange(id, propName, value, [...existingConfigs, {
      name,
      value: name === THROTTLE_PERCENT ? 0 : ''
    }])
    setShowAddExistingConfig(false);
  }, [ id, propName, onChange, value, configs ])

  const handleConfigChange = useCallback((name) => (val) => {
    const existingConfigs = configs || [];
    onChange && onChange(id, propName, value, existingConfigs.map(conf =>
      conf.name === name
      ? {
        name,
        value: val
      } : conf
    ))
  }, [ id, propName, onChange, value, configs ])

  const allowedConfigs = useMemo(() => defaultConfigs
    ? defaultConfigs.filter(conf => 
      configs.map(existing => existing.name).indexOf(conf.name) === -1)
    : []
  , [ defaultConfigs, configs ])

  const canShowAddConfig = useMemo(() => {
    return showAddConfig && allowedConfigs.length > 0
  }, [ showAddConfig, allowedConfigs ])

  return <div className='flex flex-row align-start mt-16 w-100 each-prop-container'>
    <div className="flex flex-col align-start">
      <Dropdown value={propName} options={properties} onChange={handlePropChange} />
      {configs && configs.map((conf, idx) => <div key={idx}>
        <div className="generic-label mt-16">{conf.name}</div>
        <Textbox
          value={conf.value}
          inputType={conf.name === THROTTLE_PERCENT ? 'number' : 'text'}
          placeholder='Config Value'
          onChange={handleConfigChange(conf.name)} />
      </div>)}
      {showAddExistingConfig && <AddDefaultConfig
        configs={allowedConfigs}
        handleAddConfig={handleAddConfigName}
        onCancel={() => setShowAddExistingConfig(false)}
        addText='+ Add'
      />}
      {canShowAddConfig && <div className="mt-16">
        <Button text='+ Add Config' type='link' onClick={handleAddConfig} />
      </div>}
    </div>
    <div className="ml-16">
      <IdsTextarea value={value} onChange={handleValueChange} />
    </div>
  </div>
}

export default ExclusionProp;