package me.socure.filter

import me.socure.common.config.MsConfigProvider
import me.socure.json.config.CorsResponseWrapper
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter

import javax.servlet.FilterChain
import javax.servlet.http.{HttpServletRequest, HttpServletResponse}
import scala.collection.JavaConverters._

@Component
class CorsFilter extends OncePerRequestFilter {

  val prohibitedHeaders = List("Access-Control-Max-Age", "Access-Control-Allow-Origin", "Access-Control-Allow-Credentials", "Access-Control-Allow-Methods", "Access-Control-Allow-Headers")

  val allowedDomains = MsConfigProvider.provide().value.getConfig("cors").getStringList("alloweddomains").toString

  override def doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) = {
    response.setHeader("Access-Control-Max-Age", "1800") // 30 min

    if(sys.env.get("APP_ENVIRONMENT").exists(_.equalsIgnoreCase("test"))) {
      response.addHeader("Access-Control-Allow-Origin", "*")
    } else {
      response.addHeader("Access-Control-Allow-Origin", allowedDomains)
    }

    response.setHeader("Access-Control-Allow-Credentials", "true")
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, HEAD, OPTIONS")
    response.setHeader("Access-Control-Allow-Headers", "content-type,authorization,x-http-method-override,origin,x-requested-with,accept,withCredentials,accept-encoding")

    filterChain.doFilter(request, new CorsResponseWrapper(response, prohibitedHeaders.asJava))
  }

}
