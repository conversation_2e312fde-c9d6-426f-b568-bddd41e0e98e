package me.socure.superadmin.api.v2.kyc.idgraph.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@Jacksonized
public class TransactionConsortiumResponse {
    private String socureId;
    private String entityId;
    private JsonNode entity;
    private List<String> underlyingRecordIDs;
}
